{"ast": null, "code": "var _jsxFileName = \"D:\\\\Projects\\\\SQL-Agent-Task-Submission-main\\\\SQL-Agent-Task-Submission-main\\\\src\\\\pages\\\\Dashboard.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Link } from 'react-router-dom';\nimport { FiUsers, FiUpload, FiSearch, FiActivity, FiTrendingUp, FiClock, FiFileText, FiArrowRight } from 'react-icons/fi';\nimport './Dashboard.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Dashboard = () => {\n  _s();\n  const [stats, setStats] = useState({\n    totalResumes: 0,\n    searchResults: 0,\n    aiPowered: 'GPT-4',\n    version: 'v2.0'\n  });\n  const [recentActivity, setRecentActivity] = useState([]);\n  const [quickStats, setQuickStats] = useState([]);\n  useEffect(() => {\n    // Simulate loading data\n    setStats({\n      totalResumes: 247,\n      searchResults: 0,\n      aiPowered: 'GPT-4',\n      version: 'v2.0'\n    });\n    setRecentActivity([{\n      id: 1,\n      action: 'Resume uploaded',\n      file: 'john_doe_resume.pdf',\n      time: '2 minutes ago',\n      type: 'upload'\n    }, {\n      id: 2,\n      action: 'Search completed',\n      query: 'Python developers',\n      results: 15,\n      time: '5 minutes ago',\n      type: 'search'\n    }, {\n      id: 3,\n      action: 'Resume processed',\n      file: 'jane_smith_cv.pdf',\n      time: '10 minutes ago',\n      type: 'process'\n    }, {\n      id: 4,\n      action: 'Bulk upload completed',\n      count: 5,\n      time: '1 hour ago',\n      type: 'bulk'\n    }, {\n      id: 5,\n      action: 'Search completed',\n      query: 'UI/UX designers',\n      results: 8,\n      time: '2 hours ago',\n      type: 'search'\n    }]);\n    setQuickStats([{\n      label: 'This Week',\n      value: '23',\n      change: '+12%',\n      trend: 'up'\n    }, {\n      label: 'This Month',\n      value: '156',\n      change: '+8%',\n      trend: 'up'\n    }, {\n      label: 'Success Rate',\n      value: '98.5%',\n      change: '+0.3%',\n      trend: 'up'\n    }, {\n      label: 'Avg. Process Time',\n      value: '2.3s',\n      change: '-0.5s',\n      trend: 'up'\n    }]);\n  }, []);\n  const quickActions = [{\n    title: 'Upload Resume',\n    description: 'Add new resumes to your collection',\n    icon: FiUpload,\n    color: '#10b981',\n    link: '/upload'\n  }, {\n    title: 'Search Resumes',\n    description: 'Find candidates using natural language',\n    icon: FiSearch,\n    color: '#f59e0b',\n    link: '/search'\n  }, {\n    title: 'View All Resumes',\n    description: 'Browse your complete resume database',\n    icon: FiUsers,\n    color: '#8b5cf6',\n    link: '/resumes'\n  }, {\n    title: 'Activity Log',\n    description: 'Track all system activities',\n    icon: FiActivity,\n    color: '#ef4444',\n    link: '/activity'\n  }];\n  const getActivityIcon = type => {\n    switch (type) {\n      case 'upload':\n        return /*#__PURE__*/_jsxDEV(FiUpload, {\n          className: \"activity-icon upload\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 85,\n          columnNumber: 16\n        }, this);\n      case 'search':\n        return /*#__PURE__*/_jsxDEV(FiSearch, {\n          className: \"activity-icon search\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 87,\n          columnNumber: 16\n        }, this);\n      case 'process':\n        return /*#__PURE__*/_jsxDEV(FiFileText, {\n          className: \"activity-icon process\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 89,\n          columnNumber: 16\n        }, this);\n      case 'bulk':\n        return /*#__PURE__*/_jsxDEV(FiUsers, {\n          className: \"activity-icon bulk\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 91,\n          columnNumber: 16\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(FiActivity, {\n          className: \"activity-icon default\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 93,\n          columnNumber: 16\n        }, this);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"dashboard\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"welcome-section\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"welcome-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          children: \"Welcome back! \\uD83D\\uDC4B\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 102,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Here's what's happening with your Resume AI Agent today.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 103,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 101,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"welcome-stats\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-item\",\n          children: [/*#__PURE__*/_jsxDEV(FiClock, {\n            className: \"stat-icon\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 107,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Last activity: 2 min ago\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 108,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 106,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 105,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 100,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"stats-grid\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stat-card primary\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-header\",\n          children: [/*#__PURE__*/_jsxDEV(FiUsers, {\n            className: \"stat-icon\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 117,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"stat-label\",\n            children: \"Total Resumes\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 118,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 116,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-value\",\n          children: stats.totalResumes\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 120,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-change positive\",\n          children: [/*#__PURE__*/_jsxDEV(FiTrendingUp, {\n            size: 14\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 122,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"+12 this week\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 123,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 121,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 115,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stat-card secondary\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-header\",\n          children: [/*#__PURE__*/_jsxDEV(FiSearch, {\n            className: \"stat-icon\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 129,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"stat-label\",\n            children: \"Search Results\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 130,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 128,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-value\",\n          children: stats.searchResults\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 132,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-change neutral\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Ready to search\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 134,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 133,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 127,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stat-card accent\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"ai-icon\",\n            children: \"\\u26A1\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 140,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"stat-label\",\n            children: \"AI Powered\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 141,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 139,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-value\",\n          children: stats.aiPowered\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 143,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-change positive\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Latest model\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 145,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 144,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 138,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stat-card info\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"version-icon\",\n            children: \"\\uD83D\\uDE80\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 151,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"stat-label\",\n            children: \"Version\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 152,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 150,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-value\",\n          children: stats.version\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 154,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-change positive\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Up to date\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 156,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 155,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 149,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 114,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"quick-actions-section\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"Quick Actions\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 163,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"quick-actions-grid\",\n        children: quickActions.map((action, index) => {\n          const Icon = action.icon;\n          return /*#__PURE__*/_jsxDEV(Link, {\n            to: action.link,\n            className: \"action-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"action-icon\",\n              style: {\n                backgroundColor: `${action.color}15`,\n                color: action.color\n              },\n              children: /*#__PURE__*/_jsxDEV(Icon, {\n                size: 24\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 170,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 169,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"action-content\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                children: action.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 173,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: action.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 174,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 172,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(FiArrowRight, {\n              className: \"action-arrow\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 176,\n              columnNumber: 17\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 168,\n            columnNumber: 15\n          }, this);\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 164,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 162,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bottom-grid\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"activity-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"section-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            children: \"Recent Activity\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 188,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/activity\",\n            className: \"view-all-link\",\n            children: \"View all\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 189,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 187,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"activity-list\",\n          children: recentActivity.map(activity => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"activity-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"activity-icon-wrapper\",\n              children: getActivityIcon(activity.type)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 194,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"activity-content\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"activity-text\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"activity-action\",\n                  children: activity.action\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 199,\n                  columnNumber: 21\n                }, this), activity.file && /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"activity-detail\",\n                  children: [\": \", activity.file]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 200,\n                  columnNumber: 39\n                }, this), activity.query && /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"activity-detail\",\n                  children: [\": \\\"\", activity.query, \"\\\"\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 201,\n                  columnNumber: 40\n                }, this), activity.results && /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"activity-detail\",\n                  children: [\" (\", activity.results, \" results)\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 202,\n                  columnNumber: 42\n                }, this), activity.count && /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"activity-detail\",\n                  children: [\" (\", activity.count, \" files)\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 203,\n                  columnNumber: 40\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 198,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"activity-time\",\n                children: activity.time\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 205,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 197,\n              columnNumber: 17\n            }, this)]\n          }, activity.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 193,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 191,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 186,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"quick-stats-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"section-header\",\n          children: /*#__PURE__*/_jsxDEV(\"h2\", {\n            children: \"Quick Stats\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 215,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 214,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"quick-stats-grid\",\n          children: quickStats.map((stat, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"quick-stat-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"quick-stat-label\",\n              children: stat.label\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 220,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"quick-stat-value\",\n              children: stat.value\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 221,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: `quick-stat-change ${stat.trend}`,\n              children: [stat.trend === 'up' && /*#__PURE__*/_jsxDEV(FiTrendingUp, {\n                size: 12\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 223,\n                columnNumber: 43\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: stat.change\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 224,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 222,\n              columnNumber: 17\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 219,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 217,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 213,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 184,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 98,\n    columnNumber: 5\n  }, this);\n};\n_s(Dashboard, \"32xVQziK21dGfZKDSfkSxrLoDjo=\");\n_c = Dashboard;\nexport default Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Link", "FiUsers", "FiUpload", "FiSearch", "FiActivity", "FiTrendingUp", "<PERSON><PERSON><PERSON>", "FiFileText", "FiArrowRight", "jsxDEV", "_jsxDEV", "Dashboard", "_s", "stats", "setStats", "totalResumes", "searchResults", "aiPowered", "version", "recentActivity", "setRecentActivity", "quickStats", "setQuickStats", "id", "action", "file", "time", "type", "query", "results", "count", "label", "value", "change", "trend", "quickActions", "title", "description", "icon", "color", "link", "getActivityIcon", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "children", "size", "map", "index", "Icon", "to", "style", "backgroundColor", "activity", "stat", "_c", "$RefreshReg$"], "sources": ["D:/Projects/SQL-Agent-Task-Submission-main/SQL-Agent-Task-Submission-main/src/pages/Dashboard.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Link } from 'react-router-dom';\nimport { \n  FiUsers, \n  FiUpload, \n  FiSearch, \n  FiActivity,\n  FiTrendingUp,\n  FiClock,\n  FiFileText,\n  FiArrowRight\n} from 'react-icons/fi';\nimport './Dashboard.css';\n\nconst Dashboard = () => {\n  const [stats, setStats] = useState({\n    totalResumes: 0,\n    searchResults: 0,\n    aiPowered: 'GPT-4',\n    version: 'v2.0'\n  });\n\n  const [recentActivity, setRecentActivity] = useState([]);\n  const [quickStats, setQuickStats] = useState([]);\n\n  useEffect(() => {\n    // Simulate loading data\n    setStats({\n      totalResumes: 247,\n      searchResults: 0,\n      aiPowered: 'GPT-4',\n      version: 'v2.0'\n    });\n\n    setRecentActivity([\n      { id: 1, action: 'Resume uploaded', file: 'john_doe_resume.pdf', time: '2 minutes ago', type: 'upload' },\n      { id: 2, action: 'Search completed', query: 'Python developers', results: 15, time: '5 minutes ago', type: 'search' },\n      { id: 3, action: 'Resume processed', file: 'jane_smith_cv.pdf', time: '10 minutes ago', type: 'process' },\n      { id: 4, action: 'Bulk upload completed', count: 5, time: '1 hour ago', type: 'bulk' },\n      { id: 5, action: 'Search completed', query: 'UI/UX designers', results: 8, time: '2 hours ago', type: 'search' }\n    ]);\n\n    setQuickStats([\n      { label: 'This Week', value: '23', change: '+12%', trend: 'up' },\n      { label: 'This Month', value: '156', change: '+8%', trend: 'up' },\n      { label: 'Success Rate', value: '98.5%', change: '+0.3%', trend: 'up' },\n      { label: 'Avg. Process Time', value: '2.3s', change: '-0.5s', trend: 'up' }\n    ]);\n  }, []);\n\n  const quickActions = [\n    {\n      title: 'Upload Resume',\n      description: 'Add new resumes to your collection',\n      icon: FiUpload,\n      color: '#10b981',\n      link: '/upload'\n    },\n    {\n      title: 'Search Resumes',\n      description: 'Find candidates using natural language',\n      icon: FiSearch,\n      color: '#f59e0b',\n      link: '/search'\n    },\n    {\n      title: 'View All Resumes',\n      description: 'Browse your complete resume database',\n      icon: FiUsers,\n      color: '#8b5cf6',\n      link: '/resumes'\n    },\n    {\n      title: 'Activity Log',\n      description: 'Track all system activities',\n      icon: FiActivity,\n      color: '#ef4444',\n      link: '/activity'\n    }\n  ];\n\n  const getActivityIcon = (type) => {\n    switch (type) {\n      case 'upload':\n        return <FiUpload className=\"activity-icon upload\" />;\n      case 'search':\n        return <FiSearch className=\"activity-icon search\" />;\n      case 'process':\n        return <FiFileText className=\"activity-icon process\" />;\n      case 'bulk':\n        return <FiUsers className=\"activity-icon bulk\" />;\n      default:\n        return <FiActivity className=\"activity-icon default\" />;\n    }\n  };\n\n  return (\n    <div className=\"dashboard\">\n      {/* Welcome Section */}\n      <div className=\"welcome-section\">\n        <div className=\"welcome-content\">\n          <h1>Welcome back! 👋</h1>\n          <p>Here's what's happening with your Resume AI Agent today.</p>\n        </div>\n        <div className=\"welcome-stats\">\n          <div className=\"stat-item\">\n            <FiClock className=\"stat-icon\" />\n            <span>Last activity: 2 min ago</span>\n          </div>\n        </div>\n      </div>\n\n      {/* Main Stats */}\n      <div className=\"stats-grid\">\n        <div className=\"stat-card primary\">\n          <div className=\"stat-header\">\n            <FiUsers className=\"stat-icon\" />\n            <span className=\"stat-label\">Total Resumes</span>\n          </div>\n          <div className=\"stat-value\">{stats.totalResumes}</div>\n          <div className=\"stat-change positive\">\n            <FiTrendingUp size={14} />\n            <span>+12 this week</span>\n          </div>\n        </div>\n\n        <div className=\"stat-card secondary\">\n          <div className=\"stat-header\">\n            <FiSearch className=\"stat-icon\" />\n            <span className=\"stat-label\">Search Results</span>\n          </div>\n          <div className=\"stat-value\">{stats.searchResults}</div>\n          <div className=\"stat-change neutral\">\n            <span>Ready to search</span>\n          </div>\n        </div>\n\n        <div className=\"stat-card accent\">\n          <div className=\"stat-header\">\n            <span className=\"ai-icon\">⚡</span>\n            <span className=\"stat-label\">AI Powered</span>\n          </div>\n          <div className=\"stat-value\">{stats.aiPowered}</div>\n          <div className=\"stat-change positive\">\n            <span>Latest model</span>\n          </div>\n        </div>\n\n        <div className=\"stat-card info\">\n          <div className=\"stat-header\">\n            <span className=\"version-icon\">🚀</span>\n            <span className=\"stat-label\">Version</span>\n          </div>\n          <div className=\"stat-value\">{stats.version}</div>\n          <div className=\"stat-change positive\">\n            <span>Up to date</span>\n          </div>\n        </div>\n      </div>\n\n      {/* Quick Actions */}\n      <div className=\"quick-actions-section\">\n        <h2>Quick Actions</h2>\n        <div className=\"quick-actions-grid\">\n          {quickActions.map((action, index) => {\n            const Icon = action.icon;\n            return (\n              <Link key={index} to={action.link} className=\"action-card\">\n                <div className=\"action-icon\" style={{ backgroundColor: `${action.color}15`, color: action.color }}>\n                  <Icon size={24} />\n                </div>\n                <div className=\"action-content\">\n                  <h3>{action.title}</h3>\n                  <p>{action.description}</p>\n                </div>\n                <FiArrowRight className=\"action-arrow\" />\n              </Link>\n            );\n          })}\n        </div>\n      </div>\n\n      {/* Bottom Grid */}\n      <div className=\"bottom-grid\">\n        {/* Recent Activity */}\n        <div className=\"activity-section\">\n          <div className=\"section-header\">\n            <h2>Recent Activity</h2>\n            <Link to=\"/activity\" className=\"view-all-link\">View all</Link>\n          </div>\n          <div className=\"activity-list\">\n            {recentActivity.map((activity) => (\n              <div key={activity.id} className=\"activity-item\">\n                <div className=\"activity-icon-wrapper\">\n                  {getActivityIcon(activity.type)}\n                </div>\n                <div className=\"activity-content\">\n                  <div className=\"activity-text\">\n                    <span className=\"activity-action\">{activity.action}</span>\n                    {activity.file && <span className=\"activity-detail\">: {activity.file}</span>}\n                    {activity.query && <span className=\"activity-detail\">: \"{activity.query}\"</span>}\n                    {activity.results && <span className=\"activity-detail\"> ({activity.results} results)</span>}\n                    {activity.count && <span className=\"activity-detail\"> ({activity.count} files)</span>}\n                  </div>\n                  <div className=\"activity-time\">{activity.time}</div>\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n\n        {/* Quick Stats */}\n        <div className=\"quick-stats-section\">\n          <div className=\"section-header\">\n            <h2>Quick Stats</h2>\n          </div>\n          <div className=\"quick-stats-grid\">\n            {quickStats.map((stat, index) => (\n              <div key={index} className=\"quick-stat-item\">\n                <div className=\"quick-stat-label\">{stat.label}</div>\n                <div className=\"quick-stat-value\">{stat.value}</div>\n                <div className={`quick-stat-change ${stat.trend}`}>\n                  {stat.trend === 'up' && <FiTrendingUp size={12} />}\n                  <span>{stat.change}</span>\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Dashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,IAAI,QAAQ,kBAAkB;AACvC,SACEC,OAAO,EACPC,QAAQ,EACRC,QAAQ,EACRC,UAAU,EACVC,YAAY,EACZC,OAAO,EACPC,UAAU,EACVC,YAAY,QACP,gBAAgB;AACvB,OAAO,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzB,MAAMC,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtB,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGhB,QAAQ,CAAC;IACjCiB,YAAY,EAAE,CAAC;IACfC,aAAa,EAAE,CAAC;IAChBC,SAAS,EAAE,OAAO;IAClBC,OAAO,EAAE;EACX,CAAC,CAAC;EAEF,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGtB,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACuB,UAAU,EAAEC,aAAa,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EAEhDC,SAAS,CAAC,MAAM;IACd;IACAe,QAAQ,CAAC;MACPC,YAAY,EAAE,GAAG;MACjBC,aAAa,EAAE,CAAC;MAChBC,SAAS,EAAE,OAAO;MAClBC,OAAO,EAAE;IACX,CAAC,CAAC;IAEFE,iBAAiB,CAAC,CAChB;MAAEG,EAAE,EAAE,CAAC;MAAEC,MAAM,EAAE,iBAAiB;MAAEC,IAAI,EAAE,qBAAqB;MAAEC,IAAI,EAAE,eAAe;MAAEC,IAAI,EAAE;IAAS,CAAC,EACxG;MAAEJ,EAAE,EAAE,CAAC;MAAEC,MAAM,EAAE,kBAAkB;MAAEI,KAAK,EAAE,mBAAmB;MAAEC,OAAO,EAAE,EAAE;MAAEH,IAAI,EAAE,eAAe;MAAEC,IAAI,EAAE;IAAS,CAAC,EACrH;MAAEJ,EAAE,EAAE,CAAC;MAAEC,MAAM,EAAE,kBAAkB;MAAEC,IAAI,EAAE,mBAAmB;MAAEC,IAAI,EAAE,gBAAgB;MAAEC,IAAI,EAAE;IAAU,CAAC,EACzG;MAAEJ,EAAE,EAAE,CAAC;MAAEC,MAAM,EAAE,uBAAuB;MAAEM,KAAK,EAAE,CAAC;MAAEJ,IAAI,EAAE,YAAY;MAAEC,IAAI,EAAE;IAAO,CAAC,EACtF;MAAEJ,EAAE,EAAE,CAAC;MAAEC,MAAM,EAAE,kBAAkB;MAAEI,KAAK,EAAE,iBAAiB;MAAEC,OAAO,EAAE,CAAC;MAAEH,IAAI,EAAE,aAAa;MAAEC,IAAI,EAAE;IAAS,CAAC,CACjH,CAAC;IAEFL,aAAa,CAAC,CACZ;MAAES,KAAK,EAAE,WAAW;MAAEC,KAAK,EAAE,IAAI;MAAEC,MAAM,EAAE,MAAM;MAAEC,KAAK,EAAE;IAAK,CAAC,EAChE;MAAEH,KAAK,EAAE,YAAY;MAAEC,KAAK,EAAE,KAAK;MAAEC,MAAM,EAAE,KAAK;MAAEC,KAAK,EAAE;IAAK,CAAC,EACjE;MAAEH,KAAK,EAAE,cAAc;MAAEC,KAAK,EAAE,OAAO;MAAEC,MAAM,EAAE,OAAO;MAAEC,KAAK,EAAE;IAAK,CAAC,EACvE;MAAEH,KAAK,EAAE,mBAAmB;MAAEC,KAAK,EAAE,MAAM;MAAEC,MAAM,EAAE,OAAO;MAAEC,KAAK,EAAE;IAAK,CAAC,CAC5E,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMC,YAAY,GAAG,CACnB;IACEC,KAAK,EAAE,eAAe;IACtBC,WAAW,EAAE,oCAAoC;IACjDC,IAAI,EAAEpC,QAAQ;IACdqC,KAAK,EAAE,SAAS;IAChBC,IAAI,EAAE;EACR,CAAC,EACD;IACEJ,KAAK,EAAE,gBAAgB;IACvBC,WAAW,EAAE,wCAAwC;IACrDC,IAAI,EAAEnC,QAAQ;IACdoC,KAAK,EAAE,SAAS;IAChBC,IAAI,EAAE;EACR,CAAC,EACD;IACEJ,KAAK,EAAE,kBAAkB;IACzBC,WAAW,EAAE,sCAAsC;IACnDC,IAAI,EAAErC,OAAO;IACbsC,KAAK,EAAE,SAAS;IAChBC,IAAI,EAAE;EACR,CAAC,EACD;IACEJ,KAAK,EAAE,cAAc;IACrBC,WAAW,EAAE,6BAA6B;IAC1CC,IAAI,EAAElC,UAAU;IAChBmC,KAAK,EAAE,SAAS;IAChBC,IAAI,EAAE;EACR,CAAC,CACF;EAED,MAAMC,eAAe,GAAId,IAAI,IAAK;IAChC,QAAQA,IAAI;MACV,KAAK,QAAQ;QACX,oBAAOjB,OAAA,CAACR,QAAQ;UAACwC,SAAS,EAAC;QAAsB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACtD,KAAK,QAAQ;QACX,oBAAOpC,OAAA,CAACP,QAAQ;UAACuC,SAAS,EAAC;QAAsB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACtD,KAAK,SAAS;QACZ,oBAAOpC,OAAA,CAACH,UAAU;UAACmC,SAAS,EAAC;QAAuB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACzD,KAAK,MAAM;QACT,oBAAOpC,OAAA,CAACT,OAAO;UAACyC,SAAS,EAAC;QAAoB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACnD;QACE,oBAAOpC,OAAA,CAACN,UAAU;UAACsC,SAAS,EAAC;QAAuB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;IAC3D;EACF,CAAC;EAED,oBACEpC,OAAA;IAAKgC,SAAS,EAAC,WAAW;IAAAK,QAAA,gBAExBrC,OAAA;MAAKgC,SAAS,EAAC,iBAAiB;MAAAK,QAAA,gBAC9BrC,OAAA;QAAKgC,SAAS,EAAC,iBAAiB;QAAAK,QAAA,gBAC9BrC,OAAA;UAAAqC,QAAA,EAAI;QAAgB;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACzBpC,OAAA;UAAAqC,QAAA,EAAG;QAAwD;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5D,CAAC,eACNpC,OAAA;QAAKgC,SAAS,EAAC,eAAe;QAAAK,QAAA,eAC5BrC,OAAA;UAAKgC,SAAS,EAAC,WAAW;UAAAK,QAAA,gBACxBrC,OAAA,CAACJ,OAAO;YAACoC,SAAS,EAAC;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACjCpC,OAAA;YAAAqC,QAAA,EAAM;UAAwB;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNpC,OAAA;MAAKgC,SAAS,EAAC,YAAY;MAAAK,QAAA,gBACzBrC,OAAA;QAAKgC,SAAS,EAAC,mBAAmB;QAAAK,QAAA,gBAChCrC,OAAA;UAAKgC,SAAS,EAAC,aAAa;UAAAK,QAAA,gBAC1BrC,OAAA,CAACT,OAAO;YAACyC,SAAS,EAAC;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACjCpC,OAAA;YAAMgC,SAAS,EAAC,YAAY;YAAAK,QAAA,EAAC;UAAa;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9C,CAAC,eACNpC,OAAA;UAAKgC,SAAS,EAAC,YAAY;UAAAK,QAAA,EAAElC,KAAK,CAACE;QAAY;UAAA4B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACtDpC,OAAA;UAAKgC,SAAS,EAAC,sBAAsB;UAAAK,QAAA,gBACnCrC,OAAA,CAACL,YAAY;YAAC2C,IAAI,EAAE;UAAG;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC1BpC,OAAA;YAAAqC,QAAA,EAAM;UAAa;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENpC,OAAA;QAAKgC,SAAS,EAAC,qBAAqB;QAAAK,QAAA,gBAClCrC,OAAA;UAAKgC,SAAS,EAAC,aAAa;UAAAK,QAAA,gBAC1BrC,OAAA,CAACP,QAAQ;YAACuC,SAAS,EAAC;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAClCpC,OAAA;YAAMgC,SAAS,EAAC,YAAY;YAAAK,QAAA,EAAC;UAAc;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/C,CAAC,eACNpC,OAAA;UAAKgC,SAAS,EAAC,YAAY;UAAAK,QAAA,EAAElC,KAAK,CAACG;QAAa;UAAA2B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACvDpC,OAAA;UAAKgC,SAAS,EAAC,qBAAqB;UAAAK,QAAA,eAClCrC,OAAA;YAAAqC,QAAA,EAAM;UAAe;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENpC,OAAA;QAAKgC,SAAS,EAAC,kBAAkB;QAAAK,QAAA,gBAC/BrC,OAAA;UAAKgC,SAAS,EAAC,aAAa;UAAAK,QAAA,gBAC1BrC,OAAA;YAAMgC,SAAS,EAAC,SAAS;YAAAK,QAAA,EAAC;UAAC;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAClCpC,OAAA;YAAMgC,SAAS,EAAC,YAAY;YAAAK,QAAA,EAAC;UAAU;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3C,CAAC,eACNpC,OAAA;UAAKgC,SAAS,EAAC,YAAY;UAAAK,QAAA,EAAElC,KAAK,CAACI;QAAS;UAAA0B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACnDpC,OAAA;UAAKgC,SAAS,EAAC,sBAAsB;UAAAK,QAAA,eACnCrC,OAAA;YAAAqC,QAAA,EAAM;UAAY;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENpC,OAAA;QAAKgC,SAAS,EAAC,gBAAgB;QAAAK,QAAA,gBAC7BrC,OAAA;UAAKgC,SAAS,EAAC,aAAa;UAAAK,QAAA,gBAC1BrC,OAAA;YAAMgC,SAAS,EAAC,cAAc;YAAAK,QAAA,EAAC;UAAE;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACxCpC,OAAA;YAAMgC,SAAS,EAAC,YAAY;YAAAK,QAAA,EAAC;UAAO;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxC,CAAC,eACNpC,OAAA;UAAKgC,SAAS,EAAC,YAAY;UAAAK,QAAA,EAAElC,KAAK,CAACK;QAAO;UAAAyB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACjDpC,OAAA;UAAKgC,SAAS,EAAC,sBAAsB;UAAAK,QAAA,eACnCrC,OAAA;YAAAqC,QAAA,EAAM;UAAU;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNpC,OAAA;MAAKgC,SAAS,EAAC,uBAAuB;MAAAK,QAAA,gBACpCrC,OAAA;QAAAqC,QAAA,EAAI;MAAa;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACtBpC,OAAA;QAAKgC,SAAS,EAAC,oBAAoB;QAAAK,QAAA,EAChCZ,YAAY,CAACc,GAAG,CAAC,CAACzB,MAAM,EAAE0B,KAAK,KAAK;UACnC,MAAMC,IAAI,GAAG3B,MAAM,CAACc,IAAI;UACxB,oBACE5B,OAAA,CAACV,IAAI;YAAaoD,EAAE,EAAE5B,MAAM,CAACgB,IAAK;YAACE,SAAS,EAAC,aAAa;YAAAK,QAAA,gBACxDrC,OAAA;cAAKgC,SAAS,EAAC,aAAa;cAACW,KAAK,EAAE;gBAAEC,eAAe,EAAE,GAAG9B,MAAM,CAACe,KAAK,IAAI;gBAAEA,KAAK,EAAEf,MAAM,CAACe;cAAM,CAAE;cAAAQ,QAAA,eAChGrC,OAAA,CAACyC,IAAI;gBAACH,IAAI,EAAE;cAAG;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf,CAAC,eACNpC,OAAA;cAAKgC,SAAS,EAAC,gBAAgB;cAAAK,QAAA,gBAC7BrC,OAAA;gBAAAqC,QAAA,EAAKvB,MAAM,CAACY;cAAK;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACvBpC,OAAA;gBAAAqC,QAAA,EAAIvB,MAAM,CAACa;cAAW;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB,CAAC,eACNpC,OAAA,CAACF,YAAY;cAACkC,SAAS,EAAC;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA,GARhCI,KAAK;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OASV,CAAC;QAEX,CAAC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNpC,OAAA;MAAKgC,SAAS,EAAC,aAAa;MAAAK,QAAA,gBAE1BrC,OAAA;QAAKgC,SAAS,EAAC,kBAAkB;QAAAK,QAAA,gBAC/BrC,OAAA;UAAKgC,SAAS,EAAC,gBAAgB;UAAAK,QAAA,gBAC7BrC,OAAA;YAAAqC,QAAA,EAAI;UAAe;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACxBpC,OAAA,CAACV,IAAI;YAACoD,EAAE,EAAC,WAAW;YAACV,SAAS,EAAC,eAAe;YAAAK,QAAA,EAAC;UAAQ;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3D,CAAC,eACNpC,OAAA;UAAKgC,SAAS,EAAC,eAAe;UAAAK,QAAA,EAC3B5B,cAAc,CAAC8B,GAAG,CAAEM,QAAQ,iBAC3B7C,OAAA;YAAuBgC,SAAS,EAAC,eAAe;YAAAK,QAAA,gBAC9CrC,OAAA;cAAKgC,SAAS,EAAC,uBAAuB;cAAAK,QAAA,EACnCN,eAAe,CAACc,QAAQ,CAAC5B,IAAI;YAAC;cAAAgB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5B,CAAC,eACNpC,OAAA;cAAKgC,SAAS,EAAC,kBAAkB;cAAAK,QAAA,gBAC/BrC,OAAA;gBAAKgC,SAAS,EAAC,eAAe;gBAAAK,QAAA,gBAC5BrC,OAAA;kBAAMgC,SAAS,EAAC,iBAAiB;kBAAAK,QAAA,EAAEQ,QAAQ,CAAC/B;gBAAM;kBAAAmB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,EACzDS,QAAQ,CAAC9B,IAAI,iBAAIf,OAAA;kBAAMgC,SAAS,EAAC,iBAAiB;kBAAAK,QAAA,GAAC,IAAE,EAACQ,QAAQ,CAAC9B,IAAI;gBAAA;kBAAAkB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,EAC3ES,QAAQ,CAAC3B,KAAK,iBAAIlB,OAAA;kBAAMgC,SAAS,EAAC,iBAAiB;kBAAAK,QAAA,GAAC,MAAG,EAACQ,QAAQ,CAAC3B,KAAK,EAAC,IAAC;gBAAA;kBAAAe,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,EAC/ES,QAAQ,CAAC1B,OAAO,iBAAInB,OAAA;kBAAMgC,SAAS,EAAC,iBAAiB;kBAAAK,QAAA,GAAC,IAAE,EAACQ,QAAQ,CAAC1B,OAAO,EAAC,WAAS;gBAAA;kBAAAc,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,EAC1FS,QAAQ,CAACzB,KAAK,iBAAIpB,OAAA;kBAAMgC,SAAS,EAAC,iBAAiB;kBAAAK,QAAA,GAAC,IAAE,EAACQ,QAAQ,CAACzB,KAAK,EAAC,SAAO;gBAAA;kBAAAa,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClF,CAAC,eACNpC,OAAA;gBAAKgC,SAAS,EAAC,eAAe;gBAAAK,QAAA,EAAEQ,QAAQ,CAAC7B;cAAI;gBAAAiB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjD,CAAC;UAAA,GAbES,QAAQ,CAAChC,EAAE;YAAAoB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAchB,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNpC,OAAA;QAAKgC,SAAS,EAAC,qBAAqB;QAAAK,QAAA,gBAClCrC,OAAA;UAAKgC,SAAS,EAAC,gBAAgB;UAAAK,QAAA,eAC7BrC,OAAA;YAAAqC,QAAA,EAAI;UAAW;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB,CAAC,eACNpC,OAAA;UAAKgC,SAAS,EAAC,kBAAkB;UAAAK,QAAA,EAC9B1B,UAAU,CAAC4B,GAAG,CAAC,CAACO,IAAI,EAAEN,KAAK,kBAC1BxC,OAAA;YAAiBgC,SAAS,EAAC,iBAAiB;YAAAK,QAAA,gBAC1CrC,OAAA;cAAKgC,SAAS,EAAC,kBAAkB;cAAAK,QAAA,EAAES,IAAI,CAACzB;YAAK;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACpDpC,OAAA;cAAKgC,SAAS,EAAC,kBAAkB;cAAAK,QAAA,EAAES,IAAI,CAACxB;YAAK;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACpDpC,OAAA;cAAKgC,SAAS,EAAE,qBAAqBc,IAAI,CAACtB,KAAK,EAAG;cAAAa,QAAA,GAC/CS,IAAI,CAACtB,KAAK,KAAK,IAAI,iBAAIxB,OAAA,CAACL,YAAY;gBAAC2C,IAAI,EAAE;cAAG;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAClDpC,OAAA;gBAAAqC,QAAA,EAAOS,IAAI,CAACvB;cAAM;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB,CAAC;UAAA,GANEI,KAAK;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAOV,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAClC,EAAA,CA1NID,SAAS;AAAA8C,EAAA,GAAT9C,SAAS;AA4Nf,eAAeA,SAAS;AAAC,IAAA8C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}