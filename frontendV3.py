import os
import streamlit as st
import pandas as pd
from io import StringIO
import requests
import json
import time

from pathlib import Path
from DisplayJson import CDisplayResume
from datetime import datetime
from extractDataParallel import CProcessDocument
from dotenv import load_dotenv

from textwrap import dedent
from helperMongoDb import MongoDBClient
import hashlib

from textwrap import dedent
from helperMongoDb import MongoDBClient
import hashlib

from textwrap import dedent
from helperMongoDb import MongoDBClient
import hashlib

from textwrap import dedent
from helperMongoDb import MongoDBClient
import hashlib

from textwrap import dedent
from helperMongoDb import MongoDBClient
import hashlib 

# Set page configuration at the very top
st.set_page_config(page_title="Resume AI Agent", layout="wide")

load_dotenv()

# Inject custom CSS
st.markdown(
    """
    <style>
    .block-container {
        padding-top: 2rem;
        padding-bottom: 2rem;
        padding-left: 2rem;
        padding-right: 2rem;
    }
    header {visibility: hidden;}
    [data-testid="stHorizontalBlock"] > div {
        padding-left: 1rem;
        padding-right: 1rem;
    }
    </style>
    """,
    unsafe_allow_html=True
)

# strApiUrl = "http://127.0.0.1:8503/query/"
STR_API_URL = os.getenv('STR_API_URL')
# STR_API_URL_FETCH_ALL = os.getenv('STR_API_URL_FETCH_ALL')
STR_API_URL_FETCH_ALL = "http://192.168.1.205:8000/showall"

# Use session state to store uploaded files
if "uploaded_files" not in st.session_state:
    st.session_state["uploaded_files"] = None

# uploader_key = "pdf_uploader"
    
def process_uploaded_pdfs():
    """Simulates PDF processing with notifications"""
    with st.status("📄 Processing uploaded PDFs...", expanded=True) as status:
        objCProcessDocument = CProcessDocument(strVendorName="Resume_Schema")
        objCProcessDocument.MprocessAllDocuments(iFilesToProcess=1)
        
        # MakeReadableResponseData.MReadableResponseData() 

        status.update(label="✅ Processing completed!", state="complete", expanded=False)
        st.toast("🎯 All PDFs processed successfully!", icon="✅")
    # Reset the UI after processing
    time.sleep(3)  # Wait for a moment before clearing
    st.session_state["uploaded_files"] = None
    # Remove the file uploader key and force a rerun to clear it
    if "pdf_uploader" in st.session_state:
        del st.session_state["pdf_uploader"]
    st.rerun()  # Refresh the Streamlit app to reset the uploader
    
     
# Helper function to extract 10th marks from a resume
def extract_10th_marks(resume):
    try:
        for education in resume["Resume"]["Education"]:
            degree = education["Degree"].lower()
            if "high school" in degree or "10th" in degree:
                marks = education["GPA/Marks/%"]
                try:
                    marks_float = float(marks)
                    if marks_float > 0:  # Only consider positive values as valid marks
                        return marks_float
                except ValueError:
                    pass  # Skip if marks cannot be converted to float
    except (KeyError, TypeError):
        pass  # Return None if resume structure is invalid
    return None


def calculate_checksum(file_data):
    """Calculate SHA-256 checksum from bytes."""
    sha256_hash = hashlib.sha256()
    sha256_hash.update(file_data)  # Directly update hash with bytes
    return sha256_hash.hexdigest()


def main():
    # Initialize session state variables if they don't exist
    if "query_result" not in st.session_state:
        st.session_state["query_result"] = ""
    if "resumes" not in st.session_state:
        st.session_state["resumes"] = []  # List to store JSON resumes

    # Define three columns: left for title, center for query input, right for output.
    col1, col2, col3 = st.columns([1, 1, 1])
    
    # Left column: Title and subtitle
    with col1:
        st.title("Resumes AI Agent")
        st.subheader("Search Resumes in Natural Language")
        st.text("Version 0.4.20250221")
    
    # Center column: Query form
    with col2:
        with st.form(key='query_form'):
            st.text_input("Enter your criteria here:", key="user_query")
            submit_button = st.form_submit_button("Submit")
            show_all = st.form_submit_button("Show All")
    
    # Right column: Placeholder for result content
    with col3:
        output_placeholder = st.empty()
    
    if submit_button:
        user_query = st.session_state.user_query  # Get the current input value
        if not user_query:
            st.warning("Please enter a query before submitting.")
        else:
            try:
                response = requests.get(STR_API_URL, params={"naturalQuery": user_query})
                try:
                    data = response.json()  # Convert API response to JSON
                    if isinstance(data, list):  # If API returns a direct list
                        st.session_state["resumes"] = data
                        st.session_state["query_result"] = "Data received successfully."
                    elif isinstance(data, dict):  # If API returns a dictionary
                        st.session_state["resumes"] = data.get("listOfDict", [])
                        st.session_state["query_result"] = data.get("result", "Data received successfully.")
                    else:
                        st.session_state["query_result"] = "Unexpected API response format."
                except json.decoder.JSONDecodeError:
                    st.session_state["query_result"] = "API response is not valid JSON."
                
                # Developer mode output (optional)
                st.session_state["mongoQuery"] = data.get("query", [])
                st.write("Developer mode : API Response =", response, " | ", json.dumps(st.session_state["mongoQuery"]))
            except requests.exceptions.RequestException as e:
                st.error(f"An error occurred while connecting to the API: {e}")
                st.session_state["query_result"] = "Error connecting to API. Displaying sample data."

            # Update the output placeholder in the right column with the result content
            with output_placeholder.container():
                if st.session_state["query_result"]:
                    st.markdown("**AI Answer:**")
                    total_resume = len(st.session_state["resumes"])
                    st.markdown(
                        f"<div style='background-color: #000000; padding: 10px; margin-bottom:10px; border-radius: 5px; color: white;'>{st.session_state['query_result']}<br>Total {len(st.session_state['resumes'])} resume found</div>",
                        unsafe_allow_html=True
                    )
            # Note: Do NOT reset st.session_state.user_query here because it is managed by the widget.

    # File uploader allowing multiple PDF selection
    # Define the folder where PDFs will be saved
    SAVE_PATH = r".\Data\inputData\Resume_Schema"  # Change this to your desired path
    os.makedirs(SAVE_PATH, exist_ok=True)
    MongoClient = MongoDBClient()
    # Create two columns for the upload and search sections
    col1, col2 = st.columns(2)
    # Column 1: File Upload Section
    with col1:
        uploaded_files = st.file_uploader("Choose PDF files", type=["pdf"], accept_multiple_files=True, key="pdf_uploader")

        if uploaded_files:
            st.session_state["uploaded_files"] = [file.name for file in uploaded_files]  # Store files in session state
            if st.button("Save & Process PDFs", use_container_width=True):
                saved_files = []
                duplicate_files = []

                # File saving loop
                for uploaded_file in uploaded_files:
                    
                    try:
                        file_data = bytes(uploaded_file.getbuffer())  # Convert memoryview to bytes 
                        checksum = calculate_checksum(file_data)  # Compute checksum before saving

                        # Check if the checksum already exists in the database
                        if MongoClient.is_duplicate(checksum):
                            duplicate_files.append(uploaded_file.name)
                            continue  # Skip saving this file

                        file_path = Path(SAVE_PATH) / uploaded_file.name
                        saved_files.append(file_path)

                        with open(file_path, "wb") as f:
                            f.write(file_data)

                    except Exception as e:
                        st.error(f"❌ Error saving {uploaded_file.name}: {e}")

                if duplicate_files:
                    st.toast(f"⚠️ {len(duplicate_files)} Duplicate PDFs skipped")

                # ✅ Show a single toast after all files are saved
                if saved_files:
                    st.toast(f"✅ {len(saved_files)} PDF(s) saved successfully!", icon="📂")
                    process_uploaded_pdfs()  # Process after saving
    # Column 2: Universal Search Section
    sorted_resumes = []
    with col2:        
        # Universal Search: Search within all resumes
        if st.session_state["resumes"]:
            
            # Center the search input using custom HTML/CSS
            st.markdown(
                """
                <div style="display: flex; justify-content: center;">
                    <style>
                        div[data-testid="stTextInput"] input {
                            /* width: 50%;   Adjust width as needed */
                            /* text-align: center;  Centers the text inside the input */
                        }
                    </style>
                </div>
                """,
                unsafe_allow_html=True
            )
            # Universal search input that filters the resumes already in session state
            search_term = st.text_input("Universal Search within Resumes", key="resume_search")
            # Filter resumes that contain the search term (case-insensitive)
            if search_term:
                filtered_resumes = [
                    resume for resume in st.session_state["resumes"]
                    if search_term.lower() in json.dumps(resume).lower()
                ]
            else:
                filtered_resumes = st.session_state["resumes"]

            # Sort the filtered resumes by 10th marks in descending order
            sorted_resumes = sorted(
                filtered_resumes,
                key=lambda x: extract_10th_marks(x) or 0,
                reverse=True
            )
    
# -------------------- show all resume -------------------------------------

    # Ensure session state key exists before accessing it
    if "all_resumes" not in st.session_state:
        st.session_state["all_resumes"] = []

    st.markdown("### Resume Data")

    try:
        response = requests.get(STR_API_URL_FETCH_ALL)

        try:
            data = response.json()
            st.session_state["all_resumes"] = data
        except json.decoder.JSONDecodeError:
            st.session_state["query_result"] = "API response is not valid JSON."
    except Exception as e:
        st.error(e)  
    all_resumes = [res for res in st.session_state["all_resumes"]]

    if show_all:         

        
        if all_resumes:
            for i in range(0, len(all_resumes), 2):
                cols = st.columns(2)
                
                # Left resume
                with cols[0]:
                    # st.write(all_resumes[i])
                    CDisplayResume.display_resume_with_delete(all_resumes[i])
                
                # Right resume, if available
                if i + 1 < len(all_resumes):
                    with cols[1]:
                        # with open(sorted_resumes[i+1], "r", encoding="utf-8") as f2:
                        #     data2 = json.load(f2)
                        CDisplayResume.display_resume_with_delete(all_resumes[i+1])
                else:
                    cols[1].empty()
        else:
            st.warning("No resumes in Database.")

# -------------------- show all resume -------------------------------------

    # Ensure session state for tracking deleted resumes
    if "deleted_resumes" not in st.session_state:
        st.session_state["deleted_resumes"] = set()

    # Filter resumes to exclude deleted ones
    visible_resumes = [res for res in sorted_resumes if res.get("_id") not in st.session_state["deleted_resumes"]]


    if visible_resumes:
        for i in range(0, len(visible_resumes), 2):
            cols = st.columns(2)
            
            # Left resume
            with cols[0]:
                # with open(sorted_resumes[i], "r", encoding="utf-8") as f1:
                #     data1 = json.load(f1)
                CDisplayResume.display_resume_with_delete(visible_resumes[i])
            
            # Right resume, if available
            if i + 1 < len(visible_resumes):
                with cols[1]:
                    # with open(sorted_resumes[i+1], "r", encoding="utf-8") as f2:
                    #     data2 = json.load(f2)
                    CDisplayResume.display_resume_with_delete(visible_resumes[i+1])
            else:
                cols[1].empty()
    else:
        st.warning("No resumes match with search criteria.")


if __name__ == "__main__":
    main()