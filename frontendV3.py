import os
import streamlit as st
import pandas as pd
from io import StringIO
import requests
import json
import time
from pathlib import Path
from DisplayJson import CDisplayResume
from datetime import datetime
from extractDataParallel import CProcessDocument
from dotenv import load_dotenv
from textwrap import dedent
from helperMongoDb import MongoDBClient
import hashlib

# Set page configuration at the very top
st.set_page_config(
    page_title="Resume AI Agent",
    layout="wide",
    page_icon="🤖",
    initial_sidebar_state="collapsed"
)

load_dotenv()

# Enhanced custom CSS for modern, attractive design
st.markdown(
    """
    <style>
    @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

    /* Global Styles */
    .stApp {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        font-family: 'Inter', sans-serif;
    }

    .block-container {
        padding-top: 1rem;
        padding-bottom: 2rem;
        padding-left: 2rem;
        padding-right: 2rem;
        background: rgba(255, 255, 255, 0.95);
        border-radius: 20px;
        margin: 1rem;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        backdrop-filter: blur(10px);
    }

    /* Header Styles */
    header {visibility: hidden;}

    /* Title Styling */
    .main-title {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        font-size: 3rem;
        font-weight: 700;
        text-align: center;
        margin-bottom: 0.5rem;
        text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
    }

    .subtitle {
        text-align: center;
        color: #6c757d;
        font-size: 1.2rem;
        margin-bottom: 2rem;
        font-weight: 400;
    }

    /* Card Styles */
    .feature-card {
        background: white;
        padding: 2rem;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        margin: 1rem 0;
        border: 1px solid rgba(255, 255, 255, 0.2);
        transition: all 0.3s ease;
    }

    .feature-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    }

    /* Button Styles */
    .stButton > button {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        border-radius: 10px;
        padding: 0.75rem 2rem;
        font-weight: 600;
        font-size: 1rem;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
    }

    .stButton > button:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
    }

    /* Form Styles */
    .stTextInput > div > div > input {
        border-radius: 10px;
        border: 2px solid #e9ecef;
        padding: 0.75rem;
        font-size: 1rem;
        transition: all 0.3s ease;
    }

    .stTextInput > div > div > input:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }

    /* File Uploader Styles */
    .stFileUploader > div {
        border: 2px dashed #667eea;
        border-radius: 15px;
        padding: 2rem;
        background: rgba(102, 126, 234, 0.05);
        transition: all 0.3s ease;
    }

    .stFileUploader > div:hover {
        border-color: #764ba2;
        background: rgba(118, 75, 162, 0.05);
    }

    /* Result Container */
    .result-container {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        padding: 1.5rem;
        border-radius: 15px;
        color: white;
        margin: 1rem 0;
        box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
    }

    /* Resume Card Styles */
    .resume-card {
        background: white;
        border-radius: 15px;
        padding: 1.5rem;
        margin: 1rem 0;
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(102, 126, 234, 0.1);
        transition: all 0.3s ease;
    }

    .resume-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
    }

    /* Status and Toast Styles */
    .stAlert {
        border-radius: 10px;
        border: none;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    }

    /* Progress Bar */
    .stProgress > div > div {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 10px;
    }

    /* Sidebar Styles */
    .css-1d391kg {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }

    /* Metrics */
    .metric-card {
        background: white;
        padding: 1.5rem;
        border-radius: 12px;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        text-align: center;
        margin: 0.5rem 0;
    }

    /* Animation Classes */
    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .fade-in-up {
        animation: fadeInUp 0.6s ease-out;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .main-title {
            font-size: 2rem;
        }
        .block-container {
            padding: 1rem;
            margin: 0.5rem;
        }
    }
    </style>
    """,
    unsafe_allow_html=True
)

# strApiUrl = "http://127.0.0.1:8503/query/"
STR_API_URL = os.getenv('STR_API_URL')
# STR_API_URL_FETCH_ALL = os.getenv('STR_API_URL_FETCH_ALL')
STR_API_URL_FETCH_ALL = "http://localhost:8001/showall"

# Use session state to store uploaded files
if "uploaded_files" not in st.session_state:
    st.session_state["uploaded_files"] = None

# uploader_key = "pdf_uploader"

def process_uploaded_pdfs():
    """Simulates PDF processing with notifications"""
    with st.status("📄 Processing uploaded PDFs...", expanded=True) as status:
        objCProcessDocument = CProcessDocument(strVendorName="Resume_Schema")
        objCProcessDocument.MprocessAllDocuments(iFilesToProcess=1)

        # MakeReadableResponseData.MReadableResponseData()

        status.update(label="✅ Processing completed!", state="complete", expanded=False)
        st.toast("🎯 All PDFs processed successfully!", icon="✅")
    # Reset the UI after processing
    time.sleep(3)  # Wait for a moment before clearing
    st.session_state["uploaded_files"] = None
    # Remove the file uploader key and force a rerun to clear it
    if "pdf_uploader" in st.session_state:
        del st.session_state["pdf_uploader"]
    st.rerun()  # Refresh the Streamlit app to reset the uploader


# Helper function to extract 10th marks from a resume
def extract_10th_marks(resume):
    try:
        for education in resume["Resume"]["Education"]:
            degree = education["Degree"].lower()
            if "high school" in degree or "10th" in degree:
                marks = education["GPA/Marks/%"]
                try:
                    marks_float = float(marks)
                    if marks_float > 0:  # Only consider positive values as valid marks
                        return marks_float
                except ValueError:
                    pass  # Skip if marks cannot be converted to float
    except (KeyError, TypeError):
        pass  # Return None if resume structure is invalid
    return None


def calculate_checksum(file_data):
    """Calculate SHA-256 checksum from bytes."""
    sha256_hash = hashlib.sha256()
    sha256_hash.update(file_data)  # Directly update hash with bytes
    return sha256_hash.hexdigest()


def main():
    # Initialize session state variables if they don't exist
    if "query_result" not in st.session_state:
        st.session_state["query_result"] = ""
    if "resumes" not in st.session_state:
        st.session_state["resumes"] = []  # List to store JSON resumes

    # Hero Section with animated title
    st.markdown(
        """
        <div class="fade-in-up">
            <h1 class="main-title">🤖 Resume AI Agent</h1>
            <p class="subtitle">Intelligent Resume Search & Management System</p>
        </div>
        """,
        unsafe_allow_html=True
    )

    # Add some metrics/stats at the top
    col1, col2, col3, col4 = st.columns(4)

    with col1:
        st.markdown(
            """
            <div class="metric-card">
                <h3 style="color: #667eea; margin: 0;">📊 Total Resumes</h3>
                <h2 style="margin: 0.5rem 0;">""" + str(len(st.session_state.get("all_resumes", []))) + """</h2>
            </div>
            """,
            unsafe_allow_html=True
        )

    with col2:
        st.markdown(
            """
            <div class="metric-card">
                <h3 style="color: #667eea; margin: 0;">🔍 Search Results</h3>
                <h2 style="margin: 0.5rem 0;">""" + str(len(st.session_state["resumes"])) + """</h2>
            </div>
            """,
            unsafe_allow_html=True
        )

    with col3:
        st.markdown(
            """
            <div class="metric-card">
                <h3 style="color: #667eea; margin: 0;">⚡ AI Powered</h3>
                <h2 style="margin: 0.5rem 0;">GPT-4</h2>
            </div>
            """,
            unsafe_allow_html=True
        )

    with col4:
        st.markdown(
            """
            <div class="metric-card">
                <h3 style="color: #667eea; margin: 0;">🚀 Version</h3>
                <h2 style="margin: 0.5rem 0;">v2.0</h2>
            </div>
            """,
            unsafe_allow_html=True
        )

    st.markdown("<br>", unsafe_allow_html=True)

    # Main Search Section
    st.markdown(
        """
        <div class="feature-card fade-in-up">
            <h2 style="color: #667eea; margin-bottom: 1rem;">🔍 Natural Language Search</h2>
        </div>
        """,
        unsafe_allow_html=True
    )

    # Search form with better layout
    with st.form(key='query_form'):
        col1, col2 = st.columns([3, 1])

        with col1:
            user_query = st.text_input(
                "Enter your search criteria:",
                placeholder="e.g., 'Find software engineers with Python experience' or 'Show teachers with 5+ years experience'",
                key="user_query",
                help="Use natural language to describe what you're looking for"
            )

        with col2:
            st.markdown("<br>", unsafe_allow_html=True)  # Add spacing
            submit_button = st.form_submit_button("🔍 Search", use_container_width=True)
            show_all = st.form_submit_button("📋 Show All", use_container_width=True)

    # Results placeholder
    output_placeholder = st.empty()

    if submit_button:
        user_query = st.session_state.user_query  # Get the current input value
        if not user_query:
            st.warning("Please enter a query before submitting.")
        else:
            try:
                response = requests.get(STR_API_URL, params={"naturalQuery": user_query})
                try:
                    data = response.json()  # Convert API response to JSON
                    if isinstance(data, list):  # If API returns a direct list
                        st.session_state["resumes"] = data
                        st.session_state["query_result"] = "Data received successfully."
                    elif isinstance(data, dict):  # If API returns a dictionary
                        st.session_state["resumes"] = data.get("listOfDict", [])
                        st.session_state["query_result"] = data.get("result", "Data received successfully.")
                    else:
                        st.session_state["query_result"] = "Unexpected API response format."
                except json.decoder.JSONDecodeError:
                    st.session_state["query_result"] = "API response is not valid JSON."

                # Developer mode output (optional)
                st.session_state["mongoQuery"] = data.get("query", [])
                st.write("Developer mode : API Response =", response, " | ", json.dumps(st.session_state["mongoQuery"]))
            except requests.exceptions.RequestException as e:
                st.error(f"An error occurred while connecting to the API: {e}")
                st.session_state["query_result"] = "Error connecting to API. Displaying sample data."

            # Update the output placeholder with enhanced result display
            with output_placeholder.container():
                if st.session_state["query_result"]:
                    st.markdown(
                        f"""
                        <div class="result-container fade-in-up">
                            <h3 style="margin: 0 0 1rem 0;">🎯 AI Search Results</h3>
                            <p style="margin: 0; font-size: 1.1rem; line-height: 1.6;">
                                {st.session_state['query_result']}
                            </p>
                            <div style="margin-top: 1rem; padding-top: 1rem; border-top: 1px solid rgba(255,255,255,0.3);">
                                <strong>📊 Found {len(st.session_state['resumes'])} matching resume(s)</strong>
                            </div>
                        </div>
                        """,
                        unsafe_allow_html=True
                    )
            # Note: Do NOT reset st.session_state.user_query here because it is managed by the widget.

    # Enhanced File Upload Section
    st.markdown("<br><br>", unsafe_allow_html=True)

    st.markdown(
        """
        <div class="feature-card fade-in-up">
            <h2 style="color: #667eea; margin-bottom: 1rem;">📁 Upload & Process Resumes</h2>
        </div>
        """,
        unsafe_allow_html=True
    )

    # Define the folder where PDFs will be saved
    SAVE_PATH = r".\Data\inputData\Resume_Schema"  # Change this to your desired path
    os.makedirs(SAVE_PATH, exist_ok=True)
    MongoClient = MongoDBClient()

    # Create two columns for the upload and search sections
    col1, col2 = st.columns([1, 1])

    # Column 1: File Upload Section
    with col1:
        st.markdown("### 📤 Upload PDF Resumes")
        uploaded_files = st.file_uploader(
            "Choose PDF files",
            type=["pdf"],
            accept_multiple_files=True,
            key="pdf_uploader",
            help="Select one or more PDF resume files to upload and process"
        )

        if uploaded_files:
            st.session_state["uploaded_files"] = [file.name for file in uploaded_files]  # Store files in session state
            if st.button("Save & Process PDFs", use_container_width=True):
                saved_files = []
                duplicate_files = []

                # File saving loop
                for uploaded_file in uploaded_files:

                    try:
                        file_data = bytes(uploaded_file.getbuffer())  # Convert memoryview to bytes
                        checksum = calculate_checksum(file_data)  # Compute checksum before saving

                        # Check if the checksum already exists in the database
                        if MongoClient.is_duplicate(checksum):
                            duplicate_files.append(uploaded_file.name)
                            continue  # Skip saving this file

                        file_path = Path(SAVE_PATH) / uploaded_file.name
                        saved_files.append(file_path)

                        with open(file_path, "wb") as f:
                            f.write(file_data)

                    except Exception as e:
                        st.error(f"❌ Error saving {uploaded_file.name}: {e}")

                if duplicate_files:
                    st.toast(f"⚠️ {len(duplicate_files)} Duplicate PDFs skipped")

                # ✅ Show a single toast after all files are saved
                if saved_files:
                    st.toast(f"✅ {len(saved_files)} PDF(s) saved successfully!", icon="📂")
                    process_uploaded_pdfs()  # Process after saving
    # Column 2: Universal Search Section
    sorted_resumes = []
    with col2:
        st.markdown("### 🔍 Filter Results")

        # Universal Search: Search within all resumes
        if st.session_state["resumes"]:
            # Universal search input that filters the resumes already in session state
            search_term = st.text_input(
                "Filter within current results:",
                key="resume_search",
                placeholder="Enter keywords to filter results...",
                help="Search within the current search results"
            )

            # Filter resumes that contain the search term (case-insensitive)
            if search_term:
                filtered_resumes = [
                    resume for resume in st.session_state["resumes"]
                    if search_term.lower() in json.dumps(resume).lower()
                ]
                st.info(f"🔍 Filtered to {len(filtered_resumes)} resume(s) containing '{search_term}'")
            else:
                filtered_resumes = st.session_state["resumes"]

            # Sort the filtered resumes by 10th marks in descending order
            sorted_resumes = sorted(
                filtered_resumes,
                key=lambda x: extract_10th_marks(x) or 0,
                reverse=True
            )
        else:
            st.info("🔍 Search for resumes to enable filtering")

    # Enhanced "Show All" Section
    st.markdown("<br><br>", unsafe_allow_html=True)

    # Ensure session state key exists before accessing it
    if "all_resumes" not in st.session_state:
        st.session_state["all_resumes"] = []

    # Fetch all resumes from database
    try:
        response = requests.get(STR_API_URL_FETCH_ALL)
        try:
            data = response.json()
            st.session_state["all_resumes"] = data
        except json.decoder.JSONDecodeError:
            st.session_state["query_result"] = "API response is not valid JSON."
    except Exception as e:
        st.error(f"Error fetching resumes: {e}")

    all_resumes = [res for res in st.session_state["all_resumes"]]

    if show_all:
        st.markdown(
            """
            <div class="feature-card fade-in-up">
                <h2 style="color: #667eea; margin-bottom: 1rem;">📋 All Resumes in Database</h2>
            </div>
            """,
            unsafe_allow_html=True
        )

        if all_resumes:
            st.success(f"📊 Displaying all {len(all_resumes)} resumes from the database")

            for i in range(0, len(all_resumes), 2):
                cols = st.columns(2)

                # Left resume
                with cols[0]:
                    CDisplayResume.display_resume_with_delete(all_resumes[i])

                # Right resume, if available
                if i + 1 < len(all_resumes):
                    with cols[1]:
                        CDisplayResume.display_resume_with_delete(all_resumes[i+1])
                else:
                    cols[1].empty()
        else:
            st.warning("📭 No resumes found in the database. Upload some resumes to get started!")

    # Enhanced Search Results Display
    if not show_all and st.session_state["resumes"]:
        st.markdown(
            """
            <div class="feature-card fade-in-up">
                <h2 style="color: #667eea; margin-bottom: 1rem;">🎯 Search Results</h2>
            </div>
            """,
            unsafe_allow_html=True
        )

        # Ensure session state for tracking deleted resumes
        if "deleted_resumes" not in st.session_state:
            st.session_state["deleted_resumes"] = set()

        # Filter resumes to exclude deleted ones
        visible_resumes = [res for res in sorted_resumes if res.get("_id") not in st.session_state["deleted_resumes"]]

        if visible_resumes:
            # Add sorting options
            col1, col2 = st.columns([2, 1])
            with col1:
                st.info(f"📊 Showing {len(visible_resumes)} resume(s) sorted by academic performance")
            with col2:
                if st.button("🔄 Refresh Results", use_container_width=True):
                    st.rerun()

            # Display resumes in a grid
            for i in range(0, len(visible_resumes), 2):
                cols = st.columns(2)

                # Left resume
                with cols[0]:
                    CDisplayResume.display_resume_with_delete(visible_resumes[i])

                # Right resume, if available
                if i + 1 < len(visible_resumes):
                    with cols[1]:
                        CDisplayResume.display_resume_with_delete(visible_resumes[i+1])
                else:
                    cols[1].empty()
        else:
            st.warning("🔍 No resumes match your search criteria. Try adjusting your search terms.")
    elif not show_all and not st.session_state["resumes"]:
        # Show helpful message when no search has been performed
        st.markdown(
            """
            <div class="feature-card fade-in-up" style="text-align: center; padding: 3rem;">
                <h3 style="color: #667eea;">🚀 Ready to Search!</h3>
                <p style="color: #6c757d; font-size: 1.1rem; margin: 1rem 0;">
                    Use the search box above to find resumes using natural language,<br>
                    or click "Show All" to view all resumes in the database.
                </p>
                <p style="color: #6c757d;">
                    💡 Try searches like: "Find Python developers", "Show teachers with 5+ years experience",
                    "Software engineers in Mumbai"
                </p>
            </div>
            """,
            unsafe_allow_html=True
        )

    # Add footer
    st.markdown("<br><br>", unsafe_allow_html=True)
    st.markdown(
        """
        <div style="text-align: center; padding: 2rem; background: rgba(102, 126, 234, 0.1); border-radius: 15px; margin-top: 3rem;">
            <h4 style="color: #667eea; margin-bottom: 1rem;">🤖 Resume AI Agent</h4>
            <p style="color: #6c757d; margin: 0;">
                Powered by OpenAI GPT-4 • Built with Streamlit • MongoDB Database
            </p>
            <p style="color: #6c757d; margin: 0.5rem 0 0 0; font-size: 0.9rem;">
                Version 2.0 • Enhanced UI/UX • Natural Language Processing
            </p>
        </div>
        """,
        unsafe_allow_html=True
    )


if __name__ == "__main__":
    main()