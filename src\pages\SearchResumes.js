import React, { useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Eye, <PERSON><PERSON>ser, FiMapPin, FiMail, FiPhone } from 'react-icons/fi';
import { toast } from 'react-toastify';
import './SearchResumes.css';

const SearchResumes = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [isSearching, setIsSearching] = useState(false);
  const [searchResults, setSearchResults] = useState([]);
  const [filterQuery, setFilterQuery] = useState('');
  const [showFilters, setShowFilters] = useState(false);

  // Mock data for demonstration
  const mockResults = [
    {
      id: 1,
      name: '<PERSON>',
      title: 'Senior Software Engineer',
      email: '<EMAIL>',
      phone: '+****************',
      location: 'San Francisco, CA',
      experience: '5+ years',
      skills: ['Python', 'React', 'Node.js', 'AWS'],
      education: 'MS Computer Science',
      summary: 'Experienced software engineer with expertise in full-stack development...',
      score: 95
    },
    {
      id: 2,
      name: '<PERSON>',
      title: 'Frontend Developer',
      email: '<EMAIL>',
      phone: '+****************',
      location: 'New York, NY',
      experience: '3+ years',
      skills: ['React', 'TypeScript', 'CSS', 'JavaScript'],
      education: 'BS Computer Science',
      summary: 'Creative frontend developer passionate about user experience...',
      score: 88
    },
    {
      id: 3,
      name: 'Mike Johnson',
      title: 'DevOps Engineer',
      email: '<EMAIL>',
      phone: '+****************',
      location: 'Austin, TX',
      experience: '4+ years',
      skills: ['Docker', 'Kubernetes', 'AWS', 'Python'],
      education: 'BS Information Technology',
      summary: 'DevOps engineer specializing in cloud infrastructure and automation...',
      score: 92
    }
  ];

  const handleSearch = async (e) => {
    e.preventDefault();
    if (!searchQuery.trim()) {
      toast.warning('Please enter a search query');
      return;
    }

    setIsSearching(true);
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // Filter mock results based on query
      const filtered = mockResults.filter(result => 
        result.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        result.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        result.skills.some(skill => skill.toLowerCase().includes(searchQuery.toLowerCase()))
      );
      
      setSearchResults(filtered);
      toast.success(`Found ${filtered.length} matching resumes`);
    } catch (error) {
      toast.error('Search failed. Please try again.');
    } finally {
      setIsSearching(false);
    }
  };

  const handleShowAll = async () => {
    setIsSearching(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 1000));
      setSearchResults(mockResults);
      setSearchQuery('');
      toast.success(`Showing all ${mockResults.length} resumes`);
    } catch (error) {
      toast.error('Failed to load resumes');
    } finally {
      setIsSearching(false);
    }
  };

  const filteredResults = searchResults.filter(result => {
    if (!filterQuery) return true;
    return result.name.toLowerCase().includes(filterQuery.toLowerCase()) ||
           result.title.toLowerCase().includes(filterQuery.toLowerCase()) ||
           result.location.toLowerCase().includes(filterQuery.toLowerCase());
  });

  const handleDownload = (resume) => {
    toast.success(`Downloading ${resume.name}'s resume`);
  };

  const handleView = (resume) => {
    toast.info(`Opening ${resume.name}'s detailed view`);
  };

  return (
    <div className="search-page">
      <div className="page-header">
        <h1>Search Resumes</h1>
        <p>Use natural language to find the perfect candidates for your needs</p>
      </div>

      {/* Search Section */}
      <div className="search-section">
        <form onSubmit={handleSearch} className="search-form">
          <div className="search-input-group">
            <div className="search-input-wrapper">
              <FiSearch className="search-icon" />
              <input
                type="text"
                placeholder="e.g., 'Find Python developers with 5+ years experience' or 'Show frontend engineers in California'"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="search-input"
                disabled={isSearching}
              />
            </div>
            <div className="search-buttons">
              <button 
                type="submit" 
                className="btn btn-primary"
                disabled={isSearching}
              >
                {isSearching ? (
                  <>
                    <div className="loading-spinner" />
                    Searching...
                  </>
                ) : (
                  <>
                    <FiSearch size={16} />
                    Search
                  </>
                )}
              </button>
              <button 
                type="button" 
                className="btn btn-secondary"
                onClick={handleShowAll}
                disabled={isSearching}
              >
                Show All
              </button>
            </div>
          </div>
        </form>

        {/* Filter Section */}
        {searchResults.length > 0 && (
          <div className="filter-section">
            <div className="filter-input-wrapper">
              <FiFilter className="filter-icon" />
              <input
                type="text"
                placeholder="Filter results by name, title, or location..."
                value={filterQuery}
                onChange={(e) => setFilterQuery(e.target.value)}
                className="filter-input"
              />
            </div>
            <div className="results-count">
              {filteredResults.length} of {searchResults.length} results
            </div>
          </div>
        )}
      </div>

      {/* Results Section */}
      {searchResults.length > 0 ? (
        <div className="results-section">
          <div className="results-header">
            <h2>Search Results</h2>
            <div className="results-actions">
              <button className="btn btn-secondary">
                <FiDownload size={16} />
                Export All
              </button>
            </div>
          </div>

          <div className="results-grid">
            {filteredResults.map((resume) => (
              <div key={resume.id} className="resume-card">
                <div className="resume-header">
                  <div className="resume-avatar">
                    <FiUser size={24} />
                  </div>
                  <div className="resume-info">
                    <h3 className="resume-name">{resume.name}</h3>
                    <p className="resume-title">{resume.title}</p>
                    <div className="resume-score">
                      <span className="score-label">Match Score:</span>
                      <span className="score-value">{resume.score}%</span>
                    </div>
                  </div>
                </div>

                <div className="resume-details">
                  <div className="detail-item">
                    <FiMapPin size={14} />
                    <span>{resume.location}</span>
                  </div>
                  <div className="detail-item">
                    <FiMail size={14} />
                    <span>{resume.email}</span>
                  </div>
                  <div className="detail-item">
                    <FiPhone size={14} />
                    <span>{resume.phone}</span>
                  </div>
                </div>

                <div className="resume-content">
                  <div className="content-section">
                    <h4>Experience</h4>
                    <p>{resume.experience}</p>
                  </div>
                  <div className="content-section">
                    <h4>Education</h4>
                    <p>{resume.education}</p>
                  </div>
                  <div className="content-section">
                    <h4>Skills</h4>
                    <div className="skills-list">
                      {resume.skills.map((skill, index) => (
                        <span key={index} className="skill-tag">{skill}</span>
                      ))}
                    </div>
                  </div>
                  <div className="content-section">
                    <h4>Summary</h4>
                    <p className="summary-text">{resume.summary}</p>
                  </div>
                </div>

                <div className="resume-actions">
                  <button 
                    className="btn btn-secondary"
                    onClick={() => handleView(resume)}
                  >
                    <FiEye size={16} />
                    View Details
                  </button>
                  <button 
                    className="btn btn-primary"
                    onClick={() => handleDownload(resume)}
                  >
                    <FiDownload size={16} />
                    Download
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      ) : !isSearching ? (
        <div className="empty-state">
          <div className="empty-icon">
            <FiSearch size={48} />
          </div>
          <h3>Ready to Search!</h3>
          <p>Use the search box above to find resumes using natural language, or click "Show All" to view all resumes in the database.</p>
          <div className="search-examples">
            <h4>Try searching for:</h4>
            <ul>
              <li>"Find Python developers"</li>
              <li>"Show teachers with 5+ years experience"</li>
              <li>"Software engineers in Mumbai"</li>
            </ul>
          </div>
        </div>
      ) : null}
    </div>
  );
};

export default SearchResumes;
