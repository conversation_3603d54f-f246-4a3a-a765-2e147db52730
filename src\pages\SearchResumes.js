import React, { useState } from 'react';
import { <PERSON><PERSON>earch, Fi<PERSON>ilter, <PERSON>Down<PERSON>, <PERSON>Eye, Fi<PERSON>ser, FiMapPin, FiMail, FiPhone, FiExternalLink, FiStar, FiX, FiSettings, FiChevronDown, FiChevronUp } from 'react-icons/fi';
import { toast } from 'react-toastify';
import ResumeModal from '../components/ResumeModal';
import './SearchResumes.css';

const SearchResumes = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [isSearching, setIsSearching] = useState(false);
  const [searchResults, setSearchResults] = useState([]);
  const [filterQuery, setFilterQuery] = useState('');
  const [showFilters, setShowFilters] = useState(false);
  const [advancedFilters, setAdvancedFilters] = useState({
    minExperience: '',
    maxExperience: '',
    education: '',
    location: '',
    skills: '',
    minMarks: '',
    institution: '',
    graduationYear: '',
    company: ''
  });
  const [selectedResume, setSelectedResume] = useState(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalCount, setTotalCount] = useState(0);
  const resultsPerPage = 20;

  // Mock data for demonstration
  const mockResults = [
    {
      id: 1,
      name: 'John Doe',
      title: 'Senior Software Engineer',
      email: '<EMAIL>',
      phone: '+****************',
      location: 'San Francisco, CA',
      experience: '5+ years',
      skills: ['Python', 'React', 'Node.js', 'AWS'],
      education: 'MS Computer Science',
      summary: 'Experienced software engineer with expertise in full-stack development...',
      score: 95,
      rawData: {
        Resume: {
          PersonalInformation: {
            FullName: 'John Doe',
            Email: '<EMAIL>',
            ContactNumber: '+****************',
            Address: 'San Francisco, CA'
          },
          WorkExperience: [
            {
              Role: 'Senior Software Engineer',
              CompanyName: 'Tech Corp',
              StartYear: '2019',
              EndYear: 'Present',
              'Description/Responsibility': 'Led development of scalable web applications using React and Node.js'
            },
            {
              Role: 'Software Engineer',
              CompanyName: 'StartupXYZ',
              StartYear: '2017',
              EndYear: '2019',
              'Description/Responsibility': 'Developed full-stack applications and improved system performance'
            }
          ],
          Education: [
            {
              Degree: 'MS Computer Science',
              Institution: 'Stanford University',
              GraduationYear: '2017',
              'GPA/Marks/%': 85
            }
          ],
          Skills: ['Python', 'React', 'Node.js', 'AWS'],
          TotalWorkExperienceInYears: 5
        }
      }
    },
    {
      id: 2,
      name: 'Jane Smith',
      title: 'Frontend Developer',
      email: '<EMAIL>',
      phone: '+****************',
      location: 'New York, NY',
      experience: '3+ years',
      skills: ['React', 'TypeScript', 'CSS', 'JavaScript'],
      education: 'BS Computer Science',
      summary: 'Creative frontend developer passionate about user experience...',
      score: 88,
      rawData: {
        Resume: {
          PersonalInformation: {
            FullName: 'Jane Smith',
            Email: '<EMAIL>',
            ContactNumber: '+****************',
            Address: 'New York, NY'
          },
          WorkExperience: [
            {
              Role: 'Frontend Developer',
              CompanyName: 'Design Studio',
              StartYear: '2021',
              EndYear: 'Present',
              'Description/Responsibility': 'Created responsive web interfaces using React and modern CSS'
            }
          ],
          Education: [
            {
              Degree: 'BS Computer Science',
              Institution: 'NYU',
              GraduationYear: '2021',
              'GPA/Marks/%': 78
            }
          ],
          Skills: ['React', 'TypeScript', 'CSS', 'JavaScript'],
          TotalWorkExperienceInYears: 3
        }
      }
    },
    {
      id: 3,
      name: 'Mike Johnson',
      title: 'DevOps Engineer',
      email: '<EMAIL>',
      phone: '+****************',
      location: 'Austin, TX',
      experience: '4+ years',
      skills: ['Docker', 'Kubernetes', 'AWS', 'Python'],
      education: 'BS Information Technology',
      summary: 'DevOps engineer specializing in cloud infrastructure and automation...',
      score: 92,
      rawData: {
        Resume: {
          PersonalInformation: {
            FullName: 'Mike Johnson',
            Email: '<EMAIL>',
            ContactNumber: '+****************',
            Address: 'Austin, TX'
          },
          WorkExperience: [
            {
              Role: 'DevOps Engineer',
              CompanyName: 'Cloud Solutions Inc',
              StartYear: '2020',
              EndYear: 'Present',
              'Description/Responsibility': 'Managed cloud infrastructure and automated deployment pipelines'
            }
          ],
          Education: [
            {
              Degree: 'BS Information Technology',
              Institution: 'UT Austin',
              GraduationYear: '2020',
              'GPA/Marks/%': 82
            }
          ],
          Skills: ['Docker', 'Kubernetes', 'AWS', 'Python'],
          TotalWorkExperienceInYears: 4
        }
      }
    }
  ];

  const handleSearch = async (e) => {
    e.preventDefault();
    if (!searchQuery.trim()) {
      toast.warning('Please enter a search query');
      return;
    }

    setIsSearching(true);

    try {
      // Call the FastAPI backend exactly like frontendV3.py does
      const url = new URL('http://localhost:8001/query/');
      url.searchParams.append('naturalQuery', searchQuery);

      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const data = await response.json();

      if (response.ok) {
        // Handle FastAPI response format exactly like frontendV3.py: {query: [], listOfDict: [], result: ""}
        let transformedResults = [];

        // Check if API returns a direct list (like frontendV3.py handles)
        if (Array.isArray(data)) {
          transformedResults = data.map(resume => transformResumeData(resume));
        } else if (data.listOfDict && Array.isArray(data.listOfDict)) {
          // Transform the resume data from MongoDB to display format
          transformedResults = data.listOfDict.map(resume => transformResumeData(resume));
        } else {
          // Fallback to mock data if API doesn't return results
          transformedResults = mockResults.filter(resume =>
            resume.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
            resume.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
            resume.skills.some(skill => skill.toLowerCase().includes(searchQuery.toLowerCase()))
          );
        }

        setSearchResults(transformedResults);

        // Show success message with GPT result (exactly like frontendV3.py)
        if (data.result && data.result !== 'Data received successfully.') {
          toast.success(`🤖 GPT Analysis: ${data.result}`);
        } else {
          toast.success(`🔍 Found ${transformedResults.length} matching resumes`);
        }

        // Log GPT response and MongoDB query for debugging (like frontendV3.py developer mode)
        if (data.query) {
          console.log('🤖 MongoDB Query Generated by GPT:', JSON.stringify(data.query, null, 2));
          console.log('📊 GPT Result:', data.result);
          console.log('📋 Full API Response:', data);
        }
      } else {
        throw new Error(data.error || 'Search failed');
      }
    } catch (error) {
      console.error('Search error:', error);

      // Fallback to mock data on network error
      const fallbackResults = mockResults.filter(resume =>
        resume.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        resume.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        resume.skills.some(skill => skill.toLowerCase().includes(searchQuery.toLowerCase()))
      );

      setSearchResults(fallbackResults);
      toast.warning(`Using demo data - Found ${fallbackResults.length} matching resumes`);
    } finally {
      setIsSearching(false);
    }
  };

  const handleShowAll = async (page = 1) => {
    setIsSearching(true);
    try {
      // Call the FastAPI backend exactly like frontendV3.py does
      const response = await fetch('http://localhost:8001/showall');
      const data = await response.json();

      if (response.ok) {
        let transformedResults = [];

        if (Array.isArray(data)) {
          // FastAPI /showall returns array directly (like frontendV3.py expects)
          transformedResults = data.map(resume => transformResumeData(resume));
        } else if (data.error) {
          throw new Error(data.error);
        } else {
          // Fallback to mock data if API doesn't return proper results
          transformedResults = mockResults;
        }

        setSearchResults(transformedResults);
        setCurrentPage(1);
        setTotalCount(transformedResults.length);
        setTotalPages(Math.ceil(transformedResults.length / resultsPerPage));
        setSearchQuery('');

        toast.success(`📊 Displaying all ${transformedResults.length} resumes from database`);
      } else {
        throw new Error(data.error || 'Failed to load resumes');
      }
    } catch (error) {
      console.error('Load resumes error:', error);

      // Fallback to mock data on network error
      setSearchResults(mockResults);
      setCurrentPage(1);
      setTotalCount(mockResults.length);
      setTotalPages(Math.ceil(mockResults.length / resultsPerPage));
      setSearchQuery('');

      toast.warning(`Using demo data - Showing ${mockResults.length} sample resumes`);
    } finally {
      setIsSearching(false);
    }
  };

  const handlePageChange = (page) => {
    if (page >= 1 && page <= totalPages) {
      handleShowAll(page);
    }
  };

  const filteredResults = searchResults.filter(result => {
    // Basic text filter
    if (filterQuery) {
      const matchesText = result.name.toLowerCase().includes(filterQuery.toLowerCase()) ||
                         result.title.toLowerCase().includes(filterQuery.toLowerCase()) ||
                         result.location.toLowerCase().includes(filterQuery.toLowerCase());
      if (!matchesText) return false;
    }

    // Advanced filters
    const filters = advancedFilters;

    // Experience filter
    if (filters.minExperience || filters.maxExperience) {
      const experienceYears = result.rawData?.Resume?.TotalWorkExperienceInYears ||
                             extractExperienceYears(result.experience) || 0;

      if (filters.minExperience && experienceYears < parseInt(filters.minExperience)) return false;
      if (filters.maxExperience && experienceYears > parseInt(filters.maxExperience)) return false;
    }

    // Education filter
    if (filters.education) {
      const hasEducation = result.rawData?.Resume?.Education?.some(edu =>
        (edu.Degree || '').toLowerCase().includes(filters.education.toLowerCase())
      ) || (result.education || '').toLowerCase().includes(filters.education.toLowerCase());
      if (!hasEducation) return false;
    }

    // Location filter
    if (filters.location) {
      const matchesLocation = (result.location || '').toLowerCase().includes(filters.location.toLowerCase());
      if (!matchesLocation) return false;
    }

    // Skills filter
    if (filters.skills) {
      const skillsText = filters.skills.toLowerCase();
      const hasSkill = result.skills?.some(skill => skill.toLowerCase().includes(skillsText)) ||
                      (result.rawData?.Resume?.Skills || []).some(skill =>
                        skill.toLowerCase().includes(skillsText)
                      );
      if (!hasSkill) return false;
    }

    // Institution filter
    if (filters.institution) {
      const hasInstitution = result.rawData?.Resume?.Education?.some(edu =>
        (edu.Institution || edu.School || '').toLowerCase().includes(filters.institution.toLowerCase())
      );
      if (!hasInstitution) return false;
    }

    // Graduation year filter
    if (filters.graduationYear) {
      const hasGradYear = result.rawData?.Resume?.Education?.some(edu =>
        (edu.GraduationYear || edu.Year || '').toString().includes(filters.graduationYear)
      );
      if (!hasGradYear) return false;
    }

    // Company filter
    if (filters.company) {
      const hasCompany = result.rawData?.Resume?.WorkExperience?.some(exp =>
        (exp.Company || exp.CompanyName || exp.Organization || '').toLowerCase().includes(filters.company.toLowerCase())
      );
      if (!hasCompany) return false;
    }

    // Minimum marks filter
    if (filters.minMarks) {
      const hasMinMarks = result.rawData?.Resume?.Education?.some(edu => {
        const marks = parseFloat(edu['GPA/Marks/%']) || 0;
        return marks >= parseFloat(filters.minMarks);
      });
      if (!hasMinMarks) return false;
    }

    return true;
  });

  // Helper function to extract experience years from text
  const extractExperienceYears = (experienceText) => {
    if (!experienceText) return 0;
    const match = experienceText.match(/(\d+)\+?\s*years?/i);
    return match ? parseInt(match[1]) : 0;
  };

  // Helper function to transform resume data (like frontendV3.py)
  const transformResumeData = (resume) => {
    // Handle the nested Resume structure from MongoDB
    const resumeData = resume.Resume || resume;

    return {
      id: resume._id || Math.random().toString(36).substr(2, 9),
      name: resumeData.PersonalInformation?.FullName || resumeData.name || 'Unknown',
      title: resumeData.PersonalInformation?.Designation || resumeData.title || 'No title',
      email: resumeData.PersonalInformation?.Email || resumeData.email || 'No email',
      phone: resumeData.PersonalInformation?.ContactNumber || resumeData.phone || 'No phone',
      location: resumeData.PersonalInformation?.Address || resumeData.location || 'No location',
      experience: resumeData.TotalWorkExperienceInYears ?
        `${resumeData.TotalWorkExperienceInYears} years` :
        resumeData.WorkExperience?.length ?
          `${resumeData.WorkExperience.length}+ positions` :
          resumeData.experience || 'No experience data',
      skills: resumeData.Skills || resumeData.skills || [],
      education: resumeData.Education?.length ?
        resumeData.Education[0]?.Degree || 'Education available' :
        resumeData.education || 'No education data',
      summary: resumeData.Objective || resumeData.Summary || resumeData.summary || 'No summary available',
      score: 95, // Default score
      rawData: resume // Keep original data for reference
    };
  };

  const handleAdvancedFilterChange = (filterName, value) => {
    setAdvancedFilters(prev => ({
      ...prev,
      [filterName]: value
    }));
  };

  const clearAllFilters = () => {
    setFilterQuery('');
    setAdvancedFilters({
      minExperience: '',
      maxExperience: '',
      education: '',
      location: '',
      skills: '',
      minMarks: '',
      institution: '',
      graduationYear: '',
      company: ''
    });
  };

  const hasActiveFilters = () => {
    return filterQuery || Object.values(advancedFilters).some(value => value !== '');
  };

  const handleDownload = (resume) => {
    try {
      // Create a comprehensive text-based resume content
      const resumeContent = `
${resume.name}
${resume.title}
${resume.email} | ${resume.phone} | ${resume.location}

PROFESSIONAL SUMMARY
${resume.summary}

WORK EXPERIENCE
${resume.rawData?.Resume?.WorkExperience ?
  resume.rawData.Resume.WorkExperience.map(exp =>
    `${exp.JobTitle || exp.Position} at ${exp.Company || exp.Organization}
Duration: ${exp.Duration || exp.Years || 'Not specified'}
${exp.Description ? 'Description: ' + exp.Description : ''}
${exp.Responsibilities ? 'Responsibilities: ' + exp.Responsibilities : ''}
`
  ).join('\n') : resume.experience}

${resume.rawData?.Resume?.TotalWorkExperienceInYears ?
  `Total Experience: ${resume.rawData.Resume.TotalWorkExperienceInYears} years\n` : ''}

EDUCATION
${resume.rawData?.Resume?.Education ?
  resume.rawData.Resume.Education.map(edu =>
    `${edu.Degree || 'Degree'} from ${edu.Institution || edu.School || 'Institution'}
Graduation Year: ${edu.GraduationYear || edu.Year || 'Not specified'}
${edu['GPA/Marks/%'] && edu['GPA/Marks/%'] > 0 ? 'Marks: ' + edu['GPA/Marks/%'] + '%' : ''}
`
  ).join('\n') : resume.education}

SKILLS & TECHNOLOGIES
${resume.skills.join(', ')}

${resume.rawData?.Resume?.Languages && resume.rawData.Resume.Languages.length > 0 ?
  `LANGUAGES\n${resume.rawData.Resume.Languages.join(', ')}\n` : ''}

${resume.rawData?.Resume?.Projects && resume.rawData.Resume.Projects.length > 0 ?
  `PROJECTS\n${resume.rawData.Resume.Projects.map(project =>
    `${project.Name || project.Title || 'Project'}
${project.Description ? 'Description: ' + project.Description : ''}
${project.Technologies ? 'Technologies: ' + project.Technologies : ''}
`
  ).join('\n')}` : ''}

${resume.rawData?.Resume?.Certifications && resume.rawData.Resume.Certifications.length > 0 ?
  `CERTIFICATIONS\n${resume.rawData.Resume.Certifications.map(cert =>
    `${cert.Name || cert.Title || 'Certification'}
${cert.IssuingOrganization ? 'Issued by: ' + cert.IssuingOrganization : ''}
${cert.IssueDate ? 'Date: ' + cert.IssueDate : ''}
`
  ).join('\n')}` : ''}

${resume.rawData?.Resume?.Achievements && resume.rawData.Resume.Achievements.length > 0 ?
  `ACHIEVEMENTS\n${resume.rawData.Resume.Achievements.map(achievement =>
    typeof achievement === 'string' ? achievement :
    `${achievement.AchievementName || achievement.Name || 'Achievement'}
${achievement.IssueDate ? 'Date: ' + achievement.IssueDate : ''}
${achievement.IssuingOrganization ? 'Organization: ' + achievement.IssuingOrganization : ''}
${achievement.Description ? 'Description: ' + achievement.Description : ''}
`
  ).join('\n')}` : ''}

${resume.rawData?.Resume?.PersonalInformation?.LinkedIn ?
  `ADDITIONAL INFORMATION\nLinkedIn: ${resume.rawData.Resume.PersonalInformation.LinkedIn}` : ''}

Generated on: ${new Date().toLocaleDateString()}
      `.trim();

      // Create and download the file
      const blob = new Blob([resumeContent], { type: 'text/plain' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `${resume.name.replace(/\s+/g, '_')}_Resume.txt`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      toast.success(`Downloaded resume for ${resume.name}`);
    } catch (error) {
      console.error('Download error:', error);
      toast.error('Failed to download resume. Please try again.');
    }
  };

  const handleView = (resume) => {
    setSelectedResume(resume);
    setIsModalOpen(true);
    toast.info(`Opening detailed view for ${resume.name}`);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setSelectedResume(null);
  };

  const handleEmailClick = (email, name) => {
    window.open(`mailto:${email}?subject=Regarding your resume&body=Hi ${name},%0D%0A%0D%0AI found your resume and would like to discuss potential opportunities.%0D%0A%0D%0ABest regards`, '_blank');
    toast.info(`Opening email client to contact ${name}`);
  };

  const handlePhoneClick = (phone, name) => {
    if (navigator.userAgent.match(/Mobile|Android|iPhone|iPad/)) {
      window.open(`tel:${phone}`, '_blank');
    } else {
      navigator.clipboard.writeText(phone).then(() => {
        toast.success(`Phone number copied: ${phone}`);
      }).catch(() => {
        toast.info(`Phone: ${phone}`);
      });
    }
  };



  const handleExportAll = () => {
    if (filteredResults.length === 0) {
      toast.warning('No results to export');
      return;
    }

    try {
      const exportData = filteredResults.map(resume => ({
        name: resume.name,
        title: resume.title,
        email: resume.email,
        phone: resume.phone,
        location: resume.location,
        experience: resume.experience,
        education: resume.education,
        skills: resume.skills.join(', '),
        summary: resume.summary,
        matchScore: resume.score
      }));

      const csvContent = [
        'Name,Title,Email,Phone,Location,Experience,Education,Skills,Summary,Match Score',
        ...exportData.map(row =>
          Object.values(row).map(value =>
            `"${String(value).replace(/"/g, '""')}"`
          ).join(',')
        )
      ].join('\n');

      const blob = new Blob([csvContent], { type: 'text/csv' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `Resume_Search_Results_${new Date().toISOString().split('T')[0]}.csv`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      toast.success(`Exported ${filteredResults.length} resumes to CSV`);
    } catch (error) {
      console.error('Export error:', error);
      toast.error('Failed to export results. Please try again.');
    }
  };

  return (
    <div className="search-page">
      <div className="page-header">
        <h1>Search Resumes</h1>
        <p>Use natural language to find the perfect candidates for your needs</p>
      </div>

      {/* Search Section */}
      <div className="search-section">
        <form onSubmit={handleSearch} className="search-form">
          <div className="search-input-group">
            <div className="search-input-wrapper">
              <FiSearch className="search-icon" />
              <input
                type="text"
                placeholder="e.g., 'Find Python developers with 5+ years experience' or 'Show frontend engineers in California'"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="search-input"
                disabled={isSearching}
              />
            </div>
            <div className="search-buttons">
              <button
                type="submit"
                className="btn btn-primary"
                disabled={isSearching}
              >
                {isSearching ? (
                  <>
                    <div className="loading-spinner" />
                    Searching...
                  </>
                ) : (
                  <>
                    <FiSearch size={16} />
                    Search
                  </>
                )}
              </button>
              <button
                type="button"
                className="btn btn-secondary"
                onClick={handleShowAll}
                disabled={isSearching}
              >
                Show All
              </button>
            </div>
          </div>
        </form>

        {/* Filter Section */}
        {searchResults.length > 0 && (
          <div className="filter-section">
            <div className="filter-header">
              <div className="basic-filter-wrapper">
                <FiFilter className="filter-icon" />
                <input
                  type="text"
                  placeholder="Quick filter by name, title, or location..."
                  value={filterQuery}
                  onChange={(e) => setFilterQuery(e.target.value)}
                  className="filter-input"
                />
              </div>
              <div className="filter-controls">
                <button
                  type="button"
                  className={`btn btn-secondary filter-toggle ${showFilters ? 'active' : ''}`}
                  onClick={() => setShowFilters(!showFilters)}
                >
                  <FiSettings size={16} />
                  Advanced Filters
                  {showFilters ? <FiChevronUp size={16} /> : <FiChevronDown size={16} />}
                </button>
                {hasActiveFilters() && (
                  <button
                    type="button"
                    className="btn btn-outline clear-filters"
                    onClick={clearAllFilters}
                  >
                    <FiX size={16} />
                    Clear All
                  </button>
                )}
              </div>
            </div>

            {/* Advanced Filters Panel */}
            {showFilters && (
              <div className="advanced-filters">
                <div className="filters-grid">
                  <div className="filter-group">
                    <label>Experience (Years)</label>
                    <div className="range-inputs">
                      <input
                        type="number"
                        placeholder="Min"
                        value={advancedFilters.minExperience}
                        onChange={(e) => handleAdvancedFilterChange('minExperience', e.target.value)}
                        className="filter-input small"
                        min="0"
                      />
                      <span>to</span>
                      <input
                        type="number"
                        placeholder="Max"
                        value={advancedFilters.maxExperience}
                        onChange={(e) => handleAdvancedFilterChange('maxExperience', e.target.value)}
                        className="filter-input small"
                        min="0"
                      />
                    </div>
                  </div>

                  <div className="filter-group">
                    <label>Education Level</label>
                    <input
                      type="text"
                      placeholder="e.g., Bachelor, Master, PhD"
                      value={advancedFilters.education}
                      onChange={(e) => handleAdvancedFilterChange('education', e.target.value)}
                      className="filter-input"
                    />
                  </div>

                  <div className="filter-group">
                    <label>Location</label>
                    <input
                      type="text"
                      placeholder="e.g., Mumbai, Delhi, Bangalore"
                      value={advancedFilters.location}
                      onChange={(e) => handleAdvancedFilterChange('location', e.target.value)}
                      className="filter-input"
                    />
                  </div>

                  <div className="filter-group">
                    <label>Skills</label>
                    <input
                      type="text"
                      placeholder="e.g., Python, React, Java"
                      value={advancedFilters.skills}
                      onChange={(e) => handleAdvancedFilterChange('skills', e.target.value)}
                      className="filter-input"
                    />
                  </div>

                  <div className="filter-group">
                    <label>Institution</label>
                    <input
                      type="text"
                      placeholder="e.g., IIT, University name"
                      value={advancedFilters.institution}
                      onChange={(e) => handleAdvancedFilterChange('institution', e.target.value)}
                      className="filter-input"
                    />
                  </div>

                  <div className="filter-group">
                    <label>Graduation Year</label>
                    <input
                      type="text"
                      placeholder="e.g., 2020, 2021"
                      value={advancedFilters.graduationYear}
                      onChange={(e) => handleAdvancedFilterChange('graduationYear', e.target.value)}
                      className="filter-input"
                    />
                  </div>

                  <div className="filter-group">
                    <label>Company</label>
                    <input
                      type="text"
                      placeholder="e.g., Google, Microsoft, TCS"
                      value={advancedFilters.company}
                      onChange={(e) => handleAdvancedFilterChange('company', e.target.value)}
                      className="filter-input"
                    />
                  </div>

                  <div className="filter-group">
                    <label>Minimum Marks (%)</label>
                    <input
                      type="number"
                      placeholder="e.g., 75, 80"
                      value={advancedFilters.minMarks}
                      onChange={(e) => handleAdvancedFilterChange('minMarks', e.target.value)}
                      className="filter-input"
                      min="0"
                      max="100"
                    />
                  </div>
                </div>
              </div>
            )}

            <div className="results-count">
              {filteredResults.length} of {searchResults.length} results
              {hasActiveFilters() && <span className="filtered-indicator"> (filtered)</span>}
            </div>
          </div>
        )}
      </div>

      {/* Results Section */}
      {searchResults.length > 0 ? (
        <div className="results-section">
          <div className="results-header">
            <h2>Search Results</h2>
            <div className="results-actions">
              <button
                className="btn btn-secondary"
                onClick={handleExportAll}
                title={`Export ${filteredResults.length} results to CSV`}
              >
                <FiDownload size={16} />
                Export All ({filteredResults.length})
              </button>
            </div>
          </div>

          <div className="results-grid">
            {filteredResults.map((resume) => (
              <div key={resume.id} className="resume-card">
                <div className="resume-header">
                  <div className="resume-avatar">
                    <FiUser size={24} />
                  </div>
                  <div className="resume-info">
                    <h3 className="resume-name">{resume.name}</h3>
                    <p className="resume-title">{resume.title}</p>
                  </div>
                </div>

                <div className="resume-details">
                  <div className="detail-item location-item">
                    <FiMapPin size={14} />
                    <span>{resume.location}</span>
                  </div>
                  <div
                    className="detail-item email-item clickable"
                    onClick={() => handleEmailClick(resume.email, resume.name)}
                    title={`Send email to ${resume.name}`}
                  >
                    <FiMail size={14} />
                    <span>{resume.email}</span>
                    <FiExternalLink size={12} className="external-icon" />
                  </div>
                  <div
                    className="detail-item phone-item clickable"
                    onClick={() => handlePhoneClick(resume.phone, resume.name)}
                    title={`Call ${resume.name}`}
                  >
                    <FiPhone size={14} />
                    <span>{resume.phone}</span>
                    <FiExternalLink size={12} className="external-icon" />
                  </div>
                </div>

                <div className="resume-content">
                  <div className="content-row">
                    <div className="content-section">
                      <h4>Experience</h4>
                      <p>{resume.experience}</p>
                    </div>
                    <div className="content-section">
                      <h4>Education</h4>
                      <p>{resume.education}</p>
                    </div>
                  </div>
                  <div className="content-section">
                    <h4>Skills</h4>
                    <div className="skills-list">
                      {resume.skills.map((skill, index) => (
                        <span key={index} className="skill-tag">{skill}</span>
                      ))}
                    </div>
                  </div>
                </div>

                <div className="resume-actions">
                  <button
                    className="btn btn-secondary"
                    onClick={() => handleView(resume)}
                  >
                    <FiEye size={16} />
                    View Details
                  </button>
                  <button
                    className="btn btn-primary"
                    onClick={() => handleDownload(resume)}
                  >
                    <FiDownload size={16} />
                    Download
                  </button>
                </div>
              </div>
            ))}
          </div>

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="pagination-section">
              <div className="pagination-info">
                Showing {((currentPage - 1) * resultsPerPage) + 1} to {Math.min(currentPage * resultsPerPage, totalCount)} of {totalCount} results
              </div>
              <div className="pagination-controls">
                <button
                  className="btn btn-secondary pagination-btn"
                  onClick={() => handlePageChange(currentPage - 1)}
                  disabled={currentPage === 1}
                >
                  Previous
                </button>

                <div className="page-numbers">
                  {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                    let pageNum;
                    if (totalPages <= 5) {
                      pageNum = i + 1;
                    } else if (currentPage <= 3) {
                      pageNum = i + 1;
                    } else if (currentPage >= totalPages - 2) {
                      pageNum = totalPages - 4 + i;
                    } else {
                      pageNum = currentPage - 2 + i;
                    }

                    return (
                      <button
                        key={pageNum}
                        className={`btn pagination-btn ${currentPage === pageNum ? 'btn-primary' : 'btn-secondary'}`}
                        onClick={() => handlePageChange(pageNum)}
                      >
                        {pageNum}
                      </button>
                    );
                  })}
                </div>

                <button
                  className="btn btn-secondary pagination-btn"
                  onClick={() => handlePageChange(currentPage + 1)}
                  disabled={currentPage === totalPages}
                >
                  Next
                </button>
              </div>
            </div>
          )}
        </div>
      ) : !isSearching ? (
        <div className="empty-state">
          <div className="empty-icon">
            <FiSearch size={48} />
          </div>
          <h3>Ready to Search!</h3>
          <p>Use the search box above to find resumes using natural language, or click "Show All" to view all resumes in the database.</p>
          <div className="search-examples">
            <h4>Try searching for:</h4>
            <ul>
              <li>"Find Python developers"</li>
              <li>"Show teachers with 5+ years experience"</li>
              <li>"Software engineers in Mumbai"</li>
            </ul>
          </div>
        </div>
      ) : null}

      {/* Resume Details Modal */}
      <ResumeModal
        resume={selectedResume}
        isOpen={isModalOpen}
        onClose={handleCloseModal}
      />
    </div>
  );
};

export default SearchResumes;
