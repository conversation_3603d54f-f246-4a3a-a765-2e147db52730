import React, { useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Eye, <PERSON><PERSON>ser, FiMapPin, FiMail, FiPhone, FiExternalLink, FiStar } from 'react-icons/fi';
import { toast } from 'react-toastify';
import ResumeModal from '../components/ResumeModal';
import './SearchResumes.css';

const SearchResumes = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [isSearching, setIsSearching] = useState(false);
  const [searchResults, setSearchResults] = useState([]);
  const [filterQuery, setFilterQuery] = useState('');
  const [showFilters, setShowFilters] = useState(false);
  const [selectedResume, setSelectedResume] = useState(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  // Mock data for demonstration
  const mockResults = [
    {
      id: 1,
      name: '<PERSON>',
      title: 'Senior Software Engineer',
      email: '<EMAIL>',
      phone: '+****************',
      location: 'San Francisco, CA',
      experience: '5+ years',
      skills: ['Python', 'React', 'Node.js', 'AWS'],
      education: 'MS Computer Science',
      summary: 'Experienced software engineer with expertise in full-stack development...',
      score: 95
    },
    {
      id: 2,
      name: 'Jane Smith',
      title: 'Frontend Developer',
      email: '<EMAIL>',
      phone: '+****************',
      location: 'New York, NY',
      experience: '3+ years',
      skills: ['React', 'TypeScript', 'CSS', 'JavaScript'],
      education: 'BS Computer Science',
      summary: 'Creative frontend developer passionate about user experience...',
      score: 88
    },
    {
      id: 3,
      name: 'Mike Johnson',
      title: 'DevOps Engineer',
      email: '<EMAIL>',
      phone: '+****************',
      location: 'Austin, TX',
      experience: '4+ years',
      skills: ['Docker', 'Kubernetes', 'AWS', 'Python'],
      education: 'BS Information Technology',
      summary: 'DevOps engineer specializing in cloud infrastructure and automation...',
      score: 92
    }
  ];

  const handleSearch = async (e) => {
    e.preventDefault();
    if (!searchQuery.trim()) {
      toast.warning('Please enter a search query');
      return;
    }

    setIsSearching(true);

    try {
      // Call the actual search API
      const response = await fetch('/api/search', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ query: searchQuery }),
      });

      const data = await response.json();

      if (response.ok) {
        // Transform the resume data to match our display format
        const transformedResults = data.results.map(resume => {
          // Handle the nested Resume structure from MongoDB
          const resumeData = resume.Resume || resume;

          return {
            id: resume._id || Math.random().toString(36).substr(2, 9),
            name: resumeData.PersonalInformation?.FullName || resumeData.name || 'Unknown',
            title: resumeData.PersonalInformation?.Designation || resumeData.title || 'No title',
            email: resumeData.PersonalInformation?.Email || resumeData.email || 'No email',
            phone: resumeData.PersonalInformation?.ContactNumber || resumeData.phone || 'No phone',
            location: resumeData.PersonalInformation?.Address || resumeData.location || 'No location',
            experience: resumeData.TotalWorkExperienceInYears ?
              `${resumeData.TotalWorkExperienceInYears} years` :
              resumeData.WorkExperience?.length ?
                `${resumeData.WorkExperience.length}+ positions` :
                resumeData.experience || 'No experience data',
            skills: resumeData.Skills || resumeData.skills || [],
            education: resumeData.Education?.length ?
              resumeData.Education[0]?.Degree || 'Education available' :
              resumeData.education || 'No education data',
            summary: resumeData.Objective || resumeData.Summary || resumeData.summary || 'No summary available',
            score: 95, // Default score
            rawData: resume // Keep original data for reference
          };
        });

        setSearchResults(transformedResults);

        if (data.api_status === 'success') {
          toast.success(`Found ${transformedResults.length} matching resumes using AI search`);
        } else if (data.api_status === 'fallback') {
          toast.info(`Found ${transformedResults.length} matching resumes using fallback search`);
        }

        // Show GPT response if available
        if (data.gpt_response && data.gpt_response !== 'Data received successfully.') {
          console.log('GPT Response:', data.gpt_response);
          console.log('MongoDB Query:', data.mongo_query);
        }
      } else {
        throw new Error(data.error || 'Search failed');
      }
    } catch (error) {
      console.error('Search error:', error);
      toast.error(`Search failed: ${error.message}`);
      setSearchResults([]);
    } finally {
      setIsSearching(false);
    }
  };

  const handleShowAll = async () => {
    setIsSearching(true);
    try {
      // Call the actual API to get all resumes
      const response = await fetch('/api/resumes');
      const data = await response.json();

      if (response.ok) {
        // Transform the resume data to match our display format
        const transformedResults = data.resumes.map(resume => {
          // Handle the nested Resume structure from MongoDB
          const resumeData = resume.Resume || resume;

          return {
            id: resume._id || Math.random().toString(36).substr(2, 9),
            name: resumeData.PersonalInformation?.FullName || resumeData.name || 'Unknown',
            title: resumeData.PersonalInformation?.Designation || resumeData.title || 'No title',
            email: resumeData.PersonalInformation?.Email || resumeData.email || 'No email',
            phone: resumeData.PersonalInformation?.ContactNumber || resumeData.phone || 'No phone',
            location: resumeData.PersonalInformation?.Address || resumeData.location || 'No location',
            experience: resumeData.TotalWorkExperienceInYears ?
              `${resumeData.TotalWorkExperienceInYears} years` :
              resumeData.WorkExperience?.length ?
                `${resumeData.WorkExperience.length}+ positions` :
                resumeData.experience || 'No experience data',
            skills: resumeData.Skills || resumeData.skills || [],
            education: resumeData.Education?.length ?
              resumeData.Education[0]?.Degree || 'Education available' :
              resumeData.education || 'No education data',
            summary: resumeData.Objective || resumeData.Summary || resumeData.summary || 'No summary available',
            score: 90, // Default score for show all
            rawData: resume // Keep original data for reference
          };
        });

        setSearchResults(transformedResults);
        setSearchQuery('');

        if (data.source === 'database') {
          toast.success(`Showing all ${transformedResults.length} resumes from database`);
        } else {
          toast.info(`Showing ${transformedResults.length} resumes (fallback data)`);
        }
      } else {
        throw new Error(data.error || 'Failed to load resumes');
      }
    } catch (error) {
      console.error('Load resumes error:', error);
      toast.error(`Failed to load resumes: ${error.message}`);
    } finally {
      setIsSearching(false);
    }
  };

  const filteredResults = searchResults.filter(result => {
    if (!filterQuery) return true;
    return result.name.toLowerCase().includes(filterQuery.toLowerCase()) ||
           result.title.toLowerCase().includes(filterQuery.toLowerCase()) ||
           result.location.toLowerCase().includes(filterQuery.toLowerCase());
  });

  const handleDownload = (resume) => {
    try {
      // Create a comprehensive resume content
      const resumeContent = `
${resume.name}
${resume.title}
Email: ${resume.email}
Phone: ${resume.phone}
Location: ${resume.location}
Match Score: ${resume.score}%

PROFESSIONAL SUMMARY
${resume.summary}

EXPERIENCE
${resume.experience}

EDUCATION
${resume.education}

SKILLS & TECHNOLOGIES
${resume.skills.join(', ')}

${resume.rawData ? `
ADDITIONAL INFORMATION
Database ID: ${resume.rawData._id || 'N/A'}
Data Source: ${resume.rawData.Resume ? 'MongoDB Database' : 'Mock Data'}
` : ''}
Generated on: ${new Date().toLocaleDateString()}
      `.trim();

      // Create and download the file
      const blob = new Blob([resumeContent], { type: 'text/plain' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `${resume.name.replace(/\s+/g, '_')}_Resume.txt`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      toast.success(`Downloaded ${resume.name}'s resume successfully!`);
    } catch (error) {
      console.error('Download error:', error);
      toast.error('Failed to download resume. Please try again.');
    }
  };

  const handleView = (resume) => {
    setSelectedResume(resume);
    setIsModalOpen(true);
    toast.info(`Opening detailed view for ${resume.name}`);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setSelectedResume(null);
  };

  const handleEmailClick = (email, name) => {
    window.open(`mailto:${email}?subject=Regarding your resume&body=Hi ${name},%0D%0A%0D%0AI found your resume and would like to discuss potential opportunities.%0D%0A%0D%0ABest regards`, '_blank');
    toast.info(`Opening email client to contact ${name}`);
  };

  const handlePhoneClick = (phone, name) => {
    if (navigator.userAgent.match(/Mobile|Android|iPhone|iPad/)) {
      window.open(`tel:${phone}`, '_blank');
    } else {
      navigator.clipboard.writeText(phone).then(() => {
        toast.success(`Phone number copied: ${phone}`);
      }).catch(() => {
        toast.info(`Phone: ${phone}`);
      });
    }
  };

  const handleExportAll = () => {
    if (filteredResults.length === 0) {
      toast.warning('No results to export');
      return;
    }

    try {
      const exportData = filteredResults.map(resume => ({
        name: resume.name,
        title: resume.title,
        email: resume.email,
        phone: resume.phone,
        location: resume.location,
        experience: resume.experience,
        education: resume.education,
        skills: resume.skills.join(', '),
        summary: resume.summary,
        matchScore: resume.score
      }));

      const csvContent = [
        'Name,Title,Email,Phone,Location,Experience,Education,Skills,Summary,Match Score',
        ...exportData.map(row =>
          Object.values(row).map(value =>
            `"${String(value).replace(/"/g, '""')}"`
          ).join(',')
        )
      ].join('\n');

      const blob = new Blob([csvContent], { type: 'text/csv' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `Resume_Search_Results_${new Date().toISOString().split('T')[0]}.csv`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      toast.success(`Exported ${filteredResults.length} resumes to CSV`);
    } catch (error) {
      console.error('Export error:', error);
      toast.error('Failed to export results. Please try again.');
    }
  };

  return (
    <div className="search-page">
      <div className="page-header">
        <h1>Search Resumes</h1>
        <p>Use natural language to find the perfect candidates for your needs</p>
      </div>

      {/* Search Section */}
      <div className="search-section">
        <form onSubmit={handleSearch} className="search-form">
          <div className="search-input-group">
            <div className="search-input-wrapper">
              <FiSearch className="search-icon" />
              <input
                type="text"
                placeholder="e.g., 'Find Python developers with 5+ years experience' or 'Show frontend engineers in California'"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="search-input"
                disabled={isSearching}
              />
            </div>
            <div className="search-buttons">
              <button
                type="submit"
                className="btn btn-primary"
                disabled={isSearching}
              >
                {isSearching ? (
                  <>
                    <div className="loading-spinner" />
                    Searching...
                  </>
                ) : (
                  <>
                    <FiSearch size={16} />
                    Search
                  </>
                )}
              </button>
              <button
                type="button"
                className="btn btn-secondary"
                onClick={handleShowAll}
                disabled={isSearching}
              >
                Show All
              </button>
            </div>
          </div>
        </form>

        {/* Filter Section */}
        {searchResults.length > 0 && (
          <div className="filter-section">
            <div className="filter-input-wrapper">
              <FiFilter className="filter-icon" />
              <input
                type="text"
                placeholder="Filter results by name, title, or location..."
                value={filterQuery}
                onChange={(e) => setFilterQuery(e.target.value)}
                className="filter-input"
              />
            </div>
            <div className="results-count">
              {filteredResults.length} of {searchResults.length} results
            </div>
          </div>
        )}
      </div>

      {/* Results Section */}
      {searchResults.length > 0 ? (
        <div className="results-section">
          <div className="results-header">
            <h2>Search Results</h2>
            <div className="results-actions">
              <button
                className="btn btn-secondary"
                onClick={handleExportAll}
                title={`Export ${filteredResults.length} results to CSV`}
              >
                <FiDownload size={16} />
                Export All ({filteredResults.length})
              </button>
            </div>
          </div>

          <div className="results-grid">
            {filteredResults.map((resume) => (
              <div key={resume.id} className="resume-card">
                <div className="resume-header">
                  <div className="resume-avatar">
                    <FiUser size={24} />
                  </div>
                  <div className="resume-info">
                    <h3 className="resume-name">{resume.name}</h3>
                    <p className="resume-title">{resume.title}</p>
                  </div>
                </div>

                <div className="resume-details">
                  <div className="detail-item location-item">
                    <FiMapPin size={14} />
                    <span>{resume.location}</span>
                  </div>
                  <div
                    className="detail-item email-item clickable"
                    onClick={() => handleEmailClick(resume.email, resume.name)}
                    title={`Send email to ${resume.name}`}
                  >
                    <FiMail size={14} />
                    <span>{resume.email}</span>
                    <FiExternalLink size={12} className="external-icon" />
                  </div>
                  <div
                    className="detail-item phone-item clickable"
                    onClick={() => handlePhoneClick(resume.phone, resume.name)}
                    title={`Call ${resume.name}`}
                  >
                    <FiPhone size={14} />
                    <span>{resume.phone}</span>
                    <FiExternalLink size={12} className="external-icon" />
                  </div>
                </div>

                <div className="resume-content">
                  <div className="content-row">
                    <div className="content-section">
                      <h4>Experience</h4>
                      <p>{resume.experience}</p>
                    </div>
                    <div className="content-section">
                      <h4>Education</h4>
                      <p>{resume.education}</p>
                    </div>
                  </div>
                  <div className="content-section">
                    <h4>Skills</h4>
                    <div className="skills-list">
                      {resume.skills.map((skill, index) => (
                        <span key={index} className="skill-tag">{skill}</span>
                      ))}
                    </div>
                  </div>
                </div>

                <div className="resume-actions">
                  <button
                    className="btn btn-secondary"
                    onClick={() => handleView(resume)}
                  >
                    <FiEye size={16} />
                    View Details
                  </button>
                  <button
                    className="btn btn-primary"
                    onClick={() => handleDownload(resume)}
                  >
                    <FiDownload size={16} />
                    Download
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      ) : !isSearching ? (
        <div className="empty-state">
          <div className="empty-icon">
            <FiSearch size={48} />
          </div>
          <h3>Ready to Search!</h3>
          <p>Use the search box above to find resumes using natural language, or click "Show All" to view all resumes in the database.</p>
          <div className="search-examples">
            <h4>Try searching for:</h4>
            <ul>
              <li>"Find Python developers"</li>
              <li>"Show teachers with 5+ years experience"</li>
              <li>"Software engineers in Mumbai"</li>
            </ul>
          </div>
        </div>
      ) : null}

      {/* Resume Details Modal */}
      <ResumeModal
        resume={selectedResume}
        isOpen={isModalOpen}
        onClose={handleCloseModal}
      />
    </div>
  );
};

export default SearchResumes;
