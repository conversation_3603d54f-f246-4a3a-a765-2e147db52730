import React from 'react';
import { FiX, FiMail, FiPhone, FiMapPin, FiDownload, FiExternalLink, FiUser, FiBriefcase, FiBookOpen, FiAward } from 'react-icons/fi';
import './ResumeModal.css';

const ResumeModal = ({ resume, isOpen, onClose }) => {
  if (!isOpen || !resume) return null;

  const handleDownload = () => {
    // Create a simple text-based resume content
    const resumeContent = `
${resume.name}
${resume.title}
${resume.email} | ${resume.phone} | ${resume.location}

SUMMARY
${resume.summary}

EXPERIENCE
${resume.experience}

EDUCATION
${resume.education}

SKILLS
${resume.skills.join(', ')}
    `.trim();

    // Create and download the file
    const blob = new Blob([resumeContent], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `${resume.name.replace(/\s+/g, '_')}_Resume.txt`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  const handleEmailClick = () => {
    window.open(`mailto:${resume.email}`, '_blank');
  };

  const handlePhoneClick = () => {
    window.open(`tel:${resume.phone}`, '_blank');
  };

  const handleBackdropClick = (e) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  return (
    <div className="modal-backdrop" onClick={handleBackdropClick}>
      <div className="resume-modal">
        <div className="modal-header">
          <div className="modal-title">
            <FiUser size={24} />
            <h2>Resume Details</h2>
          </div>
          <button className="modal-close" onClick={onClose}>
            <FiX size={24} />
          </button>
        </div>

        <div className="modal-content">
          {/* Personal Information Section */}
          <div className="modal-section">
            <div className="section-header">
              <div className="resume-avatar-large">
                <FiUser size={32} />
              </div>
              <div className="personal-info">
                <h1 className="candidate-name">{resume.name}</h1>
                <h2 className="candidate-title">{resume.title}</h2>
                <div className="match-score-large">
                  <span className="score-label">Match Score:</span>
                  <span className="score-value">{resume.score}%</span>
                  <div className="score-bar">
                    <div
                      className="score-fill"
                      style={{ width: `${resume.score}%` }}
                    ></div>
                  </div>
                </div>
              </div>
            </div>

            <div className="contact-actions">
              <button
                className="contact-btn email-btn"
                onClick={handleEmailClick}
                title={`Email ${resume.name}`}
              >
                <FiMail size={16} />
                <span>{resume.email}</span>
                <FiExternalLink size={12} />
              </button>
              <button
                className="contact-btn phone-btn"
                onClick={handlePhoneClick}
                title={`Call ${resume.name}`}
              >
                <FiPhone size={16} />
                <span>{resume.phone}</span>
                <FiExternalLink size={12} />
              </button>
              <div className="contact-info location-info">
                <FiMapPin size={16} />
                <span>{resume.location}</span>
              </div>
            </div>
          </div>

          {/* Summary/Objective Section */}
          <div className="modal-section">
            <div className="section-title">
              <FiUser size={20} />
              <h3>Professional Summary</h3>
            </div>
            <div className="section-content">
              <p className="summary-text">{resume.summary}</p>
            </div>
          </div>

          {/* Experience Section */}
          <div className="modal-section">
            <div className="section-title">
              <FiBriefcase size={20} />
              <h3>Work Experience</h3>
            </div>
            <div className="section-content">
              {resume.rawData?.Resume?.WorkExperience && resume.rawData.Resume.WorkExperience.length > 0 ? (
                <div className="experience-list">
                  {resume.rawData.Resume.WorkExperience.map((exp, index) => (
                    <div key={index} className="experience-item">
                      <h4>{exp.JobTitle || exp.Position || 'Position'}</h4>
                      <p className="company-name">{exp.Company || exp.Organization || 'Company'}</p>
                      <p className="duration">{exp.Duration || exp.Years || 'Duration not specified'}</p>
                      {exp.Description && <p className="description">{exp.Description}</p>}
                      {exp.Responsibilities && (
                        <div className="responsibilities">
                          <strong>Responsibilities:</strong>
                          <p>{exp.Responsibilities}</p>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              ) : (
                <p className="experience-text">{resume.experience}</p>
              )}
              {resume.rawData?.Resume?.TotalWorkExperienceInYears && (
                <p className="total-experience">
                  <strong>Total Experience:</strong> {resume.rawData.Resume.TotalWorkExperienceInYears} years
                </p>
              )}
            </div>
          </div>

          {/* Education Section */}
          <div className="modal-section">
            <div className="section-title">
              <FiBookOpen size={20} />
              <h3>Education</h3>
            </div>
            <div className="section-content">
              {resume.rawData?.Resume?.Education && resume.rawData.Resume.Education.length > 0 ? (
                <div className="education-list">
                  {resume.rawData.Resume.Education.map((edu, index) => (
                    <div key={index} className="education-item">
                      <h4>{edu.Degree || 'Degree'}</h4>
                      <p className="institution">{edu.Institution || edu.School || 'Institution'}</p>
                      <p className="graduation-year">
                        {edu.GraduationYear || edu.Year || 'Year not specified'}
                      </p>
                      {edu['GPA/Marks/%'] && edu['GPA/Marks/%'] > 0 && (
                        <p className="marks">Marks: {edu['GPA/Marks/%']}%</p>
                      )}
                    </div>
                  ))}
                </div>
              ) : (
                <p className="education-text">{resume.education}</p>
              )}
            </div>
          </div>

          {/* Skills Section */}
          <div className="modal-section">
            <div className="section-title">
              <FiAward size={20} />
              <h3>Skills & Technologies</h3>
            </div>
            <div className="section-content">
              <div className="skills-grid">
                {resume.skills.map((skill, index) => (
                  <span key={index} className="skill-tag-large">{skill}</span>
                ))}
              </div>
            </div>
          </div>

          {/* Languages Section */}
          {resume.rawData?.Resume?.Languages && resume.rawData.Resume.Languages.length > 0 && (
            <div className="modal-section">
              <div className="section-title">
                <h3>Languages</h3>
              </div>
              <div className="section-content">
                <div className="languages-list">
                  {resume.rawData.Resume.Languages.map((lang, index) => (
                    <span key={index} className="language-tag">{lang}</span>
                  ))}
                </div>
              </div>
            </div>
          )}

          {/* Projects Section */}
          {resume.rawData?.Resume?.Projects && resume.rawData.Resume.Projects.length > 0 && (
            <div className="modal-section">
              <div className="section-title">
                <h3>Projects</h3>
              </div>
              <div className="section-content">
                <div className="projects-list">
                  {resume.rawData.Resume.Projects.map((project, index) => (
                    <div key={index} className="project-item">
                      <h4>{project.Name || project.Title || `Project ${index + 1}`}</h4>
                      {project.Description && <p>{project.Description}</p>}
                      {project.Technologies && (
                        <p><strong>Technologies:</strong> {project.Technologies}</p>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}

          {/* Certifications Section */}
          {resume.rawData?.Resume?.Certifications && resume.rawData.Resume.Certifications.length > 0 && (
            <div className="modal-section">
              <div className="section-title">
                <h3>Certifications</h3>
              </div>
              <div className="section-content">
                <div className="certifications-list">
                  {resume.rawData.Resume.Certifications.map((cert, index) => (
                    <div key={index} className="certification-item">
                      <h4>{cert.Name || cert.Title || `Certification ${index + 1}`}</h4>
                      {cert.IssuingOrganization && <p>Issued by: {cert.IssuingOrganization}</p>}
                      {cert.IssueDate && <p>Date: {cert.IssueDate}</p>}
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}

          {/* Achievements Section */}
          {resume.rawData?.Resume?.Achievements && resume.rawData.Resume.Achievements.length > 0 && (
            <div className="modal-section">
              <div className="section-title">
                <h3>Achievements</h3>
              </div>
              <div className="section-content">
                <div className="achievements-list">
                  {resume.rawData.Resume.Achievements.map((achievement, index) => (
                    <div key={index} className="achievement-item">
                      <p>{achievement}</p>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}

          {/* Raw Data Section (for debugging) */}
          {resume.rawData && (
            <div className="modal-section">
              <div className="section-title">
                <h3>Additional Information</h3>
              </div>
              <div className="section-content">
                <div className="raw-data-info">
                  <p><strong>Database ID:</strong> {resume.rawData._id || 'N/A'}</p>
                  <p><strong>Data Source:</strong> {resume.rawData.Resume ? 'MongoDB' : 'Mock Data'}</p>
                  {resume.rawData.Resume?.PersonalInformation?.LinkedIn && (
                    <p><strong>LinkedIn:</strong> {resume.rawData.Resume.PersonalInformation.LinkedIn}</p>
                  )}
                </div>
              </div>
            </div>
          )}
        </div>

        <div className="modal-footer">
          <button className="btn btn-secondary" onClick={onClose}>
            Close
          </button>
          <button className="btn btn-primary" onClick={handleDownload}>
            <FiDownload size={16} />
            Download Resume
          </button>
        </div>
      </div>
    </div>
  );
};

export default ResumeModal;
