import React, { useRef } from 'react';
import { FiX, FiMail, FiPhone, FiMapPin, FiDownload, FiExternalLink, FiUser, FiBriefcase, FiBookOpen, FiAward } from 'react-icons/fi';
import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';
import './ResumeModal.css';

const ResumeModal = ({ resume, isOpen, onClose }) => {
  const modalContentRef = useRef(null);

  if (!isOpen || !resume) return null;

  const handleDownload = async () => {
    try {
      // Hide the modal backdrop and buttons temporarily for clean PDF
      const backdrop = document.querySelector('.modal-backdrop');
      const footer = document.querySelector('.modal-footer');
      const closeBtn = document.querySelector('.modal-close');

      if (backdrop) backdrop.style.background = 'white';
      if (footer) footer.style.display = 'none';
      if (closeBtn) closeBtn.style.display = 'none';

      // Wait a moment for styles to apply
      await new Promise(resolve => setTimeout(resolve, 100));

      // Capture the modal content as canvas
      const canvas = await html2canvas(modalContentRef.current, {
        scale: 2,
        useCORS: true,
        allowTaint: true,
        backgroundColor: '#ffffff',
        width: modalContentRef.current.scrollWidth,
        height: modalContentRef.current.scrollHeight
      });

      // Create PDF
      const imgData = canvas.toDataURL('image/png');
      const pdf = new jsPDF({
        orientation: 'portrait',
        unit: 'mm',
        format: 'a4'
      });

      const imgWidth = 210; // A4 width in mm
      const pageHeight = 295; // A4 height in mm
      const imgHeight = (canvas.height * imgWidth) / canvas.width;
      let heightLeft = imgHeight;
      let position = 0;

      // Add first page
      pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
      heightLeft -= pageHeight;

      // Add additional pages if needed
      while (heightLeft >= 0) {
        position = heightLeft - imgHeight;
        pdf.addPage();
        pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
        heightLeft -= pageHeight;
      }

      // Download the PDF
      pdf.save(`${resume.name.replace(/\s+/g, '_')}_Resume.pdf`);

      // Restore original styles
      if (backdrop) backdrop.style.background = '';
      if (footer) footer.style.display = '';
      if (closeBtn) closeBtn.style.display = '';

    } catch (error) {
      console.error('Error generating PDF:', error);

      // Fallback to text download if PDF generation fails
      const resumeContent = `
${resume.name}
${resume.title}
${resume.email} | ${resume.phone} | ${resume.location}

SUMMARY
${resume.summary}

EXPERIENCE
${resume.rawData?.Resume?.WorkExperience ?
  resume.rawData.Resume.WorkExperience.map(exp =>
    `${exp.JobTitle || exp.Position} at ${exp.Company || exp.Organization} (${exp.Duration || exp.Years})`
  ).join('\n') : resume.experience}

EDUCATION
${resume.rawData?.Resume?.Education ?
  resume.rawData.Resume.Education.map(edu =>
    `${edu.Degree} from ${edu.Institution || edu.School} (${edu.GraduationYear || edu.Year})`
  ).join('\n') : resume.education}

SKILLS
${resume.skills.join(', ')}
      `.trim();

      const blob = new Blob([resumeContent], { type: 'text/plain' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `${resume.name.replace(/\s+/g, '_')}_Resume.txt`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
    }
  };

  const handleEmailClick = () => {
    window.open(`mailto:${resume.email}`, '_blank');
  };

  const handlePhoneClick = () => {
    window.open(`tel:${resume.phone}`, '_blank');
  };

  const handleBackdropClick = (e) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  return (
    <div className="modal-backdrop" onClick={handleBackdropClick}>
      <div className="resume-modal" ref={modalContentRef}>
        <div className="modal-header">
          <div className="modal-title">
            <FiUser size={24} />
            <h2>Resume Details</h2>
          </div>
          <button className="modal-close" onClick={onClose}>
            <FiX size={24} />
          </button>
        </div>

        <div className="modal-content">
          {/* Personal Information Section - Full Width */}
          <div className="modal-content-full">
            <div className="modal-section">
              <div className="section-header">
                <div className="resume-avatar-large">
                  <FiUser size={32} />
                </div>
                <div className="personal-info">
                  <h1 className="candidate-name">{resume.name}</h1>
                  <h2 className="candidate-title">{resume.title}</h2>
                </div>
                <div className="contact-actions">
                  <button
                    className="contact-btn email-btn"
                    onClick={handleEmailClick}
                    title={`Email ${resume.name}`}
                  >
                    <FiMail size={16} />
                    <span>{resume.email}</span>
                    <FiExternalLink size={12} />
                  </button>
                  <button
                    className="contact-btn phone-btn"
                    onClick={handlePhoneClick}
                    title={`Call ${resume.name}`}
                  >
                    <FiPhone size={16} />
                    <span>{resume.phone}</span>
                    <FiExternalLink size={12} />
                  </button>
                  <div className="contact-info location-info">
                    <FiMapPin size={16} />
                    <span>{resume.location}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Left Column */}
          <div className="modal-content-left">
            {/* Summary/Objective Section */}
            <div className="modal-section">
              <div className="section-title">
                <FiUser size={20} />
                <h3>Professional Summary</h3>
              </div>
              <div className="section-content">
                <p className="summary-text">{resume.summary}</p>
              </div>
            </div>

            {/* Experience Section */}
            <div className="modal-section">
              <div className="section-title">
                <FiBriefcase size={20} />
                <h3>Work Experience</h3>
              </div>
              <div className="section-content">
                {resume.rawData?.Resume?.WorkExperience && resume.rawData.Resume.WorkExperience.length > 0 ? (
                  <div className="experience-list">
                    {resume.rawData.Resume.WorkExperience.map((exp, index) => {
                      // Handle different field name variations in the data
                      const jobTitle = exp.JobTitle || exp.Position || exp.Role || 'Position';
                      const company = exp.Company || exp.Organization || exp.CompanyName || 'Company';
                      const duration = exp.Duration || exp.Years ||
                        (exp.StartYear && exp.EndYear ? `${exp.StartYear} - ${exp.EndYear}` : '') ||
                        'Duration not specified';
                      const description = exp.Description || exp['Description/Responsibility'] || exp.Responsibilities;

                      return (
                        <div key={index} className="experience-item">
                          <h4>{jobTitle}</h4>
                          <p className="company-name">{company}</p>
                          <p className="duration">{duration}</p>
                          {description && (
                            <div className="description">
                              <p>{description}</p>
                            </div>
                          )}
                        </div>
                      );
                    })}
                  </div>
                ) : (
                  <p className="experience-text">{resume.experience || 'No work experience information available'}</p>
                )}
                {resume.rawData?.Resume?.TotalWorkExperienceInYears && (
                  <p className="total-experience">
                    <strong>Total Experience:</strong> {resume.rawData.Resume.TotalWorkExperienceInYears} years
                  </p>
                )}
              </div>
            </div>

            {/* Projects Section */}
            {resume.rawData?.Resume?.Projects && resume.rawData.Resume.Projects.length > 0 && (
              <div className="modal-section">
                <div className="section-title">
                  <h3>Projects</h3>
                </div>
                <div className="section-content">
                  <div className="projects-list">
                    {resume.rawData.Resume.Projects.map((project, index) => (
                      <div key={index} className="project-item">
                        <h4>{project.Name || project.Title || `Project ${index + 1}`}</h4>
                        {project.Description && <p>{project.Description}</p>}
                        {project.Technologies && (
                          <p><strong>Technologies:</strong> {project.Technologies}</p>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Right Column */}
          <div className="modal-content-right">
            {/* Education Section */}
            <div className="modal-section">
              <div className="section-title">
                <FiBookOpen size={20} />
                <h3>Education</h3>
              </div>
              <div className="section-content">
                {resume.rawData?.Resume?.Education && resume.rawData.Resume.Education.length > 0 ? (
                  <div className="education-list">
                    {resume.rawData.Resume.Education.map((edu, index) => (
                      <div key={index} className="education-item">
                        <h4>{edu.Degree || 'Degree'}</h4>
                        <p className="institution">{edu.Institution || edu.School || 'Institution'}</p>
                        <p className="graduation-year">
                          {edu.GraduationYear || edu.Year || 'Year not specified'}
                        </p>
                        {edu['GPA/Marks/%'] && edu['GPA/Marks/%'] > 0 && (
                          <p className="marks">Marks: {edu['GPA/Marks/%']}%</p>
                        )}
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="education-text">{resume.education}</p>
                )}
              </div>
            </div>

            {/* Skills Section */}
            <div className="modal-section">
              <div className="section-title">
                <FiAward size={20} />
                <h3>Skills & Technologies</h3>
              </div>
              <div className="section-content">
                <div className="skills-grid">
                  {resume.skills.map((skill, index) => (
                    <span key={index} className="skill-tag-large">{skill}</span>
                  ))}
                </div>
              </div>
            </div>

            {/* Languages Section */}
            {resume.rawData?.Resume?.Languages && resume.rawData.Resume.Languages.length > 0 && (
              <div className="modal-section">
                <div className="section-title">
                  <h3>Languages</h3>
                </div>
                <div className="section-content">
                  <div className="languages-list">
                    {resume.rawData.Resume.Languages.map((lang, index) => (
                      <span key={index} className="language-tag">{lang}</span>
                    ))}
                  </div>
                </div>
              </div>
            )}

            {/* Certifications Section */}
            {resume.rawData?.Resume?.Certifications && resume.rawData.Resume.Certifications.length > 0 && (
              <div className="modal-section">
                <div className="section-title">
                  <h3>Certifications</h3>
                </div>
                <div className="section-content">
                  <div className="certifications-list">
                    {resume.rawData.Resume.Certifications.map((cert, index) => (
                      <div key={index} className="certification-item">
                        <h4>{cert.Name || cert.Title || `Certification ${index + 1}`}</h4>
                        {cert.IssuingOrganization && <p>Issued by: {cert.IssuingOrganization}</p>}
                        {cert.IssueDate && <p>Date: {cert.IssueDate}</p>}
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}

            {/* Achievements Section */}
            {resume.rawData?.Resume?.Achievements && resume.rawData.Resume.Achievements.length > 0 && (
              <div className="modal-section">
                <div className="section-title">
                  <h3>Achievements</h3>
                </div>
                <div className="section-content">
                  <div className="achievements-list">
                    {resume.rawData.Resume.Achievements.map((achievement, index) => (
                      <div key={index} className="achievement-item">
                        {typeof achievement === 'string' ? (
                          <p>{achievement}</p>
                        ) : (
                          <div>
                            <h4>{achievement.AchievementName || achievement.Name || `Achievement ${index + 1}`}</h4>
                            {achievement.IssueDate && <p>Date: {achievement.IssueDate}</p>}
                            {achievement.IssuingOrganization && <p>Organization: {achievement.IssuingOrganization}</p>}
                            {achievement.Description && <p>{achievement.Description}</p>}
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}

            {/* Raw Data Section (for debugging) */}
            {resume.rawData && (
              <div className="modal-section">
                <div className="section-title">
                  <h3>Additional Information</h3>
                </div>
                <div className="section-content">
                  <div className="raw-data-info">
                    <p><strong>Database ID:</strong> {resume.rawData._id || 'N/A'}</p>
                    <p><strong>Data Source:</strong> {resume.rawData.Resume ? 'MongoDB' : 'Mock Data'}</p>
                    {resume.rawData.Resume?.PersonalInformation?.LinkedIn && (
                      <p><strong>LinkedIn:</strong> {resume.rawData.Resume.PersonalInformation.LinkedIn}</p>
                    )}
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>

        <div className="modal-footer">
          <button className="btn btn-secondary" onClick={onClose}>
            Close
          </button>
          <button className="btn btn-primary" onClick={handleDownload}>
            <FiDownload size={16} />
            Download Resume
          </button>
        </div>
      </div>
    </div>
  );
};

export default ResumeModal;
