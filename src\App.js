import React, { useState } from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';

import Sidebar from './components/Sidebar';
import Header from './components/Header';
import Dashboard from './pages/Dashboard';
import UploadResume from './pages/UploadResume';
import SearchResumes from './pages/SearchResumes';
import AllResumes from './pages/AllResumes';
import ActivityLog from './pages/ActivityLog';
import Profile from './pages/Profile';
import Support from './pages/Support';

import './App.css';

function App() {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [currentUser] = useState({
    name: '<PERSON><PERSON><PERSON><PERSON>',
    email: '<EMAIL>',
    avatar: null
  });

  return (
    <Router>
      <div className="app">
        <Sidebar 
          collapsed={sidebarCollapsed} 
          onToggle={() => setSidebarCollapsed(!sidebarCollapsed)}
        />
        <div className={`main-content ${sidebarCollapsed ? 'sidebar-collapsed' : ''}`}>
          <Header user={currentUser} />
          <div className="content">
            <Routes>
              <Route path="/" element={<Dashboard />} />
              <Route path="/upload" element={<UploadResume />} />
              <Route path="/search" element={<SearchResumes />} />
              <Route path="/resumes" element={<AllResumes />} />
              <Route path="/activity" element={<ActivityLog />} />
              <Route path="/profile" element={<Profile />} />
              <Route path="/support" element={<Support />} />
            </Routes>
          </div>
        </div>
        <ToastContainer
          position="top-right"
          autoClose={3000}
          hideProgressBar={false}
          newestOnTop={false}
          closeOnClick
          rtl={false}
          pauseOnFocusLoss
          draggable
          pauseOnHover
          theme="light"
        />
      </div>
    </Router>
  );
}

export default App;
