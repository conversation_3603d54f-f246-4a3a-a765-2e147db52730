import json
import logging,os
from fastapi import FastAPI, HTTPException
import mysql.connector
from openai import OpenAI
from config import DB_CONFIG
from helpers import ReadTxtFile, LoadJsonFile
from helperMongoDb import MongoDBClient
import csv
import io
from fastapi.encoders import jsonable_encoder
from bson import ObjectId

# Added Middleware to access api from sever
from fastapi.middleware.cors import CORSMiddleware


# Define log directory and file path
log_dir = "./logs"
log_file = os.path.join(log_dir, "app.log")

# Create logs directory if it doesn't exist
if not os.path.exists(log_dir):
    os.makedirs(log_dir)
logging.basicConfig(
    filename=log_file,  # Log file name
    level=logging.INFO,  # Log level
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",  # Log format
    filemode="a",  # Append mode (use "w" for overwrite mode)
)
logger = logging.getLogger(__name__)

# Initialize FastAPI and OpenAI client
app = FastAPI()
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Or use ["*"] to allow all origins (not recommended for production)
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

openAiClient = OpenAI()

# Create an instance of MongoDBClient with error handling
try:
    mongo_client = MongoDBClient()
    MONGODB_AVAILABLE = True
    print("✅ MongoDB connection successful")
except Exception as e:
    print(f"⚠️ MongoDB connection failed: {e}")
    print("📁 Will use sample data as fallback")
    mongo_client = None
    MONGODB_AVAILABLE = False

import json

def read_json_file(file_path):
    with open(file_path, 'r', encoding='utf-8') as file:
        data = json.load(file)
        return data

def load_sample_resumes():
    """Load sample resumes from the sampleResumes directory"""
    import os
    sample_resumes = []
    sample_dir = "sampleResumes"

    if os.path.exists(sample_dir):
        files = [f for f in os.listdir(sample_dir) if f.endswith('.json')]
        print(f"📁 Loading {len(files)} sample resume files...")

        # Limit to first 10 files for faster loading
        for filename in files[:10]:
            try:
                file_path = os.path.join(sample_dir, filename)
                resume_data = read_json_file(file_path)
                sample_resumes.append(resume_data)
            except Exception as e:
                print(f"Error loading {filename}: {e}")

        print(f"✅ Loaded {len(sample_resumes)} sample resumes")
    else:
        print(f"❌ Sample directory {sample_dir} not found")

    return sample_resumes

def search_sample_data(naturalQuery: str):
    """Search through sample data based on natural language query"""
    sample_resumes = load_sample_resumes()

    # Enhanced keyword-based search for demo purposes
    query_lower = naturalQuery.lower()
    filtered_resumes = []

    # Define search keywords and their mappings
    search_keywords = {
        'python': ['python', 'django', 'flask'],
        'java': ['java', 'spring', 'hibernate'],
        'javascript': ['javascript', 'js', 'react', 'node', 'angular'],
        'teacher': ['teacher', 'teaching', 'education', 'instructor'],
        'engineer': ['engineer', 'engineering', 'developer', 'development'],
        'experience': ['experience', 'years', 'work'],
        'computer': ['computer', 'cs', 'it', 'software'],
        'science': ['science', 'scientific'],
        'management': ['management', 'manager', 'lead'],
        'marketing': ['marketing', 'sales', 'business']
    }

    for resume in sample_resumes:
        resume_data = resume.get('Resume', resume)

        # Create comprehensive search text
        search_text_parts = [
            str(resume_data.get('PersonalInformation', {}).get('FullName', '')),
            str(resume_data.get('PersonalInformation', {}).get('Designation', '')),
            ' '.join(resume_data.get('Skills', [])),
            ' '.join([edu.get('Degree', '') + ' ' + edu.get('Institution', '') for edu in resume_data.get('Education', [])]),
            ' '.join([exp.get('Role', '') + ' ' + exp.get('CompanyName', '') + ' ' + exp.get('Description', '') for exp in resume_data.get('WorkExperience', [])]),
            str(resume_data.get('Objective', '')),
            str(resume_data.get('Summary', ''))
        ]

        full_search_text = ' '.join(search_text_parts).lower()

        # Check for direct keyword matches
        query_words = query_lower.split()
        match_found = False

        for word in query_words:
            # Direct word match
            if word in full_search_text:
                match_found = True
                break

            # Check keyword mappings
            for key, synonyms in search_keywords.items():
                if word in synonyms and key in full_search_text:
                    match_found = True
                    break
                if key == word and any(syn in full_search_text for syn in synonyms):
                    match_found = True
                    break

        if match_found:
            filtered_resumes.append(resume)

    # If no specific matches, return all for broad queries
    if not filtered_resumes and any(word in query_lower for word in ['all', 'show', 'list', 'get', 'resumes', 'candidates']):
        filtered_resumes = sample_resumes
        result_text = f"Showing all {len(filtered_resumes)} resumes from sample data"
    else:
        result_text = f"Found {len(filtered_resumes)} matching resumes from sample data based on your query: '{naturalQuery}'"

    return {
        "query": [{"$match": {"$text": {"$search": naturalQuery}}}],
        "listOfDict": filtered_resumes,
        "result": result_text
    }


def processQuery(naturalQuery: str) -> str:
    """
    Converts a natural language query into an SQL query using OpenAI.

    Args:
        naturalQuery (str): The natural language query provided by the user.

    Returns:
        str: The generated SQL query.

    Raises:
        HTTPException: If there is an error in generating or parsing the AI response.
    """
    logger.info("Processing natural language query: %s", naturalQuery)
    strSystemPromptInputPart1 = ReadTxtFile("resource/strSystemPromptInput.txt")
    # strDbAndTables = ReadTxtFile("resource/databaseCreateTables.sql")
    FILE_PATH = "resource\\SampleResume.json"
    resume_data = read_json_file(FILE_PATH)
    strSystemPromptInput = strSystemPromptInputPart1 + str(resume_data)

    responseFormatInput = LoadJsonFile("resource/strResponseFormatInput.json")

    try:
        logger.info("Sending query to OpenAI for processing.")
        completion = openAiClient.chat.completions.create(
            model="gpt-4.1-2025-04-14",
            messages=[
                {"role": "system", "content": strSystemPromptInput},
                {"role": "user", "content": naturalQuery}
            ],
            temperature=0.7,
            response_format=responseFormatInput
        )
        logger.info("Received response from OpenAI.")
    except Exception as e:
        logger.error("Error generating SQL query: %s", e)
        raise HTTPException(status_code=500, detail="Error generating SQL query.")

    try:
        responseContent = json.loads(completion.choices[0].message.content)
        # MongoDbQuery = responseContent["SqlQuery"]
        # logger.info("Generated SQL query: %s", MongoDbQuery)
        # return MongoDbQuery

        strMongoDbQuery = responseContent["MongoDbQuery"]
        logger.info("Generated SQL query: %s", strMongoDbQuery)
        return strMongoDbQuery

    except (KeyError, json.JSONDecodeError) as e:
        logger.error("Error parsing OpenAI response: %s", e)
        raise HTTPException(status_code=500, detail="Error processing AI response.")

def getOutputQueryText(naturalQuery: str, MongoDbQuery: str, listSqlOutput: list) -> str:
    """
    Converts SQL output into a human-readable explanation using AI.

    Args:
        naturalQuery (str): The original natural language query.
        MongoDbQuery (str): The generated SQL query.
        listSqlOutput (list): The output results from the SQL query.

    Returns:
        str: A human-readable explanation of the SQL query result.

    Raises:
        HTTPException: If there is an error in processing the AI output response.
    """
    logger.info("Generating textual explanation for SQL output.")
    strSystemPromptOutput = ReadTxtFile("resource/strSystemPromptOutput.txt")
    strUserPromptOutput = (
        f"Natural Question: {naturalQuery},\n"
        f"SQL Query: {MongoDbQuery},\n"
        f"SQL Output: {listSqlOutput}"
    )
    if len(strUserPromptOutput)>20000:
        strUserPromptOutput = strUserPromptOutput[:20000]
    responseFormatOutput = LoadJsonFile("resource/strResponseFormatOutput.json")

    try:
        completion = openAiClient.chat.completions.create(
            model="gpt-4o-2024-08-06",
            messages=[
                {"role": "system", "content": strSystemPromptOutput},
                {"role": "user", "content": strUserPromptOutput}
            ],
            temperature=0.7,
            response_format=responseFormatOutput
        )
        logger.info("Received textual explanation from OpenAI.")
    except Exception as e:
        logger.error("Error generating output query text: %s", e)
        raise HTTPException(status_code=500, detail="Error generating output text.")

    try:
        responseContent = json.loads(completion.choices[0].message.content)
        strOutputText = responseContent["SqlQueryOutputText"]
        return strOutputText
    except (KeyError, json.JSONDecodeError) as e:
        logger.error("Error parsing output response from OpenAI: %s", e)
        raise HTTPException(status_code=500, detail="Error processing AI output response.")


import json
import ast

def load_query_string(query_string):
    try:
        # First try to parse as JSON (works for double quotes)
        result = json.loads(query_string)
    except json.JSONDecodeError:
        try:
            # If JSON fails, try ast.literal_eval (works for single quotes)
            result = ast.literal_eval(query_string)
        except (ValueError, SyntaxError) as e:
            raise ValueError(f"Invalid query string format: {e}")
    return result




import pandas as pd
import mysql.connector
from fastapi import HTTPException



@app.get("/")
def root():
    """
    Root endpoint to check if the API is running.

    Returns:
        dict: A simple message indicating the API status.
    """
    return {"message": "SQL Agent API is running", "mongodb_available": MONGODB_AVAILABLE}

@app.get("/test")
def test_endpoint():
    """
    Test endpoint to check sample data loading.
    """
    try:
        sample_resumes = load_sample_resumes()
        return {"status": "success", "count": len(sample_resumes), "mongodb_available": MONGODB_AVAILABLE}
    except Exception as e:
        return {"status": "error", "error": str(e)}

@app.get("/showall")
def show_all():
    """
    Endpoint to get all resumes from MongoDB or fallback to sample data.

    Returns:
        list: List of all resume documents.
    """
    if MONGODB_AVAILABLE and mongo_client:
        try:
            cursorShowAll = mongo_client.get_all_resume_data()
            lsShowAll = [doc for doc in cursorShowAll]

            if not lsShowAll:
                print("📁 No resumes in MongoDB, using sample data")
                return load_sample_resumes()

            print(f"📊 Loaded {len(lsShowAll)} resumes from MongoDB")
            return jsonable_encoder(lsShowAll, custom_encoder={ObjectId: str})
        except Exception as e:
            print(f"⚠️ MongoDB error: {e}, using sample data")
            return load_sample_resumes()
    else:
        print("📁 Using sample data (MongoDB not available)")
        return load_sample_resumes()


@app.get("/query/")
def queryNlp(naturalQuery: str):
    """
    API endpoint to process a natural language query, execute the corresponding SQL,
    and return the result with a human-readable explanation.

    Args:
        naturalQuery (str): The user's natural language query.

    Returns:
        dict: A dictionary containing the SQL query and the human-readable result.

    Raises:
        HTTPException: If any step in query processing fails.
    """
    if MONGODB_AVAILABLE and mongo_client:
        try:
            logger.info("Processing user query: %s", naturalQuery)
            strPipeline = processQuery(naturalQuery)
            listPipeline = load_query_string(strPipeline)
            cursorMongoDb = mongo_client.execute_pipeline(listPipeline)
            result = [doc for doc in cursorMongoDb]
            strOutputText = getOutputQueryText(naturalQuery, listPipeline, result)
            return {"query": listPipeline, "listOfDict":jsonable_encoder(result, custom_encoder={ObjectId: str}), "result": strOutputText}

        except Exception as e:
            logger.error("Error processing query: %s", e)
            # Fallback to sample data search
            return search_sample_data(naturalQuery)
    else:
        # Use sample data when MongoDB is not available
        return search_sample_data(naturalQuery)


@app.get("/query/all")
def queryNlpAll():
    """
    API endpoint to process a natural language query, execute the corresponding SQL,
    and return the result with a human-readable explanation.

    Args:
        naturalQuery (str): The user's natural language query.

    Returns:
        dict: A dictionary containing the SQL query and the human-readable result.

    Raises:
        HTTPException: If any step in query processing fails.
    """
    try:
        # logger.info("Processing user query: %s", naturalQuery)
        listPipeline = [{"$match": {"Resume": {"$exists": True}}}]
        cursorMongoDb = mongo_client.execute_pipeline(listPipeline)
        result = [doc for doc in cursorMongoDb]
        strOutputText = "Showing all resumes, each with various educational backgrounds and professional experiences in teaching subjects such as Mathematics, English, Physics, Science, History, Social Studies, Environmental Science, Computer Science, Commerce, and Geography."
        return {"query": listPipeline, "listOfDict":result, "result": strOutputText}

    except Exception as e:
        logger.error("Error processing query: %s", e)
        raise HTTPException(status_code=400, detail=str(e))



if __name__ == "__main__":
    # strResponse = queryNlp("show 3 resumes ")
    # import pprint
    # pprint.pprint(strResponse)
    # # print(strResponse)

    result = queryNlp("show all resume")
    print(result)

    # uvicorn app:app --port 8001 --reload
    # streamlit run frontendV3.py --server.port 9024
    # to run --> uvicorn app:app --host ************* --port 8000 --reload