import json
import logging,os
from fastapi import FastAPI, HTTPException
import mysql.connector
from openai import OpenAI
from config import DB_CONFIG
from helpers import ReadTxtFile, LoadJsonFile
from helperMongoDb import MongoDBClient
import csv
import io
from fastapi.encoders import jsonable_encoder
from bson import ObjectId

# Added Middleware to access api from sever
from fastapi.middleware.cors import CORSMiddleware


# Define log directory and file path
log_dir = "./logs"
log_file = os.path.join(log_dir, "app.log")

# Create logs directory if it doesn't exist
if not os.path.exists(log_dir):
    os.makedirs(log_dir)
logging.basicConfig(
    filename=log_file,  # Log file name
    level=logging.INFO,  # Log level
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",  # Log format
    filemode="a",  # Append mode (use "w" for overwrite mode)
)
logger = logging.getLogger(__name__)

# Initialize FastAPI and OpenAI client
app = FastAPI()
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Or use ["*"] to allow all origins (not recommended for production)
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

openAiClient = OpenAI()

# Create an instance of MongoDBClient.
mongo_client = MongoDBClient()

import json

def read_json_file(file_path):
    with open(file_path, 'r', encoding='utf-8') as file:
        data = json.load(file)
        return data


def processQuery(naturalQuery: str) -> str:
    """
    Converts a natural language query into an SQL query using OpenAI.

    Args:
        naturalQuery (str): The natural language query provided by the user.

    Returns:
        str: The generated SQL query.

    Raises:
        HTTPException: If there is an error in generating or parsing the AI response.
    """
    logger.info("Processing natural language query: %s", naturalQuery)
    strSystemPromptInputPart1 = ReadTxtFile("resource/strSystemPromptInput.txt")
    # strDbAndTables = ReadTxtFile("resource/databaseCreateTables.sql")
    FILE_PATH = "resource\\SampleResume.json"
    resume_data = read_json_file(FILE_PATH)
    strSystemPromptInput = strSystemPromptInputPart1 + str(resume_data)

    responseFormatInput = LoadJsonFile("resource/strResponseFormatInput.json")

    try:
        logger.info("Sending query to OpenAI for processing.")
        completion = openAiClient.chat.completions.create(
            model="gpt-4.1-2025-04-14",
            messages=[
                {"role": "system", "content": strSystemPromptInput},
                {"role": "user", "content": naturalQuery}
            ],
            temperature=0.7,
            response_format=responseFormatInput
        )
        logger.info("Received response from OpenAI.")
    except Exception as e:
        logger.error("Error generating SQL query: %s", e)
        raise HTTPException(status_code=500, detail="Error generating SQL query.")

    try:
        responseContent = json.loads(completion.choices[0].message.content)
        # MongoDbQuery = responseContent["SqlQuery"]
        # logger.info("Generated SQL query: %s", MongoDbQuery)
        # return MongoDbQuery
        
        strMongoDbQuery = responseContent["MongoDbQuery"]
        logger.info("Generated SQL query: %s", strMongoDbQuery)
        return strMongoDbQuery
    
    except (KeyError, json.JSONDecodeError) as e:
        logger.error("Error parsing OpenAI response: %s", e)
        raise HTTPException(status_code=500, detail="Error processing AI response.")

def getOutputQueryText(naturalQuery: str, MongoDbQuery: str, listSqlOutput: list) -> str:
    """
    Converts SQL output into a human-readable explanation using AI.

    Args:
        naturalQuery (str): The original natural language query.
        MongoDbQuery (str): The generated SQL query.
        listSqlOutput (list): The output results from the SQL query.

    Returns:
        str: A human-readable explanation of the SQL query result.

    Raises:
        HTTPException: If there is an error in processing the AI output response.
    """
    logger.info("Generating textual explanation for SQL output.")
    strSystemPromptOutput = ReadTxtFile("resource/strSystemPromptOutput.txt")
    strUserPromptOutput = (
        f"Natural Question: {naturalQuery},\n"
        f"SQL Query: {MongoDbQuery},\n"
        f"SQL Output: {listSqlOutput}"
    )
    if len(strUserPromptOutput)>20000:
        strUserPromptOutput = strUserPromptOutput[:20000]
    responseFormatOutput = LoadJsonFile("resource/strResponseFormatOutput.json")

    try:
        completion = openAiClient.chat.completions.create(
            model="gpt-4o-2024-08-06",
            messages=[
                {"role": "system", "content": strSystemPromptOutput},
                {"role": "user", "content": strUserPromptOutput}
            ],
            temperature=0.7,
            response_format=responseFormatOutput
        )
        logger.info("Received textual explanation from OpenAI.")
    except Exception as e:
        logger.error("Error generating output query text: %s", e)
        raise HTTPException(status_code=500, detail="Error generating output text.")

    try:
        responseContent = json.loads(completion.choices[0].message.content)
        strOutputText = responseContent["SqlQueryOutputText"]
        return strOutputText
    except (KeyError, json.JSONDecodeError) as e:
        logger.error("Error parsing output response from OpenAI: %s", e)
        raise HTTPException(status_code=500, detail="Error processing AI output response.")
    

import json
import ast

def load_query_string(query_string):
    try:
        # First try to parse as JSON (works for double quotes)
        result = json.loads(query_string)
    except json.JSONDecodeError:
        try:
            # If JSON fails, try ast.literal_eval (works for single quotes)
            result = ast.literal_eval(query_string)
        except (ValueError, SyntaxError) as e:
            raise ValueError(f"Invalid query string format: {e}")
    return result




import pandas as pd
import mysql.connector
from fastapi import HTTPException



@app.get("/")
def root():
    """
    Root endpoint to check if the API is running.

    Returns:
        dict: A simple message indicating the API status.
    """
    return {"message": "SQL Agent API is running"}

@app.get("/showall")
def show_all():
    """
    Root endpoint to check if the API is running.

    Returns:
        dict: A simple message indicating the API status.
    """
    """
    try:
        MongoClient = MongoDBClient()
        resumes = MongoClient.get_all_resume_data()
        if not resumes:
            return {"message": "No resumes found"}  # Handle empty case
        return resumes
    except Exception as e:
        return {"error": str(e)}  # Show errors if MongoDB connection fails

    """
    try:
        MongoClient = MongoDBClient()
        cursorShowAll = MongoClient.get_all_resume_data()
        lsShowAll = [doc for doc in cursorShowAll]
        
        if not lsShowAll:
            return {"message": "No resumes found"}  # Handle empty case
        return jsonable_encoder(lsShowAll, custom_encoder={ObjectId: str})
    except Exception as e:
        return {"error": str(e)}  # Show errors if MongoDB connection fails
    # """


@app.get("/query/")
def queryNlp(naturalQuery: str):
    """
    API endpoint to process a natural language query, execute the corresponding SQL, 
    and return the result with a human-readable explanation.

    Args:
        naturalQuery (str): The user's natural language query.

    Returns:
        dict: A dictionary containing the SQL query and the human-readable result.

    Raises:
        HTTPException: If any step in query processing fails.
    """
    try:
        logger.info("Processing user query: %s", naturalQuery)
        strPipeline = processQuery(naturalQuery)
        listPipeline = load_query_string(strPipeline)
        cursorMongoDb = mongo_client.execute_pipeline(listPipeline)
        result = [doc for doc in cursorMongoDb]
        strOutputText = getOutputQueryText(naturalQuery, listPipeline, result)
        return {"query": listPipeline, "listOfDict":jsonable_encoder(result, custom_encoder={ObjectId: str}), "result": strOutputText}
    
    except Exception as e:
        logger.error("Error processing query: %s", e)
        raise HTTPException(status_code=400, detail=str(e))
    

@app.get("/query/all")
def queryNlpAll():
    """
    API endpoint to process a natural language query, execute the corresponding SQL, 
    and return the result with a human-readable explanation.

    Args:
        naturalQuery (str): The user's natural language query.

    Returns:
        dict: A dictionary containing the SQL query and the human-readable result.

    Raises:
        HTTPException: If any step in query processing fails.
    """
    try:
        # logger.info("Processing user query: %s", naturalQuery)
        listPipeline = [{"$match": {"Resume": {"$exists": True}}}]
        cursorMongoDb = mongo_client.execute_pipeline(listPipeline)
        result = [doc for doc in cursorMongoDb]
        strOutputText = "Showing all resumes, each with various educational backgrounds and professional experiences in teaching subjects such as Mathematics, English, Physics, Science, History, Social Studies, Environmental Science, Computer Science, Commerce, and Geography."
        return {"query": listPipeline, "listOfDict":result, "result": strOutputText}
    
    except Exception as e:
        logger.error("Error processing query: %s", e)
        raise HTTPException(status_code=400, detail=str(e))
    


if __name__ == "__main__":
    # strResponse = queryNlp("show 3 resumes ")
    # import pprint
    # pprint.pprint(strResponse)
    # # print(strResponse)

    result = queryNlp("show all resume")
    print(result)

    # uvicorn app:app --port 8001 --reload
    # streamlit run frontendV3.py --server.port 9024
    # to run --> uvicorn app:app --host ************* --port 8000 --reload