{"id": "chatcmpl-BcsCuzywqAjKu37ryxRFnLKr0hOao", "choices": [{"finish_reason": "stop", "index": 0, "logprobs": null, "message": {"content": "{\"Resume\":{\"PersonalInformation\":{\"FullName\":\"AMMAR MANSURI\",\"Gender\":\"Male\",\"BirthDate\":19940203,\"Email\":\"<EMAIL>\",\"Address\":\"3296, Nani saleperi opp police tambuchowki, Dariyapur, Ahmedabad (Gujarat) 380001\",\"ContactNumber\":\"9978881169\",\"LinkedInProfile\":\"\"},\"Objective\":\"Seeking full-time position in finance an innovation company that foster an environment of creativity continual learning among it's employees I am aspiring for a career opportunities where I can significantly contribute to the growth of the organization\",\"Education\":[{\"Degree\":\"SECONDARY SCHOOL\",\"Institution\":\"F.D High School\",\"GraduationYear\":\"\",\"GPA/Marks/%\":57},{\"Degree\":\"12th\",\"Institution\":\"F.D High School\",\"GraduationYear\":\"\",\"GPA/Marks/%\":47},{\"Degree\":\"BACHELOR OF COMMERCE\",\"Institution\":\"Gujarat University\",\"GraduationYear\":\"2016\",\"GPA/Marks/%\":0}],\"WorkExperience\":[{\"CompanyName\":\"KGN IT SOLUTIONS PVT LTD.\",\"Role\":\"ACCOUNTANT\",\"StartYear\":201701,\"EndYear\":202310,\"Description/Responsibility\":\"Having total 5 years experience currently with KGN IT SOLUTIONS PVT LTD. Daily cash & Bank receipt & payment details Sales & Purchase invoice process Vender Reconciliation Salary process Preparing files and report related to month end activity\"}],\"TotalWorkExperienceInYears\":5,\"Skills\":[\"Tally ERP 9/ Tally prime\",\"Ms office (Excel, word)\",\"GST\",\"TDS\"],\"Certifications\":[],\"Achievements\":[],\"Languages\":[],\"Projects\":[]}}", "refusal": null, "role": "assistant", "audio": null, "function_call": null, "tool_calls": null, "parsed": null, "annotations": []}}], "created": **********, "model": "gpt-4o-2024-08-06", "object": "chat.completion", "service_tier": "default", "system_fingerprint": "fp_07871e2ad8", "usage": {"completion_tokens": 339, "prompt_tokens": 3056, "total_tokens": 3395, "completion_tokens_details": {"accepted_prediction_tokens": 0, "audio_tokens": 0, "reasoning_tokens": 0, "rejected_prediction_tokens": 0}, "prompt_tokens_details": {"audio_tokens": 0, "cached_tokens": 0}}}