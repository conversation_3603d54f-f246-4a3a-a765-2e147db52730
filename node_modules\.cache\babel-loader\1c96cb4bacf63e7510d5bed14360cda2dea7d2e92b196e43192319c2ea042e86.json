{"ast": null, "code": "var _jsxFileName = \"D:\\\\Projects\\\\SQL-Agent-Task-Submission-main\\\\SQL-Agent-Task-Submission-main\\\\src\\\\pages\\\\SearchResumes.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { FiSearch, FiFilter, FiDownload, FiEye, FiUser, FiMapPin, FiMail, FiPhone, FiExternalLink, FiStar, FiX, FiSettings, FiChevronDown, FiChevronUp } from 'react-icons/fi';\nimport { toast } from 'react-toastify';\nimport ResumeModal from '../components/ResumeModal';\nimport './SearchResumes.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst SearchResumes = () => {\n  _s();\n  const [searchQuery, setSearchQuery] = useState('');\n  const [isSearching, setIsSearching] = useState(false);\n  const [searchResults, setSearchResults] = useState([]);\n  const [filterQuery, setFilterQuery] = useState('');\n  const [showFilters, setShowFilters] = useState(false);\n  const [advancedFilters, setAdvancedFilters] = useState({\n    minExperience: '',\n    maxExperience: '',\n    education: '',\n    location: '',\n    skills: '',\n    minMarks: '',\n    institution: '',\n    graduationYear: '',\n    company: ''\n  });\n  const [selectedResume, setSelectedResume] = useState(null);\n  const [isModalOpen, setIsModalOpen] = useState(false);\n  const [currentPage, setCurrentPage] = useState(1);\n  const [totalPages, setTotalPages] = useState(1);\n  const [totalCount, setTotalCount] = useState(0);\n  const resultsPerPage = 20;\n\n  // Mock data for demonstration\n  const mockResults = [{\n    id: 1,\n    name: 'John Doe',\n    title: 'Senior Software Engineer',\n    email: '<EMAIL>',\n    phone: '+****************',\n    location: 'San Francisco, CA',\n    experience: '5+ years',\n    skills: ['Python', 'React', 'Node.js', 'AWS'],\n    education: 'MS Computer Science',\n    summary: 'Experienced software engineer with expertise in full-stack development...',\n    score: 95,\n    rawData: {\n      Resume: {\n        PersonalInformation: {\n          FullName: 'John Doe',\n          Email: '<EMAIL>',\n          ContactNumber: '+****************',\n          Address: 'San Francisco, CA'\n        },\n        WorkExperience: [{\n          Role: 'Senior Software Engineer',\n          CompanyName: 'Tech Corp',\n          StartYear: '2019',\n          EndYear: 'Present',\n          'Description/Responsibility': 'Led development of scalable web applications using React and Node.js'\n        }, {\n          Role: 'Software Engineer',\n          CompanyName: 'StartupXYZ',\n          StartYear: '2017',\n          EndYear: '2019',\n          'Description/Responsibility': 'Developed full-stack applications and improved system performance'\n        }],\n        Education: [{\n          Degree: 'MS Computer Science',\n          Institution: 'Stanford University',\n          GraduationYear: '2017',\n          'GPA/Marks/%': 85\n        }],\n        Skills: ['Python', 'React', 'Node.js', 'AWS'],\n        TotalWorkExperienceInYears: 5\n      }\n    }\n  }, {\n    id: 2,\n    name: 'Jane Smith',\n    title: 'Frontend Developer',\n    email: '<EMAIL>',\n    phone: '+****************',\n    location: 'New York, NY',\n    experience: '3+ years',\n    skills: ['React', 'TypeScript', 'CSS', 'JavaScript'],\n    education: 'BS Computer Science',\n    summary: 'Creative frontend developer passionate about user experience...',\n    score: 88,\n    rawData: {\n      Resume: {\n        PersonalInformation: {\n          FullName: 'Jane Smith',\n          Email: '<EMAIL>',\n          ContactNumber: '+****************',\n          Address: 'New York, NY'\n        },\n        WorkExperience: [{\n          Role: 'Frontend Developer',\n          CompanyName: 'Design Studio',\n          StartYear: '2021',\n          EndYear: 'Present',\n          'Description/Responsibility': 'Created responsive web interfaces using React and modern CSS'\n        }],\n        Education: [{\n          Degree: 'BS Computer Science',\n          Institution: 'NYU',\n          GraduationYear: '2021',\n          'GPA/Marks/%': 78\n        }],\n        Skills: ['React', 'TypeScript', 'CSS', 'JavaScript'],\n        TotalWorkExperienceInYears: 3\n      }\n    }\n  }, {\n    id: 3,\n    name: 'Mike Johnson',\n    title: 'DevOps Engineer',\n    email: '<EMAIL>',\n    phone: '+****************',\n    location: 'Austin, TX',\n    experience: '4+ years',\n    skills: ['Docker', 'Kubernetes', 'AWS', 'Python'],\n    education: 'BS Information Technology',\n    summary: 'DevOps engineer specializing in cloud infrastructure and automation...',\n    score: 92,\n    rawData: {\n      Resume: {\n        PersonalInformation: {\n          FullName: 'Mike Johnson',\n          Email: '<EMAIL>',\n          ContactNumber: '+****************',\n          Address: 'Austin, TX'\n        },\n        WorkExperience: [{\n          Role: 'DevOps Engineer',\n          CompanyName: 'Cloud Solutions Inc',\n          StartYear: '2020',\n          EndYear: 'Present',\n          'Description/Responsibility': 'Managed cloud infrastructure and automated deployment pipelines'\n        }],\n        Education: [{\n          Degree: 'BS Information Technology',\n          Institution: 'UT Austin',\n          GraduationYear: '2020',\n          'GPA/Marks/%': 82\n        }],\n        Skills: ['Docker', 'Kubernetes', 'AWS', 'Python'],\n        TotalWorkExperienceInYears: 4\n      }\n    }\n  }];\n  const handleSearch = async e => {\n    e.preventDefault();\n    if (!searchQuery.trim()) {\n      toast.warning('Please enter a search query');\n      return;\n    }\n    setIsSearching(true);\n    try {\n      // Call the actual search API\n      const response = await fetch('/api/search', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          query: searchQuery\n        })\n      });\n      const data = await response.json();\n      if (response.ok) {\n        // Handle both real API results and fallback mock data\n        let transformedResults = [];\n        if (data.results && Array.isArray(data.results)) {\n          // Transform the resume data to match our display format\n          transformedResults = data.results.map(resume => {\n            var _resumeData$PersonalI, _resumeData$PersonalI2, _resumeData$PersonalI3, _resumeData$PersonalI4, _resumeData$PersonalI5, _resumeData$WorkExper, _resumeData$Education, _resumeData$Education2;\n            // Handle the nested Resume structure from MongoDB\n            const resumeData = resume.Resume || resume;\n            return {\n              id: resume._id || Math.random().toString(36).substr(2, 9),\n              name: ((_resumeData$PersonalI = resumeData.PersonalInformation) === null || _resumeData$PersonalI === void 0 ? void 0 : _resumeData$PersonalI.FullName) || resumeData.name || 'Unknown',\n              title: ((_resumeData$PersonalI2 = resumeData.PersonalInformation) === null || _resumeData$PersonalI2 === void 0 ? void 0 : _resumeData$PersonalI2.Designation) || resumeData.title || 'No title',\n              email: ((_resumeData$PersonalI3 = resumeData.PersonalInformation) === null || _resumeData$PersonalI3 === void 0 ? void 0 : _resumeData$PersonalI3.Email) || resumeData.email || 'No email',\n              phone: ((_resumeData$PersonalI4 = resumeData.PersonalInformation) === null || _resumeData$PersonalI4 === void 0 ? void 0 : _resumeData$PersonalI4.ContactNumber) || resumeData.phone || 'No phone',\n              location: ((_resumeData$PersonalI5 = resumeData.PersonalInformation) === null || _resumeData$PersonalI5 === void 0 ? void 0 : _resumeData$PersonalI5.Address) || resumeData.location || 'No location',\n              experience: resumeData.TotalWorkExperienceInYears ? `${resumeData.TotalWorkExperienceInYears} years` : (_resumeData$WorkExper = resumeData.WorkExperience) !== null && _resumeData$WorkExper !== void 0 && _resumeData$WorkExper.length ? `${resumeData.WorkExperience.length}+ positions` : resumeData.experience || 'No experience data',\n              skills: resumeData.Skills || resumeData.skills || [],\n              education: (_resumeData$Education = resumeData.Education) !== null && _resumeData$Education !== void 0 && _resumeData$Education.length ? ((_resumeData$Education2 = resumeData.Education[0]) === null || _resumeData$Education2 === void 0 ? void 0 : _resumeData$Education2.Degree) || 'Education available' : resumeData.education || 'No education data',\n              summary: resumeData.Objective || resumeData.Summary || resumeData.summary || 'No summary available',\n              score: 95,\n              // Default score\n              rawData: resume // Keep original data for reference\n            };\n          });\n        } else {\n          // Fallback to mock data if API doesn't return results\n          transformedResults = mockResults.filter(resume => resume.name.toLowerCase().includes(searchQuery.toLowerCase()) || resume.title.toLowerCase().includes(searchQuery.toLowerCase()) || resume.skills.some(skill => skill.toLowerCase().includes(searchQuery.toLowerCase())));\n        }\n        setSearchResults(transformedResults);\n        if (data.api_status === 'success') {\n          toast.success(`Found ${transformedResults.length} matching resumes using AI search`);\n        } else if (data.api_status === 'fallback') {\n          toast.info(`Found ${transformedResults.length} matching resumes using fallback search`);\n        } else {\n          toast.success(`Found ${transformedResults.length} matching resumes`);\n        }\n\n        // Show GPT response if available\n        if (data.gpt_response && data.gpt_response !== 'Data received successfully.') {\n          console.log('GPT Response:', data.gpt_response);\n          console.log('MongoDB Query:', data.mongo_query);\n        }\n      } else {\n        throw new Error(data.error || 'Search failed');\n      }\n    } catch (error) {\n      console.error('Search error:', error);\n\n      // Fallback to mock data on network error\n      const fallbackResults = mockResults.filter(resume => resume.name.toLowerCase().includes(searchQuery.toLowerCase()) || resume.title.toLowerCase().includes(searchQuery.toLowerCase()) || resume.skills.some(skill => skill.toLowerCase().includes(searchQuery.toLowerCase())));\n      setSearchResults(fallbackResults);\n      toast.warning(`Using demo data - Found ${fallbackResults.length} matching resumes`);\n    } finally {\n      setIsSearching(false);\n    }\n  };\n  const handleShowAll = async (page = 1) => {\n    setIsSearching(true);\n    try {\n      // Call the actual API to get paginated resumes\n      const response = await fetch(`/api/resumes?page=${page}&limit=${resultsPerPage}`);\n      const data = await response.json();\n      if (response.ok) {\n        let transformedResults = [];\n        if (data.resumes && Array.isArray(data.resumes)) {\n          // Transform the resume data to match our display format\n          transformedResults = data.resumes.map(resume => {\n            var _resumeData$PersonalI6, _resumeData$PersonalI7, _resumeData$PersonalI8, _resumeData$PersonalI9, _resumeData$PersonalI0, _resumeData$WorkExper2, _resumeData$Education3, _resumeData$Education4;\n            // Handle the nested Resume structure from MongoDB\n            const resumeData = resume.Resume || resume;\n            return {\n              id: resume._id || Math.random().toString(36).substr(2, 9),\n              name: ((_resumeData$PersonalI6 = resumeData.PersonalInformation) === null || _resumeData$PersonalI6 === void 0 ? void 0 : _resumeData$PersonalI6.FullName) || resumeData.name || 'Unknown',\n              title: ((_resumeData$PersonalI7 = resumeData.PersonalInformation) === null || _resumeData$PersonalI7 === void 0 ? void 0 : _resumeData$PersonalI7.Designation) || resumeData.title || 'No title',\n              email: ((_resumeData$PersonalI8 = resumeData.PersonalInformation) === null || _resumeData$PersonalI8 === void 0 ? void 0 : _resumeData$PersonalI8.Email) || resumeData.email || 'No email',\n              phone: ((_resumeData$PersonalI9 = resumeData.PersonalInformation) === null || _resumeData$PersonalI9 === void 0 ? void 0 : _resumeData$PersonalI9.ContactNumber) || resumeData.phone || 'No phone',\n              location: ((_resumeData$PersonalI0 = resumeData.PersonalInformation) === null || _resumeData$PersonalI0 === void 0 ? void 0 : _resumeData$PersonalI0.Address) || resumeData.location || 'No location',\n              experience: resumeData.TotalWorkExperienceInYears ? `${resumeData.TotalWorkExperienceInYears} years` : (_resumeData$WorkExper2 = resumeData.WorkExperience) !== null && _resumeData$WorkExper2 !== void 0 && _resumeData$WorkExper2.length ? `${resumeData.WorkExperience.length}+ positions` : resumeData.experience || 'No experience data',\n              skills: resumeData.Skills || resumeData.skills || [],\n              education: (_resumeData$Education3 = resumeData.Education) !== null && _resumeData$Education3 !== void 0 && _resumeData$Education3.length ? ((_resumeData$Education4 = resumeData.Education[0]) === null || _resumeData$Education4 === void 0 ? void 0 : _resumeData$Education4.Degree) || 'Education available' : resumeData.education || 'No education data',\n              summary: resumeData.Objective || resumeData.Summary || resumeData.summary || 'No summary available',\n              score: 90,\n              // Default score for show all\n              rawData: resume // Keep original data for reference\n            };\n          });\n        } else {\n          // Fallback to mock data if API doesn't return proper results\n          transformedResults = mockResults;\n        }\n        setSearchResults(transformedResults);\n        setCurrentPage(page);\n        setTotalCount(data.count || data.total || transformedResults.length);\n        setTotalPages(Math.ceil((data.count || data.total || transformedResults.length) / resultsPerPage));\n        setSearchQuery('');\n        if (data.source === 'database') {\n          toast.success(`Showing page ${page} of ${Math.ceil((data.count || data.total || transformedResults.length) / resultsPerPage)} (${data.count || data.total} total resumes)`);\n        } else {\n          toast.info(`Showing ${transformedResults.length} resumes`);\n        }\n      } else {\n        throw new Error(data.error || 'Failed to load resumes');\n      }\n    } catch (error) {\n      console.error('Load resumes error:', error);\n\n      // Fallback to mock data on network error\n      setSearchResults(mockResults);\n      setCurrentPage(1);\n      setTotalCount(mockResults.length);\n      setTotalPages(Math.ceil(mockResults.length / resultsPerPage));\n      setSearchQuery('');\n      toast.warning(`Using demo data - Showing ${mockResults.length} sample resumes`);\n    } finally {\n      setIsSearching(false);\n    }\n  };\n  const handlePageChange = page => {\n    if (page >= 1 && page <= totalPages) {\n      handleShowAll(page);\n    }\n  };\n  const filteredResults = searchResults.filter(result => {\n    // Basic text filter\n    if (filterQuery) {\n      const matchesText = result.name.toLowerCase().includes(filterQuery.toLowerCase()) || result.title.toLowerCase().includes(filterQuery.toLowerCase()) || result.location.toLowerCase().includes(filterQuery.toLowerCase());\n      if (!matchesText) return false;\n    }\n\n    // Advanced filters\n    const filters = advancedFilters;\n\n    // Experience filter\n    if (filters.minExperience || filters.maxExperience) {\n      var _result$rawData, _result$rawData$Resum;\n      const experienceYears = ((_result$rawData = result.rawData) === null || _result$rawData === void 0 ? void 0 : (_result$rawData$Resum = _result$rawData.Resume) === null || _result$rawData$Resum === void 0 ? void 0 : _result$rawData$Resum.TotalWorkExperienceInYears) || extractExperienceYears(result.experience) || 0;\n      if (filters.minExperience && experienceYears < parseInt(filters.minExperience)) return false;\n      if (filters.maxExperience && experienceYears > parseInt(filters.maxExperience)) return false;\n    }\n\n    // Education filter\n    if (filters.education) {\n      var _result$rawData2, _result$rawData2$Resu, _result$rawData2$Resu2;\n      const hasEducation = ((_result$rawData2 = result.rawData) === null || _result$rawData2 === void 0 ? void 0 : (_result$rawData2$Resu = _result$rawData2.Resume) === null || _result$rawData2$Resu === void 0 ? void 0 : (_result$rawData2$Resu2 = _result$rawData2$Resu.Education) === null || _result$rawData2$Resu2 === void 0 ? void 0 : _result$rawData2$Resu2.some(edu => (edu.Degree || '').toLowerCase().includes(filters.education.toLowerCase()))) || (result.education || '').toLowerCase().includes(filters.education.toLowerCase());\n      if (!hasEducation) return false;\n    }\n\n    // Location filter\n    if (filters.location) {\n      const matchesLocation = (result.location || '').toLowerCase().includes(filters.location.toLowerCase());\n      if (!matchesLocation) return false;\n    }\n\n    // Skills filter\n    if (filters.skills) {\n      var _result$skills, _result$rawData3, _result$rawData3$Resu;\n      const skillsText = filters.skills.toLowerCase();\n      const hasSkill = ((_result$skills = result.skills) === null || _result$skills === void 0 ? void 0 : _result$skills.some(skill => skill.toLowerCase().includes(skillsText))) || (((_result$rawData3 = result.rawData) === null || _result$rawData3 === void 0 ? void 0 : (_result$rawData3$Resu = _result$rawData3.Resume) === null || _result$rawData3$Resu === void 0 ? void 0 : _result$rawData3$Resu.Skills) || []).some(skill => skill.toLowerCase().includes(skillsText));\n      if (!hasSkill) return false;\n    }\n\n    // Institution filter\n    if (filters.institution) {\n      var _result$rawData4, _result$rawData4$Resu, _result$rawData4$Resu2;\n      const hasInstitution = (_result$rawData4 = result.rawData) === null || _result$rawData4 === void 0 ? void 0 : (_result$rawData4$Resu = _result$rawData4.Resume) === null || _result$rawData4$Resu === void 0 ? void 0 : (_result$rawData4$Resu2 = _result$rawData4$Resu.Education) === null || _result$rawData4$Resu2 === void 0 ? void 0 : _result$rawData4$Resu2.some(edu => (edu.Institution || edu.School || '').toLowerCase().includes(filters.institution.toLowerCase()));\n      if (!hasInstitution) return false;\n    }\n\n    // Graduation year filter\n    if (filters.graduationYear) {\n      var _result$rawData5, _result$rawData5$Resu, _result$rawData5$Resu2;\n      const hasGradYear = (_result$rawData5 = result.rawData) === null || _result$rawData5 === void 0 ? void 0 : (_result$rawData5$Resu = _result$rawData5.Resume) === null || _result$rawData5$Resu === void 0 ? void 0 : (_result$rawData5$Resu2 = _result$rawData5$Resu.Education) === null || _result$rawData5$Resu2 === void 0 ? void 0 : _result$rawData5$Resu2.some(edu => (edu.GraduationYear || edu.Year || '').toString().includes(filters.graduationYear));\n      if (!hasGradYear) return false;\n    }\n\n    // Company filter\n    if (filters.company) {\n      var _result$rawData6, _result$rawData6$Resu, _result$rawData6$Resu2;\n      const hasCompany = (_result$rawData6 = result.rawData) === null || _result$rawData6 === void 0 ? void 0 : (_result$rawData6$Resu = _result$rawData6.Resume) === null || _result$rawData6$Resu === void 0 ? void 0 : (_result$rawData6$Resu2 = _result$rawData6$Resu.WorkExperience) === null || _result$rawData6$Resu2 === void 0 ? void 0 : _result$rawData6$Resu2.some(exp => (exp.Company || exp.CompanyName || exp.Organization || '').toLowerCase().includes(filters.company.toLowerCase()));\n      if (!hasCompany) return false;\n    }\n\n    // Minimum marks filter\n    if (filters.minMarks) {\n      var _result$rawData7, _result$rawData7$Resu, _result$rawData7$Resu2;\n      const hasMinMarks = (_result$rawData7 = result.rawData) === null || _result$rawData7 === void 0 ? void 0 : (_result$rawData7$Resu = _result$rawData7.Resume) === null || _result$rawData7$Resu === void 0 ? void 0 : (_result$rawData7$Resu2 = _result$rawData7$Resu.Education) === null || _result$rawData7$Resu2 === void 0 ? void 0 : _result$rawData7$Resu2.some(edu => {\n        const marks = parseFloat(edu['GPA/Marks/%']) || 0;\n        return marks >= parseFloat(filters.minMarks);\n      });\n      if (!hasMinMarks) return false;\n    }\n    return true;\n  });\n\n  // Helper function to extract experience years from text\n  const extractExperienceYears = experienceText => {\n    if (!experienceText) return 0;\n    const match = experienceText.match(/(\\d+)\\+?\\s*years?/i);\n    return match ? parseInt(match[1]) : 0;\n  };\n  const handleAdvancedFilterChange = (filterName, value) => {\n    setAdvancedFilters(prev => ({\n      ...prev,\n      [filterName]: value\n    }));\n  };\n  const clearAllFilters = () => {\n    setFilterQuery('');\n    setAdvancedFilters({\n      minExperience: '',\n      maxExperience: '',\n      education: '',\n      location: '',\n      skills: '',\n      minMarks: '',\n      institution: '',\n      graduationYear: '',\n      company: ''\n    });\n  };\n  const hasActiveFilters = () => {\n    return filterQuery || Object.values(advancedFilters).some(value => value !== '');\n  };\n  const handleDownload = resume => {\n    try {\n      var _resume$rawData, _resume$rawData$Resum, _resume$rawData2, _resume$rawData2$Resu, _resume$rawData3, _resume$rawData3$Resu, _resume$rawData4, _resume$rawData4$Resu, _resume$rawData5, _resume$rawData5$Resu, _resume$rawData6, _resume$rawData6$Resu, _resume$rawData7, _resume$rawData7$Resu, _resume$rawData8, _resume$rawData8$Resu, _resume$rawData8$Resu2;\n      // Create a comprehensive text-based resume content\n      const resumeContent = `\n${resume.name}\n${resume.title}\n${resume.email} | ${resume.phone} | ${resume.location}\n\nPROFESSIONAL SUMMARY\n${resume.summary}\n\nWORK EXPERIENCE\n${(_resume$rawData = resume.rawData) !== null && _resume$rawData !== void 0 && (_resume$rawData$Resum = _resume$rawData.Resume) !== null && _resume$rawData$Resum !== void 0 && _resume$rawData$Resum.WorkExperience ? resume.rawData.Resume.WorkExperience.map(exp => `${exp.JobTitle || exp.Position} at ${exp.Company || exp.Organization}\nDuration: ${exp.Duration || exp.Years || 'Not specified'}\n${exp.Description ? 'Description: ' + exp.Description : ''}\n${exp.Responsibilities ? 'Responsibilities: ' + exp.Responsibilities : ''}\n`).join('\\n') : resume.experience}\n\n${(_resume$rawData2 = resume.rawData) !== null && _resume$rawData2 !== void 0 && (_resume$rawData2$Resu = _resume$rawData2.Resume) !== null && _resume$rawData2$Resu !== void 0 && _resume$rawData2$Resu.TotalWorkExperienceInYears ? `Total Experience: ${resume.rawData.Resume.TotalWorkExperienceInYears} years\\n` : ''}\n\nEDUCATION\n${(_resume$rawData3 = resume.rawData) !== null && _resume$rawData3 !== void 0 && (_resume$rawData3$Resu = _resume$rawData3.Resume) !== null && _resume$rawData3$Resu !== void 0 && _resume$rawData3$Resu.Education ? resume.rawData.Resume.Education.map(edu => `${edu.Degree || 'Degree'} from ${edu.Institution || edu.School || 'Institution'}\nGraduation Year: ${edu.GraduationYear || edu.Year || 'Not specified'}\n${edu['GPA/Marks/%'] && edu['GPA/Marks/%'] > 0 ? 'Marks: ' + edu['GPA/Marks/%'] + '%' : ''}\n`).join('\\n') : resume.education}\n\nSKILLS & TECHNOLOGIES\n${resume.skills.join(', ')}\n\n${(_resume$rawData4 = resume.rawData) !== null && _resume$rawData4 !== void 0 && (_resume$rawData4$Resu = _resume$rawData4.Resume) !== null && _resume$rawData4$Resu !== void 0 && _resume$rawData4$Resu.Languages && resume.rawData.Resume.Languages.length > 0 ? `LANGUAGES\\n${resume.rawData.Resume.Languages.join(', ')}\\n` : ''}\n\n${(_resume$rawData5 = resume.rawData) !== null && _resume$rawData5 !== void 0 && (_resume$rawData5$Resu = _resume$rawData5.Resume) !== null && _resume$rawData5$Resu !== void 0 && _resume$rawData5$Resu.Projects && resume.rawData.Resume.Projects.length > 0 ? `PROJECTS\\n${resume.rawData.Resume.Projects.map(project => `${project.Name || project.Title || 'Project'}\n${project.Description ? 'Description: ' + project.Description : ''}\n${project.Technologies ? 'Technologies: ' + project.Technologies : ''}\n`).join('\\n')}` : ''}\n\n${(_resume$rawData6 = resume.rawData) !== null && _resume$rawData6 !== void 0 && (_resume$rawData6$Resu = _resume$rawData6.Resume) !== null && _resume$rawData6$Resu !== void 0 && _resume$rawData6$Resu.Certifications && resume.rawData.Resume.Certifications.length > 0 ? `CERTIFICATIONS\\n${resume.rawData.Resume.Certifications.map(cert => `${cert.Name || cert.Title || 'Certification'}\n${cert.IssuingOrganization ? 'Issued by: ' + cert.IssuingOrganization : ''}\n${cert.IssueDate ? 'Date: ' + cert.IssueDate : ''}\n`).join('\\n')}` : ''}\n\n${(_resume$rawData7 = resume.rawData) !== null && _resume$rawData7 !== void 0 && (_resume$rawData7$Resu = _resume$rawData7.Resume) !== null && _resume$rawData7$Resu !== void 0 && _resume$rawData7$Resu.Achievements && resume.rawData.Resume.Achievements.length > 0 ? `ACHIEVEMENTS\\n${resume.rawData.Resume.Achievements.map(achievement => typeof achievement === 'string' ? achievement : `${achievement.AchievementName || achievement.Name || 'Achievement'}\n${achievement.IssueDate ? 'Date: ' + achievement.IssueDate : ''}\n${achievement.IssuingOrganization ? 'Organization: ' + achievement.IssuingOrganization : ''}\n${achievement.Description ? 'Description: ' + achievement.Description : ''}\n`).join('\\n')}` : ''}\n\n${(_resume$rawData8 = resume.rawData) !== null && _resume$rawData8 !== void 0 && (_resume$rawData8$Resu = _resume$rawData8.Resume) !== null && _resume$rawData8$Resu !== void 0 && (_resume$rawData8$Resu2 = _resume$rawData8$Resu.PersonalInformation) !== null && _resume$rawData8$Resu2 !== void 0 && _resume$rawData8$Resu2.LinkedIn ? `ADDITIONAL INFORMATION\\nLinkedIn: ${resume.rawData.Resume.PersonalInformation.LinkedIn}` : ''}\n\nGenerated on: ${new Date().toLocaleDateString()}\n      `.trim();\n\n      // Create and download the file\n      const blob = new Blob([resumeContent], {\n        type: 'text/plain'\n      });\n      const url = URL.createObjectURL(blob);\n      const link = document.createElement('a');\n      link.href = url;\n      link.download = `${resume.name.replace(/\\s+/g, '_')}_Resume.txt`;\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n      URL.revokeObjectURL(url);\n      toast.success(`Downloaded resume for ${resume.name}`);\n    } catch (error) {\n      console.error('Download error:', error);\n      toast.error('Failed to download resume. Please try again.');\n    }\n  };\n  const handleView = resume => {\n    setSelectedResume(resume);\n    setIsModalOpen(true);\n    toast.info(`Opening detailed view for ${resume.name}`);\n  };\n  const handleCloseModal = () => {\n    setIsModalOpen(false);\n    setSelectedResume(null);\n  };\n  const handleEmailClick = (email, name) => {\n    window.open(`mailto:${email}?subject=Regarding your resume&body=Hi ${name},%0D%0A%0D%0AI found your resume and would like to discuss potential opportunities.%0D%0A%0D%0ABest regards`, '_blank');\n    toast.info(`Opening email client to contact ${name}`);\n  };\n  const handlePhoneClick = (phone, name) => {\n    if (navigator.userAgent.match(/Mobile|Android|iPhone|iPad/)) {\n      window.open(`tel:${phone}`, '_blank');\n    } else {\n      navigator.clipboard.writeText(phone).then(() => {\n        toast.success(`Phone number copied: ${phone}`);\n      }).catch(() => {\n        toast.info(`Phone: ${phone}`);\n      });\n    }\n  };\n  const handleExportAll = () => {\n    if (filteredResults.length === 0) {\n      toast.warning('No results to export');\n      return;\n    }\n    try {\n      const exportData = filteredResults.map(resume => ({\n        name: resume.name,\n        title: resume.title,\n        email: resume.email,\n        phone: resume.phone,\n        location: resume.location,\n        experience: resume.experience,\n        education: resume.education,\n        skills: resume.skills.join(', '),\n        summary: resume.summary,\n        matchScore: resume.score\n      }));\n      const csvContent = ['Name,Title,Email,Phone,Location,Experience,Education,Skills,Summary,Match Score', ...exportData.map(row => Object.values(row).map(value => `\"${String(value).replace(/\"/g, '\"\"')}\"`).join(','))].join('\\n');\n      const blob = new Blob([csvContent], {\n        type: 'text/csv'\n      });\n      const url = URL.createObjectURL(blob);\n      const link = document.createElement('a');\n      link.href = url;\n      link.download = `Resume_Search_Results_${new Date().toISOString().split('T')[0]}.csv`;\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n      URL.revokeObjectURL(url);\n      toast.success(`Exported ${filteredResults.length} resumes to CSV`);\n    } catch (error) {\n      console.error('Export error:', error);\n      toast.error('Failed to export results. Please try again.');\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"search-page\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"page-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        children: \"Search Resumes\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 624,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Use natural language to find the perfect candidates for your needs\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 625,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 623,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"search-section\",\n      children: [/*#__PURE__*/_jsxDEV(\"form\", {\n        onSubmit: handleSearch,\n        className: \"search-form\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"search-input-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"search-input-wrapper\",\n            children: [/*#__PURE__*/_jsxDEV(FiSearch, {\n              className: \"search-icon\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 633,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              placeholder: \"e.g., 'Find Python developers with 5+ years experience' or 'Show frontend engineers in California'\",\n              value: searchQuery,\n              onChange: e => setSearchQuery(e.target.value),\n              className: \"search-input\",\n              disabled: isSearching\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 634,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 632,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"search-buttons\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"submit\",\n              className: \"btn btn-primary\",\n              disabled: isSearching,\n              children: isSearching ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"loading-spinner\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 651,\n                  columnNumber: 21\n                }, this), \"Searching...\"]\n              }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(FiSearch, {\n                  size: 16\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 656,\n                  columnNumber: 21\n                }, this), \"Search\"]\n              }, void 0, true)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 644,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              className: \"btn btn-secondary\",\n              onClick: handleShowAll,\n              disabled: isSearching,\n              children: \"Show All\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 661,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 643,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 631,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 630,\n        columnNumber: 9\n      }, this), searchResults.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"filter-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"filter-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"basic-filter-wrapper\",\n            children: [/*#__PURE__*/_jsxDEV(FiFilter, {\n              className: \"filter-icon\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 678,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              placeholder: \"Quick filter by name, title, or location...\",\n              value: filterQuery,\n              onChange: e => setFilterQuery(e.target.value),\n              className: \"filter-input\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 679,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 677,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"filter-controls\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              className: `btn btn-secondary filter-toggle ${showFilters ? 'active' : ''}`,\n              onClick: () => setShowFilters(!showFilters),\n              children: [/*#__PURE__*/_jsxDEV(FiSettings, {\n                size: 16\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 693,\n                columnNumber: 19\n              }, this), \"Advanced Filters\", showFilters ? /*#__PURE__*/_jsxDEV(FiChevronUp, {\n                size: 16\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 695,\n                columnNumber: 34\n              }, this) : /*#__PURE__*/_jsxDEV(FiChevronDown, {\n                size: 16\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 695,\n                columnNumber: 62\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 688,\n              columnNumber: 17\n            }, this), hasActiveFilters() && /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              className: \"btn btn-outline clear-filters\",\n              onClick: clearAllFilters,\n              children: [/*#__PURE__*/_jsxDEV(FiX, {\n                size: 16\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 703,\n                columnNumber: 21\n              }, this), \"Clear All\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 698,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 687,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 676,\n          columnNumber: 13\n        }, this), showFilters && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"advanced-filters\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"filters-grid\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"filter-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Experience (Years)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 715,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"range-inputs\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"number\",\n                  placeholder: \"Min\",\n                  value: advancedFilters.minExperience,\n                  onChange: e => handleAdvancedFilterChange('minExperience', e.target.value),\n                  className: \"filter-input small\",\n                  min: \"0\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 717,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"to\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 725,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"number\",\n                  placeholder: \"Max\",\n                  value: advancedFilters.maxExperience,\n                  onChange: e => handleAdvancedFilterChange('maxExperience', e.target.value),\n                  className: \"filter-input small\",\n                  min: \"0\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 726,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 716,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 714,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"filter-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Education Level\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 738,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                placeholder: \"e.g., Bachelor, Master, PhD\",\n                value: advancedFilters.education,\n                onChange: e => handleAdvancedFilterChange('education', e.target.value),\n                className: \"filter-input\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 739,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 737,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"filter-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Location\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 749,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                placeholder: \"e.g., Mumbai, Delhi, Bangalore\",\n                value: advancedFilters.location,\n                onChange: e => handleAdvancedFilterChange('location', e.target.value),\n                className: \"filter-input\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 750,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 748,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"filter-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Skills\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 760,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                placeholder: \"e.g., Python, React, Java\",\n                value: advancedFilters.skills,\n                onChange: e => handleAdvancedFilterChange('skills', e.target.value),\n                className: \"filter-input\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 761,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 759,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"filter-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Institution\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 771,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                placeholder: \"e.g., IIT, University name\",\n                value: advancedFilters.institution,\n                onChange: e => handleAdvancedFilterChange('institution', e.target.value),\n                className: \"filter-input\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 772,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 770,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"filter-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Graduation Year\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 782,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                placeholder: \"e.g., 2020, 2021\",\n                value: advancedFilters.graduationYear,\n                onChange: e => handleAdvancedFilterChange('graduationYear', e.target.value),\n                className: \"filter-input\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 783,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 781,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"filter-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Company\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 793,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                placeholder: \"e.g., Google, Microsoft, TCS\",\n                value: advancedFilters.company,\n                onChange: e => handleAdvancedFilterChange('company', e.target.value),\n                className: \"filter-input\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 794,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 792,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"filter-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Minimum Marks (%)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 804,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"number\",\n                placeholder: \"e.g., 75, 80\",\n                value: advancedFilters.minMarks,\n                onChange: e => handleAdvancedFilterChange('minMarks', e.target.value),\n                className: \"filter-input\",\n                min: \"0\",\n                max: \"100\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 805,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 803,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 713,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 712,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"results-count\",\n          children: [filteredResults.length, \" of \", searchResults.length, \" results\", hasActiveFilters() && /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"filtered-indicator\",\n            children: \" (filtered)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 821,\n            columnNumber: 38\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 819,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 675,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 629,\n      columnNumber: 7\n    }, this), searchResults.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"results-section\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"results-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Search Results\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 831,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"results-actions\",\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"btn btn-secondary\",\n            onClick: handleExportAll,\n            title: `Export ${filteredResults.length} results to CSV`,\n            children: [/*#__PURE__*/_jsxDEV(FiDownload, {\n              size: 16\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 838,\n              columnNumber: 17\n            }, this), \"Export All (\", filteredResults.length, \")\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 833,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 832,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 830,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"results-grid\",\n        children: filteredResults.map(resume => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"resume-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"resume-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"resume-avatar\",\n              children: /*#__PURE__*/_jsxDEV(FiUser, {\n                size: 24\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 849,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 848,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"resume-info\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"resume-name\",\n                children: resume.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 852,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"resume-title\",\n                children: resume.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 853,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 851,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 847,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"resume-details\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"detail-item location-item\",\n              children: [/*#__PURE__*/_jsxDEV(FiMapPin, {\n                size: 14\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 859,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: resume.location\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 860,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 858,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"detail-item email-item clickable\",\n              onClick: () => handleEmailClick(resume.email, resume.name),\n              title: `Send email to ${resume.name}`,\n              children: [/*#__PURE__*/_jsxDEV(FiMail, {\n                size: 14\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 867,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: resume.email\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 868,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(FiExternalLink, {\n                size: 12,\n                className: \"external-icon\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 869,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 862,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"detail-item phone-item clickable\",\n              onClick: () => handlePhoneClick(resume.phone, resume.name),\n              title: `Call ${resume.name}`,\n              children: [/*#__PURE__*/_jsxDEV(FiPhone, {\n                size: 14\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 876,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: resume.phone\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 877,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(FiExternalLink, {\n                size: 12,\n                className: \"external-icon\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 878,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 871,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 857,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"resume-content\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"content-row\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"content-section\",\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  children: \"Experience\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 885,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: resume.experience\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 886,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 884,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"content-section\",\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  children: \"Education\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 889,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: resume.education\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 890,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 888,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 883,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"content-section\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                children: \"Skills\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 894,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"skills-list\",\n                children: resume.skills.map((skill, index) => /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"skill-tag\",\n                  children: skill\n                }, index, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 897,\n                  columnNumber: 25\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 895,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 893,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 882,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"resume-actions\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"btn btn-secondary\",\n              onClick: () => handleView(resume),\n              children: [/*#__PURE__*/_jsxDEV(FiEye, {\n                size: 16\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 908,\n                columnNumber: 21\n              }, this), \"View Details\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 904,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"btn btn-primary\",\n              onClick: () => handleDownload(resume),\n              children: [/*#__PURE__*/_jsxDEV(FiDownload, {\n                size: 16\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 915,\n                columnNumber: 21\n              }, this), \"Download\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 911,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 903,\n            columnNumber: 17\n          }, this)]\n        }, resume.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 846,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 844,\n        columnNumber: 11\n      }, this), totalPages > 1 && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"pagination-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"pagination-info\",\n          children: [\"Showing \", (currentPage - 1) * resultsPerPage + 1, \" to \", Math.min(currentPage * resultsPerPage, totalCount), \" of \", totalCount, \" results\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 926,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"pagination-controls\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"btn btn-secondary pagination-btn\",\n            onClick: () => handlePageChange(currentPage - 1),\n            disabled: currentPage === 1,\n            children: \"Previous\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 930,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"page-numbers\",\n            children: Array.from({\n              length: Math.min(5, totalPages)\n            }, (_, i) => {\n              let pageNum;\n              if (totalPages <= 5) {\n                pageNum = i + 1;\n              } else if (currentPage <= 3) {\n                pageNum = i + 1;\n              } else if (currentPage >= totalPages - 2) {\n                pageNum = totalPages - 4 + i;\n              } else {\n                pageNum = currentPage - 2 + i;\n              }\n              return /*#__PURE__*/_jsxDEV(\"button\", {\n                className: `btn pagination-btn ${currentPage === pageNum ? 'btn-primary' : 'btn-secondary'}`,\n                onClick: () => handlePageChange(pageNum),\n                children: pageNum\n              }, pageNum, false, {\n                fileName: _jsxFileName,\n                lineNumber: 952,\n                columnNumber: 23\n              }, this);\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 938,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"btn btn-secondary pagination-btn\",\n            onClick: () => handlePageChange(currentPage + 1),\n            disabled: currentPage === totalPages,\n            children: \"Next\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 963,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 929,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 925,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 829,\n      columnNumber: 9\n    }, this) : !isSearching ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"empty-state\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"empty-icon\",\n        children: /*#__PURE__*/_jsxDEV(FiSearch, {\n          size: 48\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 977,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 976,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n        children: \"Ready to Search!\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 979,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Use the search box above to find resumes using natural language, or click \\\"Show All\\\" to view all resumes in the database.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 980,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"search-examples\",\n        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n          children: \"Try searching for:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 982,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n          children: [/*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"\\\"Find Python developers\\\"\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 984,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"\\\"Show teachers with 5+ years experience\\\"\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 985,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"\\\"Software engineers in Mumbai\\\"\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 986,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 983,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 981,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 975,\n      columnNumber: 9\n    }, this) : null, /*#__PURE__*/_jsxDEV(ResumeModal, {\n      resume: selectedResume,\n      isOpen: isModalOpen,\n      onClose: handleCloseModal\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 993,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 622,\n    columnNumber: 5\n  }, this);\n};\n_s(SearchResumes, \"uvVHvTgqjBYZkn8TcWc0iS1zcVo=\");\n_c = SearchResumes;\nexport default SearchResumes;\nvar _c;\n$RefreshReg$(_c, \"SearchResumes\");", "map": {"version": 3, "names": ["React", "useState", "FiSearch", "<PERSON><PERSON><PERSON><PERSON>", "FiDownload", "FiEye", "FiUser", "FiMapPin", "FiMail", "FiPhone", "FiExternalLink", "FiStar", "FiX", "FiSettings", "FiChevronDown", "FiChevronUp", "toast", "ResumeModal", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "SearchResumes", "_s", "searchQuery", "setSearch<PERSON>uery", "isSearching", "setIsSearching", "searchResults", "setSearchResults", "filterQuery", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "showFilters", "setShowFilters", "advancedFilters", "setAdvancedFilters", "minExperience", "maxExperience", "education", "location", "skills", "minMarks", "institution", "graduationYear", "company", "selectedResume", "setSelectedResume", "isModalOpen", "setIsModalOpen", "currentPage", "setCurrentPage", "totalPages", "setTotalPages", "totalCount", "setTotalCount", "resultsPerPage", "mockResults", "id", "name", "title", "email", "phone", "experience", "summary", "score", "rawData", "Resume", "PersonalInformation", "FullName", "Email", "ContactNumber", "Address", "WorkExperience", "Role", "CompanyName", "StartYear", "EndYear", "Education", "Degree", "Institution", "GraduationYear", "Skills", "TotalWorkExperienceInYears", "handleSearch", "e", "preventDefault", "trim", "warning", "response", "fetch", "method", "headers", "body", "JSON", "stringify", "query", "data", "json", "ok", "transformedResults", "results", "Array", "isArray", "map", "resume", "_resumeData$PersonalI", "_resumeData$PersonalI2", "_resumeData$PersonalI3", "_resumeData$PersonalI4", "_resumeData$PersonalI5", "_resumeData$WorkExper", "_resumeData$Education", "_resumeData$Education2", "resumeData", "_id", "Math", "random", "toString", "substr", "Designation", "length", "Objective", "Summary", "filter", "toLowerCase", "includes", "some", "skill", "api_status", "success", "info", "gpt_response", "console", "log", "mongo_query", "Error", "error", "fallback<PERSON><PERSON><PERSON>s", "handleShowAll", "page", "resumes", "_resumeData$PersonalI6", "_resumeData$PersonalI7", "_resumeData$PersonalI8", "_resumeData$PersonalI9", "_resumeData$PersonalI0", "_resumeData$WorkExper2", "_resumeData$Education3", "_resumeData$Education4", "count", "total", "ceil", "source", "handlePageChange", "filteredResults", "result", "matchesText", "filters", "_result$rawData", "_result$rawData$Resum", "experienceYears", "extractExperienceYears", "parseInt", "_result$rawData2", "_result$rawData2$Resu", "_result$rawData2$Resu2", "hasEducation", "edu", "matchesLocation", "_result$skills", "_result$rawData3", "_result$rawData3$Resu", "skillsText", "hasSkill", "_result$rawData4", "_result$rawData4$Resu", "_result$rawData4$Resu2", "hasInstitution", "School", "_result$rawData5", "_result$rawData5$Resu", "_result$rawData5$Resu2", "hasGradYear", "Year", "_result$rawData6", "_result$rawData6$Resu", "_result$rawData6$Resu2", "hasCompany", "exp", "Company", "Organization", "_result$rawData7", "_result$rawData7$Resu", "_result$rawData7$Resu2", "hasMinMarks", "marks", "parseFloat", "experienceText", "match", "handleAdvancedFilterChange", "filterName", "value", "prev", "clearAllFilters", "hasActiveFilters", "Object", "values", "handleDownload", "_resume$rawData", "_resume$rawData$Resum", "_resume$rawData2", "_resume$rawData2$Resu", "_resume$rawData3", "_resume$rawData3$Resu", "_resume$rawData4", "_resume$rawData4$Resu", "_resume$rawData5", "_resume$rawData5$Resu", "_resume$rawData6", "_resume$rawData6$Resu", "_resume$rawData7", "_resume$rawData7$Resu", "_resume$rawData8", "_resume$rawData8$Resu", "_resume$rawData8$Resu2", "<PERSON><PERSON><PERSON>nt", "JobTitle", "Position", "Duration", "Years", "Description", "Responsibilities", "join", "Languages", "Projects", "project", "Name", "Title", "Technologies", "Certifications", "cert", "IssuingOrganization", "IssueDate", "Achievements", "achievement", "AchievementName", "LinkedIn", "Date", "toLocaleDateString", "blob", "Blob", "type", "url", "URL", "createObjectURL", "link", "document", "createElement", "href", "download", "replace", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL", "handleView", "handleCloseModal", "handleEmailClick", "window", "open", "handlePhoneClick", "navigator", "userAgent", "clipboard", "writeText", "then", "catch", "handleExportAll", "exportData", "matchScore", "csv<PERSON><PERSON>nt", "row", "String", "toISOString", "split", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSubmit", "placeholder", "onChange", "target", "disabled", "size", "onClick", "min", "max", "index", "from", "_", "i", "pageNum", "isOpen", "onClose", "_c", "$RefreshReg$"], "sources": ["D:/Projects/SQL-Agent-Task-Submission-main/SQL-Agent-Task-Submission-main/src/pages/SearchResumes.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { <PERSON><PERSON>earch, Fi<PERSON>ilter, <PERSON>Down<PERSON>, <PERSON>Eye, Fi<PERSON>ser, FiMapPin, FiMail, FiPhone, FiExternalLink, FiStar, FiX, FiSettings, FiChevronDown, FiChevronUp } from 'react-icons/fi';\nimport { toast } from 'react-toastify';\nimport ResumeModal from '../components/ResumeModal';\nimport './SearchResumes.css';\n\nconst SearchResumes = () => {\n  const [searchQuery, setSearchQuery] = useState('');\n  const [isSearching, setIsSearching] = useState(false);\n  const [searchResults, setSearchResults] = useState([]);\n  const [filterQuery, setFilterQuery] = useState('');\n  const [showFilters, setShowFilters] = useState(false);\n  const [advancedFilters, setAdvancedFilters] = useState({\n    minExperience: '',\n    maxExperience: '',\n    education: '',\n    location: '',\n    skills: '',\n    minMarks: '',\n    institution: '',\n    graduationYear: '',\n    company: ''\n  });\n  const [selectedResume, setSelectedResume] = useState(null);\n  const [isModalOpen, setIsModalOpen] = useState(false);\n  const [currentPage, setCurrentPage] = useState(1);\n  const [totalPages, setTotalPages] = useState(1);\n  const [totalCount, setTotalCount] = useState(0);\n  const resultsPerPage = 20;\n\n  // Mock data for demonstration\n  const mockResults = [\n    {\n      id: 1,\n      name: 'John Doe',\n      title: 'Senior Software Engineer',\n      email: '<EMAIL>',\n      phone: '+****************',\n      location: 'San Francisco, CA',\n      experience: '5+ years',\n      skills: ['Python', 'React', 'Node.js', 'AWS'],\n      education: 'MS Computer Science',\n      summary: 'Experienced software engineer with expertise in full-stack development...',\n      score: 95,\n      rawData: {\n        Resume: {\n          PersonalInformation: {\n            FullName: 'John Doe',\n            Email: '<EMAIL>',\n            ContactNumber: '+****************',\n            Address: 'San Francisco, CA'\n          },\n          WorkExperience: [\n            {\n              Role: 'Senior Software Engineer',\n              CompanyName: 'Tech Corp',\n              StartYear: '2019',\n              EndYear: 'Present',\n              'Description/Responsibility': 'Led development of scalable web applications using React and Node.js'\n            },\n            {\n              Role: 'Software Engineer',\n              CompanyName: 'StartupXYZ',\n              StartYear: '2017',\n              EndYear: '2019',\n              'Description/Responsibility': 'Developed full-stack applications and improved system performance'\n            }\n          ],\n          Education: [\n            {\n              Degree: 'MS Computer Science',\n              Institution: 'Stanford University',\n              GraduationYear: '2017',\n              'GPA/Marks/%': 85\n            }\n          ],\n          Skills: ['Python', 'React', 'Node.js', 'AWS'],\n          TotalWorkExperienceInYears: 5\n        }\n      }\n    },\n    {\n      id: 2,\n      name: 'Jane Smith',\n      title: 'Frontend Developer',\n      email: '<EMAIL>',\n      phone: '+****************',\n      location: 'New York, NY',\n      experience: '3+ years',\n      skills: ['React', 'TypeScript', 'CSS', 'JavaScript'],\n      education: 'BS Computer Science',\n      summary: 'Creative frontend developer passionate about user experience...',\n      score: 88,\n      rawData: {\n        Resume: {\n          PersonalInformation: {\n            FullName: 'Jane Smith',\n            Email: '<EMAIL>',\n            ContactNumber: '+****************',\n            Address: 'New York, NY'\n          },\n          WorkExperience: [\n            {\n              Role: 'Frontend Developer',\n              CompanyName: 'Design Studio',\n              StartYear: '2021',\n              EndYear: 'Present',\n              'Description/Responsibility': 'Created responsive web interfaces using React and modern CSS'\n            }\n          ],\n          Education: [\n            {\n              Degree: 'BS Computer Science',\n              Institution: 'NYU',\n              GraduationYear: '2021',\n              'GPA/Marks/%': 78\n            }\n          ],\n          Skills: ['React', 'TypeScript', 'CSS', 'JavaScript'],\n          TotalWorkExperienceInYears: 3\n        }\n      }\n    },\n    {\n      id: 3,\n      name: 'Mike Johnson',\n      title: 'DevOps Engineer',\n      email: '<EMAIL>',\n      phone: '+****************',\n      location: 'Austin, TX',\n      experience: '4+ years',\n      skills: ['Docker', 'Kubernetes', 'AWS', 'Python'],\n      education: 'BS Information Technology',\n      summary: 'DevOps engineer specializing in cloud infrastructure and automation...',\n      score: 92,\n      rawData: {\n        Resume: {\n          PersonalInformation: {\n            FullName: 'Mike Johnson',\n            Email: '<EMAIL>',\n            ContactNumber: '+****************',\n            Address: 'Austin, TX'\n          },\n          WorkExperience: [\n            {\n              Role: 'DevOps Engineer',\n              CompanyName: 'Cloud Solutions Inc',\n              StartYear: '2020',\n              EndYear: 'Present',\n              'Description/Responsibility': 'Managed cloud infrastructure and automated deployment pipelines'\n            }\n          ],\n          Education: [\n            {\n              Degree: 'BS Information Technology',\n              Institution: 'UT Austin',\n              GraduationYear: '2020',\n              'GPA/Marks/%': 82\n            }\n          ],\n          Skills: ['Docker', 'Kubernetes', 'AWS', 'Python'],\n          TotalWorkExperienceInYears: 4\n        }\n      }\n    }\n  ];\n\n  const handleSearch = async (e) => {\n    e.preventDefault();\n    if (!searchQuery.trim()) {\n      toast.warning('Please enter a search query');\n      return;\n    }\n\n    setIsSearching(true);\n\n    try {\n      // Call the actual search API\n      const response = await fetch('/api/search', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({ query: searchQuery }),\n      });\n\n      const data = await response.json();\n\n      if (response.ok) {\n        // Handle both real API results and fallback mock data\n        let transformedResults = [];\n\n        if (data.results && Array.isArray(data.results)) {\n          // Transform the resume data to match our display format\n          transformedResults = data.results.map(resume => {\n            // Handle the nested Resume structure from MongoDB\n            const resumeData = resume.Resume || resume;\n\n            return {\n              id: resume._id || Math.random().toString(36).substr(2, 9),\n              name: resumeData.PersonalInformation?.FullName || resumeData.name || 'Unknown',\n              title: resumeData.PersonalInformation?.Designation || resumeData.title || 'No title',\n              email: resumeData.PersonalInformation?.Email || resumeData.email || 'No email',\n              phone: resumeData.PersonalInformation?.ContactNumber || resumeData.phone || 'No phone',\n              location: resumeData.PersonalInformation?.Address || resumeData.location || 'No location',\n              experience: resumeData.TotalWorkExperienceInYears ?\n                `${resumeData.TotalWorkExperienceInYears} years` :\n                resumeData.WorkExperience?.length ?\n                  `${resumeData.WorkExperience.length}+ positions` :\n                  resumeData.experience || 'No experience data',\n              skills: resumeData.Skills || resumeData.skills || [],\n              education: resumeData.Education?.length ?\n                resumeData.Education[0]?.Degree || 'Education available' :\n                resumeData.education || 'No education data',\n              summary: resumeData.Objective || resumeData.Summary || resumeData.summary || 'No summary available',\n              score: 95, // Default score\n              rawData: resume // Keep original data for reference\n            };\n          });\n        } else {\n          // Fallback to mock data if API doesn't return results\n          transformedResults = mockResults.filter(resume =>\n            resume.name.toLowerCase().includes(searchQuery.toLowerCase()) ||\n            resume.title.toLowerCase().includes(searchQuery.toLowerCase()) ||\n            resume.skills.some(skill => skill.toLowerCase().includes(searchQuery.toLowerCase()))\n          );\n        }\n\n        setSearchResults(transformedResults);\n\n        if (data.api_status === 'success') {\n          toast.success(`Found ${transformedResults.length} matching resumes using AI search`);\n        } else if (data.api_status === 'fallback') {\n          toast.info(`Found ${transformedResults.length} matching resumes using fallback search`);\n        } else {\n          toast.success(`Found ${transformedResults.length} matching resumes`);\n        }\n\n        // Show GPT response if available\n        if (data.gpt_response && data.gpt_response !== 'Data received successfully.') {\n          console.log('GPT Response:', data.gpt_response);\n          console.log('MongoDB Query:', data.mongo_query);\n        }\n      } else {\n        throw new Error(data.error || 'Search failed');\n      }\n    } catch (error) {\n      console.error('Search error:', error);\n\n      // Fallback to mock data on network error\n      const fallbackResults = mockResults.filter(resume =>\n        resume.name.toLowerCase().includes(searchQuery.toLowerCase()) ||\n        resume.title.toLowerCase().includes(searchQuery.toLowerCase()) ||\n        resume.skills.some(skill => skill.toLowerCase().includes(searchQuery.toLowerCase()))\n      );\n\n      setSearchResults(fallbackResults);\n      toast.warning(`Using demo data - Found ${fallbackResults.length} matching resumes`);\n    } finally {\n      setIsSearching(false);\n    }\n  };\n\n  const handleShowAll = async (page = 1) => {\n    setIsSearching(true);\n    try {\n      // Call the actual API to get paginated resumes\n      const response = await fetch(`/api/resumes?page=${page}&limit=${resultsPerPage}`);\n      const data = await response.json();\n\n      if (response.ok) {\n        let transformedResults = [];\n\n        if (data.resumes && Array.isArray(data.resumes)) {\n          // Transform the resume data to match our display format\n          transformedResults = data.resumes.map(resume => {\n            // Handle the nested Resume structure from MongoDB\n            const resumeData = resume.Resume || resume;\n\n            return {\n              id: resume._id || Math.random().toString(36).substr(2, 9),\n              name: resumeData.PersonalInformation?.FullName || resumeData.name || 'Unknown',\n              title: resumeData.PersonalInformation?.Designation || resumeData.title || 'No title',\n              email: resumeData.PersonalInformation?.Email || resumeData.email || 'No email',\n              phone: resumeData.PersonalInformation?.ContactNumber || resumeData.phone || 'No phone',\n              location: resumeData.PersonalInformation?.Address || resumeData.location || 'No location',\n              experience: resumeData.TotalWorkExperienceInYears ?\n                `${resumeData.TotalWorkExperienceInYears} years` :\n                resumeData.WorkExperience?.length ?\n                  `${resumeData.WorkExperience.length}+ positions` :\n                  resumeData.experience || 'No experience data',\n              skills: resumeData.Skills || resumeData.skills || [],\n              education: resumeData.Education?.length ?\n                resumeData.Education[0]?.Degree || 'Education available' :\n                resumeData.education || 'No education data',\n              summary: resumeData.Objective || resumeData.Summary || resumeData.summary || 'No summary available',\n              score: 90, // Default score for show all\n              rawData: resume // Keep original data for reference\n            };\n          });\n        } else {\n          // Fallback to mock data if API doesn't return proper results\n          transformedResults = mockResults;\n        }\n\n        setSearchResults(transformedResults);\n        setCurrentPage(page);\n        setTotalCount(data.count || data.total || transformedResults.length);\n        setTotalPages(Math.ceil((data.count || data.total || transformedResults.length) / resultsPerPage));\n        setSearchQuery('');\n\n        if (data.source === 'database') {\n          toast.success(`Showing page ${page} of ${Math.ceil((data.count || data.total || transformedResults.length) / resultsPerPage)} (${data.count || data.total} total resumes)`);\n        } else {\n          toast.info(`Showing ${transformedResults.length} resumes`);\n        }\n      } else {\n        throw new Error(data.error || 'Failed to load resumes');\n      }\n    } catch (error) {\n      console.error('Load resumes error:', error);\n\n      // Fallback to mock data on network error\n      setSearchResults(mockResults);\n      setCurrentPage(1);\n      setTotalCount(mockResults.length);\n      setTotalPages(Math.ceil(mockResults.length / resultsPerPage));\n      setSearchQuery('');\n\n      toast.warning(`Using demo data - Showing ${mockResults.length} sample resumes`);\n    } finally {\n      setIsSearching(false);\n    }\n  };\n\n  const handlePageChange = (page) => {\n    if (page >= 1 && page <= totalPages) {\n      handleShowAll(page);\n    }\n  };\n\n  const filteredResults = searchResults.filter(result => {\n    // Basic text filter\n    if (filterQuery) {\n      const matchesText = result.name.toLowerCase().includes(filterQuery.toLowerCase()) ||\n                         result.title.toLowerCase().includes(filterQuery.toLowerCase()) ||\n                         result.location.toLowerCase().includes(filterQuery.toLowerCase());\n      if (!matchesText) return false;\n    }\n\n    // Advanced filters\n    const filters = advancedFilters;\n\n    // Experience filter\n    if (filters.minExperience || filters.maxExperience) {\n      const experienceYears = result.rawData?.Resume?.TotalWorkExperienceInYears ||\n                             extractExperienceYears(result.experience) || 0;\n\n      if (filters.minExperience && experienceYears < parseInt(filters.minExperience)) return false;\n      if (filters.maxExperience && experienceYears > parseInt(filters.maxExperience)) return false;\n    }\n\n    // Education filter\n    if (filters.education) {\n      const hasEducation = result.rawData?.Resume?.Education?.some(edu =>\n        (edu.Degree || '').toLowerCase().includes(filters.education.toLowerCase())\n      ) || (result.education || '').toLowerCase().includes(filters.education.toLowerCase());\n      if (!hasEducation) return false;\n    }\n\n    // Location filter\n    if (filters.location) {\n      const matchesLocation = (result.location || '').toLowerCase().includes(filters.location.toLowerCase());\n      if (!matchesLocation) return false;\n    }\n\n    // Skills filter\n    if (filters.skills) {\n      const skillsText = filters.skills.toLowerCase();\n      const hasSkill = result.skills?.some(skill => skill.toLowerCase().includes(skillsText)) ||\n                      (result.rawData?.Resume?.Skills || []).some(skill =>\n                        skill.toLowerCase().includes(skillsText)\n                      );\n      if (!hasSkill) return false;\n    }\n\n    // Institution filter\n    if (filters.institution) {\n      const hasInstitution = result.rawData?.Resume?.Education?.some(edu =>\n        (edu.Institution || edu.School || '').toLowerCase().includes(filters.institution.toLowerCase())\n      );\n      if (!hasInstitution) return false;\n    }\n\n    // Graduation year filter\n    if (filters.graduationYear) {\n      const hasGradYear = result.rawData?.Resume?.Education?.some(edu =>\n        (edu.GraduationYear || edu.Year || '').toString().includes(filters.graduationYear)\n      );\n      if (!hasGradYear) return false;\n    }\n\n    // Company filter\n    if (filters.company) {\n      const hasCompany = result.rawData?.Resume?.WorkExperience?.some(exp =>\n        (exp.Company || exp.CompanyName || exp.Organization || '').toLowerCase().includes(filters.company.toLowerCase())\n      );\n      if (!hasCompany) return false;\n    }\n\n    // Minimum marks filter\n    if (filters.minMarks) {\n      const hasMinMarks = result.rawData?.Resume?.Education?.some(edu => {\n        const marks = parseFloat(edu['GPA/Marks/%']) || 0;\n        return marks >= parseFloat(filters.minMarks);\n      });\n      if (!hasMinMarks) return false;\n    }\n\n    return true;\n  });\n\n  // Helper function to extract experience years from text\n  const extractExperienceYears = (experienceText) => {\n    if (!experienceText) return 0;\n    const match = experienceText.match(/(\\d+)\\+?\\s*years?/i);\n    return match ? parseInt(match[1]) : 0;\n  };\n\n  const handleAdvancedFilterChange = (filterName, value) => {\n    setAdvancedFilters(prev => ({\n      ...prev,\n      [filterName]: value\n    }));\n  };\n\n  const clearAllFilters = () => {\n    setFilterQuery('');\n    setAdvancedFilters({\n      minExperience: '',\n      maxExperience: '',\n      education: '',\n      location: '',\n      skills: '',\n      minMarks: '',\n      institution: '',\n      graduationYear: '',\n      company: ''\n    });\n  };\n\n  const hasActiveFilters = () => {\n    return filterQuery || Object.values(advancedFilters).some(value => value !== '');\n  };\n\n  const handleDownload = (resume) => {\n    try {\n      // Create a comprehensive text-based resume content\n      const resumeContent = `\n${resume.name}\n${resume.title}\n${resume.email} | ${resume.phone} | ${resume.location}\n\nPROFESSIONAL SUMMARY\n${resume.summary}\n\nWORK EXPERIENCE\n${resume.rawData?.Resume?.WorkExperience ?\n  resume.rawData.Resume.WorkExperience.map(exp =>\n    `${exp.JobTitle || exp.Position} at ${exp.Company || exp.Organization}\nDuration: ${exp.Duration || exp.Years || 'Not specified'}\n${exp.Description ? 'Description: ' + exp.Description : ''}\n${exp.Responsibilities ? 'Responsibilities: ' + exp.Responsibilities : ''}\n`\n  ).join('\\n') : resume.experience}\n\n${resume.rawData?.Resume?.TotalWorkExperienceInYears ?\n  `Total Experience: ${resume.rawData.Resume.TotalWorkExperienceInYears} years\\n` : ''}\n\nEDUCATION\n${resume.rawData?.Resume?.Education ?\n  resume.rawData.Resume.Education.map(edu =>\n    `${edu.Degree || 'Degree'} from ${edu.Institution || edu.School || 'Institution'}\nGraduation Year: ${edu.GraduationYear || edu.Year || 'Not specified'}\n${edu['GPA/Marks/%'] && edu['GPA/Marks/%'] > 0 ? 'Marks: ' + edu['GPA/Marks/%'] + '%' : ''}\n`\n  ).join('\\n') : resume.education}\n\nSKILLS & TECHNOLOGIES\n${resume.skills.join(', ')}\n\n${resume.rawData?.Resume?.Languages && resume.rawData.Resume.Languages.length > 0 ?\n  `LANGUAGES\\n${resume.rawData.Resume.Languages.join(', ')}\\n` : ''}\n\n${resume.rawData?.Resume?.Projects && resume.rawData.Resume.Projects.length > 0 ?\n  `PROJECTS\\n${resume.rawData.Resume.Projects.map(project =>\n    `${project.Name || project.Title || 'Project'}\n${project.Description ? 'Description: ' + project.Description : ''}\n${project.Technologies ? 'Technologies: ' + project.Technologies : ''}\n`\n  ).join('\\n')}` : ''}\n\n${resume.rawData?.Resume?.Certifications && resume.rawData.Resume.Certifications.length > 0 ?\n  `CERTIFICATIONS\\n${resume.rawData.Resume.Certifications.map(cert =>\n    `${cert.Name || cert.Title || 'Certification'}\n${cert.IssuingOrganization ? 'Issued by: ' + cert.IssuingOrganization : ''}\n${cert.IssueDate ? 'Date: ' + cert.IssueDate : ''}\n`\n  ).join('\\n')}` : ''}\n\n${resume.rawData?.Resume?.Achievements && resume.rawData.Resume.Achievements.length > 0 ?\n  `ACHIEVEMENTS\\n${resume.rawData.Resume.Achievements.map(achievement =>\n    typeof achievement === 'string' ? achievement :\n    `${achievement.AchievementName || achievement.Name || 'Achievement'}\n${achievement.IssueDate ? 'Date: ' + achievement.IssueDate : ''}\n${achievement.IssuingOrganization ? 'Organization: ' + achievement.IssuingOrganization : ''}\n${achievement.Description ? 'Description: ' + achievement.Description : ''}\n`\n  ).join('\\n')}` : ''}\n\n${resume.rawData?.Resume?.PersonalInformation?.LinkedIn ?\n  `ADDITIONAL INFORMATION\\nLinkedIn: ${resume.rawData.Resume.PersonalInformation.LinkedIn}` : ''}\n\nGenerated on: ${new Date().toLocaleDateString()}\n      `.trim();\n\n      // Create and download the file\n      const blob = new Blob([resumeContent], { type: 'text/plain' });\n      const url = URL.createObjectURL(blob);\n      const link = document.createElement('a');\n      link.href = url;\n      link.download = `${resume.name.replace(/\\s+/g, '_')}_Resume.txt`;\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n      URL.revokeObjectURL(url);\n\n      toast.success(`Downloaded resume for ${resume.name}`);\n    } catch (error) {\n      console.error('Download error:', error);\n      toast.error('Failed to download resume. Please try again.');\n    }\n  };\n\n  const handleView = (resume) => {\n    setSelectedResume(resume);\n    setIsModalOpen(true);\n    toast.info(`Opening detailed view for ${resume.name}`);\n  };\n\n  const handleCloseModal = () => {\n    setIsModalOpen(false);\n    setSelectedResume(null);\n  };\n\n  const handleEmailClick = (email, name) => {\n    window.open(`mailto:${email}?subject=Regarding your resume&body=Hi ${name},%0D%0A%0D%0AI found your resume and would like to discuss potential opportunities.%0D%0A%0D%0ABest regards`, '_blank');\n    toast.info(`Opening email client to contact ${name}`);\n  };\n\n  const handlePhoneClick = (phone, name) => {\n    if (navigator.userAgent.match(/Mobile|Android|iPhone|iPad/)) {\n      window.open(`tel:${phone}`, '_blank');\n    } else {\n      navigator.clipboard.writeText(phone).then(() => {\n        toast.success(`Phone number copied: ${phone}`);\n      }).catch(() => {\n        toast.info(`Phone: ${phone}`);\n      });\n    }\n  };\n\n\n\n  const handleExportAll = () => {\n    if (filteredResults.length === 0) {\n      toast.warning('No results to export');\n      return;\n    }\n\n    try {\n      const exportData = filteredResults.map(resume => ({\n        name: resume.name,\n        title: resume.title,\n        email: resume.email,\n        phone: resume.phone,\n        location: resume.location,\n        experience: resume.experience,\n        education: resume.education,\n        skills: resume.skills.join(', '),\n        summary: resume.summary,\n        matchScore: resume.score\n      }));\n\n      const csvContent = [\n        'Name,Title,Email,Phone,Location,Experience,Education,Skills,Summary,Match Score',\n        ...exportData.map(row =>\n          Object.values(row).map(value =>\n            `\"${String(value).replace(/\"/g, '\"\"')}\"`\n          ).join(',')\n        )\n      ].join('\\n');\n\n      const blob = new Blob([csvContent], { type: 'text/csv' });\n      const url = URL.createObjectURL(blob);\n      const link = document.createElement('a');\n      link.href = url;\n      link.download = `Resume_Search_Results_${new Date().toISOString().split('T')[0]}.csv`;\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n      URL.revokeObjectURL(url);\n\n      toast.success(`Exported ${filteredResults.length} resumes to CSV`);\n    } catch (error) {\n      console.error('Export error:', error);\n      toast.error('Failed to export results. Please try again.');\n    }\n  };\n\n  return (\n    <div className=\"search-page\">\n      <div className=\"page-header\">\n        <h1>Search Resumes</h1>\n        <p>Use natural language to find the perfect candidates for your needs</p>\n      </div>\n\n      {/* Search Section */}\n      <div className=\"search-section\">\n        <form onSubmit={handleSearch} className=\"search-form\">\n          <div className=\"search-input-group\">\n            <div className=\"search-input-wrapper\">\n              <FiSearch className=\"search-icon\" />\n              <input\n                type=\"text\"\n                placeholder=\"e.g., 'Find Python developers with 5+ years experience' or 'Show frontend engineers in California'\"\n                value={searchQuery}\n                onChange={(e) => setSearchQuery(e.target.value)}\n                className=\"search-input\"\n                disabled={isSearching}\n              />\n            </div>\n            <div className=\"search-buttons\">\n              <button\n                type=\"submit\"\n                className=\"btn btn-primary\"\n                disabled={isSearching}\n              >\n                {isSearching ? (\n                  <>\n                    <div className=\"loading-spinner\" />\n                    Searching...\n                  </>\n                ) : (\n                  <>\n                    <FiSearch size={16} />\n                    Search\n                  </>\n                )}\n              </button>\n              <button\n                type=\"button\"\n                className=\"btn btn-secondary\"\n                onClick={handleShowAll}\n                disabled={isSearching}\n              >\n                Show All\n              </button>\n            </div>\n          </div>\n        </form>\n\n        {/* Filter Section */}\n        {searchResults.length > 0 && (\n          <div className=\"filter-section\">\n            <div className=\"filter-header\">\n              <div className=\"basic-filter-wrapper\">\n                <FiFilter className=\"filter-icon\" />\n                <input\n                  type=\"text\"\n                  placeholder=\"Quick filter by name, title, or location...\"\n                  value={filterQuery}\n                  onChange={(e) => setFilterQuery(e.target.value)}\n                  className=\"filter-input\"\n                />\n              </div>\n              <div className=\"filter-controls\">\n                <button\n                  type=\"button\"\n                  className={`btn btn-secondary filter-toggle ${showFilters ? 'active' : ''}`}\n                  onClick={() => setShowFilters(!showFilters)}\n                >\n                  <FiSettings size={16} />\n                  Advanced Filters\n                  {showFilters ? <FiChevronUp size={16} /> : <FiChevronDown size={16} />}\n                </button>\n                {hasActiveFilters() && (\n                  <button\n                    type=\"button\"\n                    className=\"btn btn-outline clear-filters\"\n                    onClick={clearAllFilters}\n                  >\n                    <FiX size={16} />\n                    Clear All\n                  </button>\n                )}\n              </div>\n            </div>\n\n            {/* Advanced Filters Panel */}\n            {showFilters && (\n              <div className=\"advanced-filters\">\n                <div className=\"filters-grid\">\n                  <div className=\"filter-group\">\n                    <label>Experience (Years)</label>\n                    <div className=\"range-inputs\">\n                      <input\n                        type=\"number\"\n                        placeholder=\"Min\"\n                        value={advancedFilters.minExperience}\n                        onChange={(e) => handleAdvancedFilterChange('minExperience', e.target.value)}\n                        className=\"filter-input small\"\n                        min=\"0\"\n                      />\n                      <span>to</span>\n                      <input\n                        type=\"number\"\n                        placeholder=\"Max\"\n                        value={advancedFilters.maxExperience}\n                        onChange={(e) => handleAdvancedFilterChange('maxExperience', e.target.value)}\n                        className=\"filter-input small\"\n                        min=\"0\"\n                      />\n                    </div>\n                  </div>\n\n                  <div className=\"filter-group\">\n                    <label>Education Level</label>\n                    <input\n                      type=\"text\"\n                      placeholder=\"e.g., Bachelor, Master, PhD\"\n                      value={advancedFilters.education}\n                      onChange={(e) => handleAdvancedFilterChange('education', e.target.value)}\n                      className=\"filter-input\"\n                    />\n                  </div>\n\n                  <div className=\"filter-group\">\n                    <label>Location</label>\n                    <input\n                      type=\"text\"\n                      placeholder=\"e.g., Mumbai, Delhi, Bangalore\"\n                      value={advancedFilters.location}\n                      onChange={(e) => handleAdvancedFilterChange('location', e.target.value)}\n                      className=\"filter-input\"\n                    />\n                  </div>\n\n                  <div className=\"filter-group\">\n                    <label>Skills</label>\n                    <input\n                      type=\"text\"\n                      placeholder=\"e.g., Python, React, Java\"\n                      value={advancedFilters.skills}\n                      onChange={(e) => handleAdvancedFilterChange('skills', e.target.value)}\n                      className=\"filter-input\"\n                    />\n                  </div>\n\n                  <div className=\"filter-group\">\n                    <label>Institution</label>\n                    <input\n                      type=\"text\"\n                      placeholder=\"e.g., IIT, University name\"\n                      value={advancedFilters.institution}\n                      onChange={(e) => handleAdvancedFilterChange('institution', e.target.value)}\n                      className=\"filter-input\"\n                    />\n                  </div>\n\n                  <div className=\"filter-group\">\n                    <label>Graduation Year</label>\n                    <input\n                      type=\"text\"\n                      placeholder=\"e.g., 2020, 2021\"\n                      value={advancedFilters.graduationYear}\n                      onChange={(e) => handleAdvancedFilterChange('graduationYear', e.target.value)}\n                      className=\"filter-input\"\n                    />\n                  </div>\n\n                  <div className=\"filter-group\">\n                    <label>Company</label>\n                    <input\n                      type=\"text\"\n                      placeholder=\"e.g., Google, Microsoft, TCS\"\n                      value={advancedFilters.company}\n                      onChange={(e) => handleAdvancedFilterChange('company', e.target.value)}\n                      className=\"filter-input\"\n                    />\n                  </div>\n\n                  <div className=\"filter-group\">\n                    <label>Minimum Marks (%)</label>\n                    <input\n                      type=\"number\"\n                      placeholder=\"e.g., 75, 80\"\n                      value={advancedFilters.minMarks}\n                      onChange={(e) => handleAdvancedFilterChange('minMarks', e.target.value)}\n                      className=\"filter-input\"\n                      min=\"0\"\n                      max=\"100\"\n                    />\n                  </div>\n                </div>\n              </div>\n            )}\n\n            <div className=\"results-count\">\n              {filteredResults.length} of {searchResults.length} results\n              {hasActiveFilters() && <span className=\"filtered-indicator\"> (filtered)</span>}\n            </div>\n          </div>\n        )}\n      </div>\n\n      {/* Results Section */}\n      {searchResults.length > 0 ? (\n        <div className=\"results-section\">\n          <div className=\"results-header\">\n            <h2>Search Results</h2>\n            <div className=\"results-actions\">\n              <button\n                className=\"btn btn-secondary\"\n                onClick={handleExportAll}\n                title={`Export ${filteredResults.length} results to CSV`}\n              >\n                <FiDownload size={16} />\n                Export All ({filteredResults.length})\n              </button>\n            </div>\n          </div>\n\n          <div className=\"results-grid\">\n            {filteredResults.map((resume) => (\n              <div key={resume.id} className=\"resume-card\">\n                <div className=\"resume-header\">\n                  <div className=\"resume-avatar\">\n                    <FiUser size={24} />\n                  </div>\n                  <div className=\"resume-info\">\n                    <h3 className=\"resume-name\">{resume.name}</h3>\n                    <p className=\"resume-title\">{resume.title}</p>\n                  </div>\n                </div>\n\n                <div className=\"resume-details\">\n                  <div className=\"detail-item location-item\">\n                    <FiMapPin size={14} />\n                    <span>{resume.location}</span>\n                  </div>\n                  <div\n                    className=\"detail-item email-item clickable\"\n                    onClick={() => handleEmailClick(resume.email, resume.name)}\n                    title={`Send email to ${resume.name}`}\n                  >\n                    <FiMail size={14} />\n                    <span>{resume.email}</span>\n                    <FiExternalLink size={12} className=\"external-icon\" />\n                  </div>\n                  <div\n                    className=\"detail-item phone-item clickable\"\n                    onClick={() => handlePhoneClick(resume.phone, resume.name)}\n                    title={`Call ${resume.name}`}\n                  >\n                    <FiPhone size={14} />\n                    <span>{resume.phone}</span>\n                    <FiExternalLink size={12} className=\"external-icon\" />\n                  </div>\n                </div>\n\n                <div className=\"resume-content\">\n                  <div className=\"content-row\">\n                    <div className=\"content-section\">\n                      <h4>Experience</h4>\n                      <p>{resume.experience}</p>\n                    </div>\n                    <div className=\"content-section\">\n                      <h4>Education</h4>\n                      <p>{resume.education}</p>\n                    </div>\n                  </div>\n                  <div className=\"content-section\">\n                    <h4>Skills</h4>\n                    <div className=\"skills-list\">\n                      {resume.skills.map((skill, index) => (\n                        <span key={index} className=\"skill-tag\">{skill}</span>\n                      ))}\n                    </div>\n                  </div>\n                </div>\n\n                <div className=\"resume-actions\">\n                  <button\n                    className=\"btn btn-secondary\"\n                    onClick={() => handleView(resume)}\n                  >\n                    <FiEye size={16} />\n                    View Details\n                  </button>\n                  <button\n                    className=\"btn btn-primary\"\n                    onClick={() => handleDownload(resume)}\n                  >\n                    <FiDownload size={16} />\n                    Download\n                  </button>\n                </div>\n              </div>\n            ))}\n          </div>\n\n          {/* Pagination */}\n          {totalPages > 1 && (\n            <div className=\"pagination-section\">\n              <div className=\"pagination-info\">\n                Showing {((currentPage - 1) * resultsPerPage) + 1} to {Math.min(currentPage * resultsPerPage, totalCount)} of {totalCount} results\n              </div>\n              <div className=\"pagination-controls\">\n                <button\n                  className=\"btn btn-secondary pagination-btn\"\n                  onClick={() => handlePageChange(currentPage - 1)}\n                  disabled={currentPage === 1}\n                >\n                  Previous\n                </button>\n\n                <div className=\"page-numbers\">\n                  {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {\n                    let pageNum;\n                    if (totalPages <= 5) {\n                      pageNum = i + 1;\n                    } else if (currentPage <= 3) {\n                      pageNum = i + 1;\n                    } else if (currentPage >= totalPages - 2) {\n                      pageNum = totalPages - 4 + i;\n                    } else {\n                      pageNum = currentPage - 2 + i;\n                    }\n\n                    return (\n                      <button\n                        key={pageNum}\n                        className={`btn pagination-btn ${currentPage === pageNum ? 'btn-primary' : 'btn-secondary'}`}\n                        onClick={() => handlePageChange(pageNum)}\n                      >\n                        {pageNum}\n                      </button>\n                    );\n                  })}\n                </div>\n\n                <button\n                  className=\"btn btn-secondary pagination-btn\"\n                  onClick={() => handlePageChange(currentPage + 1)}\n                  disabled={currentPage === totalPages}\n                >\n                  Next\n                </button>\n              </div>\n            </div>\n          )}\n        </div>\n      ) : !isSearching ? (\n        <div className=\"empty-state\">\n          <div className=\"empty-icon\">\n            <FiSearch size={48} />\n          </div>\n          <h3>Ready to Search!</h3>\n          <p>Use the search box above to find resumes using natural language, or click \"Show All\" to view all resumes in the database.</p>\n          <div className=\"search-examples\">\n            <h4>Try searching for:</h4>\n            <ul>\n              <li>\"Find Python developers\"</li>\n              <li>\"Show teachers with 5+ years experience\"</li>\n              <li>\"Software engineers in Mumbai\"</li>\n            </ul>\n          </div>\n        </div>\n      ) : null}\n\n      {/* Resume Details Modal */}\n      <ResumeModal\n        resume={selectedResume}\n        isOpen={isModalOpen}\n        onClose={handleCloseModal}\n      />\n    </div>\n  );\n};\n\nexport default SearchResumes;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,QAAQ,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,KAAK,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,OAAO,EAAEC,cAAc,EAAEC,MAAM,EAAEC,GAAG,EAAEC,UAAU,EAAEC,aAAa,EAAEC,WAAW,QAAQ,gBAAgB;AAC9K,SAASC,KAAK,QAAQ,gBAAgB;AACtC,OAAOC,WAAW,MAAM,2BAA2B;AACnD,OAAO,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE7B,MAAMC,aAAa,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC1B,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACyB,WAAW,EAAEC,cAAc,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAAC2B,aAAa,EAAEC,gBAAgB,CAAC,GAAG5B,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAAC6B,WAAW,EAAEC,cAAc,CAAC,GAAG9B,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC+B,WAAW,EAAEC,cAAc,CAAC,GAAGhC,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACiC,eAAe,EAAEC,kBAAkB,CAAC,GAAGlC,QAAQ,CAAC;IACrDmC,aAAa,EAAE,EAAE;IACjBC,aAAa,EAAE,EAAE;IACjBC,SAAS,EAAE,EAAE;IACbC,QAAQ,EAAE,EAAE;IACZC,MAAM,EAAE,EAAE;IACVC,QAAQ,EAAE,EAAE;IACZC,WAAW,EAAE,EAAE;IACfC,cAAc,EAAE,EAAE;IAClBC,OAAO,EAAE;EACX,CAAC,CAAC;EACF,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAG7C,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAAC8C,WAAW,EAAEC,cAAc,CAAC,GAAG/C,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACgD,WAAW,EAAEC,cAAc,CAAC,GAAGjD,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAACkD,UAAU,EAAEC,aAAa,CAAC,GAAGnD,QAAQ,CAAC,CAAC,CAAC;EAC/C,MAAM,CAACoD,UAAU,EAAEC,aAAa,CAAC,GAAGrD,QAAQ,CAAC,CAAC,CAAC;EAC/C,MAAMsD,cAAc,GAAG,EAAE;;EAEzB;EACA,MAAMC,WAAW,GAAG,CAClB;IACEC,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,UAAU;IAChBC,KAAK,EAAE,0BAA0B;IACjCC,KAAK,EAAE,oBAAoB;IAC3BC,KAAK,EAAE,mBAAmB;IAC1BtB,QAAQ,EAAE,mBAAmB;IAC7BuB,UAAU,EAAE,UAAU;IACtBtB,MAAM,EAAE,CAAC,QAAQ,EAAE,OAAO,EAAE,SAAS,EAAE,KAAK,CAAC;IAC7CF,SAAS,EAAE,qBAAqB;IAChCyB,OAAO,EAAE,2EAA2E;IACpFC,KAAK,EAAE,EAAE;IACTC,OAAO,EAAE;MACPC,MAAM,EAAE;QACNC,mBAAmB,EAAE;UACnBC,QAAQ,EAAE,UAAU;UACpBC,KAAK,EAAE,oBAAoB;UAC3BC,aAAa,EAAE,mBAAmB;UAClCC,OAAO,EAAE;QACX,CAAC;QACDC,cAAc,EAAE,CACd;UACEC,IAAI,EAAE,0BAA0B;UAChCC,WAAW,EAAE,WAAW;UACxBC,SAAS,EAAE,MAAM;UACjBC,OAAO,EAAE,SAAS;UAClB,4BAA4B,EAAE;QAChC,CAAC,EACD;UACEH,IAAI,EAAE,mBAAmB;UACzBC,WAAW,EAAE,YAAY;UACzBC,SAAS,EAAE,MAAM;UACjBC,OAAO,EAAE,MAAM;UACf,4BAA4B,EAAE;QAChC,CAAC,CACF;QACDC,SAAS,EAAE,CACT;UACEC,MAAM,EAAE,qBAAqB;UAC7BC,WAAW,EAAE,qBAAqB;UAClCC,cAAc,EAAE,MAAM;UACtB,aAAa,EAAE;QACjB,CAAC,CACF;QACDC,MAAM,EAAE,CAAC,QAAQ,EAAE,OAAO,EAAE,SAAS,EAAE,KAAK,CAAC;QAC7CC,0BAA0B,EAAE;MAC9B;IACF;EACF,CAAC,EACD;IACEzB,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,YAAY;IAClBC,KAAK,EAAE,oBAAoB;IAC3BC,KAAK,EAAE,sBAAsB;IAC7BC,KAAK,EAAE,mBAAmB;IAC1BtB,QAAQ,EAAE,cAAc;IACxBuB,UAAU,EAAE,UAAU;IACtBtB,MAAM,EAAE,CAAC,OAAO,EAAE,YAAY,EAAE,KAAK,EAAE,YAAY,CAAC;IACpDF,SAAS,EAAE,qBAAqB;IAChCyB,OAAO,EAAE,iEAAiE;IAC1EC,KAAK,EAAE,EAAE;IACTC,OAAO,EAAE;MACPC,MAAM,EAAE;QACNC,mBAAmB,EAAE;UACnBC,QAAQ,EAAE,YAAY;UACtBC,KAAK,EAAE,sBAAsB;UAC7BC,aAAa,EAAE,mBAAmB;UAClCC,OAAO,EAAE;QACX,CAAC;QACDC,cAAc,EAAE,CACd;UACEC,IAAI,EAAE,oBAAoB;UAC1BC,WAAW,EAAE,eAAe;UAC5BC,SAAS,EAAE,MAAM;UACjBC,OAAO,EAAE,SAAS;UAClB,4BAA4B,EAAE;QAChC,CAAC,CACF;QACDC,SAAS,EAAE,CACT;UACEC,MAAM,EAAE,qBAAqB;UAC7BC,WAAW,EAAE,KAAK;UAClBC,cAAc,EAAE,MAAM;UACtB,aAAa,EAAE;QACjB,CAAC,CACF;QACDC,MAAM,EAAE,CAAC,OAAO,EAAE,YAAY,EAAE,KAAK,EAAE,YAAY,CAAC;QACpDC,0BAA0B,EAAE;MAC9B;IACF;EACF,CAAC,EACD;IACEzB,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,cAAc;IACpBC,KAAK,EAAE,iBAAiB;IACxBC,KAAK,EAAE,wBAAwB;IAC/BC,KAAK,EAAE,mBAAmB;IAC1BtB,QAAQ,EAAE,YAAY;IACtBuB,UAAU,EAAE,UAAU;IACtBtB,MAAM,EAAE,CAAC,QAAQ,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,CAAC;IACjDF,SAAS,EAAE,2BAA2B;IACtCyB,OAAO,EAAE,wEAAwE;IACjFC,KAAK,EAAE,EAAE;IACTC,OAAO,EAAE;MACPC,MAAM,EAAE;QACNC,mBAAmB,EAAE;UACnBC,QAAQ,EAAE,cAAc;UACxBC,KAAK,EAAE,wBAAwB;UAC/BC,aAAa,EAAE,mBAAmB;UAClCC,OAAO,EAAE;QACX,CAAC;QACDC,cAAc,EAAE,CACd;UACEC,IAAI,EAAE,iBAAiB;UACvBC,WAAW,EAAE,qBAAqB;UAClCC,SAAS,EAAE,MAAM;UACjBC,OAAO,EAAE,SAAS;UAClB,4BAA4B,EAAE;QAChC,CAAC,CACF;QACDC,SAAS,EAAE,CACT;UACEC,MAAM,EAAE,2BAA2B;UACnCC,WAAW,EAAE,WAAW;UACxBC,cAAc,EAAE,MAAM;UACtB,aAAa,EAAE;QACjB,CAAC,CACF;QACDC,MAAM,EAAE,CAAC,QAAQ,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,CAAC;QACjDC,0BAA0B,EAAE;MAC9B;IACF;EACF,CAAC,CACF;EAED,MAAMC,YAAY,GAAG,MAAOC,CAAC,IAAK;IAChCA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,IAAI,CAAC7D,WAAW,CAAC8D,IAAI,CAAC,CAAC,EAAE;MACvBtE,KAAK,CAACuE,OAAO,CAAC,6BAA6B,CAAC;MAC5C;IACF;IAEA5D,cAAc,CAAC,IAAI,CAAC;IAEpB,IAAI;MACF;MACA,MAAM6D,QAAQ,GAAG,MAAMC,KAAK,CAAC,aAAa,EAAE;QAC1CC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UAAEC,KAAK,EAAEvE;QAAY,CAAC;MAC7C,CAAC,CAAC;MAEF,MAAMwE,IAAI,GAAG,MAAMR,QAAQ,CAACS,IAAI,CAAC,CAAC;MAElC,IAAIT,QAAQ,CAACU,EAAE,EAAE;QACf;QACA,IAAIC,kBAAkB,GAAG,EAAE;QAE3B,IAAIH,IAAI,CAACI,OAAO,IAAIC,KAAK,CAACC,OAAO,CAACN,IAAI,CAACI,OAAO,CAAC,EAAE;UAC/C;UACAD,kBAAkB,GAAGH,IAAI,CAACI,OAAO,CAACG,GAAG,CAACC,MAAM,IAAI;YAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,sBAAA;YAC9C;YACA,MAAMC,UAAU,GAAGT,MAAM,CAACtC,MAAM,IAAIsC,MAAM;YAE1C,OAAO;cACL/C,EAAE,EAAE+C,MAAM,CAACU,GAAG,IAAIC,IAAI,CAACC,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;cACzD5D,IAAI,EAAE,EAAA+C,qBAAA,GAAAQ,UAAU,CAAC9C,mBAAmB,cAAAsC,qBAAA,uBAA9BA,qBAAA,CAAgCrC,QAAQ,KAAI6C,UAAU,CAACvD,IAAI,IAAI,SAAS;cAC9EC,KAAK,EAAE,EAAA+C,sBAAA,GAAAO,UAAU,CAAC9C,mBAAmB,cAAAuC,sBAAA,uBAA9BA,sBAAA,CAAgCa,WAAW,KAAIN,UAAU,CAACtD,KAAK,IAAI,UAAU;cACpFC,KAAK,EAAE,EAAA+C,sBAAA,GAAAM,UAAU,CAAC9C,mBAAmB,cAAAwC,sBAAA,uBAA9BA,sBAAA,CAAgCtC,KAAK,KAAI4C,UAAU,CAACrD,KAAK,IAAI,UAAU;cAC9EC,KAAK,EAAE,EAAA+C,sBAAA,GAAAK,UAAU,CAAC9C,mBAAmB,cAAAyC,sBAAA,uBAA9BA,sBAAA,CAAgCtC,aAAa,KAAI2C,UAAU,CAACpD,KAAK,IAAI,UAAU;cACtFtB,QAAQ,EAAE,EAAAsE,sBAAA,GAAAI,UAAU,CAAC9C,mBAAmB,cAAA0C,sBAAA,uBAA9BA,sBAAA,CAAgCtC,OAAO,KAAI0C,UAAU,CAAC1E,QAAQ,IAAI,aAAa;cACzFuB,UAAU,EAAEmD,UAAU,CAAC/B,0BAA0B,GAC/C,GAAG+B,UAAU,CAAC/B,0BAA0B,QAAQ,GAChD,CAAA4B,qBAAA,GAAAG,UAAU,CAACzC,cAAc,cAAAsC,qBAAA,eAAzBA,qBAAA,CAA2BU,MAAM,GAC/B,GAAGP,UAAU,CAACzC,cAAc,CAACgD,MAAM,aAAa,GAChDP,UAAU,CAACnD,UAAU,IAAI,oBAAoB;cACjDtB,MAAM,EAAEyE,UAAU,CAAChC,MAAM,IAAIgC,UAAU,CAACzE,MAAM,IAAI,EAAE;cACpDF,SAAS,EAAE,CAAAyE,qBAAA,GAAAE,UAAU,CAACpC,SAAS,cAAAkC,qBAAA,eAApBA,qBAAA,CAAsBS,MAAM,GACrC,EAAAR,sBAAA,GAAAC,UAAU,CAACpC,SAAS,CAAC,CAAC,CAAC,cAAAmC,sBAAA,uBAAvBA,sBAAA,CAAyBlC,MAAM,KAAI,qBAAqB,GACxDmC,UAAU,CAAC3E,SAAS,IAAI,mBAAmB;cAC7CyB,OAAO,EAAEkD,UAAU,CAACQ,SAAS,IAAIR,UAAU,CAACS,OAAO,IAAIT,UAAU,CAAClD,OAAO,IAAI,sBAAsB;cACnGC,KAAK,EAAE,EAAE;cAAE;cACXC,OAAO,EAAEuC,MAAM,CAAC;YAClB,CAAC;UACH,CAAC,CAAC;QACJ,CAAC,MAAM;UACL;UACAL,kBAAkB,GAAG3C,WAAW,CAACmE,MAAM,CAACnB,MAAM,IAC5CA,MAAM,CAAC9C,IAAI,CAACkE,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACrG,WAAW,CAACoG,WAAW,CAAC,CAAC,CAAC,IAC7DpB,MAAM,CAAC7C,KAAK,CAACiE,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACrG,WAAW,CAACoG,WAAW,CAAC,CAAC,CAAC,IAC9DpB,MAAM,CAAChE,MAAM,CAACsF,IAAI,CAACC,KAAK,IAAIA,KAAK,CAACH,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACrG,WAAW,CAACoG,WAAW,CAAC,CAAC,CAAC,CACrF,CAAC;QACH;QAEA/F,gBAAgB,CAACsE,kBAAkB,CAAC;QAEpC,IAAIH,IAAI,CAACgC,UAAU,KAAK,SAAS,EAAE;UACjChH,KAAK,CAACiH,OAAO,CAAC,SAAS9B,kBAAkB,CAACqB,MAAM,mCAAmC,CAAC;QACtF,CAAC,MAAM,IAAIxB,IAAI,CAACgC,UAAU,KAAK,UAAU,EAAE;UACzChH,KAAK,CAACkH,IAAI,CAAC,SAAS/B,kBAAkB,CAACqB,MAAM,yCAAyC,CAAC;QACzF,CAAC,MAAM;UACLxG,KAAK,CAACiH,OAAO,CAAC,SAAS9B,kBAAkB,CAACqB,MAAM,mBAAmB,CAAC;QACtE;;QAEA;QACA,IAAIxB,IAAI,CAACmC,YAAY,IAAInC,IAAI,CAACmC,YAAY,KAAK,6BAA6B,EAAE;UAC5EC,OAAO,CAACC,GAAG,CAAC,eAAe,EAAErC,IAAI,CAACmC,YAAY,CAAC;UAC/CC,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAErC,IAAI,CAACsC,WAAW,CAAC;QACjD;MACF,CAAC,MAAM;QACL,MAAM,IAAIC,KAAK,CAACvC,IAAI,CAACwC,KAAK,IAAI,eAAe,CAAC;MAChD;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdJ,OAAO,CAACI,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;;MAErC;MACA,MAAMC,eAAe,GAAGjF,WAAW,CAACmE,MAAM,CAACnB,MAAM,IAC/CA,MAAM,CAAC9C,IAAI,CAACkE,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACrG,WAAW,CAACoG,WAAW,CAAC,CAAC,CAAC,IAC7DpB,MAAM,CAAC7C,KAAK,CAACiE,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACrG,WAAW,CAACoG,WAAW,CAAC,CAAC,CAAC,IAC9DpB,MAAM,CAAChE,MAAM,CAACsF,IAAI,CAACC,KAAK,IAAIA,KAAK,CAACH,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACrG,WAAW,CAACoG,WAAW,CAAC,CAAC,CAAC,CACrF,CAAC;MAED/F,gBAAgB,CAAC4G,eAAe,CAAC;MACjCzH,KAAK,CAACuE,OAAO,CAAC,2BAA2BkD,eAAe,CAACjB,MAAM,mBAAmB,CAAC;IACrF,CAAC,SAAS;MACR7F,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;EAED,MAAM+G,aAAa,GAAG,MAAAA,CAAOC,IAAI,GAAG,CAAC,KAAK;IACxChH,cAAc,CAAC,IAAI,CAAC;IACpB,IAAI;MACF;MACA,MAAM6D,QAAQ,GAAG,MAAMC,KAAK,CAAC,qBAAqBkD,IAAI,UAAUpF,cAAc,EAAE,CAAC;MACjF,MAAMyC,IAAI,GAAG,MAAMR,QAAQ,CAACS,IAAI,CAAC,CAAC;MAElC,IAAIT,QAAQ,CAACU,EAAE,EAAE;QACf,IAAIC,kBAAkB,GAAG,EAAE;QAE3B,IAAIH,IAAI,CAAC4C,OAAO,IAAIvC,KAAK,CAACC,OAAO,CAACN,IAAI,CAAC4C,OAAO,CAAC,EAAE;UAC/C;UACAzC,kBAAkB,GAAGH,IAAI,CAAC4C,OAAO,CAACrC,GAAG,CAACC,MAAM,IAAI;YAAA,IAAAqC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;YAC9C;YACA,MAAMnC,UAAU,GAAGT,MAAM,CAACtC,MAAM,IAAIsC,MAAM;YAE1C,OAAO;cACL/C,EAAE,EAAE+C,MAAM,CAACU,GAAG,IAAIC,IAAI,CAACC,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;cACzD5D,IAAI,EAAE,EAAAmF,sBAAA,GAAA5B,UAAU,CAAC9C,mBAAmB,cAAA0E,sBAAA,uBAA9BA,sBAAA,CAAgCzE,QAAQ,KAAI6C,UAAU,CAACvD,IAAI,IAAI,SAAS;cAC9EC,KAAK,EAAE,EAAAmF,sBAAA,GAAA7B,UAAU,CAAC9C,mBAAmB,cAAA2E,sBAAA,uBAA9BA,sBAAA,CAAgCvB,WAAW,KAAIN,UAAU,CAACtD,KAAK,IAAI,UAAU;cACpFC,KAAK,EAAE,EAAAmF,sBAAA,GAAA9B,UAAU,CAAC9C,mBAAmB,cAAA4E,sBAAA,uBAA9BA,sBAAA,CAAgC1E,KAAK,KAAI4C,UAAU,CAACrD,KAAK,IAAI,UAAU;cAC9EC,KAAK,EAAE,EAAAmF,sBAAA,GAAA/B,UAAU,CAAC9C,mBAAmB,cAAA6E,sBAAA,uBAA9BA,sBAAA,CAAgC1E,aAAa,KAAI2C,UAAU,CAACpD,KAAK,IAAI,UAAU;cACtFtB,QAAQ,EAAE,EAAA0G,sBAAA,GAAAhC,UAAU,CAAC9C,mBAAmB,cAAA8E,sBAAA,uBAA9BA,sBAAA,CAAgC1E,OAAO,KAAI0C,UAAU,CAAC1E,QAAQ,IAAI,aAAa;cACzFuB,UAAU,EAAEmD,UAAU,CAAC/B,0BAA0B,GAC/C,GAAG+B,UAAU,CAAC/B,0BAA0B,QAAQ,GAChD,CAAAgE,sBAAA,GAAAjC,UAAU,CAACzC,cAAc,cAAA0E,sBAAA,eAAzBA,sBAAA,CAA2B1B,MAAM,GAC/B,GAAGP,UAAU,CAACzC,cAAc,CAACgD,MAAM,aAAa,GAChDP,UAAU,CAACnD,UAAU,IAAI,oBAAoB;cACjDtB,MAAM,EAAEyE,UAAU,CAAChC,MAAM,IAAIgC,UAAU,CAACzE,MAAM,IAAI,EAAE;cACpDF,SAAS,EAAE,CAAA6G,sBAAA,GAAAlC,UAAU,CAACpC,SAAS,cAAAsE,sBAAA,eAApBA,sBAAA,CAAsB3B,MAAM,GACrC,EAAA4B,sBAAA,GAAAnC,UAAU,CAACpC,SAAS,CAAC,CAAC,CAAC,cAAAuE,sBAAA,uBAAvBA,sBAAA,CAAyBtE,MAAM,KAAI,qBAAqB,GACxDmC,UAAU,CAAC3E,SAAS,IAAI,mBAAmB;cAC7CyB,OAAO,EAAEkD,UAAU,CAACQ,SAAS,IAAIR,UAAU,CAACS,OAAO,IAAIT,UAAU,CAAClD,OAAO,IAAI,sBAAsB;cACnGC,KAAK,EAAE,EAAE;cAAE;cACXC,OAAO,EAAEuC,MAAM,CAAC;YAClB,CAAC;UACH,CAAC,CAAC;QACJ,CAAC,MAAM;UACL;UACAL,kBAAkB,GAAG3C,WAAW;QAClC;QAEA3B,gBAAgB,CAACsE,kBAAkB,CAAC;QACpCjD,cAAc,CAACyF,IAAI,CAAC;QACpBrF,aAAa,CAAC0C,IAAI,CAACqD,KAAK,IAAIrD,IAAI,CAACsD,KAAK,IAAInD,kBAAkB,CAACqB,MAAM,CAAC;QACpEpE,aAAa,CAAC+D,IAAI,CAACoC,IAAI,CAAC,CAACvD,IAAI,CAACqD,KAAK,IAAIrD,IAAI,CAACsD,KAAK,IAAInD,kBAAkB,CAACqB,MAAM,IAAIjE,cAAc,CAAC,CAAC;QAClG9B,cAAc,CAAC,EAAE,CAAC;QAElB,IAAIuE,IAAI,CAACwD,MAAM,KAAK,UAAU,EAAE;UAC9BxI,KAAK,CAACiH,OAAO,CAAC,gBAAgBU,IAAI,OAAOxB,IAAI,CAACoC,IAAI,CAAC,CAACvD,IAAI,CAACqD,KAAK,IAAIrD,IAAI,CAACsD,KAAK,IAAInD,kBAAkB,CAACqB,MAAM,IAAIjE,cAAc,CAAC,KAAKyC,IAAI,CAACqD,KAAK,IAAIrD,IAAI,CAACsD,KAAK,iBAAiB,CAAC;QAC7K,CAAC,MAAM;UACLtI,KAAK,CAACkH,IAAI,CAAC,WAAW/B,kBAAkB,CAACqB,MAAM,UAAU,CAAC;QAC5D;MACF,CAAC,MAAM;QACL,MAAM,IAAIe,KAAK,CAACvC,IAAI,CAACwC,KAAK,IAAI,wBAAwB,CAAC;MACzD;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdJ,OAAO,CAACI,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;;MAE3C;MACA3G,gBAAgB,CAAC2B,WAAW,CAAC;MAC7BN,cAAc,CAAC,CAAC,CAAC;MACjBI,aAAa,CAACE,WAAW,CAACgE,MAAM,CAAC;MACjCpE,aAAa,CAAC+D,IAAI,CAACoC,IAAI,CAAC/F,WAAW,CAACgE,MAAM,GAAGjE,cAAc,CAAC,CAAC;MAC7D9B,cAAc,CAAC,EAAE,CAAC;MAElBT,KAAK,CAACuE,OAAO,CAAC,6BAA6B/B,WAAW,CAACgE,MAAM,iBAAiB,CAAC;IACjF,CAAC,SAAS;MACR7F,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;EAED,MAAM8H,gBAAgB,GAAId,IAAI,IAAK;IACjC,IAAIA,IAAI,IAAI,CAAC,IAAIA,IAAI,IAAIxF,UAAU,EAAE;MACnCuF,aAAa,CAACC,IAAI,CAAC;IACrB;EACF,CAAC;EAED,MAAMe,eAAe,GAAG9H,aAAa,CAAC+F,MAAM,CAACgC,MAAM,IAAI;IACrD;IACA,IAAI7H,WAAW,EAAE;MACf,MAAM8H,WAAW,GAAGD,MAAM,CAACjG,IAAI,CAACkE,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC/F,WAAW,CAAC8F,WAAW,CAAC,CAAC,CAAC,IAC9D+B,MAAM,CAAChG,KAAK,CAACiE,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC/F,WAAW,CAAC8F,WAAW,CAAC,CAAC,CAAC,IAC9D+B,MAAM,CAACpH,QAAQ,CAACqF,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC/F,WAAW,CAAC8F,WAAW,CAAC,CAAC,CAAC;MACpF,IAAI,CAACgC,WAAW,EAAE,OAAO,KAAK;IAChC;;IAEA;IACA,MAAMC,OAAO,GAAG3H,eAAe;;IAE/B;IACA,IAAI2H,OAAO,CAACzH,aAAa,IAAIyH,OAAO,CAACxH,aAAa,EAAE;MAAA,IAAAyH,eAAA,EAAAC,qBAAA;MAClD,MAAMC,eAAe,GAAG,EAAAF,eAAA,GAAAH,MAAM,CAAC1F,OAAO,cAAA6F,eAAA,wBAAAC,qBAAA,GAAdD,eAAA,CAAgB5F,MAAM,cAAA6F,qBAAA,uBAAtBA,qBAAA,CAAwB7E,0BAA0B,KACnD+E,sBAAsB,CAACN,MAAM,CAAC7F,UAAU,CAAC,IAAI,CAAC;MAErE,IAAI+F,OAAO,CAACzH,aAAa,IAAI4H,eAAe,GAAGE,QAAQ,CAACL,OAAO,CAACzH,aAAa,CAAC,EAAE,OAAO,KAAK;MAC5F,IAAIyH,OAAO,CAACxH,aAAa,IAAI2H,eAAe,GAAGE,QAAQ,CAACL,OAAO,CAACxH,aAAa,CAAC,EAAE,OAAO,KAAK;IAC9F;;IAEA;IACA,IAAIwH,OAAO,CAACvH,SAAS,EAAE;MAAA,IAAA6H,gBAAA,EAAAC,qBAAA,EAAAC,sBAAA;MACrB,MAAMC,YAAY,GAAG,EAAAH,gBAAA,GAAAR,MAAM,CAAC1F,OAAO,cAAAkG,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBjG,MAAM,cAAAkG,qBAAA,wBAAAC,sBAAA,GAAtBD,qBAAA,CAAwBvF,SAAS,cAAAwF,sBAAA,uBAAjCA,sBAAA,CAAmCvC,IAAI,CAACyC,GAAG,IAC9D,CAACA,GAAG,CAACzF,MAAM,IAAI,EAAE,EAAE8C,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACgC,OAAO,CAACvH,SAAS,CAACsF,WAAW,CAAC,CAAC,CAC3E,CAAC,KAAI,CAAC+B,MAAM,CAACrH,SAAS,IAAI,EAAE,EAAEsF,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACgC,OAAO,CAACvH,SAAS,CAACsF,WAAW,CAAC,CAAC,CAAC;MACrF,IAAI,CAAC0C,YAAY,EAAE,OAAO,KAAK;IACjC;;IAEA;IACA,IAAIT,OAAO,CAACtH,QAAQ,EAAE;MACpB,MAAMiI,eAAe,GAAG,CAACb,MAAM,CAACpH,QAAQ,IAAI,EAAE,EAAEqF,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACgC,OAAO,CAACtH,QAAQ,CAACqF,WAAW,CAAC,CAAC,CAAC;MACtG,IAAI,CAAC4C,eAAe,EAAE,OAAO,KAAK;IACpC;;IAEA;IACA,IAAIX,OAAO,CAACrH,MAAM,EAAE;MAAA,IAAAiI,cAAA,EAAAC,gBAAA,EAAAC,qBAAA;MAClB,MAAMC,UAAU,GAAGf,OAAO,CAACrH,MAAM,CAACoF,WAAW,CAAC,CAAC;MAC/C,MAAMiD,QAAQ,GAAG,EAAAJ,cAAA,GAAAd,MAAM,CAACnH,MAAM,cAAAiI,cAAA,uBAAbA,cAAA,CAAe3C,IAAI,CAACC,KAAK,IAAIA,KAAK,CAACH,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC+C,UAAU,CAAC,CAAC,KACvE,CAAC,EAAAF,gBAAA,GAAAf,MAAM,CAAC1F,OAAO,cAAAyG,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBxG,MAAM,cAAAyG,qBAAA,uBAAtBA,qBAAA,CAAwB1F,MAAM,KAAI,EAAE,EAAE6C,IAAI,CAACC,KAAK,IAC/CA,KAAK,CAACH,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC+C,UAAU,CACzC,CAAC;MACjB,IAAI,CAACC,QAAQ,EAAE,OAAO,KAAK;IAC7B;;IAEA;IACA,IAAIhB,OAAO,CAACnH,WAAW,EAAE;MAAA,IAAAoI,gBAAA,EAAAC,qBAAA,EAAAC,sBAAA;MACvB,MAAMC,cAAc,IAAAH,gBAAA,GAAGnB,MAAM,CAAC1F,OAAO,cAAA6G,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgB5G,MAAM,cAAA6G,qBAAA,wBAAAC,sBAAA,GAAtBD,qBAAA,CAAwBlG,SAAS,cAAAmG,sBAAA,uBAAjCA,sBAAA,CAAmClD,IAAI,CAACyC,GAAG,IAChE,CAACA,GAAG,CAACxF,WAAW,IAAIwF,GAAG,CAACW,MAAM,IAAI,EAAE,EAAEtD,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACgC,OAAO,CAACnH,WAAW,CAACkF,WAAW,CAAC,CAAC,CAChG,CAAC;MACD,IAAI,CAACqD,cAAc,EAAE,OAAO,KAAK;IACnC;;IAEA;IACA,IAAIpB,OAAO,CAAClH,cAAc,EAAE;MAAA,IAAAwI,gBAAA,EAAAC,qBAAA,EAAAC,sBAAA;MAC1B,MAAMC,WAAW,IAAAH,gBAAA,GAAGxB,MAAM,CAAC1F,OAAO,cAAAkH,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBjH,MAAM,cAAAkH,qBAAA,wBAAAC,sBAAA,GAAtBD,qBAAA,CAAwBvG,SAAS,cAAAwG,sBAAA,uBAAjCA,sBAAA,CAAmCvD,IAAI,CAACyC,GAAG,IAC7D,CAACA,GAAG,CAACvF,cAAc,IAAIuF,GAAG,CAACgB,IAAI,IAAI,EAAE,EAAElE,QAAQ,CAAC,CAAC,CAACQ,QAAQ,CAACgC,OAAO,CAAClH,cAAc,CACnF,CAAC;MACD,IAAI,CAAC2I,WAAW,EAAE,OAAO,KAAK;IAChC;;IAEA;IACA,IAAIzB,OAAO,CAACjH,OAAO,EAAE;MAAA,IAAA4I,gBAAA,EAAAC,qBAAA,EAAAC,sBAAA;MACnB,MAAMC,UAAU,IAAAH,gBAAA,GAAG7B,MAAM,CAAC1F,OAAO,cAAAuH,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBtH,MAAM,cAAAuH,qBAAA,wBAAAC,sBAAA,GAAtBD,qBAAA,CAAwBjH,cAAc,cAAAkH,sBAAA,uBAAtCA,sBAAA,CAAwC5D,IAAI,CAAC8D,GAAG,IACjE,CAACA,GAAG,CAACC,OAAO,IAAID,GAAG,CAAClH,WAAW,IAAIkH,GAAG,CAACE,YAAY,IAAI,EAAE,EAAElE,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACgC,OAAO,CAACjH,OAAO,CAACgF,WAAW,CAAC,CAAC,CACjH,CAAC;MACD,IAAI,CAAC+D,UAAU,EAAE,OAAO,KAAK;IAC/B;;IAEA;IACA,IAAI9B,OAAO,CAACpH,QAAQ,EAAE;MAAA,IAAAsJ,gBAAA,EAAAC,qBAAA,EAAAC,sBAAA;MACpB,MAAMC,WAAW,IAAAH,gBAAA,GAAGpC,MAAM,CAAC1F,OAAO,cAAA8H,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgB7H,MAAM,cAAA8H,qBAAA,wBAAAC,sBAAA,GAAtBD,qBAAA,CAAwBnH,SAAS,cAAAoH,sBAAA,uBAAjCA,sBAAA,CAAmCnE,IAAI,CAACyC,GAAG,IAAI;QACjE,MAAM4B,KAAK,GAAGC,UAAU,CAAC7B,GAAG,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC;QACjD,OAAO4B,KAAK,IAAIC,UAAU,CAACvC,OAAO,CAACpH,QAAQ,CAAC;MAC9C,CAAC,CAAC;MACF,IAAI,CAACyJ,WAAW,EAAE,OAAO,KAAK;IAChC;IAEA,OAAO,IAAI;EACb,CAAC,CAAC;;EAEF;EACA,MAAMjC,sBAAsB,GAAIoC,cAAc,IAAK;IACjD,IAAI,CAACA,cAAc,EAAE,OAAO,CAAC;IAC7B,MAAMC,KAAK,GAAGD,cAAc,CAACC,KAAK,CAAC,oBAAoB,CAAC;IACxD,OAAOA,KAAK,GAAGpC,QAAQ,CAACoC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;EACvC,CAAC;EAED,MAAMC,0BAA0B,GAAGA,CAACC,UAAU,EAAEC,KAAK,KAAK;IACxDtK,kBAAkB,CAACuK,IAAI,KAAK;MAC1B,GAAGA,IAAI;MACP,CAACF,UAAU,GAAGC;IAChB,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAME,eAAe,GAAGA,CAAA,KAAM;IAC5B5K,cAAc,CAAC,EAAE,CAAC;IAClBI,kBAAkB,CAAC;MACjBC,aAAa,EAAE,EAAE;MACjBC,aAAa,EAAE,EAAE;MACjBC,SAAS,EAAE,EAAE;MACbC,QAAQ,EAAE,EAAE;MACZC,MAAM,EAAE,EAAE;MACVC,QAAQ,EAAE,EAAE;MACZC,WAAW,EAAE,EAAE;MACfC,cAAc,EAAE,EAAE;MAClBC,OAAO,EAAE;IACX,CAAC,CAAC;EACJ,CAAC;EAED,MAAMgK,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,OAAO9K,WAAW,IAAI+K,MAAM,CAACC,MAAM,CAAC5K,eAAe,CAAC,CAAC4F,IAAI,CAAC2E,KAAK,IAAIA,KAAK,KAAK,EAAE,CAAC;EAClF,CAAC;EAED,MAAMM,cAAc,GAAIvG,MAAM,IAAK;IACjC,IAAI;MAAA,IAAAwG,eAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,qBAAA,EAAAC,sBAAA;MACF;MACA,MAAMC,aAAa,GAAG;AAC5B,EAAEzH,MAAM,CAAC9C,IAAI;AACb,EAAE8C,MAAM,CAAC7C,KAAK;AACd,EAAE6C,MAAM,CAAC5C,KAAK,MAAM4C,MAAM,CAAC3C,KAAK,MAAM2C,MAAM,CAACjE,QAAQ;AACrD;AACA;AACA,EAAEiE,MAAM,CAACzC,OAAO;AAChB;AACA;AACA,EAAE,CAAAiJ,eAAA,GAAAxG,MAAM,CAACvC,OAAO,cAAA+I,eAAA,gBAAAC,qBAAA,GAAdD,eAAA,CAAgB9I,MAAM,cAAA+I,qBAAA,eAAtBA,qBAAA,CAAwBzI,cAAc,GACtCgC,MAAM,CAACvC,OAAO,CAACC,MAAM,CAACM,cAAc,CAAC+B,GAAG,CAACqF,GAAG,IAC1C,GAAGA,GAAG,CAACsC,QAAQ,IAAItC,GAAG,CAACuC,QAAQ,OAAOvC,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,YAAY;AACzE,YAAYF,GAAG,CAACwC,QAAQ,IAAIxC,GAAG,CAACyC,KAAK,IAAI,eAAe;AACxD,EAAEzC,GAAG,CAAC0C,WAAW,GAAG,eAAe,GAAG1C,GAAG,CAAC0C,WAAW,GAAG,EAAE;AAC1D,EAAE1C,GAAG,CAAC2C,gBAAgB,GAAG,oBAAoB,GAAG3C,GAAG,CAAC2C,gBAAgB,GAAG,EAAE;AACzE,CACE,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC,GAAGhI,MAAM,CAAC1C,UAAU;AAClC;AACA,EAAE,CAAAoJ,gBAAA,GAAA1G,MAAM,CAACvC,OAAO,cAAAiJ,gBAAA,gBAAAC,qBAAA,GAAdD,gBAAA,CAAgBhJ,MAAM,cAAAiJ,qBAAA,eAAtBA,qBAAA,CAAwBjI,0BAA0B,GAClD,qBAAqBsB,MAAM,CAACvC,OAAO,CAACC,MAAM,CAACgB,0BAA0B,UAAU,GAAG,EAAE;AACtF;AACA;AACA,EAAE,CAAAkI,gBAAA,GAAA5G,MAAM,CAACvC,OAAO,cAAAmJ,gBAAA,gBAAAC,qBAAA,GAAdD,gBAAA,CAAgBlJ,MAAM,cAAAmJ,qBAAA,eAAtBA,qBAAA,CAAwBxI,SAAS,GACjC2B,MAAM,CAACvC,OAAO,CAACC,MAAM,CAACW,SAAS,CAAC0B,GAAG,CAACgE,GAAG,IACrC,GAAGA,GAAG,CAACzF,MAAM,IAAI,QAAQ,SAASyF,GAAG,CAACxF,WAAW,IAAIwF,GAAG,CAACW,MAAM,IAAI,aAAa;AACpF,mBAAmBX,GAAG,CAACvF,cAAc,IAAIuF,GAAG,CAACgB,IAAI,IAAI,eAAe;AACpE,EAAEhB,GAAG,CAAC,aAAa,CAAC,IAAIA,GAAG,CAAC,aAAa,CAAC,GAAG,CAAC,GAAG,SAAS,GAAGA,GAAG,CAAC,aAAa,CAAC,GAAG,GAAG,GAAG,EAAE;AAC1F,CACE,CAAC,CAACiE,IAAI,CAAC,IAAI,CAAC,GAAGhI,MAAM,CAAClE,SAAS;AACjC;AACA;AACA,EAAEkE,MAAM,CAAChE,MAAM,CAACgM,IAAI,CAAC,IAAI,CAAC;AAC1B;AACA,EAAE,CAAAlB,gBAAA,GAAA9G,MAAM,CAACvC,OAAO,cAAAqJ,gBAAA,gBAAAC,qBAAA,GAAdD,gBAAA,CAAgBpJ,MAAM,cAAAqJ,qBAAA,eAAtBA,qBAAA,CAAwBkB,SAAS,IAAIjI,MAAM,CAACvC,OAAO,CAACC,MAAM,CAACuK,SAAS,CAACjH,MAAM,GAAG,CAAC,GAC/E,cAAchB,MAAM,CAACvC,OAAO,CAACC,MAAM,CAACuK,SAAS,CAACD,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,EAAE;AACnE;AACA,EAAE,CAAAhB,gBAAA,GAAAhH,MAAM,CAACvC,OAAO,cAAAuJ,gBAAA,gBAAAC,qBAAA,GAAdD,gBAAA,CAAgBtJ,MAAM,cAAAuJ,qBAAA,eAAtBA,qBAAA,CAAwBiB,QAAQ,IAAIlI,MAAM,CAACvC,OAAO,CAACC,MAAM,CAACwK,QAAQ,CAAClH,MAAM,GAAG,CAAC,GAC7E,aAAahB,MAAM,CAACvC,OAAO,CAACC,MAAM,CAACwK,QAAQ,CAACnI,GAAG,CAACoI,OAAO,IACrD,GAAGA,OAAO,CAACC,IAAI,IAAID,OAAO,CAACE,KAAK,IAAI,SAAS;AACjD,EAAEF,OAAO,CAACL,WAAW,GAAG,eAAe,GAAGK,OAAO,CAACL,WAAW,GAAG,EAAE;AAClE,EAAEK,OAAO,CAACG,YAAY,GAAG,gBAAgB,GAAGH,OAAO,CAACG,YAAY,GAAG,EAAE;AACrE,CACE,CAAC,CAACN,IAAI,CAAC,IAAI,CAAC,EAAE,GAAG,EAAE;AACrB;AACA,EAAE,CAAAd,gBAAA,GAAAlH,MAAM,CAACvC,OAAO,cAAAyJ,gBAAA,gBAAAC,qBAAA,GAAdD,gBAAA,CAAgBxJ,MAAM,cAAAyJ,qBAAA,eAAtBA,qBAAA,CAAwBoB,cAAc,IAAIvI,MAAM,CAACvC,OAAO,CAACC,MAAM,CAAC6K,cAAc,CAACvH,MAAM,GAAG,CAAC,GACzF,mBAAmBhB,MAAM,CAACvC,OAAO,CAACC,MAAM,CAAC6K,cAAc,CAACxI,GAAG,CAACyI,IAAI,IAC9D,GAAGA,IAAI,CAACJ,IAAI,IAAII,IAAI,CAACH,KAAK,IAAI,eAAe;AACjD,EAAEG,IAAI,CAACC,mBAAmB,GAAG,aAAa,GAAGD,IAAI,CAACC,mBAAmB,GAAG,EAAE;AAC1E,EAAED,IAAI,CAACE,SAAS,GAAG,QAAQ,GAAGF,IAAI,CAACE,SAAS,GAAG,EAAE;AACjD,CACE,CAAC,CAACV,IAAI,CAAC,IAAI,CAAC,EAAE,GAAG,EAAE;AACrB;AACA,EAAE,CAAAZ,gBAAA,GAAApH,MAAM,CAACvC,OAAO,cAAA2J,gBAAA,gBAAAC,qBAAA,GAAdD,gBAAA,CAAgB1J,MAAM,cAAA2J,qBAAA,eAAtBA,qBAAA,CAAwBsB,YAAY,IAAI3I,MAAM,CAACvC,OAAO,CAACC,MAAM,CAACiL,YAAY,CAAC3H,MAAM,GAAG,CAAC,GACrF,iBAAiBhB,MAAM,CAACvC,OAAO,CAACC,MAAM,CAACiL,YAAY,CAAC5I,GAAG,CAAC6I,WAAW,IACjE,OAAOA,WAAW,KAAK,QAAQ,GAAGA,WAAW,GAC7C,GAAGA,WAAW,CAACC,eAAe,IAAID,WAAW,CAACR,IAAI,IAAI,aAAa;AACvE,EAAEQ,WAAW,CAACF,SAAS,GAAG,QAAQ,GAAGE,WAAW,CAACF,SAAS,GAAG,EAAE;AAC/D,EAAEE,WAAW,CAACH,mBAAmB,GAAG,gBAAgB,GAAGG,WAAW,CAACH,mBAAmB,GAAG,EAAE;AAC3F,EAAEG,WAAW,CAACd,WAAW,GAAG,eAAe,GAAGc,WAAW,CAACd,WAAW,GAAG,EAAE;AAC1E,CACE,CAAC,CAACE,IAAI,CAAC,IAAI,CAAC,EAAE,GAAG,EAAE;AACrB;AACA,EAAE,CAAAV,gBAAA,GAAAtH,MAAM,CAACvC,OAAO,cAAA6J,gBAAA,gBAAAC,qBAAA,GAAdD,gBAAA,CAAgB5J,MAAM,cAAA6J,qBAAA,gBAAAC,sBAAA,GAAtBD,qBAAA,CAAwB5J,mBAAmB,cAAA6J,sBAAA,eAA3CA,sBAAA,CAA6CsB,QAAQ,GACrD,qCAAqC9I,MAAM,CAACvC,OAAO,CAACC,MAAM,CAACC,mBAAmB,CAACmL,QAAQ,EAAE,GAAG,EAAE;AAChG;AACA,gBAAgB,IAAIC,IAAI,CAAC,CAAC,CAACC,kBAAkB,CAAC,CAAC;AAC/C,OAAO,CAAClK,IAAI,CAAC,CAAC;;MAER;MACA,MAAMmK,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACzB,aAAa,CAAC,EAAE;QAAE0B,IAAI,EAAE;MAAa,CAAC,CAAC;MAC9D,MAAMC,GAAG,GAAGC,GAAG,CAACC,eAAe,CAACL,IAAI,CAAC;MACrC,MAAMM,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;MACxCF,IAAI,CAACG,IAAI,GAAGN,GAAG;MACfG,IAAI,CAACI,QAAQ,GAAG,GAAG3J,MAAM,CAAC9C,IAAI,CAAC0M,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,aAAa;MAChEJ,QAAQ,CAACpK,IAAI,CAACyK,WAAW,CAACN,IAAI,CAAC;MAC/BA,IAAI,CAACO,KAAK,CAAC,CAAC;MACZN,QAAQ,CAACpK,IAAI,CAAC2K,WAAW,CAACR,IAAI,CAAC;MAC/BF,GAAG,CAACW,eAAe,CAACZ,GAAG,CAAC;MAExB5O,KAAK,CAACiH,OAAO,CAAC,yBAAyBzB,MAAM,CAAC9C,IAAI,EAAE,CAAC;IACvD,CAAC,CAAC,OAAO8E,KAAK,EAAE;MACdJ,OAAO,CAACI,KAAK,CAAC,iBAAiB,EAAEA,KAAK,CAAC;MACvCxH,KAAK,CAACwH,KAAK,CAAC,8CAA8C,CAAC;IAC7D;EACF,CAAC;EAED,MAAMiI,UAAU,GAAIjK,MAAM,IAAK;IAC7B1D,iBAAiB,CAAC0D,MAAM,CAAC;IACzBxD,cAAc,CAAC,IAAI,CAAC;IACpBhC,KAAK,CAACkH,IAAI,CAAC,6BAA6B1B,MAAM,CAAC9C,IAAI,EAAE,CAAC;EACxD,CAAC;EAED,MAAMgN,gBAAgB,GAAGA,CAAA,KAAM;IAC7B1N,cAAc,CAAC,KAAK,CAAC;IACrBF,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;EAED,MAAM6N,gBAAgB,GAAGA,CAAC/M,KAAK,EAAEF,IAAI,KAAK;IACxCkN,MAAM,CAACC,IAAI,CAAC,UAAUjN,KAAK,0CAA0CF,IAAI,6GAA6G,EAAE,QAAQ,CAAC;IACjM1C,KAAK,CAACkH,IAAI,CAAC,mCAAmCxE,IAAI,EAAE,CAAC;EACvD,CAAC;EAED,MAAMoN,gBAAgB,GAAGA,CAACjN,KAAK,EAAEH,IAAI,KAAK;IACxC,IAAIqN,SAAS,CAACC,SAAS,CAAC1E,KAAK,CAAC,4BAA4B,CAAC,EAAE;MAC3DsE,MAAM,CAACC,IAAI,CAAC,OAAOhN,KAAK,EAAE,EAAE,QAAQ,CAAC;IACvC,CAAC,MAAM;MACLkN,SAAS,CAACE,SAAS,CAACC,SAAS,CAACrN,KAAK,CAAC,CAACsN,IAAI,CAAC,MAAM;QAC9CnQ,KAAK,CAACiH,OAAO,CAAC,wBAAwBpE,KAAK,EAAE,CAAC;MAChD,CAAC,CAAC,CAACuN,KAAK,CAAC,MAAM;QACbpQ,KAAK,CAACkH,IAAI,CAAC,UAAUrE,KAAK,EAAE,CAAC;MAC/B,CAAC,CAAC;IACJ;EACF,CAAC;EAID,MAAMwN,eAAe,GAAGA,CAAA,KAAM;IAC5B,IAAI3H,eAAe,CAAClC,MAAM,KAAK,CAAC,EAAE;MAChCxG,KAAK,CAACuE,OAAO,CAAC,sBAAsB,CAAC;MACrC;IACF;IAEA,IAAI;MACF,MAAM+L,UAAU,GAAG5H,eAAe,CAACnD,GAAG,CAACC,MAAM,KAAK;QAChD9C,IAAI,EAAE8C,MAAM,CAAC9C,IAAI;QACjBC,KAAK,EAAE6C,MAAM,CAAC7C,KAAK;QACnBC,KAAK,EAAE4C,MAAM,CAAC5C,KAAK;QACnBC,KAAK,EAAE2C,MAAM,CAAC3C,KAAK;QACnBtB,QAAQ,EAAEiE,MAAM,CAACjE,QAAQ;QACzBuB,UAAU,EAAE0C,MAAM,CAAC1C,UAAU;QAC7BxB,SAAS,EAAEkE,MAAM,CAAClE,SAAS;QAC3BE,MAAM,EAAEgE,MAAM,CAAChE,MAAM,CAACgM,IAAI,CAAC,IAAI,CAAC;QAChCzK,OAAO,EAAEyC,MAAM,CAACzC,OAAO;QACvBwN,UAAU,EAAE/K,MAAM,CAACxC;MACrB,CAAC,CAAC,CAAC;MAEH,MAAMwN,UAAU,GAAG,CACjB,iFAAiF,EACjF,GAAGF,UAAU,CAAC/K,GAAG,CAACkL,GAAG,IACnB5E,MAAM,CAACC,MAAM,CAAC2E,GAAG,CAAC,CAAClL,GAAG,CAACkG,KAAK,IAC1B,IAAIiF,MAAM,CAACjF,KAAK,CAAC,CAAC2D,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,GACvC,CAAC,CAAC5B,IAAI,CAAC,GAAG,CACZ,CAAC,CACF,CAACA,IAAI,CAAC,IAAI,CAAC;MAEZ,MAAMiB,IAAI,GAAG,IAAIC,IAAI,CAAC,CAAC8B,UAAU,CAAC,EAAE;QAAE7B,IAAI,EAAE;MAAW,CAAC,CAAC;MACzD,MAAMC,GAAG,GAAGC,GAAG,CAACC,eAAe,CAACL,IAAI,CAAC;MACrC,MAAMM,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;MACxCF,IAAI,CAACG,IAAI,GAAGN,GAAG;MACfG,IAAI,CAACI,QAAQ,GAAG,yBAAyB,IAAIZ,IAAI,CAAC,CAAC,CAACoC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM;MACrF5B,QAAQ,CAACpK,IAAI,CAACyK,WAAW,CAACN,IAAI,CAAC;MAC/BA,IAAI,CAACO,KAAK,CAAC,CAAC;MACZN,QAAQ,CAACpK,IAAI,CAAC2K,WAAW,CAACR,IAAI,CAAC;MAC/BF,GAAG,CAACW,eAAe,CAACZ,GAAG,CAAC;MAExB5O,KAAK,CAACiH,OAAO,CAAC,YAAYyB,eAAe,CAAClC,MAAM,iBAAiB,CAAC;IACpE,CAAC,CAAC,OAAOgB,KAAK,EAAE;MACdJ,OAAO,CAACI,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;MACrCxH,KAAK,CAACwH,KAAK,CAAC,6CAA6C,CAAC;IAC5D;EACF,CAAC;EAED,oBACErH,OAAA;IAAK0Q,SAAS,EAAC,aAAa;IAAAC,QAAA,gBAC1B3Q,OAAA;MAAK0Q,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1B3Q,OAAA;QAAA2Q,QAAA,EAAI;MAAc;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACvB/Q,OAAA;QAAA2Q,QAAA,EAAG;MAAkE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtE,CAAC,eAGN/Q,OAAA;MAAK0Q,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAC7B3Q,OAAA;QAAMgR,QAAQ,EAAEhN,YAAa;QAAC0M,SAAS,EAAC,aAAa;QAAAC,QAAA,eACnD3Q,OAAA;UAAK0Q,SAAS,EAAC,oBAAoB;UAAAC,QAAA,gBACjC3Q,OAAA;YAAK0Q,SAAS,EAAC,sBAAsB;YAAAC,QAAA,gBACnC3Q,OAAA,CAACjB,QAAQ;cAAC2R,SAAS,EAAC;YAAa;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACpC/Q,OAAA;cACEwO,IAAI,EAAC,MAAM;cACXyC,WAAW,EAAC,oGAAoG;cAChH3F,KAAK,EAAEjL,WAAY;cACnB6Q,QAAQ,EAAGjN,CAAC,IAAK3D,cAAc,CAAC2D,CAAC,CAACkN,MAAM,CAAC7F,KAAK,CAAE;cAChDoF,SAAS,EAAC,cAAc;cACxBU,QAAQ,EAAE7Q;YAAY;cAAAqQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACN/Q,OAAA;YAAK0Q,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7B3Q,OAAA;cACEwO,IAAI,EAAC,QAAQ;cACbkC,SAAS,EAAC,iBAAiB;cAC3BU,QAAQ,EAAE7Q,WAAY;cAAAoQ,QAAA,EAErBpQ,WAAW,gBACVP,OAAA,CAAAE,SAAA;gBAAAyQ,QAAA,gBACE3Q,OAAA;kBAAK0Q,SAAS,EAAC;gBAAiB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gBAErC;cAAA,eAAE,CAAC,gBAEH/Q,OAAA,CAAAE,SAAA;gBAAAyQ,QAAA,gBACE3Q,OAAA,CAACjB,QAAQ;kBAACsS,IAAI,EAAE;gBAAG;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,UAExB;cAAA,eAAE;YACH;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK,CAAC,eACT/Q,OAAA;cACEwO,IAAI,EAAC,QAAQ;cACbkC,SAAS,EAAC,mBAAmB;cAC7BY,OAAO,EAAE/J,aAAc;cACvB6J,QAAQ,EAAE7Q,WAAY;cAAAoQ,QAAA,EACvB;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,EAGNtQ,aAAa,CAAC4F,MAAM,GAAG,CAAC,iBACvBrG,OAAA;QAAK0Q,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7B3Q,OAAA;UAAK0Q,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5B3Q,OAAA;YAAK0Q,SAAS,EAAC,sBAAsB;YAAAC,QAAA,gBACnC3Q,OAAA,CAAChB,QAAQ;cAAC0R,SAAS,EAAC;YAAa;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACpC/Q,OAAA;cACEwO,IAAI,EAAC,MAAM;cACXyC,WAAW,EAAC,6CAA6C;cACzD3F,KAAK,EAAE3K,WAAY;cACnBuQ,QAAQ,EAAGjN,CAAC,IAAKrD,cAAc,CAACqD,CAAC,CAACkN,MAAM,CAAC7F,KAAK,CAAE;cAChDoF,SAAS,EAAC;YAAc;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACN/Q,OAAA;YAAK0Q,SAAS,EAAC,iBAAiB;YAAAC,QAAA,gBAC9B3Q,OAAA;cACEwO,IAAI,EAAC,QAAQ;cACbkC,SAAS,EAAE,mCAAmC7P,WAAW,GAAG,QAAQ,GAAG,EAAE,EAAG;cAC5EyQ,OAAO,EAAEA,CAAA,KAAMxQ,cAAc,CAAC,CAACD,WAAW,CAAE;cAAA8P,QAAA,gBAE5C3Q,OAAA,CAACN,UAAU;gBAAC2R,IAAI,EAAE;cAAG;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,oBAExB,EAAClQ,WAAW,gBAAGb,OAAA,CAACJ,WAAW;gBAACyR,IAAI,EAAE;cAAG;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAAG/Q,OAAA,CAACL,aAAa;gBAAC0R,IAAI,EAAE;cAAG;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChE,CAAC,EACRtF,gBAAgB,CAAC,CAAC,iBACjBzL,OAAA;cACEwO,IAAI,EAAC,QAAQ;cACbkC,SAAS,EAAC,+BAA+B;cACzCY,OAAO,EAAE9F,eAAgB;cAAAmF,QAAA,gBAEzB3Q,OAAA,CAACP,GAAG;gBAAC4R,IAAI,EAAE;cAAG;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,aAEnB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CACT;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAGLlQ,WAAW,iBACVb,OAAA;UAAK0Q,SAAS,EAAC,kBAAkB;UAAAC,QAAA,eAC/B3Q,OAAA;YAAK0Q,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3B3Q,OAAA;cAAK0Q,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3B3Q,OAAA;gBAAA2Q,QAAA,EAAO;cAAkB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACjC/Q,OAAA;gBAAK0Q,SAAS,EAAC,cAAc;gBAAAC,QAAA,gBAC3B3Q,OAAA;kBACEwO,IAAI,EAAC,QAAQ;kBACbyC,WAAW,EAAC,KAAK;kBACjB3F,KAAK,EAAEvK,eAAe,CAACE,aAAc;kBACrCiQ,QAAQ,EAAGjN,CAAC,IAAKmH,0BAA0B,CAAC,eAAe,EAAEnH,CAAC,CAACkN,MAAM,CAAC7F,KAAK,CAAE;kBAC7EoF,SAAS,EAAC,oBAAoB;kBAC9Ba,GAAG,EAAC;gBAAG;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR,CAAC,eACF/Q,OAAA;kBAAA2Q,QAAA,EAAM;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACf/Q,OAAA;kBACEwO,IAAI,EAAC,QAAQ;kBACbyC,WAAW,EAAC,KAAK;kBACjB3F,KAAK,EAAEvK,eAAe,CAACG,aAAc;kBACrCgQ,QAAQ,EAAGjN,CAAC,IAAKmH,0BAA0B,CAAC,eAAe,EAAEnH,CAAC,CAACkN,MAAM,CAAC7F,KAAK,CAAE;kBAC7EoF,SAAS,EAAC,oBAAoB;kBAC9Ba,GAAG,EAAC;gBAAG;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN/Q,OAAA;cAAK0Q,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3B3Q,OAAA;gBAAA2Q,QAAA,EAAO;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC9B/Q,OAAA;gBACEwO,IAAI,EAAC,MAAM;gBACXyC,WAAW,EAAC,6BAA6B;gBACzC3F,KAAK,EAAEvK,eAAe,CAACI,SAAU;gBACjC+P,QAAQ,EAAGjN,CAAC,IAAKmH,0BAA0B,CAAC,WAAW,EAAEnH,CAAC,CAACkN,MAAM,CAAC7F,KAAK,CAAE;gBACzEoF,SAAS,EAAC;cAAc;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAEN/Q,OAAA;cAAK0Q,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3B3Q,OAAA;gBAAA2Q,QAAA,EAAO;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACvB/Q,OAAA;gBACEwO,IAAI,EAAC,MAAM;gBACXyC,WAAW,EAAC,gCAAgC;gBAC5C3F,KAAK,EAAEvK,eAAe,CAACK,QAAS;gBAChC8P,QAAQ,EAAGjN,CAAC,IAAKmH,0BAA0B,CAAC,UAAU,EAAEnH,CAAC,CAACkN,MAAM,CAAC7F,KAAK,CAAE;gBACxEoF,SAAS,EAAC;cAAc;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAEN/Q,OAAA;cAAK0Q,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3B3Q,OAAA;gBAAA2Q,QAAA,EAAO;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACrB/Q,OAAA;gBACEwO,IAAI,EAAC,MAAM;gBACXyC,WAAW,EAAC,2BAA2B;gBACvC3F,KAAK,EAAEvK,eAAe,CAACM,MAAO;gBAC9B6P,QAAQ,EAAGjN,CAAC,IAAKmH,0BAA0B,CAAC,QAAQ,EAAEnH,CAAC,CAACkN,MAAM,CAAC7F,KAAK,CAAE;gBACtEoF,SAAS,EAAC;cAAc;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAEN/Q,OAAA;cAAK0Q,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3B3Q,OAAA;gBAAA2Q,QAAA,EAAO;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC1B/Q,OAAA;gBACEwO,IAAI,EAAC,MAAM;gBACXyC,WAAW,EAAC,4BAA4B;gBACxC3F,KAAK,EAAEvK,eAAe,CAACQ,WAAY;gBACnC2P,QAAQ,EAAGjN,CAAC,IAAKmH,0BAA0B,CAAC,aAAa,EAAEnH,CAAC,CAACkN,MAAM,CAAC7F,KAAK,CAAE;gBAC3EoF,SAAS,EAAC;cAAc;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAEN/Q,OAAA;cAAK0Q,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3B3Q,OAAA;gBAAA2Q,QAAA,EAAO;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC9B/Q,OAAA;gBACEwO,IAAI,EAAC,MAAM;gBACXyC,WAAW,EAAC,kBAAkB;gBAC9B3F,KAAK,EAAEvK,eAAe,CAACS,cAAe;gBACtC0P,QAAQ,EAAGjN,CAAC,IAAKmH,0BAA0B,CAAC,gBAAgB,EAAEnH,CAAC,CAACkN,MAAM,CAAC7F,KAAK,CAAE;gBAC9EoF,SAAS,EAAC;cAAc;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAEN/Q,OAAA;cAAK0Q,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3B3Q,OAAA;gBAAA2Q,QAAA,EAAO;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACtB/Q,OAAA;gBACEwO,IAAI,EAAC,MAAM;gBACXyC,WAAW,EAAC,8BAA8B;gBAC1C3F,KAAK,EAAEvK,eAAe,CAACU,OAAQ;gBAC/ByP,QAAQ,EAAGjN,CAAC,IAAKmH,0BAA0B,CAAC,SAAS,EAAEnH,CAAC,CAACkN,MAAM,CAAC7F,KAAK,CAAE;gBACvEoF,SAAS,EAAC;cAAc;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAEN/Q,OAAA;cAAK0Q,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3B3Q,OAAA;gBAAA2Q,QAAA,EAAO;cAAiB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAChC/Q,OAAA;gBACEwO,IAAI,EAAC,QAAQ;gBACbyC,WAAW,EAAC,cAAc;gBAC1B3F,KAAK,EAAEvK,eAAe,CAACO,QAAS;gBAChC4P,QAAQ,EAAGjN,CAAC,IAAKmH,0BAA0B,CAAC,UAAU,EAAEnH,CAAC,CAACkN,MAAM,CAAC7F,KAAK,CAAE;gBACxEoF,SAAS,EAAC,cAAc;gBACxBa,GAAG,EAAC,GAAG;gBACPC,GAAG,EAAC;cAAK;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,eAED/Q,OAAA;UAAK0Q,SAAS,EAAC,eAAe;UAAAC,QAAA,GAC3BpI,eAAe,CAAClC,MAAM,EAAC,MAAI,EAAC5F,aAAa,CAAC4F,MAAM,EAAC,UAClD,EAACoF,gBAAgB,CAAC,CAAC,iBAAIzL,OAAA;YAAM0Q,SAAS,EAAC,oBAAoB;YAAAC,QAAA,EAAC;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3E,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAGLtQ,aAAa,CAAC4F,MAAM,GAAG,CAAC,gBACvBrG,OAAA;MAAK0Q,SAAS,EAAC,iBAAiB;MAAAC,QAAA,gBAC9B3Q,OAAA;QAAK0Q,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7B3Q,OAAA;UAAA2Q,QAAA,EAAI;QAAc;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACvB/Q,OAAA;UAAK0Q,SAAS,EAAC,iBAAiB;UAAAC,QAAA,eAC9B3Q,OAAA;YACE0Q,SAAS,EAAC,mBAAmB;YAC7BY,OAAO,EAAEpB,eAAgB;YACzB1N,KAAK,EAAE,UAAU+F,eAAe,CAAClC,MAAM,iBAAkB;YAAAsK,QAAA,gBAEzD3Q,OAAA,CAACf,UAAU;cAACoS,IAAI,EAAE;YAAG;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBACZ,EAACxI,eAAe,CAAClC,MAAM,EAAC,GACtC;UAAA;YAAAuK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN/Q,OAAA;QAAK0Q,SAAS,EAAC,cAAc;QAAAC,QAAA,EAC1BpI,eAAe,CAACnD,GAAG,CAAEC,MAAM,iBAC1BrF,OAAA;UAAqB0Q,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1C3Q,OAAA;YAAK0Q,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5B3Q,OAAA;cAAK0Q,SAAS,EAAC,eAAe;cAAAC,QAAA,eAC5B3Q,OAAA,CAACb,MAAM;gBAACkS,IAAI,EAAE;cAAG;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjB,CAAC,eACN/Q,OAAA;cAAK0Q,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1B3Q,OAAA;gBAAI0Q,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAEtL,MAAM,CAAC9C;cAAI;gBAAAqO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC9C/Q,OAAA;gBAAG0Q,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAEtL,MAAM,CAAC7C;cAAK;gBAAAoO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN/Q,OAAA;YAAK0Q,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7B3Q,OAAA;cAAK0Q,SAAS,EAAC,2BAA2B;cAAAC,QAAA,gBACxC3Q,OAAA,CAACZ,QAAQ;gBAACiS,IAAI,EAAE;cAAG;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACtB/Q,OAAA;gBAAA2Q,QAAA,EAAOtL,MAAM,CAACjE;cAAQ;gBAAAwP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3B,CAAC,eACN/Q,OAAA;cACE0Q,SAAS,EAAC,kCAAkC;cAC5CY,OAAO,EAAEA,CAAA,KAAM9B,gBAAgB,CAACnK,MAAM,CAAC5C,KAAK,EAAE4C,MAAM,CAAC9C,IAAI,CAAE;cAC3DC,KAAK,EAAE,iBAAiB6C,MAAM,CAAC9C,IAAI,EAAG;cAAAoO,QAAA,gBAEtC3Q,OAAA,CAACX,MAAM;gBAACgS,IAAI,EAAE;cAAG;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACpB/Q,OAAA;gBAAA2Q,QAAA,EAAOtL,MAAM,CAAC5C;cAAK;gBAAAmO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC3B/Q,OAAA,CAACT,cAAc;gBAAC8R,IAAI,EAAE,EAAG;gBAACX,SAAS,EAAC;cAAe;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnD,CAAC,eACN/Q,OAAA;cACE0Q,SAAS,EAAC,kCAAkC;cAC5CY,OAAO,EAAEA,CAAA,KAAM3B,gBAAgB,CAACtK,MAAM,CAAC3C,KAAK,EAAE2C,MAAM,CAAC9C,IAAI,CAAE;cAC3DC,KAAK,EAAE,QAAQ6C,MAAM,CAAC9C,IAAI,EAAG;cAAAoO,QAAA,gBAE7B3Q,OAAA,CAACV,OAAO;gBAAC+R,IAAI,EAAE;cAAG;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACrB/Q,OAAA;gBAAA2Q,QAAA,EAAOtL,MAAM,CAAC3C;cAAK;gBAAAkO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC3B/Q,OAAA,CAACT,cAAc;gBAAC8R,IAAI,EAAE,EAAG;gBAACX,SAAS,EAAC;cAAe;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN/Q,OAAA;YAAK0Q,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7B3Q,OAAA;cAAK0Q,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1B3Q,OAAA;gBAAK0Q,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,gBAC9B3Q,OAAA;kBAAA2Q,QAAA,EAAI;gBAAU;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACnB/Q,OAAA;kBAAA2Q,QAAA,EAAItL,MAAM,CAAC1C;gBAAU;kBAAAiO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvB,CAAC,eACN/Q,OAAA;gBAAK0Q,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,gBAC9B3Q,OAAA;kBAAA2Q,QAAA,EAAI;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAClB/Q,OAAA;kBAAA2Q,QAAA,EAAItL,MAAM,CAAClE;gBAAS;kBAAAyP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACN/Q,OAAA;cAAK0Q,SAAS,EAAC,iBAAiB;cAAAC,QAAA,gBAC9B3Q,OAAA;gBAAA2Q,QAAA,EAAI;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACf/Q,OAAA;gBAAK0Q,SAAS,EAAC,aAAa;gBAAAC,QAAA,EACzBtL,MAAM,CAAChE,MAAM,CAAC+D,GAAG,CAAC,CAACwB,KAAK,EAAE6K,KAAK,kBAC9BzR,OAAA;kBAAkB0Q,SAAS,EAAC,WAAW;kBAAAC,QAAA,EAAE/J;gBAAK,GAAnC6K,KAAK;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAqC,CACtD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN/Q,OAAA;YAAK0Q,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7B3Q,OAAA;cACE0Q,SAAS,EAAC,mBAAmB;cAC7BY,OAAO,EAAEA,CAAA,KAAMhC,UAAU,CAACjK,MAAM,CAAE;cAAAsL,QAAA,gBAElC3Q,OAAA,CAACd,KAAK;gBAACmS,IAAI,EAAE;cAAG;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAErB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACT/Q,OAAA;cACE0Q,SAAS,EAAC,iBAAiB;cAC3BY,OAAO,EAAEA,CAAA,KAAM1F,cAAc,CAACvG,MAAM,CAAE;cAAAsL,QAAA,gBAEtC3Q,OAAA,CAACf,UAAU;gBAACoS,IAAI,EAAE;cAAG;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,YAE1B;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA,GAxEE1L,MAAM,CAAC/C,EAAE;UAAAsO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAyEd,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,EAGL/O,UAAU,GAAG,CAAC,iBACbhC,OAAA;QAAK0Q,SAAS,EAAC,oBAAoB;QAAAC,QAAA,gBACjC3Q,OAAA;UAAK0Q,SAAS,EAAC,iBAAiB;UAAAC,QAAA,GAAC,UACvB,EAAE,CAAC7O,WAAW,GAAG,CAAC,IAAIM,cAAc,GAAI,CAAC,EAAC,MAAI,EAAC4D,IAAI,CAACuL,GAAG,CAACzP,WAAW,GAAGM,cAAc,EAAEF,UAAU,CAAC,EAAC,MAAI,EAACA,UAAU,EAAC,UAC5H;QAAA;UAAA0O,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACN/Q,OAAA;UAAK0Q,SAAS,EAAC,qBAAqB;UAAAC,QAAA,gBAClC3Q,OAAA;YACE0Q,SAAS,EAAC,kCAAkC;YAC5CY,OAAO,EAAEA,CAAA,KAAMhJ,gBAAgB,CAACxG,WAAW,GAAG,CAAC,CAAE;YACjDsP,QAAQ,EAAEtP,WAAW,KAAK,CAAE;YAAA6O,QAAA,EAC7B;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAET/Q,OAAA;YAAK0Q,SAAS,EAAC,cAAc;YAAAC,QAAA,EAC1BzL,KAAK,CAACwM,IAAI,CAAC;cAAErL,MAAM,EAAEL,IAAI,CAACuL,GAAG,CAAC,CAAC,EAAEvP,UAAU;YAAE,CAAC,EAAE,CAAC2P,CAAC,EAAEC,CAAC,KAAK;cACzD,IAAIC,OAAO;cACX,IAAI7P,UAAU,IAAI,CAAC,EAAE;gBACnB6P,OAAO,GAAGD,CAAC,GAAG,CAAC;cACjB,CAAC,MAAM,IAAI9P,WAAW,IAAI,CAAC,EAAE;gBAC3B+P,OAAO,GAAGD,CAAC,GAAG,CAAC;cACjB,CAAC,MAAM,IAAI9P,WAAW,IAAIE,UAAU,GAAG,CAAC,EAAE;gBACxC6P,OAAO,GAAG7P,UAAU,GAAG,CAAC,GAAG4P,CAAC;cAC9B,CAAC,MAAM;gBACLC,OAAO,GAAG/P,WAAW,GAAG,CAAC,GAAG8P,CAAC;cAC/B;cAEA,oBACE5R,OAAA;gBAEE0Q,SAAS,EAAE,sBAAsB5O,WAAW,KAAK+P,OAAO,GAAG,aAAa,GAAG,eAAe,EAAG;gBAC7FP,OAAO,EAAEA,CAAA,KAAMhJ,gBAAgB,CAACuJ,OAAO,CAAE;gBAAAlB,QAAA,EAExCkB;cAAO,GAJHA,OAAO;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAKN,CAAC;YAEb,CAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAEN/Q,OAAA;YACE0Q,SAAS,EAAC,kCAAkC;YAC5CY,OAAO,EAAEA,CAAA,KAAMhJ,gBAAgB,CAACxG,WAAW,GAAG,CAAC,CAAE;YACjDsP,QAAQ,EAAEtP,WAAW,KAAKE,UAAW;YAAA2O,QAAA,EACtC;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,GACJ,CAACxQ,WAAW,gBACdP,OAAA;MAAK0Q,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1B3Q,OAAA;QAAK0Q,SAAS,EAAC,YAAY;QAAAC,QAAA,eACzB3Q,OAAA,CAACjB,QAAQ;UAACsS,IAAI,EAAE;QAAG;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnB,CAAC,eACN/Q,OAAA;QAAA2Q,QAAA,EAAI;MAAgB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACzB/Q,OAAA;QAAA2Q,QAAA,EAAG;MAAyH;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eAChI/Q,OAAA;QAAK0Q,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9B3Q,OAAA;UAAA2Q,QAAA,EAAI;QAAkB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC3B/Q,OAAA;UAAA2Q,QAAA,gBACE3Q,OAAA;YAAA2Q,QAAA,EAAI;UAAwB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACjC/Q,OAAA;YAAA2Q,QAAA,EAAI;UAAwC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACjD/Q,OAAA;YAAA2Q,QAAA,EAAI;UAA8B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,GACJ,IAAI,eAGR/Q,OAAA,CAACF,WAAW;MACVuF,MAAM,EAAE3D,cAAe;MACvBoQ,MAAM,EAAElQ,WAAY;MACpBmQ,OAAO,EAAExC;IAAiB;MAAAqB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3B,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAAC3Q,EAAA,CAj+BID,aAAa;AAAA6R,EAAA,GAAb7R,aAAa;AAm+BnB,eAAeA,aAAa;AAAC,IAAA6R,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}