import React, { useState, useEffect } from 'react';
import { FiActivity, FiUpload, FiSearch, FiFileText, FiUsers, FiClock } from 'react-icons/fi';
import './ActivityLog.css';

const ActivityLog = () => {
  const [activities, setActivities] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Simulate loading data
    setTimeout(() => {
      setActivities([
        { id: 1, type: 'upload', action: 'Resume uploaded', detail: 'john_doe_resume.pdf', time: '2 minutes ago', user: 'Admin' },
        { id: 2, type: 'search', action: 'Search completed', detail: 'Python developers (15 results)', time: '5 minutes ago', user: 'Admin' },
        { id: 3, type: 'process', action: 'Resume processed', detail: 'jane_smith_cv.pdf', time: '10 minutes ago', user: 'System' },
        { id: 4, type: 'bulk', action: 'Bulk upload completed', detail: '5 files processed', time: '1 hour ago', user: 'Admin' },
        { id: 5, type: 'search', action: 'Search completed', detail: 'UI/UX designers (8 results)', time: '2 hours ago', user: 'Admin' }
      ]);
      setLoading(false);
    }, 1000);
  }, []);

  const getActivityIcon = (type) => {
    switch (type) {
      case 'upload': return <FiUpload className="activity-icon upload" />;
      case 'search': return <FiSearch className="activity-icon search" />;
      case 'process': return <FiFileText className="activity-icon process" />;
      case 'bulk': return <FiUsers className="activity-icon bulk" />;
      default: return <FiActivity className="activity-icon default" />;
    }
  };

  if (loading) {
    return (
      <div className="activity-log-page">
        <div className="loading-container">
          <div className="loading-spinner large" />
          <p>Loading activity log...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="activity-log-page">
      <div className="page-header">
        <h1>Activity Log</h1>
        <p>Track all system activities and user actions</p>
      </div>

      <div className="activity-container">
        <div className="activity-stats">
          <div className="stat-item">
            <FiClock size={16} />
            <span>{activities.length} activities today</span>
          </div>
        </div>

        <div className="activity-list">
          {activities.map(activity => (
            <div key={activity.id} className="activity-item">
              <div className="activity-icon-wrapper">
                {getActivityIcon(activity.type)}
              </div>
              <div className="activity-content">
                <div className="activity-main">
                  <span className="activity-action">{activity.action}</span>
                  <span className="activity-detail">: {activity.detail}</span>
                </div>
                <div className="activity-meta">
                  <span className="activity-user">by {activity.user}</span>
                  <span className="activity-time">{activity.time}</span>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default ActivityLog;
