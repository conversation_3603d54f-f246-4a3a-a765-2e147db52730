.sidebar {
  position: fixed;
  left: 0;
  top: 0;
  height: 100vh;
  width: 280px;
  background: linear-gradient(180deg, #ffffff 0%, #f8fafc 100%);
  border-right: 1px solid #e2e8f0;
  display: flex;
  flex-direction: column;
  transition: all 0.3s ease;
  z-index: 1000;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.sidebar.collapsed {
  width: 80px;
}

/* Header */
.sidebar-header {
  padding: 20px;
  border-bottom: 1px solid #e2e8f0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-height: 80px;
}

.logo-container {
  display: flex;
  align-items: center;
  flex: 1;
}

.logo-text {
  display: flex;
  align-items: center;
  gap: 12px;
}

.logo-collapsed {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
}

.logo-icon {
  font-size: 28px;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.logo-title {
  font-size: 20px;
  font-weight: 700;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.toggle-btn {
  background: none;
  border: none;
  color: #6b7280;
  cursor: pointer;
  padding: 8px;
  border-radius: 6px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.toggle-btn:hover {
  background-color: #f1f5f9;
  color: #374151;
}

/* Navigation */
.sidebar-nav {
  flex: 1;
  padding: 20px 0;
  overflow-y: auto;
}

.nav-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.nav-item {
  margin-bottom: 4px;
}

.nav-link {
  display: flex;
  align-items: center;
  padding: 12px 20px;
  color: #6b7280;
  text-decoration: none;
  transition: all 0.2s ease;
  position: relative;
  border-radius: 0;
  margin: 0 12px;
  border-radius: 8px;
}

.sidebar.collapsed .nav-link {
  justify-content: center;
  margin: 0 16px;
  padding: 12px;
}

.nav-link:hover {
  background-color: #f1f5f9;
  color: #374151;
}

.nav-link.active {
  background-color: #eff6ff;
  color: #1d4ed8;
  font-weight: 500;
}

.nav-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 20px;
  transition: color 0.2s ease;
}

.nav-label {
  margin-left: 12px;
  font-size: 14px;
  font-weight: 500;
  white-space: nowrap;
}

.active-indicator {
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 3px;
  height: 20px;
  border-radius: 2px;
  background-color: #3b82f6;
}

.sidebar.collapsed .active-indicator {
  display: none;
}

/* Footer */
.sidebar-footer {
  padding: 20px;
  border-top: 1px solid #e2e8f0;
  background: linear-gradient(180deg, #f8fafc 0%, #f1f5f9 100%);
}

.logout-btn {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 12px 0;
  background: none;
  border: none;
  color: #ef4444;
  cursor: pointer;
  border-radius: 8px;
  transition: all 0.2s ease;
  font-size: 14px;
  font-weight: 500;
}

.sidebar.collapsed .logout-btn {
  justify-content: center;
}

.logout-btn:hover {
  background-color: #fef2f2;
  color: #dc2626;
}

.logout-btn .nav-label {
  color: inherit;
}

.sidebar-info {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #e2e8f0;
}

.version-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.version-text {
  font-size: 12px;
  font-weight: 600;
  color: #374151;
}

.powered-by {
  font-size: 11px;
  color: #6b7280;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .sidebar {
    width: 80px;
  }
  
  .sidebar .nav-link {
    justify-content: center;
    margin: 0 16px;
    padding: 12px;
  }
  
  .sidebar .nav-label {
    display: none;
  }
  
  .sidebar .active-indicator {
    display: none;
  }
  
  .sidebar .sidebar-info {
    display: none;
  }
  
  .sidebar .logout-btn {
    justify-content: center;
  }
  
  .sidebar .logout-btn .nav-label {
    display: none;
  }
}

@media (max-width: 768px) {
  .sidebar {
    transform: translateX(-100%);
    width: 280px;
  }
  
  .sidebar.collapsed {
    transform: translateX(0);
    width: 80px;
  }
}

/* Scrollbar for navigation */
.sidebar-nav::-webkit-scrollbar {
  width: 4px;
}

.sidebar-nav::-webkit-scrollbar-track {
  background: transparent;
}

.sidebar-nav::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 2px;
}

.sidebar-nav::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Animation for nav items */
.nav-item {
  animation: slideInLeft 0.3s ease-out;
  animation-fill-mode: both;
}

.nav-item:nth-child(1) { animation-delay: 0.1s; }
.nav-item:nth-child(2) { animation-delay: 0.15s; }
.nav-item:nth-child(3) { animation-delay: 0.2s; }
.nav-item:nth-child(4) { animation-delay: 0.25s; }
.nav-item:nth-child(5) { animation-delay: 0.3s; }
.nav-item:nth-child(6) { animation-delay: 0.35s; }
.nav-item:nth-child(7) { animation-delay: 0.4s; }

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}
