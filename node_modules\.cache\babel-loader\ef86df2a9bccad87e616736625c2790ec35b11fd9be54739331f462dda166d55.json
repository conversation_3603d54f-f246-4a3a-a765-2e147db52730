{"ast": null, "code": "var _jsxFileName = \"D:\\\\Projects\\\\SQL-Agent-Task-Submission-main\\\\SQL-Agent-Task-Submission-main\\\\src\\\\pages\\\\SearchResumes.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { FiSearch, FiFilter, FiDownload, FiEye, FiUser, FiMapPin, FiMail, FiPhone, FiExternalLink, FiStar, FiX, FiSettings, FiChevronDown, FiChevronUp } from 'react-icons/fi';\nimport { toast } from 'react-toastify';\nimport ResumeModal from '../components/ResumeModal';\nimport './SearchResumes.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst SearchResumes = () => {\n  _s();\n  const [searchQuery, setSearchQuery] = useState('');\n  const [isSearching, setIsSearching] = useState(false);\n  const [searchResults, setSearchResults] = useState([]);\n  const [filterQuery, setFilterQuery] = useState('');\n  const [showFilters, setShowFilters] = useState(false);\n  const [advancedFilters, setAdvancedFilters] = useState({\n    minExperience: '',\n    maxExperience: '',\n    education: '',\n    location: '',\n    skills: '',\n    minMarks: '',\n    institution: '',\n    graduationYear: '',\n    company: ''\n  });\n  const [selectedResume, setSelectedResume] = useState(null);\n  const [isModalOpen, setIsModalOpen] = useState(false);\n  const [currentPage, setCurrentPage] = useState(1);\n  const [totalPages, setTotalPages] = useState(1);\n  const [totalCount, setTotalCount] = useState(0);\n  const resultsPerPage = 20;\n\n  // Mock data for demonstration\n  const mockResults = [{\n    id: 1,\n    name: 'John Doe',\n    title: 'Senior Software Engineer',\n    email: '<EMAIL>',\n    phone: '+****************',\n    location: 'San Francisco, CA',\n    experience: '5+ years',\n    skills: ['Python', 'React', 'Node.js', 'AWS'],\n    education: 'MS Computer Science',\n    summary: 'Experienced software engineer with expertise in full-stack development...',\n    score: 95\n  }, {\n    id: 2,\n    name: 'Jane Smith',\n    title: 'Frontend Developer',\n    email: '<EMAIL>',\n    phone: '+****************',\n    location: 'New York, NY',\n    experience: '3+ years',\n    skills: ['React', 'TypeScript', 'CSS', 'JavaScript'],\n    education: 'BS Computer Science',\n    summary: 'Creative frontend developer passionate about user experience...',\n    score: 88\n  }, {\n    id: 3,\n    name: 'Mike Johnson',\n    title: 'DevOps Engineer',\n    email: '<EMAIL>',\n    phone: '+****************',\n    location: 'Austin, TX',\n    experience: '4+ years',\n    skills: ['Docker', 'Kubernetes', 'AWS', 'Python'],\n    education: 'BS Information Technology',\n    summary: 'DevOps engineer specializing in cloud infrastructure and automation...',\n    score: 92\n  }];\n  const handleSearch = async e => {\n    e.preventDefault();\n    if (!searchQuery.trim()) {\n      toast.warning('Please enter a search query');\n      return;\n    }\n    setIsSearching(true);\n    try {\n      // Call the actual search API\n      const response = await fetch('/api/search', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          query: searchQuery\n        })\n      });\n      const data = await response.json();\n      if (response.ok) {\n        // Handle both real API results and fallback mock data\n        let transformedResults = [];\n        if (data.results && Array.isArray(data.results)) {\n          // Transform the resume data to match our display format\n          transformedResults = data.results.map(resume => {\n            var _resumeData$PersonalI, _resumeData$PersonalI2, _resumeData$PersonalI3, _resumeData$PersonalI4, _resumeData$PersonalI5, _resumeData$WorkExper, _resumeData$Education, _resumeData$Education2;\n            // Handle the nested Resume structure from MongoDB\n            const resumeData = resume.Resume || resume;\n            return {\n              id: resume._id || Math.random().toString(36).substr(2, 9),\n              name: ((_resumeData$PersonalI = resumeData.PersonalInformation) === null || _resumeData$PersonalI === void 0 ? void 0 : _resumeData$PersonalI.FullName) || resumeData.name || 'Unknown',\n              title: ((_resumeData$PersonalI2 = resumeData.PersonalInformation) === null || _resumeData$PersonalI2 === void 0 ? void 0 : _resumeData$PersonalI2.Designation) || resumeData.title || 'No title',\n              email: ((_resumeData$PersonalI3 = resumeData.PersonalInformation) === null || _resumeData$PersonalI3 === void 0 ? void 0 : _resumeData$PersonalI3.Email) || resumeData.email || 'No email',\n              phone: ((_resumeData$PersonalI4 = resumeData.PersonalInformation) === null || _resumeData$PersonalI4 === void 0 ? void 0 : _resumeData$PersonalI4.ContactNumber) || resumeData.phone || 'No phone',\n              location: ((_resumeData$PersonalI5 = resumeData.PersonalInformation) === null || _resumeData$PersonalI5 === void 0 ? void 0 : _resumeData$PersonalI5.Address) || resumeData.location || 'No location',\n              experience: resumeData.TotalWorkExperienceInYears ? `${resumeData.TotalWorkExperienceInYears} years` : (_resumeData$WorkExper = resumeData.WorkExperience) !== null && _resumeData$WorkExper !== void 0 && _resumeData$WorkExper.length ? `${resumeData.WorkExperience.length}+ positions` : resumeData.experience || 'No experience data',\n              skills: resumeData.Skills || resumeData.skills || [],\n              education: (_resumeData$Education = resumeData.Education) !== null && _resumeData$Education !== void 0 && _resumeData$Education.length ? ((_resumeData$Education2 = resumeData.Education[0]) === null || _resumeData$Education2 === void 0 ? void 0 : _resumeData$Education2.Degree) || 'Education available' : resumeData.education || 'No education data',\n              summary: resumeData.Objective || resumeData.Summary || resumeData.summary || 'No summary available',\n              score: 95,\n              // Default score\n              rawData: resume // Keep original data for reference\n            };\n          });\n        } else {\n          // Fallback to mock data if API doesn't return results\n          transformedResults = mockResults.filter(resume => resume.name.toLowerCase().includes(searchQuery.toLowerCase()) || resume.title.toLowerCase().includes(searchQuery.toLowerCase()) || resume.skills.some(skill => skill.toLowerCase().includes(searchQuery.toLowerCase())));\n        }\n        setSearchResults(transformedResults);\n        if (data.api_status === 'success') {\n          toast.success(`Found ${transformedResults.length} matching resumes using AI search`);\n        } else if (data.api_status === 'fallback') {\n          toast.info(`Found ${transformedResults.length} matching resumes using fallback search`);\n        } else {\n          toast.success(`Found ${transformedResults.length} matching resumes`);\n        }\n\n        // Show GPT response if available\n        if (data.gpt_response && data.gpt_response !== 'Data received successfully.') {\n          console.log('GPT Response:', data.gpt_response);\n          console.log('MongoDB Query:', data.mongo_query);\n        }\n      } else {\n        throw new Error(data.error || 'Search failed');\n      }\n    } catch (error) {\n      console.error('Search error:', error);\n\n      // Fallback to mock data on network error\n      const fallbackResults = mockResults.filter(resume => resume.name.toLowerCase().includes(searchQuery.toLowerCase()) || resume.title.toLowerCase().includes(searchQuery.toLowerCase()) || resume.skills.some(skill => skill.toLowerCase().includes(searchQuery.toLowerCase())));\n      setSearchResults(fallbackResults);\n      toast.warning(`Using demo data - Found ${fallbackResults.length} matching resumes`);\n    } finally {\n      setIsSearching(false);\n    }\n  };\n  const handleShowAll = async (page = 1) => {\n    setIsSearching(true);\n    try {\n      // Call the actual API to get paginated resumes\n      const response = await fetch(`/api/resumes?page=${page}&limit=${resultsPerPage}`);\n      const data = await response.json();\n      if (response.ok) {\n        let transformedResults = [];\n        if (data.resumes && Array.isArray(data.resumes)) {\n          // Transform the resume data to match our display format\n          transformedResults = data.resumes.map(resume => {\n            var _resumeData$PersonalI6, _resumeData$PersonalI7, _resumeData$PersonalI8, _resumeData$PersonalI9, _resumeData$PersonalI0, _resumeData$WorkExper2, _resumeData$Education3, _resumeData$Education4;\n            // Handle the nested Resume structure from MongoDB\n            const resumeData = resume.Resume || resume;\n            return {\n              id: resume._id || Math.random().toString(36).substr(2, 9),\n              name: ((_resumeData$PersonalI6 = resumeData.PersonalInformation) === null || _resumeData$PersonalI6 === void 0 ? void 0 : _resumeData$PersonalI6.FullName) || resumeData.name || 'Unknown',\n              title: ((_resumeData$PersonalI7 = resumeData.PersonalInformation) === null || _resumeData$PersonalI7 === void 0 ? void 0 : _resumeData$PersonalI7.Designation) || resumeData.title || 'No title',\n              email: ((_resumeData$PersonalI8 = resumeData.PersonalInformation) === null || _resumeData$PersonalI8 === void 0 ? void 0 : _resumeData$PersonalI8.Email) || resumeData.email || 'No email',\n              phone: ((_resumeData$PersonalI9 = resumeData.PersonalInformation) === null || _resumeData$PersonalI9 === void 0 ? void 0 : _resumeData$PersonalI9.ContactNumber) || resumeData.phone || 'No phone',\n              location: ((_resumeData$PersonalI0 = resumeData.PersonalInformation) === null || _resumeData$PersonalI0 === void 0 ? void 0 : _resumeData$PersonalI0.Address) || resumeData.location || 'No location',\n              experience: resumeData.TotalWorkExperienceInYears ? `${resumeData.TotalWorkExperienceInYears} years` : (_resumeData$WorkExper2 = resumeData.WorkExperience) !== null && _resumeData$WorkExper2 !== void 0 && _resumeData$WorkExper2.length ? `${resumeData.WorkExperience.length}+ positions` : resumeData.experience || 'No experience data',\n              skills: resumeData.Skills || resumeData.skills || [],\n              education: (_resumeData$Education3 = resumeData.Education) !== null && _resumeData$Education3 !== void 0 && _resumeData$Education3.length ? ((_resumeData$Education4 = resumeData.Education[0]) === null || _resumeData$Education4 === void 0 ? void 0 : _resumeData$Education4.Degree) || 'Education available' : resumeData.education || 'No education data',\n              summary: resumeData.Objective || resumeData.Summary || resumeData.summary || 'No summary available',\n              score: 90,\n              // Default score for show all\n              rawData: resume // Keep original data for reference\n            };\n          });\n        } else {\n          // Fallback to mock data if API doesn't return proper results\n          transformedResults = mockResults;\n        }\n        setSearchResults(transformedResults);\n        setCurrentPage(page);\n        setTotalCount(data.count || data.total || transformedResults.length);\n        setTotalPages(Math.ceil((data.count || data.total || transformedResults.length) / resultsPerPage));\n        setSearchQuery('');\n        if (data.source === 'database') {\n          toast.success(`Showing page ${page} of ${Math.ceil((data.count || data.total || transformedResults.length) / resultsPerPage)} (${data.count || data.total} total resumes)`);\n        } else {\n          toast.info(`Showing ${transformedResults.length} resumes`);\n        }\n      } else {\n        throw new Error(data.error || 'Failed to load resumes');\n      }\n    } catch (error) {\n      console.error('Load resumes error:', error);\n\n      // Fallback to mock data on network error\n      setSearchResults(mockResults);\n      setCurrentPage(1);\n      setTotalCount(mockResults.length);\n      setTotalPages(Math.ceil(mockResults.length / resultsPerPage));\n      setSearchQuery('');\n      toast.warning(`Using demo data - Showing ${mockResults.length} sample resumes`);\n    } finally {\n      setIsSearching(false);\n    }\n  };\n  const handlePageChange = page => {\n    if (page >= 1 && page <= totalPages) {\n      handleShowAll(page);\n    }\n  };\n  const filteredResults = searchResults.filter(result => {\n    // Basic text filter\n    if (filterQuery) {\n      const matchesText = result.name.toLowerCase().includes(filterQuery.toLowerCase()) || result.title.toLowerCase().includes(filterQuery.toLowerCase()) || result.location.toLowerCase().includes(filterQuery.toLowerCase());\n      if (!matchesText) return false;\n    }\n\n    // Advanced filters\n    const filters = advancedFilters;\n\n    // Experience filter\n    if (filters.minExperience || filters.maxExperience) {\n      var _result$rawData, _result$rawData$Resum;\n      const experienceYears = ((_result$rawData = result.rawData) === null || _result$rawData === void 0 ? void 0 : (_result$rawData$Resum = _result$rawData.Resume) === null || _result$rawData$Resum === void 0 ? void 0 : _result$rawData$Resum.TotalWorkExperienceInYears) || extractExperienceYears(result.experience) || 0;\n      if (filters.minExperience && experienceYears < parseInt(filters.minExperience)) return false;\n      if (filters.maxExperience && experienceYears > parseInt(filters.maxExperience)) return false;\n    }\n\n    // Education filter\n    if (filters.education) {\n      var _result$rawData2, _result$rawData2$Resu, _result$rawData2$Resu2;\n      const hasEducation = ((_result$rawData2 = result.rawData) === null || _result$rawData2 === void 0 ? void 0 : (_result$rawData2$Resu = _result$rawData2.Resume) === null || _result$rawData2$Resu === void 0 ? void 0 : (_result$rawData2$Resu2 = _result$rawData2$Resu.Education) === null || _result$rawData2$Resu2 === void 0 ? void 0 : _result$rawData2$Resu2.some(edu => (edu.Degree || '').toLowerCase().includes(filters.education.toLowerCase()))) || (result.education || '').toLowerCase().includes(filters.education.toLowerCase());\n      if (!hasEducation) return false;\n    }\n\n    // Location filter\n    if (filters.location) {\n      const matchesLocation = (result.location || '').toLowerCase().includes(filters.location.toLowerCase());\n      if (!matchesLocation) return false;\n    }\n\n    // Skills filter\n    if (filters.skills) {\n      var _result$skills, _result$rawData3, _result$rawData3$Resu;\n      const skillsText = filters.skills.toLowerCase();\n      const hasSkill = ((_result$skills = result.skills) === null || _result$skills === void 0 ? void 0 : _result$skills.some(skill => skill.toLowerCase().includes(skillsText))) || (((_result$rawData3 = result.rawData) === null || _result$rawData3 === void 0 ? void 0 : (_result$rawData3$Resu = _result$rawData3.Resume) === null || _result$rawData3$Resu === void 0 ? void 0 : _result$rawData3$Resu.Skills) || []).some(skill => skill.toLowerCase().includes(skillsText));\n      if (!hasSkill) return false;\n    }\n\n    // Institution filter\n    if (filters.institution) {\n      var _result$rawData4, _result$rawData4$Resu, _result$rawData4$Resu2;\n      const hasInstitution = (_result$rawData4 = result.rawData) === null || _result$rawData4 === void 0 ? void 0 : (_result$rawData4$Resu = _result$rawData4.Resume) === null || _result$rawData4$Resu === void 0 ? void 0 : (_result$rawData4$Resu2 = _result$rawData4$Resu.Education) === null || _result$rawData4$Resu2 === void 0 ? void 0 : _result$rawData4$Resu2.some(edu => (edu.Institution || edu.School || '').toLowerCase().includes(filters.institution.toLowerCase()));\n      if (!hasInstitution) return false;\n    }\n\n    // Graduation year filter\n    if (filters.graduationYear) {\n      var _result$rawData5, _result$rawData5$Resu, _result$rawData5$Resu2;\n      const hasGradYear = (_result$rawData5 = result.rawData) === null || _result$rawData5 === void 0 ? void 0 : (_result$rawData5$Resu = _result$rawData5.Resume) === null || _result$rawData5$Resu === void 0 ? void 0 : (_result$rawData5$Resu2 = _result$rawData5$Resu.Education) === null || _result$rawData5$Resu2 === void 0 ? void 0 : _result$rawData5$Resu2.some(edu => (edu.GraduationYear || edu.Year || '').toString().includes(filters.graduationYear));\n      if (!hasGradYear) return false;\n    }\n\n    // Company filter\n    if (filters.company) {\n      var _result$rawData6, _result$rawData6$Resu, _result$rawData6$Resu2;\n      const hasCompany = (_result$rawData6 = result.rawData) === null || _result$rawData6 === void 0 ? void 0 : (_result$rawData6$Resu = _result$rawData6.Resume) === null || _result$rawData6$Resu === void 0 ? void 0 : (_result$rawData6$Resu2 = _result$rawData6$Resu.WorkExperience) === null || _result$rawData6$Resu2 === void 0 ? void 0 : _result$rawData6$Resu2.some(exp => (exp.Company || exp.CompanyName || exp.Organization || '').toLowerCase().includes(filters.company.toLowerCase()));\n      if (!hasCompany) return false;\n    }\n\n    // Minimum marks filter\n    if (filters.minMarks) {\n      var _result$rawData7, _result$rawData7$Resu, _result$rawData7$Resu2;\n      const hasMinMarks = (_result$rawData7 = result.rawData) === null || _result$rawData7 === void 0 ? void 0 : (_result$rawData7$Resu = _result$rawData7.Resume) === null || _result$rawData7$Resu === void 0 ? void 0 : (_result$rawData7$Resu2 = _result$rawData7$Resu.Education) === null || _result$rawData7$Resu2 === void 0 ? void 0 : _result$rawData7$Resu2.some(edu => {\n        const marks = parseFloat(edu['GPA/Marks/%']) || 0;\n        return marks >= parseFloat(filters.minMarks);\n      });\n      if (!hasMinMarks) return false;\n    }\n    return true;\n  });\n\n  // Helper function to extract experience years from text\n  const extractExperienceYears = experienceText => {\n    if (!experienceText) return 0;\n    const match = experienceText.match(/(\\d+)\\+?\\s*years?/i);\n    return match ? parseInt(match[1]) : 0;\n  };\n  const handleAdvancedFilterChange = (filterName, value) => {\n    setAdvancedFilters(prev => ({\n      ...prev,\n      [filterName]: value\n    }));\n  };\n  const clearAllFilters = () => {\n    setFilterQuery('');\n    setAdvancedFilters({\n      minExperience: '',\n      maxExperience: '',\n      education: '',\n      location: '',\n      skills: '',\n      minMarks: '',\n      institution: '',\n      graduationYear: '',\n      company: ''\n    });\n  };\n  const hasActiveFilters = () => {\n    return filterQuery || Object.values(advancedFilters).some(value => value !== '');\n  };\n  const handleDownload = resume => {\n    try {\n      var _resume$rawData, _resume$rawData$Resum, _resume$rawData2, _resume$rawData2$Resu, _resume$rawData3, _resume$rawData3$Resu, _resume$rawData4, _resume$rawData4$Resu, _resume$rawData5, _resume$rawData5$Resu, _resume$rawData6, _resume$rawData6$Resu, _resume$rawData7, _resume$rawData7$Resu, _resume$rawData8, _resume$rawData8$Resu, _resume$rawData8$Resu2;\n      // Create a comprehensive text-based resume content\n      const resumeContent = `\n${resume.name}\n${resume.title}\n${resume.email} | ${resume.phone} | ${resume.location}\n\nPROFESSIONAL SUMMARY\n${resume.summary}\n\nWORK EXPERIENCE\n${(_resume$rawData = resume.rawData) !== null && _resume$rawData !== void 0 && (_resume$rawData$Resum = _resume$rawData.Resume) !== null && _resume$rawData$Resum !== void 0 && _resume$rawData$Resum.WorkExperience ? resume.rawData.Resume.WorkExperience.map(exp => `${exp.JobTitle || exp.Position} at ${exp.Company || exp.Organization}\nDuration: ${exp.Duration || exp.Years || 'Not specified'}\n${exp.Description ? 'Description: ' + exp.Description : ''}\n${exp.Responsibilities ? 'Responsibilities: ' + exp.Responsibilities : ''}\n`).join('\\n') : resume.experience}\n\n${(_resume$rawData2 = resume.rawData) !== null && _resume$rawData2 !== void 0 && (_resume$rawData2$Resu = _resume$rawData2.Resume) !== null && _resume$rawData2$Resu !== void 0 && _resume$rawData2$Resu.TotalWorkExperienceInYears ? `Total Experience: ${resume.rawData.Resume.TotalWorkExperienceInYears} years\\n` : ''}\n\nEDUCATION\n${(_resume$rawData3 = resume.rawData) !== null && _resume$rawData3 !== void 0 && (_resume$rawData3$Resu = _resume$rawData3.Resume) !== null && _resume$rawData3$Resu !== void 0 && _resume$rawData3$Resu.Education ? resume.rawData.Resume.Education.map(edu => `${edu.Degree || 'Degree'} from ${edu.Institution || edu.School || 'Institution'}\nGraduation Year: ${edu.GraduationYear || edu.Year || 'Not specified'}\n${edu['GPA/Marks/%'] && edu['GPA/Marks/%'] > 0 ? 'Marks: ' + edu['GPA/Marks/%'] + '%' : ''}\n`).join('\\n') : resume.education}\n\nSKILLS & TECHNOLOGIES\n${resume.skills.join(', ')}\n\n${(_resume$rawData4 = resume.rawData) !== null && _resume$rawData4 !== void 0 && (_resume$rawData4$Resu = _resume$rawData4.Resume) !== null && _resume$rawData4$Resu !== void 0 && _resume$rawData4$Resu.Languages && resume.rawData.Resume.Languages.length > 0 ? `LANGUAGES\\n${resume.rawData.Resume.Languages.join(', ')}\\n` : ''}\n\n${(_resume$rawData5 = resume.rawData) !== null && _resume$rawData5 !== void 0 && (_resume$rawData5$Resu = _resume$rawData5.Resume) !== null && _resume$rawData5$Resu !== void 0 && _resume$rawData5$Resu.Projects && resume.rawData.Resume.Projects.length > 0 ? `PROJECTS\\n${resume.rawData.Resume.Projects.map(project => `${project.Name || project.Title || 'Project'}\n${project.Description ? 'Description: ' + project.Description : ''}\n${project.Technologies ? 'Technologies: ' + project.Technologies : ''}\n`).join('\\n')}` : ''}\n\n${(_resume$rawData6 = resume.rawData) !== null && _resume$rawData6 !== void 0 && (_resume$rawData6$Resu = _resume$rawData6.Resume) !== null && _resume$rawData6$Resu !== void 0 && _resume$rawData6$Resu.Certifications && resume.rawData.Resume.Certifications.length > 0 ? `CERTIFICATIONS\\n${resume.rawData.Resume.Certifications.map(cert => `${cert.Name || cert.Title || 'Certification'}\n${cert.IssuingOrganization ? 'Issued by: ' + cert.IssuingOrganization : ''}\n${cert.IssueDate ? 'Date: ' + cert.IssueDate : ''}\n`).join('\\n')}` : ''}\n\n${(_resume$rawData7 = resume.rawData) !== null && _resume$rawData7 !== void 0 && (_resume$rawData7$Resu = _resume$rawData7.Resume) !== null && _resume$rawData7$Resu !== void 0 && _resume$rawData7$Resu.Achievements && resume.rawData.Resume.Achievements.length > 0 ? `ACHIEVEMENTS\\n${resume.rawData.Resume.Achievements.map(achievement => typeof achievement === 'string' ? achievement : `${achievement.AchievementName || achievement.Name || 'Achievement'}\n${achievement.IssueDate ? 'Date: ' + achievement.IssueDate : ''}\n${achievement.IssuingOrganization ? 'Organization: ' + achievement.IssuingOrganization : ''}\n${achievement.Description ? 'Description: ' + achievement.Description : ''}\n`).join('\\n')}` : ''}\n\n${(_resume$rawData8 = resume.rawData) !== null && _resume$rawData8 !== void 0 && (_resume$rawData8$Resu = _resume$rawData8.Resume) !== null && _resume$rawData8$Resu !== void 0 && (_resume$rawData8$Resu2 = _resume$rawData8$Resu.PersonalInformation) !== null && _resume$rawData8$Resu2 !== void 0 && _resume$rawData8$Resu2.LinkedIn ? `ADDITIONAL INFORMATION\\nLinkedIn: ${resume.rawData.Resume.PersonalInformation.LinkedIn}` : ''}\n\nGenerated on: ${new Date().toLocaleDateString()}\n      `.trim();\n\n      // Create and download the file\n      const blob = new Blob([resumeContent], {\n        type: 'text/plain'\n      });\n      const url = URL.createObjectURL(blob);\n      const link = document.createElement('a');\n      link.href = url;\n      link.download = `${resume.name.replace(/\\s+/g, '_')}_Resume.txt`;\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n      URL.revokeObjectURL(url);\n      toast.success(`Downloaded resume for ${resume.name}`);\n    } catch (error) {\n      console.error('Download error:', error);\n      toast.error('Failed to download resume. Please try again.');\n    }\n  };\n  const handleView = resume => {\n    setSelectedResume(resume);\n    setIsModalOpen(true);\n    toast.info(`Opening detailed view for ${resume.name}`);\n  };\n  const handleCloseModal = () => {\n    setIsModalOpen(false);\n    setSelectedResume(null);\n  };\n  const handleEmailClick = (email, name) => {\n    window.open(`mailto:${email}?subject=Regarding your resume&body=Hi ${name},%0D%0A%0D%0AI found your resume and would like to discuss potential opportunities.%0D%0A%0D%0ABest regards`, '_blank');\n    toast.info(`Opening email client to contact ${name}`);\n  };\n  const handlePhoneClick = (phone, name) => {\n    if (navigator.userAgent.match(/Mobile|Android|iPhone|iPad/)) {\n      window.open(`tel:${phone}`, '_blank');\n    } else {\n      navigator.clipboard.writeText(phone).then(() => {\n        toast.success(`Phone number copied: ${phone}`);\n      }).catch(() => {\n        toast.info(`Phone: ${phone}`);\n      });\n    }\n  };\n  const handleExportAll = () => {\n    if (filteredResults.length === 0) {\n      toast.warning('No results to export');\n      return;\n    }\n    try {\n      const exportData = filteredResults.map(resume => ({\n        name: resume.name,\n        title: resume.title,\n        email: resume.email,\n        phone: resume.phone,\n        location: resume.location,\n        experience: resume.experience,\n        education: resume.education,\n        skills: resume.skills.join(', '),\n        summary: resume.summary,\n        matchScore: resume.score\n      }));\n      const csvContent = ['Name,Title,Email,Phone,Location,Experience,Education,Skills,Summary,Match Score', ...exportData.map(row => Object.values(row).map(value => `\"${String(value).replace(/\"/g, '\"\"')}\"`).join(','))].join('\\n');\n      const blob = new Blob([csvContent], {\n        type: 'text/csv'\n      });\n      const url = URL.createObjectURL(blob);\n      const link = document.createElement('a');\n      link.href = url;\n      link.download = `Resume_Search_Results_${new Date().toISOString().split('T')[0]}.csv`;\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n      URL.revokeObjectURL(url);\n      toast.success(`Exported ${filteredResults.length} resumes to CSV`);\n    } catch (error) {\n      console.error('Export error:', error);\n      toast.error('Failed to export results. Please try again.');\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"search-page\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"page-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        children: \"Search Resumes\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 530,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Use natural language to find the perfect candidates for your needs\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 531,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 529,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"search-section\",\n      children: [/*#__PURE__*/_jsxDEV(\"form\", {\n        onSubmit: handleSearch,\n        className: \"search-form\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"search-input-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"search-input-wrapper\",\n            children: [/*#__PURE__*/_jsxDEV(FiSearch, {\n              className: \"search-icon\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 539,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              placeholder: \"e.g., 'Find Python developers with 5+ years experience' or 'Show frontend engineers in California'\",\n              value: searchQuery,\n              onChange: e => setSearchQuery(e.target.value),\n              className: \"search-input\",\n              disabled: isSearching\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 540,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 538,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"search-buttons\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"submit\",\n              className: \"btn btn-primary\",\n              disabled: isSearching,\n              children: isSearching ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"loading-spinner\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 557,\n                  columnNumber: 21\n                }, this), \"Searching...\"]\n              }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(FiSearch, {\n                  size: 16\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 562,\n                  columnNumber: 21\n                }, this), \"Search\"]\n              }, void 0, true)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 550,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              className: \"btn btn-secondary\",\n              onClick: handleShowAll,\n              disabled: isSearching,\n              children: \"Show All\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 567,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 549,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 537,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 536,\n        columnNumber: 9\n      }, this), searchResults.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"filter-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"filter-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"basic-filter-wrapper\",\n            children: [/*#__PURE__*/_jsxDEV(FiFilter, {\n              className: \"filter-icon\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 584,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              placeholder: \"Quick filter by name, title, or location...\",\n              value: filterQuery,\n              onChange: e => setFilterQuery(e.target.value),\n              className: \"filter-input\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 585,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 583,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"filter-controls\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              className: `btn btn-secondary filter-toggle ${showFilters ? 'active' : ''}`,\n              onClick: () => setShowFilters(!showFilters),\n              children: [/*#__PURE__*/_jsxDEV(FiSettings, {\n                size: 16\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 599,\n                columnNumber: 19\n              }, this), \"Advanced Filters\", showFilters ? /*#__PURE__*/_jsxDEV(FiChevronUp, {\n                size: 16\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 601,\n                columnNumber: 34\n              }, this) : /*#__PURE__*/_jsxDEV(FiChevronDown, {\n                size: 16\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 601,\n                columnNumber: 62\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 594,\n              columnNumber: 17\n            }, this), hasActiveFilters() && /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              className: \"btn btn-outline clear-filters\",\n              onClick: clearAllFilters,\n              children: [/*#__PURE__*/_jsxDEV(FiX, {\n                size: 16\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 609,\n                columnNumber: 21\n              }, this), \"Clear All\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 604,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 593,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 582,\n          columnNumber: 13\n        }, this), showFilters && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"advanced-filters\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"filters-grid\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"filter-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Experience (Years)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 621,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"range-inputs\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"number\",\n                  placeholder: \"Min\",\n                  value: advancedFilters.minExperience,\n                  onChange: e => handleAdvancedFilterChange('minExperience', e.target.value),\n                  className: \"filter-input small\",\n                  min: \"0\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 623,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"to\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 631,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"number\",\n                  placeholder: \"Max\",\n                  value: advancedFilters.maxExperience,\n                  onChange: e => handleAdvancedFilterChange('maxExperience', e.target.value),\n                  className: \"filter-input small\",\n                  min: \"0\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 632,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 622,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 620,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"filter-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Education Level\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 644,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                placeholder: \"e.g., Bachelor, Master, PhD\",\n                value: advancedFilters.education,\n                onChange: e => handleAdvancedFilterChange('education', e.target.value),\n                className: \"filter-input\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 645,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 643,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"filter-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Location\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 655,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                placeholder: \"e.g., Mumbai, Delhi, Bangalore\",\n                value: advancedFilters.location,\n                onChange: e => handleAdvancedFilterChange('location', e.target.value),\n                className: \"filter-input\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 656,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 654,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"filter-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Skills\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 666,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                placeholder: \"e.g., Python, React, Java\",\n                value: advancedFilters.skills,\n                onChange: e => handleAdvancedFilterChange('skills', e.target.value),\n                className: \"filter-input\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 667,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 665,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"filter-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Institution\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 677,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                placeholder: \"e.g., IIT, University name\",\n                value: advancedFilters.institution,\n                onChange: e => handleAdvancedFilterChange('institution', e.target.value),\n                className: \"filter-input\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 678,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 676,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"filter-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Graduation Year\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 688,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                placeholder: \"e.g., 2020, 2021\",\n                value: advancedFilters.graduationYear,\n                onChange: e => handleAdvancedFilterChange('graduationYear', e.target.value),\n                className: \"filter-input\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 689,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 687,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"filter-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Company\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 699,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                placeholder: \"e.g., Google, Microsoft, TCS\",\n                value: advancedFilters.company,\n                onChange: e => handleAdvancedFilterChange('company', e.target.value),\n                className: \"filter-input\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 700,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 698,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"filter-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Minimum Marks (%)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 710,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"number\",\n                placeholder: \"e.g., 75, 80\",\n                value: advancedFilters.minMarks,\n                onChange: e => handleAdvancedFilterChange('minMarks', e.target.value),\n                className: \"filter-input\",\n                min: \"0\",\n                max: \"100\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 711,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 709,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 619,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 618,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"results-count\",\n          children: [filteredResults.length, \" of \", searchResults.length, \" results\", hasActiveFilters() && /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"filtered-indicator\",\n            children: \" (filtered)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 727,\n            columnNumber: 38\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 725,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 581,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 535,\n      columnNumber: 7\n    }, this), searchResults.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"results-section\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"results-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Search Results\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 737,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"results-actions\",\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"btn btn-secondary\",\n            onClick: handleExportAll,\n            title: `Export ${filteredResults.length} results to CSV`,\n            children: [/*#__PURE__*/_jsxDEV(FiDownload, {\n              size: 16\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 744,\n              columnNumber: 17\n            }, this), \"Export All (\", filteredResults.length, \")\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 739,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 738,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 736,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"results-grid\",\n        children: filteredResults.map(resume => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"resume-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"resume-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"resume-avatar\",\n              children: /*#__PURE__*/_jsxDEV(FiUser, {\n                size: 24\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 755,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 754,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"resume-info\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"resume-name\",\n                children: resume.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 758,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"resume-title\",\n                children: resume.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 759,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 757,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 753,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"resume-details\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"detail-item location-item\",\n              children: [/*#__PURE__*/_jsxDEV(FiMapPin, {\n                size: 14\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 765,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: resume.location\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 766,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 764,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"detail-item email-item clickable\",\n              onClick: () => handleEmailClick(resume.email, resume.name),\n              title: `Send email to ${resume.name}`,\n              children: [/*#__PURE__*/_jsxDEV(FiMail, {\n                size: 14\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 773,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: resume.email\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 774,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(FiExternalLink, {\n                size: 12,\n                className: \"external-icon\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 775,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 768,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"detail-item phone-item clickable\",\n              onClick: () => handlePhoneClick(resume.phone, resume.name),\n              title: `Call ${resume.name}`,\n              children: [/*#__PURE__*/_jsxDEV(FiPhone, {\n                size: 14\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 782,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: resume.phone\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 783,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(FiExternalLink, {\n                size: 12,\n                className: \"external-icon\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 784,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 777,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 763,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"resume-content\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"content-row\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"content-section\",\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  children: \"Experience\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 791,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: resume.experience\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 792,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 790,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"content-section\",\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  children: \"Education\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 795,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: resume.education\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 796,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 794,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 789,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"content-section\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                children: \"Skills\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 800,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"skills-list\",\n                children: resume.skills.map((skill, index) => /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"skill-tag\",\n                  children: skill\n                }, index, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 803,\n                  columnNumber: 25\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 801,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 799,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 788,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"resume-actions\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"btn btn-secondary\",\n              onClick: () => handleView(resume),\n              children: [/*#__PURE__*/_jsxDEV(FiEye, {\n                size: 16\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 814,\n                columnNumber: 21\n              }, this), \"View Details\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 810,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"btn btn-primary\",\n              onClick: () => handleDownload(resume),\n              children: [/*#__PURE__*/_jsxDEV(FiDownload, {\n                size: 16\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 821,\n                columnNumber: 21\n              }, this), \"Download\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 817,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 809,\n            columnNumber: 17\n          }, this)]\n        }, resume.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 752,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 750,\n        columnNumber: 11\n      }, this), totalPages > 1 && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"pagination-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"pagination-info\",\n          children: [\"Showing \", (currentPage - 1) * resultsPerPage + 1, \" to \", Math.min(currentPage * resultsPerPage, totalCount), \" of \", totalCount, \" results\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 832,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"pagination-controls\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"btn btn-secondary pagination-btn\",\n            onClick: () => handlePageChange(currentPage - 1),\n            disabled: currentPage === 1,\n            children: \"Previous\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 836,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"page-numbers\",\n            children: Array.from({\n              length: Math.min(5, totalPages)\n            }, (_, i) => {\n              let pageNum;\n              if (totalPages <= 5) {\n                pageNum = i + 1;\n              } else if (currentPage <= 3) {\n                pageNum = i + 1;\n              } else if (currentPage >= totalPages - 2) {\n                pageNum = totalPages - 4 + i;\n              } else {\n                pageNum = currentPage - 2 + i;\n              }\n              return /*#__PURE__*/_jsxDEV(\"button\", {\n                className: `btn pagination-btn ${currentPage === pageNum ? 'btn-primary' : 'btn-secondary'}`,\n                onClick: () => handlePageChange(pageNum),\n                children: pageNum\n              }, pageNum, false, {\n                fileName: _jsxFileName,\n                lineNumber: 858,\n                columnNumber: 23\n              }, this);\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 844,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"btn btn-secondary pagination-btn\",\n            onClick: () => handlePageChange(currentPage + 1),\n            disabled: currentPage === totalPages,\n            children: \"Next\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 869,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 835,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 831,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 735,\n      columnNumber: 9\n    }, this) : !isSearching ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"empty-state\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"empty-icon\",\n        children: /*#__PURE__*/_jsxDEV(FiSearch, {\n          size: 48\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 883,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 882,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n        children: \"Ready to Search!\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 885,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Use the search box above to find resumes using natural language, or click \\\"Show All\\\" to view all resumes in the database.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 886,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"search-examples\",\n        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n          children: \"Try searching for:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 888,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n          children: [/*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"\\\"Find Python developers\\\"\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 890,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"\\\"Show teachers with 5+ years experience\\\"\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 891,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"\\\"Software engineers in Mumbai\\\"\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 892,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 889,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 887,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 881,\n      columnNumber: 9\n    }, this) : null, /*#__PURE__*/_jsxDEV(ResumeModal, {\n      resume: selectedResume,\n      isOpen: isModalOpen,\n      onClose: handleCloseModal\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 899,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 528,\n    columnNumber: 5\n  }, this);\n};\n_s(SearchResumes, \"uvVHvTgqjBYZkn8TcWc0iS1zcVo=\");\n_c = SearchResumes;\nexport default SearchResumes;\nvar _c;\n$RefreshReg$(_c, \"SearchResumes\");", "map": {"version": 3, "names": ["React", "useState", "FiSearch", "<PERSON><PERSON><PERSON><PERSON>", "FiDownload", "FiEye", "FiUser", "FiMapPin", "FiMail", "FiPhone", "FiExternalLink", "FiStar", "FiX", "FiSettings", "FiChevronDown", "FiChevronUp", "toast", "ResumeModal", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "SearchResumes", "_s", "searchQuery", "setSearch<PERSON>uery", "isSearching", "setIsSearching", "searchResults", "setSearchResults", "filterQuery", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "showFilters", "setShowFilters", "advancedFilters", "setAdvancedFilters", "minExperience", "maxExperience", "education", "location", "skills", "minMarks", "institution", "graduationYear", "company", "selectedResume", "setSelectedResume", "isModalOpen", "setIsModalOpen", "currentPage", "setCurrentPage", "totalPages", "setTotalPages", "totalCount", "setTotalCount", "resultsPerPage", "mockResults", "id", "name", "title", "email", "phone", "experience", "summary", "score", "handleSearch", "e", "preventDefault", "trim", "warning", "response", "fetch", "method", "headers", "body", "JSON", "stringify", "query", "data", "json", "ok", "transformedResults", "results", "Array", "isArray", "map", "resume", "_resumeData$PersonalI", "_resumeData$PersonalI2", "_resumeData$PersonalI3", "_resumeData$PersonalI4", "_resumeData$PersonalI5", "_resumeData$WorkExper", "_resumeData$Education", "_resumeData$Education2", "resumeData", "Resume", "_id", "Math", "random", "toString", "substr", "PersonalInformation", "FullName", "Designation", "Email", "ContactNumber", "Address", "TotalWorkExperienceInYears", "WorkExperience", "length", "Skills", "Education", "Degree", "Objective", "Summary", "rawData", "filter", "toLowerCase", "includes", "some", "skill", "api_status", "success", "info", "gpt_response", "console", "log", "mongo_query", "Error", "error", "fallback<PERSON><PERSON><PERSON>s", "handleShowAll", "page", "resumes", "_resumeData$PersonalI6", "_resumeData$PersonalI7", "_resumeData$PersonalI8", "_resumeData$PersonalI9", "_resumeData$PersonalI0", "_resumeData$WorkExper2", "_resumeData$Education3", "_resumeData$Education4", "count", "total", "ceil", "source", "handlePageChange", "filteredResults", "result", "matchesText", "filters", "_result$rawData", "_result$rawData$Resum", "experienceYears", "extractExperienceYears", "parseInt", "_result$rawData2", "_result$rawData2$Resu", "_result$rawData2$Resu2", "hasEducation", "edu", "matchesLocation", "_result$skills", "_result$rawData3", "_result$rawData3$Resu", "skillsText", "hasSkill", "_result$rawData4", "_result$rawData4$Resu", "_result$rawData4$Resu2", "hasInstitution", "Institution", "School", "_result$rawData5", "_result$rawData5$Resu", "_result$rawData5$Resu2", "hasGradYear", "GraduationYear", "Year", "_result$rawData6", "_result$rawData6$Resu", "_result$rawData6$Resu2", "hasCompany", "exp", "Company", "CompanyName", "Organization", "_result$rawData7", "_result$rawData7$Resu", "_result$rawData7$Resu2", "hasMinMarks", "marks", "parseFloat", "experienceText", "match", "handleAdvancedFilterChange", "filterName", "value", "prev", "clearAllFilters", "hasActiveFilters", "Object", "values", "handleDownload", "_resume$rawData", "_resume$rawData$Resum", "_resume$rawData2", "_resume$rawData2$Resu", "_resume$rawData3", "_resume$rawData3$Resu", "_resume$rawData4", "_resume$rawData4$Resu", "_resume$rawData5", "_resume$rawData5$Resu", "_resume$rawData6", "_resume$rawData6$Resu", "_resume$rawData7", "_resume$rawData7$Resu", "_resume$rawData8", "_resume$rawData8$Resu", "_resume$rawData8$Resu2", "<PERSON><PERSON><PERSON>nt", "JobTitle", "Position", "Duration", "Years", "Description", "Responsibilities", "join", "Languages", "Projects", "project", "Name", "Title", "Technologies", "Certifications", "cert", "IssuingOrganization", "IssueDate", "Achievements", "achievement", "AchievementName", "LinkedIn", "Date", "toLocaleDateString", "blob", "Blob", "type", "url", "URL", "createObjectURL", "link", "document", "createElement", "href", "download", "replace", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL", "handleView", "handleCloseModal", "handleEmailClick", "window", "open", "handlePhoneClick", "navigator", "userAgent", "clipboard", "writeText", "then", "catch", "handleExportAll", "exportData", "matchScore", "csv<PERSON><PERSON>nt", "row", "String", "toISOString", "split", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSubmit", "placeholder", "onChange", "target", "disabled", "size", "onClick", "min", "max", "index", "from", "_", "i", "pageNum", "isOpen", "onClose", "_c", "$RefreshReg$"], "sources": ["D:/Projects/SQL-Agent-Task-Submission-main/SQL-Agent-Task-Submission-main/src/pages/SearchResumes.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { <PERSON><PERSON>earch, Fi<PERSON>ilter, <PERSON>Down<PERSON>, <PERSON>Eye, Fi<PERSON>ser, FiMapPin, FiMail, FiPhone, FiExternalLink, FiStar, FiX, FiSettings, FiChevronDown, FiChevronUp } from 'react-icons/fi';\nimport { toast } from 'react-toastify';\nimport ResumeModal from '../components/ResumeModal';\nimport './SearchResumes.css';\n\nconst SearchResumes = () => {\n  const [searchQuery, setSearchQuery] = useState('');\n  const [isSearching, setIsSearching] = useState(false);\n  const [searchResults, setSearchResults] = useState([]);\n  const [filterQuery, setFilterQuery] = useState('');\n  const [showFilters, setShowFilters] = useState(false);\n  const [advancedFilters, setAdvancedFilters] = useState({\n    minExperience: '',\n    maxExperience: '',\n    education: '',\n    location: '',\n    skills: '',\n    minMarks: '',\n    institution: '',\n    graduationYear: '',\n    company: ''\n  });\n  const [selectedResume, setSelectedResume] = useState(null);\n  const [isModalOpen, setIsModalOpen] = useState(false);\n  const [currentPage, setCurrentPage] = useState(1);\n  const [totalPages, setTotalPages] = useState(1);\n  const [totalCount, setTotalCount] = useState(0);\n  const resultsPerPage = 20;\n\n  // Mock data for demonstration\n  const mockResults = [\n    {\n      id: 1,\n      name: 'John Doe',\n      title: 'Senior Software Engineer',\n      email: '<EMAIL>',\n      phone: '+****************',\n      location: 'San Francisco, CA',\n      experience: '5+ years',\n      skills: ['Python', 'React', 'Node.js', 'AWS'],\n      education: 'MS Computer Science',\n      summary: 'Experienced software engineer with expertise in full-stack development...',\n      score: 95\n    },\n    {\n      id: 2,\n      name: 'Jane Smith',\n      title: 'Frontend Developer',\n      email: '<EMAIL>',\n      phone: '+****************',\n      location: 'New York, NY',\n      experience: '3+ years',\n      skills: ['React', 'TypeScript', 'CSS', 'JavaScript'],\n      education: 'BS Computer Science',\n      summary: 'Creative frontend developer passionate about user experience...',\n      score: 88\n    },\n    {\n      id: 3,\n      name: 'Mike Johnson',\n      title: 'DevOps Engineer',\n      email: '<EMAIL>',\n      phone: '+****************',\n      location: 'Austin, TX',\n      experience: '4+ years',\n      skills: ['Docker', 'Kubernetes', 'AWS', 'Python'],\n      education: 'BS Information Technology',\n      summary: 'DevOps engineer specializing in cloud infrastructure and automation...',\n      score: 92\n    }\n  ];\n\n  const handleSearch = async (e) => {\n    e.preventDefault();\n    if (!searchQuery.trim()) {\n      toast.warning('Please enter a search query');\n      return;\n    }\n\n    setIsSearching(true);\n\n    try {\n      // Call the actual search API\n      const response = await fetch('/api/search', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({ query: searchQuery }),\n      });\n\n      const data = await response.json();\n\n      if (response.ok) {\n        // Handle both real API results and fallback mock data\n        let transformedResults = [];\n\n        if (data.results && Array.isArray(data.results)) {\n          // Transform the resume data to match our display format\n          transformedResults = data.results.map(resume => {\n            // Handle the nested Resume structure from MongoDB\n            const resumeData = resume.Resume || resume;\n\n            return {\n              id: resume._id || Math.random().toString(36).substr(2, 9),\n              name: resumeData.PersonalInformation?.FullName || resumeData.name || 'Unknown',\n              title: resumeData.PersonalInformation?.Designation || resumeData.title || 'No title',\n              email: resumeData.PersonalInformation?.Email || resumeData.email || 'No email',\n              phone: resumeData.PersonalInformation?.ContactNumber || resumeData.phone || 'No phone',\n              location: resumeData.PersonalInformation?.Address || resumeData.location || 'No location',\n              experience: resumeData.TotalWorkExperienceInYears ?\n                `${resumeData.TotalWorkExperienceInYears} years` :\n                resumeData.WorkExperience?.length ?\n                  `${resumeData.WorkExperience.length}+ positions` :\n                  resumeData.experience || 'No experience data',\n              skills: resumeData.Skills || resumeData.skills || [],\n              education: resumeData.Education?.length ?\n                resumeData.Education[0]?.Degree || 'Education available' :\n                resumeData.education || 'No education data',\n              summary: resumeData.Objective || resumeData.Summary || resumeData.summary || 'No summary available',\n              score: 95, // Default score\n              rawData: resume // Keep original data for reference\n            };\n          });\n        } else {\n          // Fallback to mock data if API doesn't return results\n          transformedResults = mockResults.filter(resume =>\n            resume.name.toLowerCase().includes(searchQuery.toLowerCase()) ||\n            resume.title.toLowerCase().includes(searchQuery.toLowerCase()) ||\n            resume.skills.some(skill => skill.toLowerCase().includes(searchQuery.toLowerCase()))\n          );\n        }\n\n        setSearchResults(transformedResults);\n\n        if (data.api_status === 'success') {\n          toast.success(`Found ${transformedResults.length} matching resumes using AI search`);\n        } else if (data.api_status === 'fallback') {\n          toast.info(`Found ${transformedResults.length} matching resumes using fallback search`);\n        } else {\n          toast.success(`Found ${transformedResults.length} matching resumes`);\n        }\n\n        // Show GPT response if available\n        if (data.gpt_response && data.gpt_response !== 'Data received successfully.') {\n          console.log('GPT Response:', data.gpt_response);\n          console.log('MongoDB Query:', data.mongo_query);\n        }\n      } else {\n        throw new Error(data.error || 'Search failed');\n      }\n    } catch (error) {\n      console.error('Search error:', error);\n\n      // Fallback to mock data on network error\n      const fallbackResults = mockResults.filter(resume =>\n        resume.name.toLowerCase().includes(searchQuery.toLowerCase()) ||\n        resume.title.toLowerCase().includes(searchQuery.toLowerCase()) ||\n        resume.skills.some(skill => skill.toLowerCase().includes(searchQuery.toLowerCase()))\n      );\n\n      setSearchResults(fallbackResults);\n      toast.warning(`Using demo data - Found ${fallbackResults.length} matching resumes`);\n    } finally {\n      setIsSearching(false);\n    }\n  };\n\n  const handleShowAll = async (page = 1) => {\n    setIsSearching(true);\n    try {\n      // Call the actual API to get paginated resumes\n      const response = await fetch(`/api/resumes?page=${page}&limit=${resultsPerPage}`);\n      const data = await response.json();\n\n      if (response.ok) {\n        let transformedResults = [];\n\n        if (data.resumes && Array.isArray(data.resumes)) {\n          // Transform the resume data to match our display format\n          transformedResults = data.resumes.map(resume => {\n            // Handle the nested Resume structure from MongoDB\n            const resumeData = resume.Resume || resume;\n\n            return {\n              id: resume._id || Math.random().toString(36).substr(2, 9),\n              name: resumeData.PersonalInformation?.FullName || resumeData.name || 'Unknown',\n              title: resumeData.PersonalInformation?.Designation || resumeData.title || 'No title',\n              email: resumeData.PersonalInformation?.Email || resumeData.email || 'No email',\n              phone: resumeData.PersonalInformation?.ContactNumber || resumeData.phone || 'No phone',\n              location: resumeData.PersonalInformation?.Address || resumeData.location || 'No location',\n              experience: resumeData.TotalWorkExperienceInYears ?\n                `${resumeData.TotalWorkExperienceInYears} years` :\n                resumeData.WorkExperience?.length ?\n                  `${resumeData.WorkExperience.length}+ positions` :\n                  resumeData.experience || 'No experience data',\n              skills: resumeData.Skills || resumeData.skills || [],\n              education: resumeData.Education?.length ?\n                resumeData.Education[0]?.Degree || 'Education available' :\n                resumeData.education || 'No education data',\n              summary: resumeData.Objective || resumeData.Summary || resumeData.summary || 'No summary available',\n              score: 90, // Default score for show all\n              rawData: resume // Keep original data for reference\n            };\n          });\n        } else {\n          // Fallback to mock data if API doesn't return proper results\n          transformedResults = mockResults;\n        }\n\n        setSearchResults(transformedResults);\n        setCurrentPage(page);\n        setTotalCount(data.count || data.total || transformedResults.length);\n        setTotalPages(Math.ceil((data.count || data.total || transformedResults.length) / resultsPerPage));\n        setSearchQuery('');\n\n        if (data.source === 'database') {\n          toast.success(`Showing page ${page} of ${Math.ceil((data.count || data.total || transformedResults.length) / resultsPerPage)} (${data.count || data.total} total resumes)`);\n        } else {\n          toast.info(`Showing ${transformedResults.length} resumes`);\n        }\n      } else {\n        throw new Error(data.error || 'Failed to load resumes');\n      }\n    } catch (error) {\n      console.error('Load resumes error:', error);\n\n      // Fallback to mock data on network error\n      setSearchResults(mockResults);\n      setCurrentPage(1);\n      setTotalCount(mockResults.length);\n      setTotalPages(Math.ceil(mockResults.length / resultsPerPage));\n      setSearchQuery('');\n\n      toast.warning(`Using demo data - Showing ${mockResults.length} sample resumes`);\n    } finally {\n      setIsSearching(false);\n    }\n  };\n\n  const handlePageChange = (page) => {\n    if (page >= 1 && page <= totalPages) {\n      handleShowAll(page);\n    }\n  };\n\n  const filteredResults = searchResults.filter(result => {\n    // Basic text filter\n    if (filterQuery) {\n      const matchesText = result.name.toLowerCase().includes(filterQuery.toLowerCase()) ||\n                         result.title.toLowerCase().includes(filterQuery.toLowerCase()) ||\n                         result.location.toLowerCase().includes(filterQuery.toLowerCase());\n      if (!matchesText) return false;\n    }\n\n    // Advanced filters\n    const filters = advancedFilters;\n\n    // Experience filter\n    if (filters.minExperience || filters.maxExperience) {\n      const experienceYears = result.rawData?.Resume?.TotalWorkExperienceInYears ||\n                             extractExperienceYears(result.experience) || 0;\n\n      if (filters.minExperience && experienceYears < parseInt(filters.minExperience)) return false;\n      if (filters.maxExperience && experienceYears > parseInt(filters.maxExperience)) return false;\n    }\n\n    // Education filter\n    if (filters.education) {\n      const hasEducation = result.rawData?.Resume?.Education?.some(edu =>\n        (edu.Degree || '').toLowerCase().includes(filters.education.toLowerCase())\n      ) || (result.education || '').toLowerCase().includes(filters.education.toLowerCase());\n      if (!hasEducation) return false;\n    }\n\n    // Location filter\n    if (filters.location) {\n      const matchesLocation = (result.location || '').toLowerCase().includes(filters.location.toLowerCase());\n      if (!matchesLocation) return false;\n    }\n\n    // Skills filter\n    if (filters.skills) {\n      const skillsText = filters.skills.toLowerCase();\n      const hasSkill = result.skills?.some(skill => skill.toLowerCase().includes(skillsText)) ||\n                      (result.rawData?.Resume?.Skills || []).some(skill =>\n                        skill.toLowerCase().includes(skillsText)\n                      );\n      if (!hasSkill) return false;\n    }\n\n    // Institution filter\n    if (filters.institution) {\n      const hasInstitution = result.rawData?.Resume?.Education?.some(edu =>\n        (edu.Institution || edu.School || '').toLowerCase().includes(filters.institution.toLowerCase())\n      );\n      if (!hasInstitution) return false;\n    }\n\n    // Graduation year filter\n    if (filters.graduationYear) {\n      const hasGradYear = result.rawData?.Resume?.Education?.some(edu =>\n        (edu.GraduationYear || edu.Year || '').toString().includes(filters.graduationYear)\n      );\n      if (!hasGradYear) return false;\n    }\n\n    // Company filter\n    if (filters.company) {\n      const hasCompany = result.rawData?.Resume?.WorkExperience?.some(exp =>\n        (exp.Company || exp.CompanyName || exp.Organization || '').toLowerCase().includes(filters.company.toLowerCase())\n      );\n      if (!hasCompany) return false;\n    }\n\n    // Minimum marks filter\n    if (filters.minMarks) {\n      const hasMinMarks = result.rawData?.Resume?.Education?.some(edu => {\n        const marks = parseFloat(edu['GPA/Marks/%']) || 0;\n        return marks >= parseFloat(filters.minMarks);\n      });\n      if (!hasMinMarks) return false;\n    }\n\n    return true;\n  });\n\n  // Helper function to extract experience years from text\n  const extractExperienceYears = (experienceText) => {\n    if (!experienceText) return 0;\n    const match = experienceText.match(/(\\d+)\\+?\\s*years?/i);\n    return match ? parseInt(match[1]) : 0;\n  };\n\n  const handleAdvancedFilterChange = (filterName, value) => {\n    setAdvancedFilters(prev => ({\n      ...prev,\n      [filterName]: value\n    }));\n  };\n\n  const clearAllFilters = () => {\n    setFilterQuery('');\n    setAdvancedFilters({\n      minExperience: '',\n      maxExperience: '',\n      education: '',\n      location: '',\n      skills: '',\n      minMarks: '',\n      institution: '',\n      graduationYear: '',\n      company: ''\n    });\n  };\n\n  const hasActiveFilters = () => {\n    return filterQuery || Object.values(advancedFilters).some(value => value !== '');\n  };\n\n  const handleDownload = (resume) => {\n    try {\n      // Create a comprehensive text-based resume content\n      const resumeContent = `\n${resume.name}\n${resume.title}\n${resume.email} | ${resume.phone} | ${resume.location}\n\nPROFESSIONAL SUMMARY\n${resume.summary}\n\nWORK EXPERIENCE\n${resume.rawData?.Resume?.WorkExperience ?\n  resume.rawData.Resume.WorkExperience.map(exp =>\n    `${exp.JobTitle || exp.Position} at ${exp.Company || exp.Organization}\nDuration: ${exp.Duration || exp.Years || 'Not specified'}\n${exp.Description ? 'Description: ' + exp.Description : ''}\n${exp.Responsibilities ? 'Responsibilities: ' + exp.Responsibilities : ''}\n`\n  ).join('\\n') : resume.experience}\n\n${resume.rawData?.Resume?.TotalWorkExperienceInYears ?\n  `Total Experience: ${resume.rawData.Resume.TotalWorkExperienceInYears} years\\n` : ''}\n\nEDUCATION\n${resume.rawData?.Resume?.Education ?\n  resume.rawData.Resume.Education.map(edu =>\n    `${edu.Degree || 'Degree'} from ${edu.Institution || edu.School || 'Institution'}\nGraduation Year: ${edu.GraduationYear || edu.Year || 'Not specified'}\n${edu['GPA/Marks/%'] && edu['GPA/Marks/%'] > 0 ? 'Marks: ' + edu['GPA/Marks/%'] + '%' : ''}\n`\n  ).join('\\n') : resume.education}\n\nSKILLS & TECHNOLOGIES\n${resume.skills.join(', ')}\n\n${resume.rawData?.Resume?.Languages && resume.rawData.Resume.Languages.length > 0 ?\n  `LANGUAGES\\n${resume.rawData.Resume.Languages.join(', ')}\\n` : ''}\n\n${resume.rawData?.Resume?.Projects && resume.rawData.Resume.Projects.length > 0 ?\n  `PROJECTS\\n${resume.rawData.Resume.Projects.map(project =>\n    `${project.Name || project.Title || 'Project'}\n${project.Description ? 'Description: ' + project.Description : ''}\n${project.Technologies ? 'Technologies: ' + project.Technologies : ''}\n`\n  ).join('\\n')}` : ''}\n\n${resume.rawData?.Resume?.Certifications && resume.rawData.Resume.Certifications.length > 0 ?\n  `CERTIFICATIONS\\n${resume.rawData.Resume.Certifications.map(cert =>\n    `${cert.Name || cert.Title || 'Certification'}\n${cert.IssuingOrganization ? 'Issued by: ' + cert.IssuingOrganization : ''}\n${cert.IssueDate ? 'Date: ' + cert.IssueDate : ''}\n`\n  ).join('\\n')}` : ''}\n\n${resume.rawData?.Resume?.Achievements && resume.rawData.Resume.Achievements.length > 0 ?\n  `ACHIEVEMENTS\\n${resume.rawData.Resume.Achievements.map(achievement =>\n    typeof achievement === 'string' ? achievement :\n    `${achievement.AchievementName || achievement.Name || 'Achievement'}\n${achievement.IssueDate ? 'Date: ' + achievement.IssueDate : ''}\n${achievement.IssuingOrganization ? 'Organization: ' + achievement.IssuingOrganization : ''}\n${achievement.Description ? 'Description: ' + achievement.Description : ''}\n`\n  ).join('\\n')}` : ''}\n\n${resume.rawData?.Resume?.PersonalInformation?.LinkedIn ?\n  `ADDITIONAL INFORMATION\\nLinkedIn: ${resume.rawData.Resume.PersonalInformation.LinkedIn}` : ''}\n\nGenerated on: ${new Date().toLocaleDateString()}\n      `.trim();\n\n      // Create and download the file\n      const blob = new Blob([resumeContent], { type: 'text/plain' });\n      const url = URL.createObjectURL(blob);\n      const link = document.createElement('a');\n      link.href = url;\n      link.download = `${resume.name.replace(/\\s+/g, '_')}_Resume.txt`;\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n      URL.revokeObjectURL(url);\n\n      toast.success(`Downloaded resume for ${resume.name}`);\n    } catch (error) {\n      console.error('Download error:', error);\n      toast.error('Failed to download resume. Please try again.');\n    }\n  };\n\n  const handleView = (resume) => {\n    setSelectedResume(resume);\n    setIsModalOpen(true);\n    toast.info(`Opening detailed view for ${resume.name}`);\n  };\n\n  const handleCloseModal = () => {\n    setIsModalOpen(false);\n    setSelectedResume(null);\n  };\n\n  const handleEmailClick = (email, name) => {\n    window.open(`mailto:${email}?subject=Regarding your resume&body=Hi ${name},%0D%0A%0D%0AI found your resume and would like to discuss potential opportunities.%0D%0A%0D%0ABest regards`, '_blank');\n    toast.info(`Opening email client to contact ${name}`);\n  };\n\n  const handlePhoneClick = (phone, name) => {\n    if (navigator.userAgent.match(/Mobile|Android|iPhone|iPad/)) {\n      window.open(`tel:${phone}`, '_blank');\n    } else {\n      navigator.clipboard.writeText(phone).then(() => {\n        toast.success(`Phone number copied: ${phone}`);\n      }).catch(() => {\n        toast.info(`Phone: ${phone}`);\n      });\n    }\n  };\n\n\n\n  const handleExportAll = () => {\n    if (filteredResults.length === 0) {\n      toast.warning('No results to export');\n      return;\n    }\n\n    try {\n      const exportData = filteredResults.map(resume => ({\n        name: resume.name,\n        title: resume.title,\n        email: resume.email,\n        phone: resume.phone,\n        location: resume.location,\n        experience: resume.experience,\n        education: resume.education,\n        skills: resume.skills.join(', '),\n        summary: resume.summary,\n        matchScore: resume.score\n      }));\n\n      const csvContent = [\n        'Name,Title,Email,Phone,Location,Experience,Education,Skills,Summary,Match Score',\n        ...exportData.map(row =>\n          Object.values(row).map(value =>\n            `\"${String(value).replace(/\"/g, '\"\"')}\"`\n          ).join(',')\n        )\n      ].join('\\n');\n\n      const blob = new Blob([csvContent], { type: 'text/csv' });\n      const url = URL.createObjectURL(blob);\n      const link = document.createElement('a');\n      link.href = url;\n      link.download = `Resume_Search_Results_${new Date().toISOString().split('T')[0]}.csv`;\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n      URL.revokeObjectURL(url);\n\n      toast.success(`Exported ${filteredResults.length} resumes to CSV`);\n    } catch (error) {\n      console.error('Export error:', error);\n      toast.error('Failed to export results. Please try again.');\n    }\n  };\n\n  return (\n    <div className=\"search-page\">\n      <div className=\"page-header\">\n        <h1>Search Resumes</h1>\n        <p>Use natural language to find the perfect candidates for your needs</p>\n      </div>\n\n      {/* Search Section */}\n      <div className=\"search-section\">\n        <form onSubmit={handleSearch} className=\"search-form\">\n          <div className=\"search-input-group\">\n            <div className=\"search-input-wrapper\">\n              <FiSearch className=\"search-icon\" />\n              <input\n                type=\"text\"\n                placeholder=\"e.g., 'Find Python developers with 5+ years experience' or 'Show frontend engineers in California'\"\n                value={searchQuery}\n                onChange={(e) => setSearchQuery(e.target.value)}\n                className=\"search-input\"\n                disabled={isSearching}\n              />\n            </div>\n            <div className=\"search-buttons\">\n              <button\n                type=\"submit\"\n                className=\"btn btn-primary\"\n                disabled={isSearching}\n              >\n                {isSearching ? (\n                  <>\n                    <div className=\"loading-spinner\" />\n                    Searching...\n                  </>\n                ) : (\n                  <>\n                    <FiSearch size={16} />\n                    Search\n                  </>\n                )}\n              </button>\n              <button\n                type=\"button\"\n                className=\"btn btn-secondary\"\n                onClick={handleShowAll}\n                disabled={isSearching}\n              >\n                Show All\n              </button>\n            </div>\n          </div>\n        </form>\n\n        {/* Filter Section */}\n        {searchResults.length > 0 && (\n          <div className=\"filter-section\">\n            <div className=\"filter-header\">\n              <div className=\"basic-filter-wrapper\">\n                <FiFilter className=\"filter-icon\" />\n                <input\n                  type=\"text\"\n                  placeholder=\"Quick filter by name, title, or location...\"\n                  value={filterQuery}\n                  onChange={(e) => setFilterQuery(e.target.value)}\n                  className=\"filter-input\"\n                />\n              </div>\n              <div className=\"filter-controls\">\n                <button\n                  type=\"button\"\n                  className={`btn btn-secondary filter-toggle ${showFilters ? 'active' : ''}`}\n                  onClick={() => setShowFilters(!showFilters)}\n                >\n                  <FiSettings size={16} />\n                  Advanced Filters\n                  {showFilters ? <FiChevronUp size={16} /> : <FiChevronDown size={16} />}\n                </button>\n                {hasActiveFilters() && (\n                  <button\n                    type=\"button\"\n                    className=\"btn btn-outline clear-filters\"\n                    onClick={clearAllFilters}\n                  >\n                    <FiX size={16} />\n                    Clear All\n                  </button>\n                )}\n              </div>\n            </div>\n\n            {/* Advanced Filters Panel */}\n            {showFilters && (\n              <div className=\"advanced-filters\">\n                <div className=\"filters-grid\">\n                  <div className=\"filter-group\">\n                    <label>Experience (Years)</label>\n                    <div className=\"range-inputs\">\n                      <input\n                        type=\"number\"\n                        placeholder=\"Min\"\n                        value={advancedFilters.minExperience}\n                        onChange={(e) => handleAdvancedFilterChange('minExperience', e.target.value)}\n                        className=\"filter-input small\"\n                        min=\"0\"\n                      />\n                      <span>to</span>\n                      <input\n                        type=\"number\"\n                        placeholder=\"Max\"\n                        value={advancedFilters.maxExperience}\n                        onChange={(e) => handleAdvancedFilterChange('maxExperience', e.target.value)}\n                        className=\"filter-input small\"\n                        min=\"0\"\n                      />\n                    </div>\n                  </div>\n\n                  <div className=\"filter-group\">\n                    <label>Education Level</label>\n                    <input\n                      type=\"text\"\n                      placeholder=\"e.g., Bachelor, Master, PhD\"\n                      value={advancedFilters.education}\n                      onChange={(e) => handleAdvancedFilterChange('education', e.target.value)}\n                      className=\"filter-input\"\n                    />\n                  </div>\n\n                  <div className=\"filter-group\">\n                    <label>Location</label>\n                    <input\n                      type=\"text\"\n                      placeholder=\"e.g., Mumbai, Delhi, Bangalore\"\n                      value={advancedFilters.location}\n                      onChange={(e) => handleAdvancedFilterChange('location', e.target.value)}\n                      className=\"filter-input\"\n                    />\n                  </div>\n\n                  <div className=\"filter-group\">\n                    <label>Skills</label>\n                    <input\n                      type=\"text\"\n                      placeholder=\"e.g., Python, React, Java\"\n                      value={advancedFilters.skills}\n                      onChange={(e) => handleAdvancedFilterChange('skills', e.target.value)}\n                      className=\"filter-input\"\n                    />\n                  </div>\n\n                  <div className=\"filter-group\">\n                    <label>Institution</label>\n                    <input\n                      type=\"text\"\n                      placeholder=\"e.g., IIT, University name\"\n                      value={advancedFilters.institution}\n                      onChange={(e) => handleAdvancedFilterChange('institution', e.target.value)}\n                      className=\"filter-input\"\n                    />\n                  </div>\n\n                  <div className=\"filter-group\">\n                    <label>Graduation Year</label>\n                    <input\n                      type=\"text\"\n                      placeholder=\"e.g., 2020, 2021\"\n                      value={advancedFilters.graduationYear}\n                      onChange={(e) => handleAdvancedFilterChange('graduationYear', e.target.value)}\n                      className=\"filter-input\"\n                    />\n                  </div>\n\n                  <div className=\"filter-group\">\n                    <label>Company</label>\n                    <input\n                      type=\"text\"\n                      placeholder=\"e.g., Google, Microsoft, TCS\"\n                      value={advancedFilters.company}\n                      onChange={(e) => handleAdvancedFilterChange('company', e.target.value)}\n                      className=\"filter-input\"\n                    />\n                  </div>\n\n                  <div className=\"filter-group\">\n                    <label>Minimum Marks (%)</label>\n                    <input\n                      type=\"number\"\n                      placeholder=\"e.g., 75, 80\"\n                      value={advancedFilters.minMarks}\n                      onChange={(e) => handleAdvancedFilterChange('minMarks', e.target.value)}\n                      className=\"filter-input\"\n                      min=\"0\"\n                      max=\"100\"\n                    />\n                  </div>\n                </div>\n              </div>\n            )}\n\n            <div className=\"results-count\">\n              {filteredResults.length} of {searchResults.length} results\n              {hasActiveFilters() && <span className=\"filtered-indicator\"> (filtered)</span>}\n            </div>\n          </div>\n        )}\n      </div>\n\n      {/* Results Section */}\n      {searchResults.length > 0 ? (\n        <div className=\"results-section\">\n          <div className=\"results-header\">\n            <h2>Search Results</h2>\n            <div className=\"results-actions\">\n              <button\n                className=\"btn btn-secondary\"\n                onClick={handleExportAll}\n                title={`Export ${filteredResults.length} results to CSV`}\n              >\n                <FiDownload size={16} />\n                Export All ({filteredResults.length})\n              </button>\n            </div>\n          </div>\n\n          <div className=\"results-grid\">\n            {filteredResults.map((resume) => (\n              <div key={resume.id} className=\"resume-card\">\n                <div className=\"resume-header\">\n                  <div className=\"resume-avatar\">\n                    <FiUser size={24} />\n                  </div>\n                  <div className=\"resume-info\">\n                    <h3 className=\"resume-name\">{resume.name}</h3>\n                    <p className=\"resume-title\">{resume.title}</p>\n                  </div>\n                </div>\n\n                <div className=\"resume-details\">\n                  <div className=\"detail-item location-item\">\n                    <FiMapPin size={14} />\n                    <span>{resume.location}</span>\n                  </div>\n                  <div\n                    className=\"detail-item email-item clickable\"\n                    onClick={() => handleEmailClick(resume.email, resume.name)}\n                    title={`Send email to ${resume.name}`}\n                  >\n                    <FiMail size={14} />\n                    <span>{resume.email}</span>\n                    <FiExternalLink size={12} className=\"external-icon\" />\n                  </div>\n                  <div\n                    className=\"detail-item phone-item clickable\"\n                    onClick={() => handlePhoneClick(resume.phone, resume.name)}\n                    title={`Call ${resume.name}`}\n                  >\n                    <FiPhone size={14} />\n                    <span>{resume.phone}</span>\n                    <FiExternalLink size={12} className=\"external-icon\" />\n                  </div>\n                </div>\n\n                <div className=\"resume-content\">\n                  <div className=\"content-row\">\n                    <div className=\"content-section\">\n                      <h4>Experience</h4>\n                      <p>{resume.experience}</p>\n                    </div>\n                    <div className=\"content-section\">\n                      <h4>Education</h4>\n                      <p>{resume.education}</p>\n                    </div>\n                  </div>\n                  <div className=\"content-section\">\n                    <h4>Skills</h4>\n                    <div className=\"skills-list\">\n                      {resume.skills.map((skill, index) => (\n                        <span key={index} className=\"skill-tag\">{skill}</span>\n                      ))}\n                    </div>\n                  </div>\n                </div>\n\n                <div className=\"resume-actions\">\n                  <button\n                    className=\"btn btn-secondary\"\n                    onClick={() => handleView(resume)}\n                  >\n                    <FiEye size={16} />\n                    View Details\n                  </button>\n                  <button\n                    className=\"btn btn-primary\"\n                    onClick={() => handleDownload(resume)}\n                  >\n                    <FiDownload size={16} />\n                    Download\n                  </button>\n                </div>\n              </div>\n            ))}\n          </div>\n\n          {/* Pagination */}\n          {totalPages > 1 && (\n            <div className=\"pagination-section\">\n              <div className=\"pagination-info\">\n                Showing {((currentPage - 1) * resultsPerPage) + 1} to {Math.min(currentPage * resultsPerPage, totalCount)} of {totalCount} results\n              </div>\n              <div className=\"pagination-controls\">\n                <button\n                  className=\"btn btn-secondary pagination-btn\"\n                  onClick={() => handlePageChange(currentPage - 1)}\n                  disabled={currentPage === 1}\n                >\n                  Previous\n                </button>\n\n                <div className=\"page-numbers\">\n                  {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {\n                    let pageNum;\n                    if (totalPages <= 5) {\n                      pageNum = i + 1;\n                    } else if (currentPage <= 3) {\n                      pageNum = i + 1;\n                    } else if (currentPage >= totalPages - 2) {\n                      pageNum = totalPages - 4 + i;\n                    } else {\n                      pageNum = currentPage - 2 + i;\n                    }\n\n                    return (\n                      <button\n                        key={pageNum}\n                        className={`btn pagination-btn ${currentPage === pageNum ? 'btn-primary' : 'btn-secondary'}`}\n                        onClick={() => handlePageChange(pageNum)}\n                      >\n                        {pageNum}\n                      </button>\n                    );\n                  })}\n                </div>\n\n                <button\n                  className=\"btn btn-secondary pagination-btn\"\n                  onClick={() => handlePageChange(currentPage + 1)}\n                  disabled={currentPage === totalPages}\n                >\n                  Next\n                </button>\n              </div>\n            </div>\n          )}\n        </div>\n      ) : !isSearching ? (\n        <div className=\"empty-state\">\n          <div className=\"empty-icon\">\n            <FiSearch size={48} />\n          </div>\n          <h3>Ready to Search!</h3>\n          <p>Use the search box above to find resumes using natural language, or click \"Show All\" to view all resumes in the database.</p>\n          <div className=\"search-examples\">\n            <h4>Try searching for:</h4>\n            <ul>\n              <li>\"Find Python developers\"</li>\n              <li>\"Show teachers with 5+ years experience\"</li>\n              <li>\"Software engineers in Mumbai\"</li>\n            </ul>\n          </div>\n        </div>\n      ) : null}\n\n      {/* Resume Details Modal */}\n      <ResumeModal\n        resume={selectedResume}\n        isOpen={isModalOpen}\n        onClose={handleCloseModal}\n      />\n    </div>\n  );\n};\n\nexport default SearchResumes;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,QAAQ,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,KAAK,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,OAAO,EAAEC,cAAc,EAAEC,MAAM,EAAEC,GAAG,EAAEC,UAAU,EAAEC,aAAa,EAAEC,WAAW,QAAQ,gBAAgB;AAC9K,SAASC,KAAK,QAAQ,gBAAgB;AACtC,OAAOC,WAAW,MAAM,2BAA2B;AACnD,OAAO,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE7B,MAAMC,aAAa,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC1B,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACyB,WAAW,EAAEC,cAAc,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAAC2B,aAAa,EAAEC,gBAAgB,CAAC,GAAG5B,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAAC6B,WAAW,EAAEC,cAAc,CAAC,GAAG9B,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC+B,WAAW,EAAEC,cAAc,CAAC,GAAGhC,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACiC,eAAe,EAAEC,kBAAkB,CAAC,GAAGlC,QAAQ,CAAC;IACrDmC,aAAa,EAAE,EAAE;IACjBC,aAAa,EAAE,EAAE;IACjBC,SAAS,EAAE,EAAE;IACbC,QAAQ,EAAE,EAAE;IACZC,MAAM,EAAE,EAAE;IACVC,QAAQ,EAAE,EAAE;IACZC,WAAW,EAAE,EAAE;IACfC,cAAc,EAAE,EAAE;IAClBC,OAAO,EAAE;EACX,CAAC,CAAC;EACF,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAG7C,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAAC8C,WAAW,EAAEC,cAAc,CAAC,GAAG/C,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACgD,WAAW,EAAEC,cAAc,CAAC,GAAGjD,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAACkD,UAAU,EAAEC,aAAa,CAAC,GAAGnD,QAAQ,CAAC,CAAC,CAAC;EAC/C,MAAM,CAACoD,UAAU,EAAEC,aAAa,CAAC,GAAGrD,QAAQ,CAAC,CAAC,CAAC;EAC/C,MAAMsD,cAAc,GAAG,EAAE;;EAEzB;EACA,MAAMC,WAAW,GAAG,CAClB;IACEC,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,UAAU;IAChBC,KAAK,EAAE,0BAA0B;IACjCC,KAAK,EAAE,oBAAoB;IAC3BC,KAAK,EAAE,mBAAmB;IAC1BtB,QAAQ,EAAE,mBAAmB;IAC7BuB,UAAU,EAAE,UAAU;IACtBtB,MAAM,EAAE,CAAC,QAAQ,EAAE,OAAO,EAAE,SAAS,EAAE,KAAK,CAAC;IAC7CF,SAAS,EAAE,qBAAqB;IAChCyB,OAAO,EAAE,2EAA2E;IACpFC,KAAK,EAAE;EACT,CAAC,EACD;IACEP,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,YAAY;IAClBC,KAAK,EAAE,oBAAoB;IAC3BC,KAAK,EAAE,sBAAsB;IAC7BC,KAAK,EAAE,mBAAmB;IAC1BtB,QAAQ,EAAE,cAAc;IACxBuB,UAAU,EAAE,UAAU;IACtBtB,MAAM,EAAE,CAAC,OAAO,EAAE,YAAY,EAAE,KAAK,EAAE,YAAY,CAAC;IACpDF,SAAS,EAAE,qBAAqB;IAChCyB,OAAO,EAAE,iEAAiE;IAC1EC,KAAK,EAAE;EACT,CAAC,EACD;IACEP,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,cAAc;IACpBC,KAAK,EAAE,iBAAiB;IACxBC,KAAK,EAAE,wBAAwB;IAC/BC,KAAK,EAAE,mBAAmB;IAC1BtB,QAAQ,EAAE,YAAY;IACtBuB,UAAU,EAAE,UAAU;IACtBtB,MAAM,EAAE,CAAC,QAAQ,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,CAAC;IACjDF,SAAS,EAAE,2BAA2B;IACtCyB,OAAO,EAAE,wEAAwE;IACjFC,KAAK,EAAE;EACT,CAAC,CACF;EAED,MAAMC,YAAY,GAAG,MAAOC,CAAC,IAAK;IAChCA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,IAAI,CAAC3C,WAAW,CAAC4C,IAAI,CAAC,CAAC,EAAE;MACvBpD,KAAK,CAACqD,OAAO,CAAC,6BAA6B,CAAC;MAC5C;IACF;IAEA1C,cAAc,CAAC,IAAI,CAAC;IAEpB,IAAI;MACF;MACA,MAAM2C,QAAQ,GAAG,MAAMC,KAAK,CAAC,aAAa,EAAE;QAC1CC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UAAEC,KAAK,EAAErD;QAAY,CAAC;MAC7C,CAAC,CAAC;MAEF,MAAMsD,IAAI,GAAG,MAAMR,QAAQ,CAACS,IAAI,CAAC,CAAC;MAElC,IAAIT,QAAQ,CAACU,EAAE,EAAE;QACf;QACA,IAAIC,kBAAkB,GAAG,EAAE;QAE3B,IAAIH,IAAI,CAACI,OAAO,IAAIC,KAAK,CAACC,OAAO,CAACN,IAAI,CAACI,OAAO,CAAC,EAAE;UAC/C;UACAD,kBAAkB,GAAGH,IAAI,CAACI,OAAO,CAACG,GAAG,CAACC,MAAM,IAAI;YAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,sBAAA;YAC9C;YACA,MAAMC,UAAU,GAAGT,MAAM,CAACU,MAAM,IAAIV,MAAM;YAE1C,OAAO;cACL7B,EAAE,EAAE6B,MAAM,CAACW,GAAG,IAAIC,IAAI,CAACC,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;cACzD3C,IAAI,EAAE,EAAA6B,qBAAA,GAAAQ,UAAU,CAACO,mBAAmB,cAAAf,qBAAA,uBAA9BA,qBAAA,CAAgCgB,QAAQ,KAAIR,UAAU,CAACrC,IAAI,IAAI,SAAS;cAC9EC,KAAK,EAAE,EAAA6B,sBAAA,GAAAO,UAAU,CAACO,mBAAmB,cAAAd,sBAAA,uBAA9BA,sBAAA,CAAgCgB,WAAW,KAAIT,UAAU,CAACpC,KAAK,IAAI,UAAU;cACpFC,KAAK,EAAE,EAAA6B,sBAAA,GAAAM,UAAU,CAACO,mBAAmB,cAAAb,sBAAA,uBAA9BA,sBAAA,CAAgCgB,KAAK,KAAIV,UAAU,CAACnC,KAAK,IAAI,UAAU;cAC9EC,KAAK,EAAE,EAAA6B,sBAAA,GAAAK,UAAU,CAACO,mBAAmB,cAAAZ,sBAAA,uBAA9BA,sBAAA,CAAgCgB,aAAa,KAAIX,UAAU,CAAClC,KAAK,IAAI,UAAU;cACtFtB,QAAQ,EAAE,EAAAoD,sBAAA,GAAAI,UAAU,CAACO,mBAAmB,cAAAX,sBAAA,uBAA9BA,sBAAA,CAAgCgB,OAAO,KAAIZ,UAAU,CAACxD,QAAQ,IAAI,aAAa;cACzFuB,UAAU,EAAEiC,UAAU,CAACa,0BAA0B,GAC/C,GAAGb,UAAU,CAACa,0BAA0B,QAAQ,GAChD,CAAAhB,qBAAA,GAAAG,UAAU,CAACc,cAAc,cAAAjB,qBAAA,eAAzBA,qBAAA,CAA2BkB,MAAM,GAC/B,GAAGf,UAAU,CAACc,cAAc,CAACC,MAAM,aAAa,GAChDf,UAAU,CAACjC,UAAU,IAAI,oBAAoB;cACjDtB,MAAM,EAAEuD,UAAU,CAACgB,MAAM,IAAIhB,UAAU,CAACvD,MAAM,IAAI,EAAE;cACpDF,SAAS,EAAE,CAAAuD,qBAAA,GAAAE,UAAU,CAACiB,SAAS,cAAAnB,qBAAA,eAApBA,qBAAA,CAAsBiB,MAAM,GACrC,EAAAhB,sBAAA,GAAAC,UAAU,CAACiB,SAAS,CAAC,CAAC,CAAC,cAAAlB,sBAAA,uBAAvBA,sBAAA,CAAyBmB,MAAM,KAAI,qBAAqB,GACxDlB,UAAU,CAACzD,SAAS,IAAI,mBAAmB;cAC7CyB,OAAO,EAAEgC,UAAU,CAACmB,SAAS,IAAInB,UAAU,CAACoB,OAAO,IAAIpB,UAAU,CAAChC,OAAO,IAAI,sBAAsB;cACnGC,KAAK,EAAE,EAAE;cAAE;cACXoD,OAAO,EAAE9B,MAAM,CAAC;YAClB,CAAC;UACH,CAAC,CAAC;QACJ,CAAC,MAAM;UACL;UACAL,kBAAkB,GAAGzB,WAAW,CAAC6D,MAAM,CAAC/B,MAAM,IAC5CA,MAAM,CAAC5B,IAAI,CAAC4D,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC/F,WAAW,CAAC8F,WAAW,CAAC,CAAC,CAAC,IAC7DhC,MAAM,CAAC3B,KAAK,CAAC2D,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC/F,WAAW,CAAC8F,WAAW,CAAC,CAAC,CAAC,IAC9DhC,MAAM,CAAC9C,MAAM,CAACgF,IAAI,CAACC,KAAK,IAAIA,KAAK,CAACH,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC/F,WAAW,CAAC8F,WAAW,CAAC,CAAC,CAAC,CACrF,CAAC;QACH;QAEAzF,gBAAgB,CAACoD,kBAAkB,CAAC;QAEpC,IAAIH,IAAI,CAAC4C,UAAU,KAAK,SAAS,EAAE;UACjC1G,KAAK,CAAC2G,OAAO,CAAC,SAAS1C,kBAAkB,CAAC6B,MAAM,mCAAmC,CAAC;QACtF,CAAC,MAAM,IAAIhC,IAAI,CAAC4C,UAAU,KAAK,UAAU,EAAE;UACzC1G,KAAK,CAAC4G,IAAI,CAAC,SAAS3C,kBAAkB,CAAC6B,MAAM,yCAAyC,CAAC;QACzF,CAAC,MAAM;UACL9F,KAAK,CAAC2G,OAAO,CAAC,SAAS1C,kBAAkB,CAAC6B,MAAM,mBAAmB,CAAC;QACtE;;QAEA;QACA,IAAIhC,IAAI,CAAC+C,YAAY,IAAI/C,IAAI,CAAC+C,YAAY,KAAK,6BAA6B,EAAE;UAC5EC,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEjD,IAAI,CAAC+C,YAAY,CAAC;UAC/CC,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEjD,IAAI,CAACkD,WAAW,CAAC;QACjD;MACF,CAAC,MAAM;QACL,MAAM,IAAIC,KAAK,CAACnD,IAAI,CAACoD,KAAK,IAAI,eAAe,CAAC;MAChD;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdJ,OAAO,CAACI,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;;MAErC;MACA,MAAMC,eAAe,GAAG3E,WAAW,CAAC6D,MAAM,CAAC/B,MAAM,IAC/CA,MAAM,CAAC5B,IAAI,CAAC4D,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC/F,WAAW,CAAC8F,WAAW,CAAC,CAAC,CAAC,IAC7DhC,MAAM,CAAC3B,KAAK,CAAC2D,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC/F,WAAW,CAAC8F,WAAW,CAAC,CAAC,CAAC,IAC9DhC,MAAM,CAAC9C,MAAM,CAACgF,IAAI,CAACC,KAAK,IAAIA,KAAK,CAACH,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC/F,WAAW,CAAC8F,WAAW,CAAC,CAAC,CAAC,CACrF,CAAC;MAEDzF,gBAAgB,CAACsG,eAAe,CAAC;MACjCnH,KAAK,CAACqD,OAAO,CAAC,2BAA2B8D,eAAe,CAACrB,MAAM,mBAAmB,CAAC;IACrF,CAAC,SAAS;MACRnF,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;EAED,MAAMyG,aAAa,GAAG,MAAAA,CAAOC,IAAI,GAAG,CAAC,KAAK;IACxC1G,cAAc,CAAC,IAAI,CAAC;IACpB,IAAI;MACF;MACA,MAAM2C,QAAQ,GAAG,MAAMC,KAAK,CAAC,qBAAqB8D,IAAI,UAAU9E,cAAc,EAAE,CAAC;MACjF,MAAMuB,IAAI,GAAG,MAAMR,QAAQ,CAACS,IAAI,CAAC,CAAC;MAElC,IAAIT,QAAQ,CAACU,EAAE,EAAE;QACf,IAAIC,kBAAkB,GAAG,EAAE;QAE3B,IAAIH,IAAI,CAACwD,OAAO,IAAInD,KAAK,CAACC,OAAO,CAACN,IAAI,CAACwD,OAAO,CAAC,EAAE;UAC/C;UACArD,kBAAkB,GAAGH,IAAI,CAACwD,OAAO,CAACjD,GAAG,CAACC,MAAM,IAAI;YAAA,IAAAiD,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;YAC9C;YACA,MAAM/C,UAAU,GAAGT,MAAM,CAACU,MAAM,IAAIV,MAAM;YAE1C,OAAO;cACL7B,EAAE,EAAE6B,MAAM,CAACW,GAAG,IAAIC,IAAI,CAACC,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;cACzD3C,IAAI,EAAE,EAAA6E,sBAAA,GAAAxC,UAAU,CAACO,mBAAmB,cAAAiC,sBAAA,uBAA9BA,sBAAA,CAAgChC,QAAQ,KAAIR,UAAU,CAACrC,IAAI,IAAI,SAAS;cAC9EC,KAAK,EAAE,EAAA6E,sBAAA,GAAAzC,UAAU,CAACO,mBAAmB,cAAAkC,sBAAA,uBAA9BA,sBAAA,CAAgChC,WAAW,KAAIT,UAAU,CAACpC,KAAK,IAAI,UAAU;cACpFC,KAAK,EAAE,EAAA6E,sBAAA,GAAA1C,UAAU,CAACO,mBAAmB,cAAAmC,sBAAA,uBAA9BA,sBAAA,CAAgChC,KAAK,KAAIV,UAAU,CAACnC,KAAK,IAAI,UAAU;cAC9EC,KAAK,EAAE,EAAA6E,sBAAA,GAAA3C,UAAU,CAACO,mBAAmB,cAAAoC,sBAAA,uBAA9BA,sBAAA,CAAgChC,aAAa,KAAIX,UAAU,CAAClC,KAAK,IAAI,UAAU;cACtFtB,QAAQ,EAAE,EAAAoG,sBAAA,GAAA5C,UAAU,CAACO,mBAAmB,cAAAqC,sBAAA,uBAA9BA,sBAAA,CAAgChC,OAAO,KAAIZ,UAAU,CAACxD,QAAQ,IAAI,aAAa;cACzFuB,UAAU,EAAEiC,UAAU,CAACa,0BAA0B,GAC/C,GAAGb,UAAU,CAACa,0BAA0B,QAAQ,GAChD,CAAAgC,sBAAA,GAAA7C,UAAU,CAACc,cAAc,cAAA+B,sBAAA,eAAzBA,sBAAA,CAA2B9B,MAAM,GAC/B,GAAGf,UAAU,CAACc,cAAc,CAACC,MAAM,aAAa,GAChDf,UAAU,CAACjC,UAAU,IAAI,oBAAoB;cACjDtB,MAAM,EAAEuD,UAAU,CAACgB,MAAM,IAAIhB,UAAU,CAACvD,MAAM,IAAI,EAAE;cACpDF,SAAS,EAAE,CAAAuG,sBAAA,GAAA9C,UAAU,CAACiB,SAAS,cAAA6B,sBAAA,eAApBA,sBAAA,CAAsB/B,MAAM,GACrC,EAAAgC,sBAAA,GAAA/C,UAAU,CAACiB,SAAS,CAAC,CAAC,CAAC,cAAA8B,sBAAA,uBAAvBA,sBAAA,CAAyB7B,MAAM,KAAI,qBAAqB,GACxDlB,UAAU,CAACzD,SAAS,IAAI,mBAAmB;cAC7CyB,OAAO,EAAEgC,UAAU,CAACmB,SAAS,IAAInB,UAAU,CAACoB,OAAO,IAAIpB,UAAU,CAAChC,OAAO,IAAI,sBAAsB;cACnGC,KAAK,EAAE,EAAE;cAAE;cACXoD,OAAO,EAAE9B,MAAM,CAAC;YAClB,CAAC;UACH,CAAC,CAAC;QACJ,CAAC,MAAM;UACL;UACAL,kBAAkB,GAAGzB,WAAW;QAClC;QAEA3B,gBAAgB,CAACoD,kBAAkB,CAAC;QACpC/B,cAAc,CAACmF,IAAI,CAAC;QACpB/E,aAAa,CAACwB,IAAI,CAACiE,KAAK,IAAIjE,IAAI,CAACkE,KAAK,IAAI/D,kBAAkB,CAAC6B,MAAM,CAAC;QACpE1D,aAAa,CAAC8C,IAAI,CAAC+C,IAAI,CAAC,CAACnE,IAAI,CAACiE,KAAK,IAAIjE,IAAI,CAACkE,KAAK,IAAI/D,kBAAkB,CAAC6B,MAAM,IAAIvD,cAAc,CAAC,CAAC;QAClG9B,cAAc,CAAC,EAAE,CAAC;QAElB,IAAIqD,IAAI,CAACoE,MAAM,KAAK,UAAU,EAAE;UAC9BlI,KAAK,CAAC2G,OAAO,CAAC,gBAAgBU,IAAI,OAAOnC,IAAI,CAAC+C,IAAI,CAAC,CAACnE,IAAI,CAACiE,KAAK,IAAIjE,IAAI,CAACkE,KAAK,IAAI/D,kBAAkB,CAAC6B,MAAM,IAAIvD,cAAc,CAAC,KAAKuB,IAAI,CAACiE,KAAK,IAAIjE,IAAI,CAACkE,KAAK,iBAAiB,CAAC;QAC7K,CAAC,MAAM;UACLhI,KAAK,CAAC4G,IAAI,CAAC,WAAW3C,kBAAkB,CAAC6B,MAAM,UAAU,CAAC;QAC5D;MACF,CAAC,MAAM;QACL,MAAM,IAAImB,KAAK,CAACnD,IAAI,CAACoD,KAAK,IAAI,wBAAwB,CAAC;MACzD;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdJ,OAAO,CAACI,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;;MAE3C;MACArG,gBAAgB,CAAC2B,WAAW,CAAC;MAC7BN,cAAc,CAAC,CAAC,CAAC;MACjBI,aAAa,CAACE,WAAW,CAACsD,MAAM,CAAC;MACjC1D,aAAa,CAAC8C,IAAI,CAAC+C,IAAI,CAACzF,WAAW,CAACsD,MAAM,GAAGvD,cAAc,CAAC,CAAC;MAC7D9B,cAAc,CAAC,EAAE,CAAC;MAElBT,KAAK,CAACqD,OAAO,CAAC,6BAA6Bb,WAAW,CAACsD,MAAM,iBAAiB,CAAC;IACjF,CAAC,SAAS;MACRnF,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;EAED,MAAMwH,gBAAgB,GAAId,IAAI,IAAK;IACjC,IAAIA,IAAI,IAAI,CAAC,IAAIA,IAAI,IAAIlF,UAAU,EAAE;MACnCiF,aAAa,CAACC,IAAI,CAAC;IACrB;EACF,CAAC;EAED,MAAMe,eAAe,GAAGxH,aAAa,CAACyF,MAAM,CAACgC,MAAM,IAAI;IACrD;IACA,IAAIvH,WAAW,EAAE;MACf,MAAMwH,WAAW,GAAGD,MAAM,CAAC3F,IAAI,CAAC4D,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACzF,WAAW,CAACwF,WAAW,CAAC,CAAC,CAAC,IAC9D+B,MAAM,CAAC1F,KAAK,CAAC2D,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACzF,WAAW,CAACwF,WAAW,CAAC,CAAC,CAAC,IAC9D+B,MAAM,CAAC9G,QAAQ,CAAC+E,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACzF,WAAW,CAACwF,WAAW,CAAC,CAAC,CAAC;MACpF,IAAI,CAACgC,WAAW,EAAE,OAAO,KAAK;IAChC;;IAEA;IACA,MAAMC,OAAO,GAAGrH,eAAe;;IAE/B;IACA,IAAIqH,OAAO,CAACnH,aAAa,IAAImH,OAAO,CAAClH,aAAa,EAAE;MAAA,IAAAmH,eAAA,EAAAC,qBAAA;MAClD,MAAMC,eAAe,GAAG,EAAAF,eAAA,GAAAH,MAAM,CAACjC,OAAO,cAAAoC,eAAA,wBAAAC,qBAAA,GAAdD,eAAA,CAAgBxD,MAAM,cAAAyD,qBAAA,uBAAtBA,qBAAA,CAAwB7C,0BAA0B,KACnD+C,sBAAsB,CAACN,MAAM,CAACvF,UAAU,CAAC,IAAI,CAAC;MAErE,IAAIyF,OAAO,CAACnH,aAAa,IAAIsH,eAAe,GAAGE,QAAQ,CAACL,OAAO,CAACnH,aAAa,CAAC,EAAE,OAAO,KAAK;MAC5F,IAAImH,OAAO,CAAClH,aAAa,IAAIqH,eAAe,GAAGE,QAAQ,CAACL,OAAO,CAAClH,aAAa,CAAC,EAAE,OAAO,KAAK;IAC9F;;IAEA;IACA,IAAIkH,OAAO,CAACjH,SAAS,EAAE;MAAA,IAAAuH,gBAAA,EAAAC,qBAAA,EAAAC,sBAAA;MACrB,MAAMC,YAAY,GAAG,EAAAH,gBAAA,GAAAR,MAAM,CAACjC,OAAO,cAAAyC,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgB7D,MAAM,cAAA8D,qBAAA,wBAAAC,sBAAA,GAAtBD,qBAAA,CAAwB9C,SAAS,cAAA+C,sBAAA,uBAAjCA,sBAAA,CAAmCvC,IAAI,CAACyC,GAAG,IAC9D,CAACA,GAAG,CAAChD,MAAM,IAAI,EAAE,EAAEK,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACgC,OAAO,CAACjH,SAAS,CAACgF,WAAW,CAAC,CAAC,CAC3E,CAAC,KAAI,CAAC+B,MAAM,CAAC/G,SAAS,IAAI,EAAE,EAAEgF,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACgC,OAAO,CAACjH,SAAS,CAACgF,WAAW,CAAC,CAAC,CAAC;MACrF,IAAI,CAAC0C,YAAY,EAAE,OAAO,KAAK;IACjC;;IAEA;IACA,IAAIT,OAAO,CAAChH,QAAQ,EAAE;MACpB,MAAM2H,eAAe,GAAG,CAACb,MAAM,CAAC9G,QAAQ,IAAI,EAAE,EAAE+E,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACgC,OAAO,CAAChH,QAAQ,CAAC+E,WAAW,CAAC,CAAC,CAAC;MACtG,IAAI,CAAC4C,eAAe,EAAE,OAAO,KAAK;IACpC;;IAEA;IACA,IAAIX,OAAO,CAAC/G,MAAM,EAAE;MAAA,IAAA2H,cAAA,EAAAC,gBAAA,EAAAC,qBAAA;MAClB,MAAMC,UAAU,GAAGf,OAAO,CAAC/G,MAAM,CAAC8E,WAAW,CAAC,CAAC;MAC/C,MAAMiD,QAAQ,GAAG,EAAAJ,cAAA,GAAAd,MAAM,CAAC7G,MAAM,cAAA2H,cAAA,uBAAbA,cAAA,CAAe3C,IAAI,CAACC,KAAK,IAAIA,KAAK,CAACH,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC+C,UAAU,CAAC,CAAC,KACvE,CAAC,EAAAF,gBAAA,GAAAf,MAAM,CAACjC,OAAO,cAAAgD,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBpE,MAAM,cAAAqE,qBAAA,uBAAtBA,qBAAA,CAAwBtD,MAAM,KAAI,EAAE,EAAES,IAAI,CAACC,KAAK,IAC/CA,KAAK,CAACH,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC+C,UAAU,CACzC,CAAC;MACjB,IAAI,CAACC,QAAQ,EAAE,OAAO,KAAK;IAC7B;;IAEA;IACA,IAAIhB,OAAO,CAAC7G,WAAW,EAAE;MAAA,IAAA8H,gBAAA,EAAAC,qBAAA,EAAAC,sBAAA;MACvB,MAAMC,cAAc,IAAAH,gBAAA,GAAGnB,MAAM,CAACjC,OAAO,cAAAoD,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBxE,MAAM,cAAAyE,qBAAA,wBAAAC,sBAAA,GAAtBD,qBAAA,CAAwBzD,SAAS,cAAA0D,sBAAA,uBAAjCA,sBAAA,CAAmClD,IAAI,CAACyC,GAAG,IAChE,CAACA,GAAG,CAACW,WAAW,IAAIX,GAAG,CAACY,MAAM,IAAI,EAAE,EAAEvD,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACgC,OAAO,CAAC7G,WAAW,CAAC4E,WAAW,CAAC,CAAC,CAChG,CAAC;MACD,IAAI,CAACqD,cAAc,EAAE,OAAO,KAAK;IACnC;;IAEA;IACA,IAAIpB,OAAO,CAAC5G,cAAc,EAAE;MAAA,IAAAmI,gBAAA,EAAAC,qBAAA,EAAAC,sBAAA;MAC1B,MAAMC,WAAW,IAAAH,gBAAA,GAAGzB,MAAM,CAACjC,OAAO,cAAA0D,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgB9E,MAAM,cAAA+E,qBAAA,wBAAAC,sBAAA,GAAtBD,qBAAA,CAAwB/D,SAAS,cAAAgE,sBAAA,uBAAjCA,sBAAA,CAAmCxD,IAAI,CAACyC,GAAG,IAC7D,CAACA,GAAG,CAACiB,cAAc,IAAIjB,GAAG,CAACkB,IAAI,IAAI,EAAE,EAAE/E,QAAQ,CAAC,CAAC,CAACmB,QAAQ,CAACgC,OAAO,CAAC5G,cAAc,CACnF,CAAC;MACD,IAAI,CAACsI,WAAW,EAAE,OAAO,KAAK;IAChC;;IAEA;IACA,IAAI1B,OAAO,CAAC3G,OAAO,EAAE;MAAA,IAAAwI,gBAAA,EAAAC,qBAAA,EAAAC,sBAAA;MACnB,MAAMC,UAAU,IAAAH,gBAAA,GAAG/B,MAAM,CAACjC,OAAO,cAAAgE,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBpF,MAAM,cAAAqF,qBAAA,wBAAAC,sBAAA,GAAtBD,qBAAA,CAAwBxE,cAAc,cAAAyE,sBAAA,uBAAtCA,sBAAA,CAAwC9D,IAAI,CAACgE,GAAG,IACjE,CAACA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,WAAW,IAAIF,GAAG,CAACG,YAAY,IAAI,EAAE,EAAErE,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACgC,OAAO,CAAC3G,OAAO,CAAC0E,WAAW,CAAC,CAAC,CACjH,CAAC;MACD,IAAI,CAACiE,UAAU,EAAE,OAAO,KAAK;IAC/B;;IAEA;IACA,IAAIhC,OAAO,CAAC9G,QAAQ,EAAE;MAAA,IAAAmJ,gBAAA,EAAAC,qBAAA,EAAAC,sBAAA;MACpB,MAAMC,WAAW,IAAAH,gBAAA,GAAGvC,MAAM,CAACjC,OAAO,cAAAwE,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgB5F,MAAM,cAAA6F,qBAAA,wBAAAC,sBAAA,GAAtBD,qBAAA,CAAwB7E,SAAS,cAAA8E,sBAAA,uBAAjCA,sBAAA,CAAmCtE,IAAI,CAACyC,GAAG,IAAI;QACjE,MAAM+B,KAAK,GAAGC,UAAU,CAAChC,GAAG,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC;QACjD,OAAO+B,KAAK,IAAIC,UAAU,CAAC1C,OAAO,CAAC9G,QAAQ,CAAC;MAC9C,CAAC,CAAC;MACF,IAAI,CAACsJ,WAAW,EAAE,OAAO,KAAK;IAChC;IAEA,OAAO,IAAI;EACb,CAAC,CAAC;;EAEF;EACA,MAAMpC,sBAAsB,GAAIuC,cAAc,IAAK;IACjD,IAAI,CAACA,cAAc,EAAE,OAAO,CAAC;IAC7B,MAAMC,KAAK,GAAGD,cAAc,CAACC,KAAK,CAAC,oBAAoB,CAAC;IACxD,OAAOA,KAAK,GAAGvC,QAAQ,CAACuC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;EACvC,CAAC;EAED,MAAMC,0BAA0B,GAAGA,CAACC,UAAU,EAAEC,KAAK,KAAK;IACxDnK,kBAAkB,CAACoK,IAAI,KAAK;MAC1B,GAAGA,IAAI;MACP,CAACF,UAAU,GAAGC;IAChB,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAME,eAAe,GAAGA,CAAA,KAAM;IAC5BzK,cAAc,CAAC,EAAE,CAAC;IAClBI,kBAAkB,CAAC;MACjBC,aAAa,EAAE,EAAE;MACjBC,aAAa,EAAE,EAAE;MACjBC,SAAS,EAAE,EAAE;MACbC,QAAQ,EAAE,EAAE;MACZC,MAAM,EAAE,EAAE;MACVC,QAAQ,EAAE,EAAE;MACZC,WAAW,EAAE,EAAE;MACfC,cAAc,EAAE,EAAE;MAClBC,OAAO,EAAE;IACX,CAAC,CAAC;EACJ,CAAC;EAED,MAAM6J,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,OAAO3K,WAAW,IAAI4K,MAAM,CAACC,MAAM,CAACzK,eAAe,CAAC,CAACsF,IAAI,CAAC8E,KAAK,IAAIA,KAAK,KAAK,EAAE,CAAC;EAClF,CAAC;EAED,MAAMM,cAAc,GAAItH,MAAM,IAAK;IACjC,IAAI;MAAA,IAAAuH,eAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,qBAAA,EAAAC,sBAAA;MACF;MACA,MAAMC,aAAa,GAAG;AAC5B,EAAExI,MAAM,CAAC5B,IAAI;AACb,EAAE4B,MAAM,CAAC3B,KAAK;AACd,EAAE2B,MAAM,CAAC1B,KAAK,MAAM0B,MAAM,CAACzB,KAAK,MAAMyB,MAAM,CAAC/C,QAAQ;AACrD;AACA;AACA,EAAE+C,MAAM,CAACvB,OAAO;AAChB;AACA;AACA,EAAE,CAAA8I,eAAA,GAAAvH,MAAM,CAAC8B,OAAO,cAAAyF,eAAA,gBAAAC,qBAAA,GAAdD,eAAA,CAAgB7G,MAAM,cAAA8G,qBAAA,eAAtBA,qBAAA,CAAwBjG,cAAc,GACtCvB,MAAM,CAAC8B,OAAO,CAACpB,MAAM,CAACa,cAAc,CAACxB,GAAG,CAACmG,GAAG,IAC1C,GAAGA,GAAG,CAACuC,QAAQ,IAAIvC,GAAG,CAACwC,QAAQ,OAAOxC,GAAG,CAACC,OAAO,IAAID,GAAG,CAACG,YAAY;AACzE,YAAYH,GAAG,CAACyC,QAAQ,IAAIzC,GAAG,CAAC0C,KAAK,IAAI,eAAe;AACxD,EAAE1C,GAAG,CAAC2C,WAAW,GAAG,eAAe,GAAG3C,GAAG,CAAC2C,WAAW,GAAG,EAAE;AAC1D,EAAE3C,GAAG,CAAC4C,gBAAgB,GAAG,oBAAoB,GAAG5C,GAAG,CAAC4C,gBAAgB,GAAG,EAAE;AACzE,CACE,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC,GAAG/I,MAAM,CAACxB,UAAU;AAClC;AACA,EAAE,CAAAiJ,gBAAA,GAAAzH,MAAM,CAAC8B,OAAO,cAAA2F,gBAAA,gBAAAC,qBAAA,GAAdD,gBAAA,CAAgB/G,MAAM,cAAAgH,qBAAA,eAAtBA,qBAAA,CAAwBpG,0BAA0B,GAClD,qBAAqBtB,MAAM,CAAC8B,OAAO,CAACpB,MAAM,CAACY,0BAA0B,UAAU,GAAG,EAAE;AACtF;AACA;AACA,EAAE,CAAAqG,gBAAA,GAAA3H,MAAM,CAAC8B,OAAO,cAAA6F,gBAAA,gBAAAC,qBAAA,GAAdD,gBAAA,CAAgBjH,MAAM,cAAAkH,qBAAA,eAAtBA,qBAAA,CAAwBlG,SAAS,GACjC1B,MAAM,CAAC8B,OAAO,CAACpB,MAAM,CAACgB,SAAS,CAAC3B,GAAG,CAAC4E,GAAG,IACrC,GAAGA,GAAG,CAAChD,MAAM,IAAI,QAAQ,SAASgD,GAAG,CAACW,WAAW,IAAIX,GAAG,CAACY,MAAM,IAAI,aAAa;AACpF,mBAAmBZ,GAAG,CAACiB,cAAc,IAAIjB,GAAG,CAACkB,IAAI,IAAI,eAAe;AACpE,EAAElB,GAAG,CAAC,aAAa,CAAC,IAAIA,GAAG,CAAC,aAAa,CAAC,GAAG,CAAC,GAAG,SAAS,GAAGA,GAAG,CAAC,aAAa,CAAC,GAAG,GAAG,GAAG,EAAE;AAC1F,CACE,CAAC,CAACoE,IAAI,CAAC,IAAI,CAAC,GAAG/I,MAAM,CAAChD,SAAS;AACjC;AACA;AACA,EAAEgD,MAAM,CAAC9C,MAAM,CAAC6L,IAAI,CAAC,IAAI,CAAC;AAC1B;AACA,EAAE,CAAAlB,gBAAA,GAAA7H,MAAM,CAAC8B,OAAO,cAAA+F,gBAAA,gBAAAC,qBAAA,GAAdD,gBAAA,CAAgBnH,MAAM,cAAAoH,qBAAA,eAAtBA,qBAAA,CAAwBkB,SAAS,IAAIhJ,MAAM,CAAC8B,OAAO,CAACpB,MAAM,CAACsI,SAAS,CAACxH,MAAM,GAAG,CAAC,GAC/E,cAAcxB,MAAM,CAAC8B,OAAO,CAACpB,MAAM,CAACsI,SAAS,CAACD,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,EAAE;AACnE;AACA,EAAE,CAAAhB,gBAAA,GAAA/H,MAAM,CAAC8B,OAAO,cAAAiG,gBAAA,gBAAAC,qBAAA,GAAdD,gBAAA,CAAgBrH,MAAM,cAAAsH,qBAAA,eAAtBA,qBAAA,CAAwBiB,QAAQ,IAAIjJ,MAAM,CAAC8B,OAAO,CAACpB,MAAM,CAACuI,QAAQ,CAACzH,MAAM,GAAG,CAAC,GAC7E,aAAaxB,MAAM,CAAC8B,OAAO,CAACpB,MAAM,CAACuI,QAAQ,CAAClJ,GAAG,CAACmJ,OAAO,IACrD,GAAGA,OAAO,CAACC,IAAI,IAAID,OAAO,CAACE,KAAK,IAAI,SAAS;AACjD,EAAEF,OAAO,CAACL,WAAW,GAAG,eAAe,GAAGK,OAAO,CAACL,WAAW,GAAG,EAAE;AAClE,EAAEK,OAAO,CAACG,YAAY,GAAG,gBAAgB,GAAGH,OAAO,CAACG,YAAY,GAAG,EAAE;AACrE,CACE,CAAC,CAACN,IAAI,CAAC,IAAI,CAAC,EAAE,GAAG,EAAE;AACrB;AACA,EAAE,CAAAd,gBAAA,GAAAjI,MAAM,CAAC8B,OAAO,cAAAmG,gBAAA,gBAAAC,qBAAA,GAAdD,gBAAA,CAAgBvH,MAAM,cAAAwH,qBAAA,eAAtBA,qBAAA,CAAwBoB,cAAc,IAAItJ,MAAM,CAAC8B,OAAO,CAACpB,MAAM,CAAC4I,cAAc,CAAC9H,MAAM,GAAG,CAAC,GACzF,mBAAmBxB,MAAM,CAAC8B,OAAO,CAACpB,MAAM,CAAC4I,cAAc,CAACvJ,GAAG,CAACwJ,IAAI,IAC9D,GAAGA,IAAI,CAACJ,IAAI,IAAII,IAAI,CAACH,KAAK,IAAI,eAAe;AACjD,EAAEG,IAAI,CAACC,mBAAmB,GAAG,aAAa,GAAGD,IAAI,CAACC,mBAAmB,GAAG,EAAE;AAC1E,EAAED,IAAI,CAACE,SAAS,GAAG,QAAQ,GAAGF,IAAI,CAACE,SAAS,GAAG,EAAE;AACjD,CACE,CAAC,CAACV,IAAI,CAAC,IAAI,CAAC,EAAE,GAAG,EAAE;AACrB;AACA,EAAE,CAAAZ,gBAAA,GAAAnI,MAAM,CAAC8B,OAAO,cAAAqG,gBAAA,gBAAAC,qBAAA,GAAdD,gBAAA,CAAgBzH,MAAM,cAAA0H,qBAAA,eAAtBA,qBAAA,CAAwBsB,YAAY,IAAI1J,MAAM,CAAC8B,OAAO,CAACpB,MAAM,CAACgJ,YAAY,CAAClI,MAAM,GAAG,CAAC,GACrF,iBAAiBxB,MAAM,CAAC8B,OAAO,CAACpB,MAAM,CAACgJ,YAAY,CAAC3J,GAAG,CAAC4J,WAAW,IACjE,OAAOA,WAAW,KAAK,QAAQ,GAAGA,WAAW,GAC7C,GAAGA,WAAW,CAACC,eAAe,IAAID,WAAW,CAACR,IAAI,IAAI,aAAa;AACvE,EAAEQ,WAAW,CAACF,SAAS,GAAG,QAAQ,GAAGE,WAAW,CAACF,SAAS,GAAG,EAAE;AAC/D,EAAEE,WAAW,CAACH,mBAAmB,GAAG,gBAAgB,GAAGG,WAAW,CAACH,mBAAmB,GAAG,EAAE;AAC3F,EAAEG,WAAW,CAACd,WAAW,GAAG,eAAe,GAAGc,WAAW,CAACd,WAAW,GAAG,EAAE;AAC1E,CACE,CAAC,CAACE,IAAI,CAAC,IAAI,CAAC,EAAE,GAAG,EAAE;AACrB;AACA,EAAE,CAAAV,gBAAA,GAAArI,MAAM,CAAC8B,OAAO,cAAAuG,gBAAA,gBAAAC,qBAAA,GAAdD,gBAAA,CAAgB3H,MAAM,cAAA4H,qBAAA,gBAAAC,sBAAA,GAAtBD,qBAAA,CAAwBtH,mBAAmB,cAAAuH,sBAAA,eAA3CA,sBAAA,CAA6CsB,QAAQ,GACrD,qCAAqC7J,MAAM,CAAC8B,OAAO,CAACpB,MAAM,CAACM,mBAAmB,CAAC6I,QAAQ,EAAE,GAAG,EAAE;AAChG;AACA,gBAAgB,IAAIC,IAAI,CAAC,CAAC,CAACC,kBAAkB,CAAC,CAAC;AAC/C,OAAO,CAACjL,IAAI,CAAC,CAAC;;MAER;MACA,MAAMkL,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACzB,aAAa,CAAC,EAAE;QAAE0B,IAAI,EAAE;MAAa,CAAC,CAAC;MAC9D,MAAMC,GAAG,GAAGC,GAAG,CAACC,eAAe,CAACL,IAAI,CAAC;MACrC,MAAMM,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;MACxCF,IAAI,CAACG,IAAI,GAAGN,GAAG;MACfG,IAAI,CAACI,QAAQ,GAAG,GAAG1K,MAAM,CAAC5B,IAAI,CAACuM,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,aAAa;MAChEJ,QAAQ,CAACnL,IAAI,CAACwL,WAAW,CAACN,IAAI,CAAC;MAC/BA,IAAI,CAACO,KAAK,CAAC,CAAC;MACZN,QAAQ,CAACnL,IAAI,CAAC0L,WAAW,CAACR,IAAI,CAAC;MAC/BF,GAAG,CAACW,eAAe,CAACZ,GAAG,CAAC;MAExBzO,KAAK,CAAC2G,OAAO,CAAC,yBAAyBrC,MAAM,CAAC5B,IAAI,EAAE,CAAC;IACvD,CAAC,CAAC,OAAOwE,KAAK,EAAE;MACdJ,OAAO,CAACI,KAAK,CAAC,iBAAiB,EAAEA,KAAK,CAAC;MACvClH,KAAK,CAACkH,KAAK,CAAC,8CAA8C,CAAC;IAC7D;EACF,CAAC;EAED,MAAMoI,UAAU,GAAIhL,MAAM,IAAK;IAC7BxC,iBAAiB,CAACwC,MAAM,CAAC;IACzBtC,cAAc,CAAC,IAAI,CAAC;IACpBhC,KAAK,CAAC4G,IAAI,CAAC,6BAA6BtC,MAAM,CAAC5B,IAAI,EAAE,CAAC;EACxD,CAAC;EAED,MAAM6M,gBAAgB,GAAGA,CAAA,KAAM;IAC7BvN,cAAc,CAAC,KAAK,CAAC;IACrBF,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;EAED,MAAM0N,gBAAgB,GAAGA,CAAC5M,KAAK,EAAEF,IAAI,KAAK;IACxC+M,MAAM,CAACC,IAAI,CAAC,UAAU9M,KAAK,0CAA0CF,IAAI,6GAA6G,EAAE,QAAQ,CAAC;IACjM1C,KAAK,CAAC4G,IAAI,CAAC,mCAAmClE,IAAI,EAAE,CAAC;EACvD,CAAC;EAED,MAAMiN,gBAAgB,GAAGA,CAAC9M,KAAK,EAAEH,IAAI,KAAK;IACxC,IAAIkN,SAAS,CAACC,SAAS,CAAC1E,KAAK,CAAC,4BAA4B,CAAC,EAAE;MAC3DsE,MAAM,CAACC,IAAI,CAAC,OAAO7M,KAAK,EAAE,EAAE,QAAQ,CAAC;IACvC,CAAC,MAAM;MACL+M,SAAS,CAACE,SAAS,CAACC,SAAS,CAAClN,KAAK,CAAC,CAACmN,IAAI,CAAC,MAAM;QAC9ChQ,KAAK,CAAC2G,OAAO,CAAC,wBAAwB9D,KAAK,EAAE,CAAC;MAChD,CAAC,CAAC,CAACoN,KAAK,CAAC,MAAM;QACbjQ,KAAK,CAAC4G,IAAI,CAAC,UAAU/D,KAAK,EAAE,CAAC;MAC/B,CAAC,CAAC;IACJ;EACF,CAAC;EAID,MAAMqN,eAAe,GAAGA,CAAA,KAAM;IAC5B,IAAI9H,eAAe,CAACtC,MAAM,KAAK,CAAC,EAAE;MAChC9F,KAAK,CAACqD,OAAO,CAAC,sBAAsB,CAAC;MACrC;IACF;IAEA,IAAI;MACF,MAAM8M,UAAU,GAAG/H,eAAe,CAAC/D,GAAG,CAACC,MAAM,KAAK;QAChD5B,IAAI,EAAE4B,MAAM,CAAC5B,IAAI;QACjBC,KAAK,EAAE2B,MAAM,CAAC3B,KAAK;QACnBC,KAAK,EAAE0B,MAAM,CAAC1B,KAAK;QACnBC,KAAK,EAAEyB,MAAM,CAACzB,KAAK;QACnBtB,QAAQ,EAAE+C,MAAM,CAAC/C,QAAQ;QACzBuB,UAAU,EAAEwB,MAAM,CAACxB,UAAU;QAC7BxB,SAAS,EAAEgD,MAAM,CAAChD,SAAS;QAC3BE,MAAM,EAAE8C,MAAM,CAAC9C,MAAM,CAAC6L,IAAI,CAAC,IAAI,CAAC;QAChCtK,OAAO,EAAEuB,MAAM,CAACvB,OAAO;QACvBqN,UAAU,EAAE9L,MAAM,CAACtB;MACrB,CAAC,CAAC,CAAC;MAEH,MAAMqN,UAAU,GAAG,CACjB,iFAAiF,EACjF,GAAGF,UAAU,CAAC9L,GAAG,CAACiM,GAAG,IACnB5E,MAAM,CAACC,MAAM,CAAC2E,GAAG,CAAC,CAACjM,GAAG,CAACiH,KAAK,IAC1B,IAAIiF,MAAM,CAACjF,KAAK,CAAC,CAAC2D,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,GACvC,CAAC,CAAC5B,IAAI,CAAC,GAAG,CACZ,CAAC,CACF,CAACA,IAAI,CAAC,IAAI,CAAC;MAEZ,MAAMiB,IAAI,GAAG,IAAIC,IAAI,CAAC,CAAC8B,UAAU,CAAC,EAAE;QAAE7B,IAAI,EAAE;MAAW,CAAC,CAAC;MACzD,MAAMC,GAAG,GAAGC,GAAG,CAACC,eAAe,CAACL,IAAI,CAAC;MACrC,MAAMM,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;MACxCF,IAAI,CAACG,IAAI,GAAGN,GAAG;MACfG,IAAI,CAACI,QAAQ,GAAG,yBAAyB,IAAIZ,IAAI,CAAC,CAAC,CAACoC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM;MACrF5B,QAAQ,CAACnL,IAAI,CAACwL,WAAW,CAACN,IAAI,CAAC;MAC/BA,IAAI,CAACO,KAAK,CAAC,CAAC;MACZN,QAAQ,CAACnL,IAAI,CAAC0L,WAAW,CAACR,IAAI,CAAC;MAC/BF,GAAG,CAACW,eAAe,CAACZ,GAAG,CAAC;MAExBzO,KAAK,CAAC2G,OAAO,CAAC,YAAYyB,eAAe,CAACtC,MAAM,iBAAiB,CAAC;IACpE,CAAC,CAAC,OAAOoB,KAAK,EAAE;MACdJ,OAAO,CAACI,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;MACrClH,KAAK,CAACkH,KAAK,CAAC,6CAA6C,CAAC;IAC5D;EACF,CAAC;EAED,oBACE/G,OAAA;IAAKuQ,SAAS,EAAC,aAAa;IAAAC,QAAA,gBAC1BxQ,OAAA;MAAKuQ,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1BxQ,OAAA;QAAAwQ,QAAA,EAAI;MAAc;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACvB5Q,OAAA;QAAAwQ,QAAA,EAAG;MAAkE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtE,CAAC,eAGN5Q,OAAA;MAAKuQ,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAC7BxQ,OAAA;QAAM6Q,QAAQ,EAAE/N,YAAa;QAACyN,SAAS,EAAC,aAAa;QAAAC,QAAA,eACnDxQ,OAAA;UAAKuQ,SAAS,EAAC,oBAAoB;UAAAC,QAAA,gBACjCxQ,OAAA;YAAKuQ,SAAS,EAAC,sBAAsB;YAAAC,QAAA,gBACnCxQ,OAAA,CAACjB,QAAQ;cAACwR,SAAS,EAAC;YAAa;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACpC5Q,OAAA;cACEqO,IAAI,EAAC,MAAM;cACXyC,WAAW,EAAC,oGAAoG;cAChH3F,KAAK,EAAE9K,WAAY;cACnB0Q,QAAQ,EAAGhO,CAAC,IAAKzC,cAAc,CAACyC,CAAC,CAACiO,MAAM,CAAC7F,KAAK,CAAE;cAChDoF,SAAS,EAAC,cAAc;cACxBU,QAAQ,EAAE1Q;YAAY;cAAAkQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACN5Q,OAAA;YAAKuQ,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7BxQ,OAAA;cACEqO,IAAI,EAAC,QAAQ;cACbkC,SAAS,EAAC,iBAAiB;cAC3BU,QAAQ,EAAE1Q,WAAY;cAAAiQ,QAAA,EAErBjQ,WAAW,gBACVP,OAAA,CAAAE,SAAA;gBAAAsQ,QAAA,gBACExQ,OAAA;kBAAKuQ,SAAS,EAAC;gBAAiB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gBAErC;cAAA,eAAE,CAAC,gBAEH5Q,OAAA,CAAAE,SAAA;gBAAAsQ,QAAA,gBACExQ,OAAA,CAACjB,QAAQ;kBAACmS,IAAI,EAAE;gBAAG;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,UAExB;cAAA,eAAE;YACH;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK,CAAC,eACT5Q,OAAA;cACEqO,IAAI,EAAC,QAAQ;cACbkC,SAAS,EAAC,mBAAmB;cAC7BY,OAAO,EAAElK,aAAc;cACvBgK,QAAQ,EAAE1Q,WAAY;cAAAiQ,QAAA,EACvB;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,EAGNnQ,aAAa,CAACkF,MAAM,GAAG,CAAC,iBACvB3F,OAAA;QAAKuQ,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7BxQ,OAAA;UAAKuQ,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5BxQ,OAAA;YAAKuQ,SAAS,EAAC,sBAAsB;YAAAC,QAAA,gBACnCxQ,OAAA,CAAChB,QAAQ;cAACuR,SAAS,EAAC;YAAa;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACpC5Q,OAAA;cACEqO,IAAI,EAAC,MAAM;cACXyC,WAAW,EAAC,6CAA6C;cACzD3F,KAAK,EAAExK,WAAY;cACnBoQ,QAAQ,EAAGhO,CAAC,IAAKnC,cAAc,CAACmC,CAAC,CAACiO,MAAM,CAAC7F,KAAK,CAAE;cAChDoF,SAAS,EAAC;YAAc;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACN5Q,OAAA;YAAKuQ,SAAS,EAAC,iBAAiB;YAAAC,QAAA,gBAC9BxQ,OAAA;cACEqO,IAAI,EAAC,QAAQ;cACbkC,SAAS,EAAE,mCAAmC1P,WAAW,GAAG,QAAQ,GAAG,EAAE,EAAG;cAC5EsQ,OAAO,EAAEA,CAAA,KAAMrQ,cAAc,CAAC,CAACD,WAAW,CAAE;cAAA2P,QAAA,gBAE5CxQ,OAAA,CAACN,UAAU;gBAACwR,IAAI,EAAE;cAAG;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,oBAExB,EAAC/P,WAAW,gBAAGb,OAAA,CAACJ,WAAW;gBAACsR,IAAI,EAAE;cAAG;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAAG5Q,OAAA,CAACL,aAAa;gBAACuR,IAAI,EAAE;cAAG;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChE,CAAC,EACRtF,gBAAgB,CAAC,CAAC,iBACjBtL,OAAA;cACEqO,IAAI,EAAC,QAAQ;cACbkC,SAAS,EAAC,+BAA+B;cACzCY,OAAO,EAAE9F,eAAgB;cAAAmF,QAAA,gBAEzBxQ,OAAA,CAACP,GAAG;gBAACyR,IAAI,EAAE;cAAG;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,aAEnB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CACT;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAGL/P,WAAW,iBACVb,OAAA;UAAKuQ,SAAS,EAAC,kBAAkB;UAAAC,QAAA,eAC/BxQ,OAAA;YAAKuQ,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BxQ,OAAA;cAAKuQ,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3BxQ,OAAA;gBAAAwQ,QAAA,EAAO;cAAkB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACjC5Q,OAAA;gBAAKuQ,SAAS,EAAC,cAAc;gBAAAC,QAAA,gBAC3BxQ,OAAA;kBACEqO,IAAI,EAAC,QAAQ;kBACbyC,WAAW,EAAC,KAAK;kBACjB3F,KAAK,EAAEpK,eAAe,CAACE,aAAc;kBACrC8P,QAAQ,EAAGhO,CAAC,IAAKkI,0BAA0B,CAAC,eAAe,EAAElI,CAAC,CAACiO,MAAM,CAAC7F,KAAK,CAAE;kBAC7EoF,SAAS,EAAC,oBAAoB;kBAC9Ba,GAAG,EAAC;gBAAG;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR,CAAC,eACF5Q,OAAA;kBAAAwQ,QAAA,EAAM;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACf5Q,OAAA;kBACEqO,IAAI,EAAC,QAAQ;kBACbyC,WAAW,EAAC,KAAK;kBACjB3F,KAAK,EAAEpK,eAAe,CAACG,aAAc;kBACrC6P,QAAQ,EAAGhO,CAAC,IAAKkI,0BAA0B,CAAC,eAAe,EAAElI,CAAC,CAACiO,MAAM,CAAC7F,KAAK,CAAE;kBAC7EoF,SAAS,EAAC,oBAAoB;kBAC9Ba,GAAG,EAAC;gBAAG;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN5Q,OAAA;cAAKuQ,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3BxQ,OAAA;gBAAAwQ,QAAA,EAAO;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC9B5Q,OAAA;gBACEqO,IAAI,EAAC,MAAM;gBACXyC,WAAW,EAAC,6BAA6B;gBACzC3F,KAAK,EAAEpK,eAAe,CAACI,SAAU;gBACjC4P,QAAQ,EAAGhO,CAAC,IAAKkI,0BAA0B,CAAC,WAAW,EAAElI,CAAC,CAACiO,MAAM,CAAC7F,KAAK,CAAE;gBACzEoF,SAAS,EAAC;cAAc;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAEN5Q,OAAA;cAAKuQ,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3BxQ,OAAA;gBAAAwQ,QAAA,EAAO;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACvB5Q,OAAA;gBACEqO,IAAI,EAAC,MAAM;gBACXyC,WAAW,EAAC,gCAAgC;gBAC5C3F,KAAK,EAAEpK,eAAe,CAACK,QAAS;gBAChC2P,QAAQ,EAAGhO,CAAC,IAAKkI,0BAA0B,CAAC,UAAU,EAAElI,CAAC,CAACiO,MAAM,CAAC7F,KAAK,CAAE;gBACxEoF,SAAS,EAAC;cAAc;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAEN5Q,OAAA;cAAKuQ,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3BxQ,OAAA;gBAAAwQ,QAAA,EAAO;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACrB5Q,OAAA;gBACEqO,IAAI,EAAC,MAAM;gBACXyC,WAAW,EAAC,2BAA2B;gBACvC3F,KAAK,EAAEpK,eAAe,CAACM,MAAO;gBAC9B0P,QAAQ,EAAGhO,CAAC,IAAKkI,0BAA0B,CAAC,QAAQ,EAAElI,CAAC,CAACiO,MAAM,CAAC7F,KAAK,CAAE;gBACtEoF,SAAS,EAAC;cAAc;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAEN5Q,OAAA;cAAKuQ,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3BxQ,OAAA;gBAAAwQ,QAAA,EAAO;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC1B5Q,OAAA;gBACEqO,IAAI,EAAC,MAAM;gBACXyC,WAAW,EAAC,4BAA4B;gBACxC3F,KAAK,EAAEpK,eAAe,CAACQ,WAAY;gBACnCwP,QAAQ,EAAGhO,CAAC,IAAKkI,0BAA0B,CAAC,aAAa,EAAElI,CAAC,CAACiO,MAAM,CAAC7F,KAAK,CAAE;gBAC3EoF,SAAS,EAAC;cAAc;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAEN5Q,OAAA;cAAKuQ,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3BxQ,OAAA;gBAAAwQ,QAAA,EAAO;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC9B5Q,OAAA;gBACEqO,IAAI,EAAC,MAAM;gBACXyC,WAAW,EAAC,kBAAkB;gBAC9B3F,KAAK,EAAEpK,eAAe,CAACS,cAAe;gBACtCuP,QAAQ,EAAGhO,CAAC,IAAKkI,0BAA0B,CAAC,gBAAgB,EAAElI,CAAC,CAACiO,MAAM,CAAC7F,KAAK,CAAE;gBAC9EoF,SAAS,EAAC;cAAc;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAEN5Q,OAAA;cAAKuQ,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3BxQ,OAAA;gBAAAwQ,QAAA,EAAO;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACtB5Q,OAAA;gBACEqO,IAAI,EAAC,MAAM;gBACXyC,WAAW,EAAC,8BAA8B;gBAC1C3F,KAAK,EAAEpK,eAAe,CAACU,OAAQ;gBAC/BsP,QAAQ,EAAGhO,CAAC,IAAKkI,0BAA0B,CAAC,SAAS,EAAElI,CAAC,CAACiO,MAAM,CAAC7F,KAAK,CAAE;gBACvEoF,SAAS,EAAC;cAAc;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAEN5Q,OAAA;cAAKuQ,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3BxQ,OAAA;gBAAAwQ,QAAA,EAAO;cAAiB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAChC5Q,OAAA;gBACEqO,IAAI,EAAC,QAAQ;gBACbyC,WAAW,EAAC,cAAc;gBAC1B3F,KAAK,EAAEpK,eAAe,CAACO,QAAS;gBAChCyP,QAAQ,EAAGhO,CAAC,IAAKkI,0BAA0B,CAAC,UAAU,EAAElI,CAAC,CAACiO,MAAM,CAAC7F,KAAK,CAAE;gBACxEoF,SAAS,EAAC,cAAc;gBACxBa,GAAG,EAAC,GAAG;gBACPC,GAAG,EAAC;cAAK;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,eAED5Q,OAAA;UAAKuQ,SAAS,EAAC,eAAe;UAAAC,QAAA,GAC3BvI,eAAe,CAACtC,MAAM,EAAC,MAAI,EAAClF,aAAa,CAACkF,MAAM,EAAC,UAClD,EAAC2F,gBAAgB,CAAC,CAAC,iBAAItL,OAAA;YAAMuQ,SAAS,EAAC,oBAAoB;YAAAC,QAAA,EAAC;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3E,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAGLnQ,aAAa,CAACkF,MAAM,GAAG,CAAC,gBACvB3F,OAAA;MAAKuQ,SAAS,EAAC,iBAAiB;MAAAC,QAAA,gBAC9BxQ,OAAA;QAAKuQ,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7BxQ,OAAA;UAAAwQ,QAAA,EAAI;QAAc;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACvB5Q,OAAA;UAAKuQ,SAAS,EAAC,iBAAiB;UAAAC,QAAA,eAC9BxQ,OAAA;YACEuQ,SAAS,EAAC,mBAAmB;YAC7BY,OAAO,EAAEpB,eAAgB;YACzBvN,KAAK,EAAE,UAAUyF,eAAe,CAACtC,MAAM,iBAAkB;YAAA6K,QAAA,gBAEzDxQ,OAAA,CAACf,UAAU;cAACiS,IAAI,EAAE;YAAG;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBACZ,EAAC3I,eAAe,CAACtC,MAAM,EAAC,GACtC;UAAA;YAAA8K,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN5Q,OAAA;QAAKuQ,SAAS,EAAC,cAAc;QAAAC,QAAA,EAC1BvI,eAAe,CAAC/D,GAAG,CAAEC,MAAM,iBAC1BnE,OAAA;UAAqBuQ,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1CxQ,OAAA;YAAKuQ,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5BxQ,OAAA;cAAKuQ,SAAS,EAAC,eAAe;cAAAC,QAAA,eAC5BxQ,OAAA,CAACb,MAAM;gBAAC+R,IAAI,EAAE;cAAG;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjB,CAAC,eACN5Q,OAAA;cAAKuQ,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BxQ,OAAA;gBAAIuQ,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAErM,MAAM,CAAC5B;cAAI;gBAAAkO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC9C5Q,OAAA;gBAAGuQ,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAErM,MAAM,CAAC3B;cAAK;gBAAAiO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN5Q,OAAA;YAAKuQ,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7BxQ,OAAA;cAAKuQ,SAAS,EAAC,2BAA2B;cAAAC,QAAA,gBACxCxQ,OAAA,CAACZ,QAAQ;gBAAC8R,IAAI,EAAE;cAAG;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACtB5Q,OAAA;gBAAAwQ,QAAA,EAAOrM,MAAM,CAAC/C;cAAQ;gBAAAqP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3B,CAAC,eACN5Q,OAAA;cACEuQ,SAAS,EAAC,kCAAkC;cAC5CY,OAAO,EAAEA,CAAA,KAAM9B,gBAAgB,CAAClL,MAAM,CAAC1B,KAAK,EAAE0B,MAAM,CAAC5B,IAAI,CAAE;cAC3DC,KAAK,EAAE,iBAAiB2B,MAAM,CAAC5B,IAAI,EAAG;cAAAiO,QAAA,gBAEtCxQ,OAAA,CAACX,MAAM;gBAAC6R,IAAI,EAAE;cAAG;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACpB5Q,OAAA;gBAAAwQ,QAAA,EAAOrM,MAAM,CAAC1B;cAAK;gBAAAgO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC3B5Q,OAAA,CAACT,cAAc;gBAAC2R,IAAI,EAAE,EAAG;gBAACX,SAAS,EAAC;cAAe;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnD,CAAC,eACN5Q,OAAA;cACEuQ,SAAS,EAAC,kCAAkC;cAC5CY,OAAO,EAAEA,CAAA,KAAM3B,gBAAgB,CAACrL,MAAM,CAACzB,KAAK,EAAEyB,MAAM,CAAC5B,IAAI,CAAE;cAC3DC,KAAK,EAAE,QAAQ2B,MAAM,CAAC5B,IAAI,EAAG;cAAAiO,QAAA,gBAE7BxQ,OAAA,CAACV,OAAO;gBAAC4R,IAAI,EAAE;cAAG;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACrB5Q,OAAA;gBAAAwQ,QAAA,EAAOrM,MAAM,CAACzB;cAAK;gBAAA+N,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC3B5Q,OAAA,CAACT,cAAc;gBAAC2R,IAAI,EAAE,EAAG;gBAACX,SAAS,EAAC;cAAe;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN5Q,OAAA;YAAKuQ,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7BxQ,OAAA;cAAKuQ,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BxQ,OAAA;gBAAKuQ,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,gBAC9BxQ,OAAA;kBAAAwQ,QAAA,EAAI;gBAAU;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACnB5Q,OAAA;kBAAAwQ,QAAA,EAAIrM,MAAM,CAACxB;gBAAU;kBAAA8N,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvB,CAAC,eACN5Q,OAAA;gBAAKuQ,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,gBAC9BxQ,OAAA;kBAAAwQ,QAAA,EAAI;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAClB5Q,OAAA;kBAAAwQ,QAAA,EAAIrM,MAAM,CAAChD;gBAAS;kBAAAsP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACN5Q,OAAA;cAAKuQ,SAAS,EAAC,iBAAiB;cAAAC,QAAA,gBAC9BxQ,OAAA;gBAAAwQ,QAAA,EAAI;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACf5Q,OAAA;gBAAKuQ,SAAS,EAAC,aAAa;gBAAAC,QAAA,EACzBrM,MAAM,CAAC9C,MAAM,CAAC6C,GAAG,CAAC,CAACoC,KAAK,EAAEgL,KAAK,kBAC9BtR,OAAA;kBAAkBuQ,SAAS,EAAC,WAAW;kBAAAC,QAAA,EAAElK;gBAAK,GAAnCgL,KAAK;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAqC,CACtD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN5Q,OAAA;YAAKuQ,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7BxQ,OAAA;cACEuQ,SAAS,EAAC,mBAAmB;cAC7BY,OAAO,EAAEA,CAAA,KAAMhC,UAAU,CAAChL,MAAM,CAAE;cAAAqM,QAAA,gBAElCxQ,OAAA,CAACd,KAAK;gBAACgS,IAAI,EAAE;cAAG;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAErB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACT5Q,OAAA;cACEuQ,SAAS,EAAC,iBAAiB;cAC3BY,OAAO,EAAEA,CAAA,KAAM1F,cAAc,CAACtH,MAAM,CAAE;cAAAqM,QAAA,gBAEtCxQ,OAAA,CAACf,UAAU;gBAACiS,IAAI,EAAE;cAAG;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,YAE1B;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA,GAxEEzM,MAAM,CAAC7B,EAAE;UAAAmO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAyEd,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,EAGL5O,UAAU,GAAG,CAAC,iBACbhC,OAAA;QAAKuQ,SAAS,EAAC,oBAAoB;QAAAC,QAAA,gBACjCxQ,OAAA;UAAKuQ,SAAS,EAAC,iBAAiB;UAAAC,QAAA,GAAC,UACvB,EAAE,CAAC1O,WAAW,GAAG,CAAC,IAAIM,cAAc,GAAI,CAAC,EAAC,MAAI,EAAC2C,IAAI,CAACqM,GAAG,CAACtP,WAAW,GAAGM,cAAc,EAAEF,UAAU,CAAC,EAAC,MAAI,EAACA,UAAU,EAAC,UAC5H;QAAA;UAAAuO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACN5Q,OAAA;UAAKuQ,SAAS,EAAC,qBAAqB;UAAAC,QAAA,gBAClCxQ,OAAA;YACEuQ,SAAS,EAAC,kCAAkC;YAC5CY,OAAO,EAAEA,CAAA,KAAMnJ,gBAAgB,CAAClG,WAAW,GAAG,CAAC,CAAE;YACjDmP,QAAQ,EAAEnP,WAAW,KAAK,CAAE;YAAA0O,QAAA,EAC7B;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAET5Q,OAAA;YAAKuQ,SAAS,EAAC,cAAc;YAAAC,QAAA,EAC1BxM,KAAK,CAACuN,IAAI,CAAC;cAAE5L,MAAM,EAAEZ,IAAI,CAACqM,GAAG,CAAC,CAAC,EAAEpP,UAAU;YAAE,CAAC,EAAE,CAACwP,CAAC,EAAEC,CAAC,KAAK;cACzD,IAAIC,OAAO;cACX,IAAI1P,UAAU,IAAI,CAAC,EAAE;gBACnB0P,OAAO,GAAGD,CAAC,GAAG,CAAC;cACjB,CAAC,MAAM,IAAI3P,WAAW,IAAI,CAAC,EAAE;gBAC3B4P,OAAO,GAAGD,CAAC,GAAG,CAAC;cACjB,CAAC,MAAM,IAAI3P,WAAW,IAAIE,UAAU,GAAG,CAAC,EAAE;gBACxC0P,OAAO,GAAG1P,UAAU,GAAG,CAAC,GAAGyP,CAAC;cAC9B,CAAC,MAAM;gBACLC,OAAO,GAAG5P,WAAW,GAAG,CAAC,GAAG2P,CAAC;cAC/B;cAEA,oBACEzR,OAAA;gBAEEuQ,SAAS,EAAE,sBAAsBzO,WAAW,KAAK4P,OAAO,GAAG,aAAa,GAAG,eAAe,EAAG;gBAC7FP,OAAO,EAAEA,CAAA,KAAMnJ,gBAAgB,CAAC0J,OAAO,CAAE;gBAAAlB,QAAA,EAExCkB;cAAO,GAJHA,OAAO;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAKN,CAAC;YAEb,CAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAEN5Q,OAAA;YACEuQ,SAAS,EAAC,kCAAkC;YAC5CY,OAAO,EAAEA,CAAA,KAAMnJ,gBAAgB,CAAClG,WAAW,GAAG,CAAC,CAAE;YACjDmP,QAAQ,EAAEnP,WAAW,KAAKE,UAAW;YAAAwO,QAAA,EACtC;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,GACJ,CAACrQ,WAAW,gBACdP,OAAA;MAAKuQ,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1BxQ,OAAA;QAAKuQ,SAAS,EAAC,YAAY;QAAAC,QAAA,eACzBxQ,OAAA,CAACjB,QAAQ;UAACmS,IAAI,EAAE;QAAG;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnB,CAAC,eACN5Q,OAAA;QAAAwQ,QAAA,EAAI;MAAgB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACzB5Q,OAAA;QAAAwQ,QAAA,EAAG;MAAyH;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eAChI5Q,OAAA;QAAKuQ,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9BxQ,OAAA;UAAAwQ,QAAA,EAAI;QAAkB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC3B5Q,OAAA;UAAAwQ,QAAA,gBACExQ,OAAA;YAAAwQ,QAAA,EAAI;UAAwB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACjC5Q,OAAA;YAAAwQ,QAAA,EAAI;UAAwC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACjD5Q,OAAA;YAAAwQ,QAAA,EAAI;UAA8B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,GACJ,IAAI,eAGR5Q,OAAA,CAACF,WAAW;MACVqE,MAAM,EAAEzC,cAAe;MACvBiQ,MAAM,EAAE/P,WAAY;MACpBgQ,OAAO,EAAExC;IAAiB;MAAAqB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3B,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAACxQ,EAAA,CAn4BID,aAAa;AAAA0R,EAAA,GAAb1R,aAAa;AAq4BnB,eAAeA,aAAa;AAAC,IAAA0R,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}