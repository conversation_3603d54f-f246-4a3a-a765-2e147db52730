{"ast": null, "code": "var _jsxFileName = \"D:\\\\Projects\\\\SQL-Agent-Task-Submission-main\\\\SQL-Agent-Task-Submission-main\\\\src\\\\pages\\\\ActivityLog.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { FiActivity, FiUpload, FiSearch, FiFileText, FiUsers, FiClock } from 'react-icons/fi';\nimport './ActivityLog.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ActivityLog = () => {\n  _s();\n  const [activities, setActivities] = useState([]);\n  const [loading, setLoading] = useState(true);\n  useEffect(() => {\n    // Simulate loading data\n    setTimeout(() => {\n      setActivities([{\n        id: 1,\n        type: 'upload',\n        action: 'Resume uploaded',\n        detail: 'john_doe_resume.pdf',\n        time: '2 minutes ago',\n        user: 'Admin'\n      }, {\n        id: 2,\n        type: 'search',\n        action: 'Search completed',\n        detail: 'Python developers (15 results)',\n        time: '5 minutes ago',\n        user: 'Admin'\n      }, {\n        id: 3,\n        type: 'process',\n        action: 'Resume processed',\n        detail: 'jane_smith_cv.pdf',\n        time: '10 minutes ago',\n        user: 'System'\n      }, {\n        id: 4,\n        type: 'bulk',\n        action: 'Bulk upload completed',\n        detail: '5 files processed',\n        time: '1 hour ago',\n        user: 'Admin'\n      }, {\n        id: 5,\n        type: 'search',\n        action: 'Search completed',\n        detail: 'UI/UX designers (8 results)',\n        time: '2 hours ago',\n        user: 'Admin'\n      }]);\n      setLoading(false);\n    }, 1000);\n  }, []);\n  const getActivityIcon = type => {\n    switch (type) {\n      case 'upload':\n        return /*#__PURE__*/_jsxDEV(FiUpload, {\n          className: \"activity-icon upload\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 25,\n          columnNumber: 29\n        }, this);\n      case 'search':\n        return /*#__PURE__*/_jsxDEV(FiSearch, {\n          className: \"activity-icon search\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 26,\n          columnNumber: 29\n        }, this);\n      case 'process':\n        return /*#__PURE__*/_jsxDEV(FiFileText, {\n          className: \"activity-icon process\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 27,\n          columnNumber: 30\n        }, this);\n      case 'bulk':\n        return /*#__PURE__*/_jsxDEV(FiUsers, {\n          className: \"activity-icon bulk\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 28,\n          columnNumber: 27\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(FiActivity, {\n          className: \"activity-icon default\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 29,\n          columnNumber: 23\n        }, this);\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"activity-log-page\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading-container\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"loading-spinner large\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 37,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Loading activity log...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 38,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 36,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 35,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"activity-log-page\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"page-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        children: \"Activity Log\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 47,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Track all system activities and user actions\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 48,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 46,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"activity-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"activity-stats\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-item\",\n          children: [/*#__PURE__*/_jsxDEV(FiClock, {\n            size: 16\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 54,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: [activities.length, \" activities today\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 55,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 53,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 52,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"activity-list\",\n        children: activities.map(activity => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"activity-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"activity-icon-wrapper\",\n            children: getActivityIcon(activity.type)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 62,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"activity-content\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"activity-main\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"activity-action\",\n                children: activity.action\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 67,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"activity-detail\",\n                children: [\": \", activity.detail]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 68,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 66,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"activity-meta\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"activity-user\",\n                children: [\"by \", activity.user]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 71,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"activity-time\",\n                children: activity.time\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 72,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 70,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 65,\n            columnNumber: 15\n          }, this)]\n        }, activity.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 61,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 59,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 51,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 45,\n    columnNumber: 5\n  }, this);\n};\n_s(ActivityLog, \"C8uifjA9jREC8y+qhfs5+cS/Q5A=\");\n_c = ActivityLog;\nexport default ActivityLog;\nvar _c;\n$RefreshReg$(_c, \"ActivityLog\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "FiActivity", "FiUpload", "FiSearch", "FiFileText", "FiUsers", "<PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "ActivityLog", "_s", "activities", "setActivities", "loading", "setLoading", "setTimeout", "id", "type", "action", "detail", "time", "user", "getActivityIcon", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "children", "size", "length", "map", "activity", "_c", "$RefreshReg$"], "sources": ["D:/Projects/SQL-Agent-Task-Submission-main/SQL-Agent-Task-Submission-main/src/pages/ActivityLog.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { FiActivity, FiUpload, FiSearch, FiFileText, FiUsers, FiClock } from 'react-icons/fi';\nimport './ActivityLog.css';\n\nconst ActivityLog = () => {\n  const [activities, setActivities] = useState([]);\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    // Simulate loading data\n    setTimeout(() => {\n      setActivities([\n        { id: 1, type: 'upload', action: 'Resume uploaded', detail: 'john_doe_resume.pdf', time: '2 minutes ago', user: 'Admin' },\n        { id: 2, type: 'search', action: 'Search completed', detail: 'Python developers (15 results)', time: '5 minutes ago', user: 'Admin' },\n        { id: 3, type: 'process', action: 'Resume processed', detail: 'jane_smith_cv.pdf', time: '10 minutes ago', user: 'System' },\n        { id: 4, type: 'bulk', action: 'Bulk upload completed', detail: '5 files processed', time: '1 hour ago', user: 'Admin' },\n        { id: 5, type: 'search', action: 'Search completed', detail: 'UI/UX designers (8 results)', time: '2 hours ago', user: 'Admin' }\n      ]);\n      setLoading(false);\n    }, 1000);\n  }, []);\n\n  const getActivityIcon = (type) => {\n    switch (type) {\n      case 'upload': return <FiUpload className=\"activity-icon upload\" />;\n      case 'search': return <FiSearch className=\"activity-icon search\" />;\n      case 'process': return <FiFileText className=\"activity-icon process\" />;\n      case 'bulk': return <FiUsers className=\"activity-icon bulk\" />;\n      default: return <FiActivity className=\"activity-icon default\" />;\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className=\"activity-log-page\">\n        <div className=\"loading-container\">\n          <div className=\"loading-spinner large\" />\n          <p>Loading activity log...</p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"activity-log-page\">\n      <div className=\"page-header\">\n        <h1>Activity Log</h1>\n        <p>Track all system activities and user actions</p>\n      </div>\n\n      <div className=\"activity-container\">\n        <div className=\"activity-stats\">\n          <div className=\"stat-item\">\n            <FiClock size={16} />\n            <span>{activities.length} activities today</span>\n          </div>\n        </div>\n\n        <div className=\"activity-list\">\n          {activities.map(activity => (\n            <div key={activity.id} className=\"activity-item\">\n              <div className=\"activity-icon-wrapper\">\n                {getActivityIcon(activity.type)}\n              </div>\n              <div className=\"activity-content\">\n                <div className=\"activity-main\">\n                  <span className=\"activity-action\">{activity.action}</span>\n                  <span className=\"activity-detail\">: {activity.detail}</span>\n                </div>\n                <div className=\"activity-meta\">\n                  <span className=\"activity-user\">by {activity.user}</span>\n                  <span className=\"activity-time\">{activity.time}</span>\n                </div>\n              </div>\n            </div>\n          ))}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default ActivityLog;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,UAAU,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,OAAO,EAAEC,OAAO,QAAQ,gBAAgB;AAC7F,OAAO,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3B,MAAMC,WAAW,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACxB,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGb,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACc,OAAO,EAAEC,UAAU,CAAC,GAAGf,QAAQ,CAAC,IAAI,CAAC;EAE5CC,SAAS,CAAC,MAAM;IACd;IACAe,UAAU,CAAC,MAAM;MACfH,aAAa,CAAC,CACZ;QAAEI,EAAE,EAAE,CAAC;QAAEC,IAAI,EAAE,QAAQ;QAAEC,MAAM,EAAE,iBAAiB;QAAEC,MAAM,EAAE,qBAAqB;QAAEC,IAAI,EAAE,eAAe;QAAEC,IAAI,EAAE;MAAQ,CAAC,EACzH;QAAEL,EAAE,EAAE,CAAC;QAAEC,IAAI,EAAE,QAAQ;QAAEC,MAAM,EAAE,kBAAkB;QAAEC,MAAM,EAAE,gCAAgC;QAAEC,IAAI,EAAE,eAAe;QAAEC,IAAI,EAAE;MAAQ,CAAC,EACrI;QAAEL,EAAE,EAAE,CAAC;QAAEC,IAAI,EAAE,SAAS;QAAEC,MAAM,EAAE,kBAAkB;QAAEC,MAAM,EAAE,mBAAmB;QAAEC,IAAI,EAAE,gBAAgB;QAAEC,IAAI,EAAE;MAAS,CAAC,EAC3H;QAAEL,EAAE,EAAE,CAAC;QAAEC,IAAI,EAAE,MAAM;QAAEC,MAAM,EAAE,uBAAuB;QAAEC,MAAM,EAAE,mBAAmB;QAAEC,IAAI,EAAE,YAAY;QAAEC,IAAI,EAAE;MAAQ,CAAC,EACxH;QAAEL,EAAE,EAAE,CAAC;QAAEC,IAAI,EAAE,QAAQ;QAAEC,MAAM,EAAE,kBAAkB;QAAEC,MAAM,EAAE,6BAA6B;QAAEC,IAAI,EAAE,aAAa;QAAEC,IAAI,EAAE;MAAQ,CAAC,CACjI,CAAC;MACFP,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC,EAAE,IAAI,CAAC;EACV,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMQ,eAAe,GAAIL,IAAI,IAAK;IAChC,QAAQA,IAAI;MACV,KAAK,QAAQ;QAAE,oBAAOT,OAAA,CAACN,QAAQ;UAACqB,SAAS,EAAC;QAAsB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACnE,KAAK,QAAQ;QAAE,oBAAOnB,OAAA,CAACL,QAAQ;UAACoB,SAAS,EAAC;QAAsB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACnE,KAAK,SAAS;QAAE,oBAAOnB,OAAA,CAACJ,UAAU;UAACmB,SAAS,EAAC;QAAuB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACvE,KAAK,MAAM;QAAE,oBAAOnB,OAAA,CAACH,OAAO;UAACkB,SAAS,EAAC;QAAoB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC9D;QAAS,oBAAOnB,OAAA,CAACP,UAAU;UAACsB,SAAS,EAAC;QAAuB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;IAClE;EACF,CAAC;EAED,IAAId,OAAO,EAAE;IACX,oBACEL,OAAA;MAAKe,SAAS,EAAC,mBAAmB;MAAAK,QAAA,eAChCpB,OAAA;QAAKe,SAAS,EAAC,mBAAmB;QAAAK,QAAA,gBAChCpB,OAAA;UAAKe,SAAS,EAAC;QAAuB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACzCnB,OAAA;UAAAoB,QAAA,EAAG;QAAuB;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3B;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACEnB,OAAA;IAAKe,SAAS,EAAC,mBAAmB;IAAAK,QAAA,gBAChCpB,OAAA;MAAKe,SAAS,EAAC,aAAa;MAAAK,QAAA,gBAC1BpB,OAAA;QAAAoB,QAAA,EAAI;MAAY;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACrBnB,OAAA;QAAAoB,QAAA,EAAG;MAA4C;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChD,CAAC,eAENnB,OAAA;MAAKe,SAAS,EAAC,oBAAoB;MAAAK,QAAA,gBACjCpB,OAAA;QAAKe,SAAS,EAAC,gBAAgB;QAAAK,QAAA,eAC7BpB,OAAA;UAAKe,SAAS,EAAC,WAAW;UAAAK,QAAA,gBACxBpB,OAAA,CAACF,OAAO;YAACuB,IAAI,EAAE;UAAG;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACrBnB,OAAA;YAAAoB,QAAA,GAAOjB,UAAU,CAACmB,MAAM,EAAC,mBAAiB;UAAA;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9C;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENnB,OAAA;QAAKe,SAAS,EAAC,eAAe;QAAAK,QAAA,EAC3BjB,UAAU,CAACoB,GAAG,CAACC,QAAQ,iBACtBxB,OAAA;UAAuBe,SAAS,EAAC,eAAe;UAAAK,QAAA,gBAC9CpB,OAAA;YAAKe,SAAS,EAAC,uBAAuB;YAAAK,QAAA,EACnCN,eAAe,CAACU,QAAQ,CAACf,IAAI;UAAC;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B,CAAC,eACNnB,OAAA;YAAKe,SAAS,EAAC,kBAAkB;YAAAK,QAAA,gBAC/BpB,OAAA;cAAKe,SAAS,EAAC,eAAe;cAAAK,QAAA,gBAC5BpB,OAAA;gBAAMe,SAAS,EAAC,iBAAiB;gBAAAK,QAAA,EAAEI,QAAQ,CAACd;cAAM;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC1DnB,OAAA;gBAAMe,SAAS,EAAC,iBAAiB;gBAAAK,QAAA,GAAC,IAAE,EAACI,QAAQ,CAACb,MAAM;cAAA;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzD,CAAC,eACNnB,OAAA;cAAKe,SAAS,EAAC,eAAe;cAAAK,QAAA,gBAC5BpB,OAAA;gBAAMe,SAAS,EAAC,eAAe;gBAAAK,QAAA,GAAC,KAAG,EAACI,QAAQ,CAACX,IAAI;cAAA;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACzDnB,OAAA;gBAAMe,SAAS,EAAC,eAAe;gBAAAK,QAAA,EAAEI,QAAQ,CAACZ;cAAI;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA,GAbEK,QAAQ,CAAChB,EAAE;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAchB,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACjB,EAAA,CA5EID,WAAW;AAAAwB,EAAA,GAAXxB,WAAW;AA8EjB,eAAeA,WAAW;AAAC,IAAAwB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}