/* SearchResumes.css */
.search-page {
  animation: fadeIn 0.5s ease-in;
}

.page-header {
  margin-bottom: 32px;
  text-align: center;
}

.page-header h1 {
  font-size: 32px;
  font-weight: 700;
  color: #1f2937;
  margin: 0 0 12px 0;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.page-header p {
  font-size: 16px;
  color: #6b7280;
  margin: 0;
  line-height: 1.6;
}

/* Search Section */
.search-section {
  background: white;
  padding: 32px;
  border-radius: 16px;
  border: 1px solid #e2e8f0;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  margin-bottom: 32px;
}

.search-form {
  margin-bottom: 24px;
}

.search-input-group {
  display: flex;
  gap: 16px;
  align-items: flex-end;
}

.search-input-wrapper {
  flex: 1;
  position: relative;
}

.search-icon {
  position: absolute;
  left: 16px;
  top: 50%;
  transform: translateY(-50%);
  color: #6b7280;
  z-index: 1;
}

.search-input {
  width: 100%;
  padding: 16px 16px 16px 48px;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  font-size: 16px;
  transition: all 0.2s ease;
}

.search-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.search-buttons {
  display: flex;
  gap: 12px;
}

.filter-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 24px;
  border-top: 1px solid #e2e8f0;
}

.filter-input-wrapper {
  position: relative;
  flex: 1;
  max-width: 400px;
}

.filter-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #6b7280;
}

.filter-input {
  width: 100%;
  padding: 10px 12px 10px 36px;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  font-size: 14px;
}

.results-count {
  font-size: 14px;
  color: #6b7280;
  font-weight: 500;
}

/* Results Section */
.results-section {
  background: white;
  border-radius: 16px;
  border: 1px solid #e2e8f0;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.results-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 32px;
  border-bottom: 1px solid #e2e8f0;
  background: #f8fafc;
}

.results-header h2 {
  font-size: 20px;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.results-actions {
  display: flex;
  gap: 12px;
}

.results-grid {
  padding: 24px;
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 24px;
}

/* Resume Cards */
.resume-card {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 24px;
  transition: all 0.3s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
}

.resume-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #3b82f6 0%, #1d4ed8 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.resume-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  border-color: #3b82f6;
}

.resume-card:hover::before {
  opacity: 1;
}

.resume-header {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  margin-bottom: 20px;
}

.resume-avatar {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  flex-shrink: 0;
}

.resume-info {
  flex: 1;
}

.resume-name {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 4px 0;
}

.resume-title {
  font-size: 14px;
  color: #6b7280;
  margin: 0 0 8px 0;
}

.resume-score {
  display: flex;
  align-items: center;
  gap: 8px;
}

.score-label {
  font-size: 12px;
  color: #6b7280;
}

.score-value {
  font-size: 12px;
  font-weight: 600;
  color: #10b981;
  background: #f0fdf4;
  padding: 2px 8px;
  border-radius: 12px;
}

.resume-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 20px;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #6b7280;
  transition: all 0.2s ease;
}

.detail-item.clickable {
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 8px;
  margin: -4px -8px;
  position: relative;
}

.detail-item.clickable:hover {
  background: #f8fafc;
  color: #374151;
  transform: translateX(4px);
}

.detail-item.email-item.clickable:hover {
  background: #f0fdf4;
  color: #059669;
}

.detail-item.phone-item.clickable:hover {
  background: #fef2f2;
  color: #dc2626;
}

.external-icon {
  opacity: 0;
  transition: opacity 0.2s ease;
  margin-left: auto;
}

.detail-item.clickable:hover .external-icon {
  opacity: 1;
}

.resume-content {
  margin-bottom: 20px;
}

.content-section {
  margin-bottom: 16px;
}

.content-section h4 {
  font-size: 14px;
  font-weight: 600;
  color: #374151;
  margin: 0 0 8px 0;
}

.content-section p {
  font-size: 14px;
  color: #6b7280;
  margin: 0;
  line-height: 1.4;
}

.skills-list {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.skill-tag {
  background: #eff6ff;
  color: #1d4ed8;
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
}

.summary-text {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.resume-actions {
  display: flex;
  gap: 12px;
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 64px 32px;
  background: white;
  border-radius: 16px;
  border: 1px solid #e2e8f0;
}

.empty-icon {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: #f1f5f9;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 24px;
  color: #6b7280;
}

.empty-state h3 {
  font-size: 20px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 12px 0;
}

.empty-state p {
  font-size: 16px;
  color: #6b7280;
  margin: 0 0 24px 0;
  line-height: 1.6;
}

.search-examples {
  background: #f8fafc;
  padding: 20px;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
  text-align: left;
  max-width: 400px;
  margin: 0 auto;
}

.search-examples h4 {
  font-size: 14px;
  font-weight: 600;
  color: #374151;
  margin: 0 0 12px 0;
}

.search-examples ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.search-examples li {
  font-size: 14px;
  color: #6b7280;
  margin-bottom: 8px;
  padding-left: 16px;
  position: relative;
}

.search-examples li:before {
  content: "•";
  color: #3b82f6;
  position: absolute;
  left: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .search-input-group {
    flex-direction: column;
    align-items: stretch;
  }

  .search-buttons {
    width: 100%;
  }

  .search-buttons .btn {
    flex: 1;
  }

  .filter-section {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
  }

  .results-grid {
    grid-template-columns: 1fr;
    padding: 16px;
  }

  .results-header {
    padding: 20px;
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
  }

  .resume-actions {
    flex-direction: column;
  }
}
