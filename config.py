import os,logging
from dotenv import load_dotenv

log_dir = "./logs"
log_file = os.path.join(log_dir, "app.log")

# Create logs directory if it doesn't exist
if not os.path.exists(log_dir):
    os.makedirs(log_dir)
logging.basicConfig(
    filename=log_file,  # Log file name
    level=logging.INFO,  # Log level
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",  # Log format
    filemode="a",  # Append mode (use "w" for overwrite mode)
)

logger = logging.getLogger(__name__)

# Load environment variables from .env file
load_dotenv()

DB_CONFIG = {
    "host": os.getenv("DB_HOST", "localhost"),
    "user": os.getenv("DB_USER", "root"),
    "password": os.getenv("DB_PASSWORD", "password")
}

logger.info(f"Database configuration loaded: Host={DB_CONFIG['host']}, User={DB_CONFIG['user']}")
