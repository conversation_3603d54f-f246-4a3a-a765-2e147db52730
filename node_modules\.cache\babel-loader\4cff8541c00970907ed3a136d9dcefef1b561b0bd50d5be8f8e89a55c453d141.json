{"ast": null, "code": "var _jsxFileName = \"D:\\\\Projects\\\\SQL-Agent-Task-Submission-main\\\\SQL-Agent-Task-Submission-main\\\\src\\\\pages\\\\SearchResumes.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { FiSearch, FiFilter, FiDownload, FiEye, FiUser, FiMapPin, FiMail, FiPhone, FiExternalLink, FiStar, FiX, FiSettings, FiChevronDown, FiChevronUp } from 'react-icons/fi';\nimport { toast } from 'react-toastify';\nimport ResumeModal from '../components/ResumeModal';\nimport './SearchResumes.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst SearchResumes = () => {\n  _s();\n  const [searchQuery, setSearchQuery] = useState('');\n  const [isSearching, setIsSearching] = useState(false);\n  const [searchResults, setSearchResults] = useState([]);\n  const [filterQuery, setFilterQuery] = useState('');\n  const [showFilters, setShowFilters] = useState(false);\n  const [advancedFilters, setAdvancedFilters] = useState({\n    minExperience: '',\n    maxExperience: '',\n    education: '',\n    location: '',\n    skills: '',\n    minMarks: '',\n    institution: '',\n    graduationYear: '',\n    company: ''\n  });\n  const [selectedResume, setSelectedResume] = useState(null);\n  const [isModalOpen, setIsModalOpen] = useState(false);\n  const [currentPage, setCurrentPage] = useState(1);\n  const [totalPages, setTotalPages] = useState(1);\n  const [totalCount, setTotalCount] = useState(0);\n  const resultsPerPage = 20;\n\n  // Mock data for demonstration\n  const mockResults = [{\n    id: 1,\n    name: 'John Doe',\n    title: 'Senior Software Engineer',\n    email: '<EMAIL>',\n    phone: '+****************',\n    location: 'San Francisco, CA',\n    experience: '5+ years',\n    skills: ['Python', 'React', 'Node.js', 'AWS'],\n    education: 'MS Computer Science',\n    summary: 'Experienced software engineer with expertise in full-stack development...',\n    score: 95,\n    rawData: {\n      Resume: {\n        PersonalInformation: {\n          FullName: 'John Doe',\n          Email: '<EMAIL>',\n          ContactNumber: '+****************',\n          Address: 'San Francisco, CA'\n        },\n        WorkExperience: [{\n          Role: 'Senior Software Engineer',\n          CompanyName: 'Tech Corp',\n          StartYear: '2019',\n          EndYear: 'Present',\n          'Description/Responsibility': 'Led development of scalable web applications using React and Node.js'\n        }, {\n          Role: 'Software Engineer',\n          CompanyName: 'StartupXYZ',\n          StartYear: '2017',\n          EndYear: '2019',\n          'Description/Responsibility': 'Developed full-stack applications and improved system performance'\n        }],\n        Education: [{\n          Degree: 'MS Computer Science',\n          Institution: 'Stanford University',\n          GraduationYear: '2017',\n          'GPA/Marks/%': 85\n        }],\n        Skills: ['Python', 'React', 'Node.js', 'AWS'],\n        TotalWorkExperienceInYears: 5\n      }\n    }\n  }, {\n    id: 2,\n    name: 'Jane Smith',\n    title: 'Frontend Developer',\n    email: '<EMAIL>',\n    phone: '+****************',\n    location: 'New York, NY',\n    experience: '3+ years',\n    skills: ['React', 'TypeScript', 'CSS', 'JavaScript'],\n    education: 'BS Computer Science',\n    summary: 'Creative frontend developer passionate about user experience...',\n    score: 88,\n    rawData: {\n      Resume: {\n        PersonalInformation: {\n          FullName: 'Jane Smith',\n          Email: '<EMAIL>',\n          ContactNumber: '+****************',\n          Address: 'New York, NY'\n        },\n        WorkExperience: [{\n          Role: 'Frontend Developer',\n          CompanyName: 'Design Studio',\n          StartYear: '2021',\n          EndYear: 'Present',\n          'Description/Responsibility': 'Created responsive web interfaces using React and modern CSS'\n        }],\n        Education: [{\n          Degree: 'BS Computer Science',\n          Institution: 'NYU',\n          GraduationYear: '2021',\n          'GPA/Marks/%': 78\n        }],\n        Skills: ['React', 'TypeScript', 'CSS', 'JavaScript'],\n        TotalWorkExperienceInYears: 3\n      }\n    }\n  }, {\n    id: 3,\n    name: 'Mike Johnson',\n    title: 'DevOps Engineer',\n    email: '<EMAIL>',\n    phone: '+****************',\n    location: 'Austin, TX',\n    experience: '4+ years',\n    skills: ['Docker', 'Kubernetes', 'AWS', 'Python'],\n    education: 'BS Information Technology',\n    summary: 'DevOps engineer specializing in cloud infrastructure and automation...',\n    score: 92,\n    rawData: {\n      Resume: {\n        PersonalInformation: {\n          FullName: 'Mike Johnson',\n          Email: '<EMAIL>',\n          ContactNumber: '+****************',\n          Address: 'Austin, TX'\n        },\n        WorkExperience: [{\n          Role: 'DevOps Engineer',\n          CompanyName: 'Cloud Solutions Inc',\n          StartYear: '2020',\n          EndYear: 'Present',\n          'Description/Responsibility': 'Managed cloud infrastructure and automated deployment pipelines'\n        }],\n        Education: [{\n          Degree: 'BS Information Technology',\n          Institution: 'UT Austin',\n          GraduationYear: '2020',\n          'GPA/Marks/%': 82\n        }],\n        Skills: ['Docker', 'Kubernetes', 'AWS', 'Python'],\n        TotalWorkExperienceInYears: 4\n      }\n    }\n  }];\n  const handleSearch = async e => {\n    e.preventDefault();\n    if (!searchQuery.trim()) {\n      toast.warning('Please enter a search query');\n      return;\n    }\n    setIsSearching(true);\n    try {\n      // Call the FastAPI backend exactly like frontendV3.py does\n      const url = new URL('http://localhost:8001/query/');\n      url.searchParams.append('naturalQuery', searchQuery);\n      const response = await fetch(url, {\n        method: 'GET',\n        headers: {\n          'Content-Type': 'application/json'\n        }\n      });\n      const data = await response.json();\n      if (response.ok) {\n        // Handle FastAPI response format exactly like frontendV3.py: {query: [], listOfDict: [], result: \"\"}\n        let transformedResults = [];\n\n        // Check if API returns a direct list (like frontendV3.py handles)\n        if (Array.isArray(data)) {\n          transformedResults = data.map(resume => transformResumeData(resume));\n        } else if (data.listOfDict && Array.isArray(data.listOfDict)) {\n          // Transform the resume data from MongoDB to display format\n          transformedResults = data.listOfDict.map(resume => transformResumeData(resume));\n        } else {\n          // Fallback to mock data if API doesn't return results\n          transformedResults = mockResults.filter(resume => resume.name.toLowerCase().includes(searchQuery.toLowerCase()) || resume.title.toLowerCase().includes(searchQuery.toLowerCase()) || resume.skills.some(skill => skill.toLowerCase().includes(searchQuery.toLowerCase())));\n        }\n        setSearchResults(transformedResults);\n\n        // Show success message with GPT result (exactly like frontendV3.py)\n        if (data.result && data.result !== 'Data received successfully.') {\n          toast.success(`🤖 GPT Analysis: ${data.result}`);\n        } else {\n          toast.success(`🔍 Found ${transformedResults.length} matching resumes`);\n        }\n\n        // Log GPT response and MongoDB query for debugging (like frontendV3.py developer mode)\n        if (data.query) {\n          console.log('🤖 MongoDB Query Generated by GPT:', JSON.stringify(data.query, null, 2));\n          console.log('📊 GPT Result:', data.result);\n          console.log('📋 Full API Response:', data);\n        }\n      } else {\n        throw new Error(data.error || 'Search failed');\n      }\n    } catch (error) {\n      console.error('Search error:', error);\n\n      // Fallback to mock data on network error\n      const fallbackResults = mockResults.filter(resume => resume.name.toLowerCase().includes(searchQuery.toLowerCase()) || resume.title.toLowerCase().includes(searchQuery.toLowerCase()) || resume.skills.some(skill => skill.toLowerCase().includes(searchQuery.toLowerCase())));\n      setSearchResults(fallbackResults);\n      toast.warning(`Using demo data - Found ${fallbackResults.length} matching resumes`);\n    } finally {\n      setIsSearching(false);\n    }\n  };\n  const handleShowAll = async (page = 1) => {\n    setIsSearching(true);\n    try {\n      // Call the FastAPI backend directly to get all resumes\n      const response = await fetch('http://localhost:8001/showall');\n      const data = await response.json();\n      if (response.ok) {\n        let transformedResults = [];\n        if (Array.isArray(data)) {\n          // FastAPI /showall returns array directly\n          transformedResults = data.map(resume => {\n            var _resumeData$PersonalI, _resumeData$PersonalI2, _resumeData$PersonalI3, _resumeData$PersonalI4, _resumeData$PersonalI5, _resumeData$WorkExper, _resumeData$Education, _resumeData$Education2;\n            // Handle the nested Resume structure from MongoDB\n            const resumeData = resume.Resume || resume;\n            return {\n              id: resume._id || Math.random().toString(36).substr(2, 9),\n              name: ((_resumeData$PersonalI = resumeData.PersonalInformation) === null || _resumeData$PersonalI === void 0 ? void 0 : _resumeData$PersonalI.FullName) || resumeData.name || 'Unknown',\n              title: ((_resumeData$PersonalI2 = resumeData.PersonalInformation) === null || _resumeData$PersonalI2 === void 0 ? void 0 : _resumeData$PersonalI2.Designation) || resumeData.title || 'No title',\n              email: ((_resumeData$PersonalI3 = resumeData.PersonalInformation) === null || _resumeData$PersonalI3 === void 0 ? void 0 : _resumeData$PersonalI3.Email) || resumeData.email || 'No email',\n              phone: ((_resumeData$PersonalI4 = resumeData.PersonalInformation) === null || _resumeData$PersonalI4 === void 0 ? void 0 : _resumeData$PersonalI4.ContactNumber) || resumeData.phone || 'No phone',\n              location: ((_resumeData$PersonalI5 = resumeData.PersonalInformation) === null || _resumeData$PersonalI5 === void 0 ? void 0 : _resumeData$PersonalI5.Address) || resumeData.location || 'No location',\n              experience: resumeData.TotalWorkExperienceInYears ? `${resumeData.TotalWorkExperienceInYears} years` : (_resumeData$WorkExper = resumeData.WorkExperience) !== null && _resumeData$WorkExper !== void 0 && _resumeData$WorkExper.length ? `${resumeData.WorkExperience.length}+ positions` : resumeData.experience || 'No experience data',\n              skills: resumeData.Skills || resumeData.skills || [],\n              education: (_resumeData$Education = resumeData.Education) !== null && _resumeData$Education !== void 0 && _resumeData$Education.length ? ((_resumeData$Education2 = resumeData.Education[0]) === null || _resumeData$Education2 === void 0 ? void 0 : _resumeData$Education2.Degree) || 'Education available' : resumeData.education || 'No education data',\n              summary: resumeData.Objective || resumeData.Summary || resumeData.summary || 'No summary available',\n              score: 90,\n              // Default score for show all\n              rawData: resume // Keep original data for reference\n            };\n          });\n        } else if (data.error) {\n          throw new Error(data.error);\n        } else {\n          // Fallback to mock data if API doesn't return proper results\n          transformedResults = mockResults;\n        }\n        setSearchResults(transformedResults);\n        setCurrentPage(1);\n        setTotalCount(transformedResults.length);\n        setTotalPages(Math.ceil(transformedResults.length / resultsPerPage));\n        setSearchQuery('');\n        toast.success(`📊 Loaded ${transformedResults.length} resumes from database`);\n      } else {\n        throw new Error(data.error || 'Failed to load resumes');\n      }\n    } catch (error) {\n      console.error('Load resumes error:', error);\n\n      // Fallback to mock data on network error\n      setSearchResults(mockResults);\n      setCurrentPage(1);\n      setTotalCount(mockResults.length);\n      setTotalPages(Math.ceil(mockResults.length / resultsPerPage));\n      setSearchQuery('');\n      toast.warning(`Using demo data - Showing ${mockResults.length} sample resumes`);\n    } finally {\n      setIsSearching(false);\n    }\n  };\n  const handlePageChange = page => {\n    if (page >= 1 && page <= totalPages) {\n      handleShowAll(page);\n    }\n  };\n  const filteredResults = searchResults.filter(result => {\n    // Basic text filter\n    if (filterQuery) {\n      const matchesText = result.name.toLowerCase().includes(filterQuery.toLowerCase()) || result.title.toLowerCase().includes(filterQuery.toLowerCase()) || result.location.toLowerCase().includes(filterQuery.toLowerCase());\n      if (!matchesText) return false;\n    }\n\n    // Advanced filters\n    const filters = advancedFilters;\n\n    // Experience filter\n    if (filters.minExperience || filters.maxExperience) {\n      var _result$rawData, _result$rawData$Resum;\n      const experienceYears = ((_result$rawData = result.rawData) === null || _result$rawData === void 0 ? void 0 : (_result$rawData$Resum = _result$rawData.Resume) === null || _result$rawData$Resum === void 0 ? void 0 : _result$rawData$Resum.TotalWorkExperienceInYears) || extractExperienceYears(result.experience) || 0;\n      if (filters.minExperience && experienceYears < parseInt(filters.minExperience)) return false;\n      if (filters.maxExperience && experienceYears > parseInt(filters.maxExperience)) return false;\n    }\n\n    // Education filter\n    if (filters.education) {\n      var _result$rawData2, _result$rawData2$Resu, _result$rawData2$Resu2;\n      const hasEducation = ((_result$rawData2 = result.rawData) === null || _result$rawData2 === void 0 ? void 0 : (_result$rawData2$Resu = _result$rawData2.Resume) === null || _result$rawData2$Resu === void 0 ? void 0 : (_result$rawData2$Resu2 = _result$rawData2$Resu.Education) === null || _result$rawData2$Resu2 === void 0 ? void 0 : _result$rawData2$Resu2.some(edu => (edu.Degree || '').toLowerCase().includes(filters.education.toLowerCase()))) || (result.education || '').toLowerCase().includes(filters.education.toLowerCase());\n      if (!hasEducation) return false;\n    }\n\n    // Location filter\n    if (filters.location) {\n      const matchesLocation = (result.location || '').toLowerCase().includes(filters.location.toLowerCase());\n      if (!matchesLocation) return false;\n    }\n\n    // Skills filter\n    if (filters.skills) {\n      var _result$skills, _result$rawData3, _result$rawData3$Resu;\n      const skillsText = filters.skills.toLowerCase();\n      const hasSkill = ((_result$skills = result.skills) === null || _result$skills === void 0 ? void 0 : _result$skills.some(skill => skill.toLowerCase().includes(skillsText))) || (((_result$rawData3 = result.rawData) === null || _result$rawData3 === void 0 ? void 0 : (_result$rawData3$Resu = _result$rawData3.Resume) === null || _result$rawData3$Resu === void 0 ? void 0 : _result$rawData3$Resu.Skills) || []).some(skill => skill.toLowerCase().includes(skillsText));\n      if (!hasSkill) return false;\n    }\n\n    // Institution filter\n    if (filters.institution) {\n      var _result$rawData4, _result$rawData4$Resu, _result$rawData4$Resu2;\n      const hasInstitution = (_result$rawData4 = result.rawData) === null || _result$rawData4 === void 0 ? void 0 : (_result$rawData4$Resu = _result$rawData4.Resume) === null || _result$rawData4$Resu === void 0 ? void 0 : (_result$rawData4$Resu2 = _result$rawData4$Resu.Education) === null || _result$rawData4$Resu2 === void 0 ? void 0 : _result$rawData4$Resu2.some(edu => (edu.Institution || edu.School || '').toLowerCase().includes(filters.institution.toLowerCase()));\n      if (!hasInstitution) return false;\n    }\n\n    // Graduation year filter\n    if (filters.graduationYear) {\n      var _result$rawData5, _result$rawData5$Resu, _result$rawData5$Resu2;\n      const hasGradYear = (_result$rawData5 = result.rawData) === null || _result$rawData5 === void 0 ? void 0 : (_result$rawData5$Resu = _result$rawData5.Resume) === null || _result$rawData5$Resu === void 0 ? void 0 : (_result$rawData5$Resu2 = _result$rawData5$Resu.Education) === null || _result$rawData5$Resu2 === void 0 ? void 0 : _result$rawData5$Resu2.some(edu => (edu.GraduationYear || edu.Year || '').toString().includes(filters.graduationYear));\n      if (!hasGradYear) return false;\n    }\n\n    // Company filter\n    if (filters.company) {\n      var _result$rawData6, _result$rawData6$Resu, _result$rawData6$Resu2;\n      const hasCompany = (_result$rawData6 = result.rawData) === null || _result$rawData6 === void 0 ? void 0 : (_result$rawData6$Resu = _result$rawData6.Resume) === null || _result$rawData6$Resu === void 0 ? void 0 : (_result$rawData6$Resu2 = _result$rawData6$Resu.WorkExperience) === null || _result$rawData6$Resu2 === void 0 ? void 0 : _result$rawData6$Resu2.some(exp => (exp.Company || exp.CompanyName || exp.Organization || '').toLowerCase().includes(filters.company.toLowerCase()));\n      if (!hasCompany) return false;\n    }\n\n    // Minimum marks filter\n    if (filters.minMarks) {\n      var _result$rawData7, _result$rawData7$Resu, _result$rawData7$Resu2;\n      const hasMinMarks = (_result$rawData7 = result.rawData) === null || _result$rawData7 === void 0 ? void 0 : (_result$rawData7$Resu = _result$rawData7.Resume) === null || _result$rawData7$Resu === void 0 ? void 0 : (_result$rawData7$Resu2 = _result$rawData7$Resu.Education) === null || _result$rawData7$Resu2 === void 0 ? void 0 : _result$rawData7$Resu2.some(edu => {\n        const marks = parseFloat(edu['GPA/Marks/%']) || 0;\n        return marks >= parseFloat(filters.minMarks);\n      });\n      if (!hasMinMarks) return false;\n    }\n    return true;\n  });\n\n  // Helper function to extract experience years from text\n  const extractExperienceYears = experienceText => {\n    if (!experienceText) return 0;\n    const match = experienceText.match(/(\\d+)\\+?\\s*years?/i);\n    return match ? parseInt(match[1]) : 0;\n  };\n  const handleAdvancedFilterChange = (filterName, value) => {\n    setAdvancedFilters(prev => ({\n      ...prev,\n      [filterName]: value\n    }));\n  };\n  const clearAllFilters = () => {\n    setFilterQuery('');\n    setAdvancedFilters({\n      minExperience: '',\n      maxExperience: '',\n      education: '',\n      location: '',\n      skills: '',\n      minMarks: '',\n      institution: '',\n      graduationYear: '',\n      company: ''\n    });\n  };\n  const hasActiveFilters = () => {\n    return filterQuery || Object.values(advancedFilters).some(value => value !== '');\n  };\n  const handleDownload = resume => {\n    try {\n      var _resume$rawData, _resume$rawData$Resum, _resume$rawData2, _resume$rawData2$Resu, _resume$rawData3, _resume$rawData3$Resu, _resume$rawData4, _resume$rawData4$Resu, _resume$rawData5, _resume$rawData5$Resu, _resume$rawData6, _resume$rawData6$Resu, _resume$rawData7, _resume$rawData7$Resu, _resume$rawData8, _resume$rawData8$Resu, _resume$rawData8$Resu2;\n      // Create a comprehensive text-based resume content\n      const resumeContent = `\n${resume.name}\n${resume.title}\n${resume.email} | ${resume.phone} | ${resume.location}\n\nPROFESSIONAL SUMMARY\n${resume.summary}\n\nWORK EXPERIENCE\n${(_resume$rawData = resume.rawData) !== null && _resume$rawData !== void 0 && (_resume$rawData$Resum = _resume$rawData.Resume) !== null && _resume$rawData$Resum !== void 0 && _resume$rawData$Resum.WorkExperience ? resume.rawData.Resume.WorkExperience.map(exp => `${exp.JobTitle || exp.Position} at ${exp.Company || exp.Organization}\nDuration: ${exp.Duration || exp.Years || 'Not specified'}\n${exp.Description ? 'Description: ' + exp.Description : ''}\n${exp.Responsibilities ? 'Responsibilities: ' + exp.Responsibilities : ''}\n`).join('\\n') : resume.experience}\n\n${(_resume$rawData2 = resume.rawData) !== null && _resume$rawData2 !== void 0 && (_resume$rawData2$Resu = _resume$rawData2.Resume) !== null && _resume$rawData2$Resu !== void 0 && _resume$rawData2$Resu.TotalWorkExperienceInYears ? `Total Experience: ${resume.rawData.Resume.TotalWorkExperienceInYears} years\\n` : ''}\n\nEDUCATION\n${(_resume$rawData3 = resume.rawData) !== null && _resume$rawData3 !== void 0 && (_resume$rawData3$Resu = _resume$rawData3.Resume) !== null && _resume$rawData3$Resu !== void 0 && _resume$rawData3$Resu.Education ? resume.rawData.Resume.Education.map(edu => `${edu.Degree || 'Degree'} from ${edu.Institution || edu.School || 'Institution'}\nGraduation Year: ${edu.GraduationYear || edu.Year || 'Not specified'}\n${edu['GPA/Marks/%'] && edu['GPA/Marks/%'] > 0 ? 'Marks: ' + edu['GPA/Marks/%'] + '%' : ''}\n`).join('\\n') : resume.education}\n\nSKILLS & TECHNOLOGIES\n${resume.skills.join(', ')}\n\n${(_resume$rawData4 = resume.rawData) !== null && _resume$rawData4 !== void 0 && (_resume$rawData4$Resu = _resume$rawData4.Resume) !== null && _resume$rawData4$Resu !== void 0 && _resume$rawData4$Resu.Languages && resume.rawData.Resume.Languages.length > 0 ? `LANGUAGES\\n${resume.rawData.Resume.Languages.join(', ')}\\n` : ''}\n\n${(_resume$rawData5 = resume.rawData) !== null && _resume$rawData5 !== void 0 && (_resume$rawData5$Resu = _resume$rawData5.Resume) !== null && _resume$rawData5$Resu !== void 0 && _resume$rawData5$Resu.Projects && resume.rawData.Resume.Projects.length > 0 ? `PROJECTS\\n${resume.rawData.Resume.Projects.map(project => `${project.Name || project.Title || 'Project'}\n${project.Description ? 'Description: ' + project.Description : ''}\n${project.Technologies ? 'Technologies: ' + project.Technologies : ''}\n`).join('\\n')}` : ''}\n\n${(_resume$rawData6 = resume.rawData) !== null && _resume$rawData6 !== void 0 && (_resume$rawData6$Resu = _resume$rawData6.Resume) !== null && _resume$rawData6$Resu !== void 0 && _resume$rawData6$Resu.Certifications && resume.rawData.Resume.Certifications.length > 0 ? `CERTIFICATIONS\\n${resume.rawData.Resume.Certifications.map(cert => `${cert.Name || cert.Title || 'Certification'}\n${cert.IssuingOrganization ? 'Issued by: ' + cert.IssuingOrganization : ''}\n${cert.IssueDate ? 'Date: ' + cert.IssueDate : ''}\n`).join('\\n')}` : ''}\n\n${(_resume$rawData7 = resume.rawData) !== null && _resume$rawData7 !== void 0 && (_resume$rawData7$Resu = _resume$rawData7.Resume) !== null && _resume$rawData7$Resu !== void 0 && _resume$rawData7$Resu.Achievements && resume.rawData.Resume.Achievements.length > 0 ? `ACHIEVEMENTS\\n${resume.rawData.Resume.Achievements.map(achievement => typeof achievement === 'string' ? achievement : `${achievement.AchievementName || achievement.Name || 'Achievement'}\n${achievement.IssueDate ? 'Date: ' + achievement.IssueDate : ''}\n${achievement.IssuingOrganization ? 'Organization: ' + achievement.IssuingOrganization : ''}\n${achievement.Description ? 'Description: ' + achievement.Description : ''}\n`).join('\\n')}` : ''}\n\n${(_resume$rawData8 = resume.rawData) !== null && _resume$rawData8 !== void 0 && (_resume$rawData8$Resu = _resume$rawData8.Resume) !== null && _resume$rawData8$Resu !== void 0 && (_resume$rawData8$Resu2 = _resume$rawData8$Resu.PersonalInformation) !== null && _resume$rawData8$Resu2 !== void 0 && _resume$rawData8$Resu2.LinkedIn ? `ADDITIONAL INFORMATION\\nLinkedIn: ${resume.rawData.Resume.PersonalInformation.LinkedIn}` : ''}\n\nGenerated on: ${new Date().toLocaleDateString()}\n      `.trim();\n\n      // Create and download the file\n      const blob = new Blob([resumeContent], {\n        type: 'text/plain'\n      });\n      const url = URL.createObjectURL(blob);\n      const link = document.createElement('a');\n      link.href = url;\n      link.download = `${resume.name.replace(/\\s+/g, '_')}_Resume.txt`;\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n      URL.revokeObjectURL(url);\n      toast.success(`Downloaded resume for ${resume.name}`);\n    } catch (error) {\n      console.error('Download error:', error);\n      toast.error('Failed to download resume. Please try again.');\n    }\n  };\n  const handleView = resume => {\n    setSelectedResume(resume);\n    setIsModalOpen(true);\n    toast.info(`Opening detailed view for ${resume.name}`);\n  };\n  const handleCloseModal = () => {\n    setIsModalOpen(false);\n    setSelectedResume(null);\n  };\n  const handleEmailClick = (email, name) => {\n    window.open(`mailto:${email}?subject=Regarding your resume&body=Hi ${name},%0D%0A%0D%0AI found your resume and would like to discuss potential opportunities.%0D%0A%0D%0ABest regards`, '_blank');\n    toast.info(`Opening email client to contact ${name}`);\n  };\n  const handlePhoneClick = (phone, name) => {\n    if (navigator.userAgent.match(/Mobile|Android|iPhone|iPad/)) {\n      window.open(`tel:${phone}`, '_blank');\n    } else {\n      navigator.clipboard.writeText(phone).then(() => {\n        toast.success(`Phone number copied: ${phone}`);\n      }).catch(() => {\n        toast.info(`Phone: ${phone}`);\n      });\n    }\n  };\n  const handleExportAll = () => {\n    if (filteredResults.length === 0) {\n      toast.warning('No results to export');\n      return;\n    }\n    try {\n      const exportData = filteredResults.map(resume => ({\n        name: resume.name,\n        title: resume.title,\n        email: resume.email,\n        phone: resume.phone,\n        location: resume.location,\n        experience: resume.experience,\n        education: resume.education,\n        skills: resume.skills.join(', '),\n        summary: resume.summary,\n        matchScore: resume.score\n      }));\n      const csvContent = ['Name,Title,Email,Phone,Location,Experience,Education,Skills,Summary,Match Score', ...exportData.map(row => Object.values(row).map(value => `\"${String(value).replace(/\"/g, '\"\"')}\"`).join(','))].join('\\n');\n      const blob = new Blob([csvContent], {\n        type: 'text/csv'\n      });\n      const url = URL.createObjectURL(blob);\n      const link = document.createElement('a');\n      link.href = url;\n      link.download = `Resume_Search_Results_${new Date().toISOString().split('T')[0]}.csv`;\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n      URL.revokeObjectURL(url);\n      toast.success(`Exported ${filteredResults.length} resumes to CSV`);\n    } catch (error) {\n      console.error('Export error:', error);\n      toast.error('Failed to export results. Please try again.');\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"search-page\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"page-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        children: \"Search Resumes\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 603,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Use natural language to find the perfect candidates for your needs\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 604,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 602,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"search-section\",\n      children: [/*#__PURE__*/_jsxDEV(\"form\", {\n        onSubmit: handleSearch,\n        className: \"search-form\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"search-input-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"search-input-wrapper\",\n            children: [/*#__PURE__*/_jsxDEV(FiSearch, {\n              className: \"search-icon\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 612,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              placeholder: \"e.g., 'Find Python developers with 5+ years experience' or 'Show frontend engineers in California'\",\n              value: searchQuery,\n              onChange: e => setSearchQuery(e.target.value),\n              className: \"search-input\",\n              disabled: isSearching\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 613,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 611,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"search-buttons\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"submit\",\n              className: \"btn btn-primary\",\n              disabled: isSearching,\n              children: isSearching ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"loading-spinner\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 630,\n                  columnNumber: 21\n                }, this), \"Searching...\"]\n              }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(FiSearch, {\n                  size: 16\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 635,\n                  columnNumber: 21\n                }, this), \"Search\"]\n              }, void 0, true)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 623,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              className: \"btn btn-secondary\",\n              onClick: handleShowAll,\n              disabled: isSearching,\n              children: \"Show All\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 640,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 622,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 610,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 609,\n        columnNumber: 9\n      }, this), searchResults.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"filter-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"filter-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"basic-filter-wrapper\",\n            children: [/*#__PURE__*/_jsxDEV(FiFilter, {\n              className: \"filter-icon\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 657,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              placeholder: \"Quick filter by name, title, or location...\",\n              value: filterQuery,\n              onChange: e => setFilterQuery(e.target.value),\n              className: \"filter-input\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 658,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 656,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"filter-controls\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              className: `btn btn-secondary filter-toggle ${showFilters ? 'active' : ''}`,\n              onClick: () => setShowFilters(!showFilters),\n              children: [/*#__PURE__*/_jsxDEV(FiSettings, {\n                size: 16\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 672,\n                columnNumber: 19\n              }, this), \"Advanced Filters\", showFilters ? /*#__PURE__*/_jsxDEV(FiChevronUp, {\n                size: 16\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 674,\n                columnNumber: 34\n              }, this) : /*#__PURE__*/_jsxDEV(FiChevronDown, {\n                size: 16\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 674,\n                columnNumber: 62\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 667,\n              columnNumber: 17\n            }, this), hasActiveFilters() && /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              className: \"btn btn-outline clear-filters\",\n              onClick: clearAllFilters,\n              children: [/*#__PURE__*/_jsxDEV(FiX, {\n                size: 16\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 682,\n                columnNumber: 21\n              }, this), \"Clear All\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 677,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 666,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 655,\n          columnNumber: 13\n        }, this), showFilters && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"advanced-filters\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"filters-grid\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"filter-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Experience (Years)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 694,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"range-inputs\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"number\",\n                  placeholder: \"Min\",\n                  value: advancedFilters.minExperience,\n                  onChange: e => handleAdvancedFilterChange('minExperience', e.target.value),\n                  className: \"filter-input small\",\n                  min: \"0\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 696,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"to\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 704,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"number\",\n                  placeholder: \"Max\",\n                  value: advancedFilters.maxExperience,\n                  onChange: e => handleAdvancedFilterChange('maxExperience', e.target.value),\n                  className: \"filter-input small\",\n                  min: \"0\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 705,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 695,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 693,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"filter-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Education Level\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 717,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                placeholder: \"e.g., Bachelor, Master, PhD\",\n                value: advancedFilters.education,\n                onChange: e => handleAdvancedFilterChange('education', e.target.value),\n                className: \"filter-input\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 718,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 716,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"filter-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Location\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 728,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                placeholder: \"e.g., Mumbai, Delhi, Bangalore\",\n                value: advancedFilters.location,\n                onChange: e => handleAdvancedFilterChange('location', e.target.value),\n                className: \"filter-input\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 729,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 727,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"filter-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Skills\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 739,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                placeholder: \"e.g., Python, React, Java\",\n                value: advancedFilters.skills,\n                onChange: e => handleAdvancedFilterChange('skills', e.target.value),\n                className: \"filter-input\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 740,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 738,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"filter-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Institution\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 750,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                placeholder: \"e.g., IIT, University name\",\n                value: advancedFilters.institution,\n                onChange: e => handleAdvancedFilterChange('institution', e.target.value),\n                className: \"filter-input\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 751,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 749,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"filter-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Graduation Year\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 761,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                placeholder: \"e.g., 2020, 2021\",\n                value: advancedFilters.graduationYear,\n                onChange: e => handleAdvancedFilterChange('graduationYear', e.target.value),\n                className: \"filter-input\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 762,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 760,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"filter-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Company\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 772,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                placeholder: \"e.g., Google, Microsoft, TCS\",\n                value: advancedFilters.company,\n                onChange: e => handleAdvancedFilterChange('company', e.target.value),\n                className: \"filter-input\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 773,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 771,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"filter-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Minimum Marks (%)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 783,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"number\",\n                placeholder: \"e.g., 75, 80\",\n                value: advancedFilters.minMarks,\n                onChange: e => handleAdvancedFilterChange('minMarks', e.target.value),\n                className: \"filter-input\",\n                min: \"0\",\n                max: \"100\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 784,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 782,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 692,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 691,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"results-count\",\n          children: [filteredResults.length, \" of \", searchResults.length, \" results\", hasActiveFilters() && /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"filtered-indicator\",\n            children: \" (filtered)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 800,\n            columnNumber: 38\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 798,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 654,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 608,\n      columnNumber: 7\n    }, this), searchResults.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"results-section\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"results-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Search Results\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 810,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"results-actions\",\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"btn btn-secondary\",\n            onClick: handleExportAll,\n            title: `Export ${filteredResults.length} results to CSV`,\n            children: [/*#__PURE__*/_jsxDEV(FiDownload, {\n              size: 16\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 817,\n              columnNumber: 17\n            }, this), \"Export All (\", filteredResults.length, \")\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 812,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 811,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 809,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"results-grid\",\n        children: filteredResults.map(resume => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"resume-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"resume-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"resume-avatar\",\n              children: /*#__PURE__*/_jsxDEV(FiUser, {\n                size: 24\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 828,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 827,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"resume-info\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"resume-name\",\n                children: resume.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 831,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"resume-title\",\n                children: resume.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 832,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 830,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 826,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"resume-details\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"detail-item location-item\",\n              children: [/*#__PURE__*/_jsxDEV(FiMapPin, {\n                size: 14\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 838,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: resume.location\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 839,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 837,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"detail-item email-item clickable\",\n              onClick: () => handleEmailClick(resume.email, resume.name),\n              title: `Send email to ${resume.name}`,\n              children: [/*#__PURE__*/_jsxDEV(FiMail, {\n                size: 14\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 846,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: resume.email\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 847,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(FiExternalLink, {\n                size: 12,\n                className: \"external-icon\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 848,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 841,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"detail-item phone-item clickable\",\n              onClick: () => handlePhoneClick(resume.phone, resume.name),\n              title: `Call ${resume.name}`,\n              children: [/*#__PURE__*/_jsxDEV(FiPhone, {\n                size: 14\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 855,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: resume.phone\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 856,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(FiExternalLink, {\n                size: 12,\n                className: \"external-icon\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 857,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 850,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 836,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"resume-content\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"content-row\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"content-section\",\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  children: \"Experience\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 864,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: resume.experience\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 865,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 863,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"content-section\",\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  children: \"Education\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 868,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: resume.education\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 869,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 867,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 862,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"content-section\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                children: \"Skills\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 873,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"skills-list\",\n                children: resume.skills.map((skill, index) => /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"skill-tag\",\n                  children: skill\n                }, index, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 876,\n                  columnNumber: 25\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 874,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 872,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 861,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"resume-actions\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"btn btn-secondary\",\n              onClick: () => handleView(resume),\n              children: [/*#__PURE__*/_jsxDEV(FiEye, {\n                size: 16\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 887,\n                columnNumber: 21\n              }, this), \"View Details\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 883,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"btn btn-primary\",\n              onClick: () => handleDownload(resume),\n              children: [/*#__PURE__*/_jsxDEV(FiDownload, {\n                size: 16\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 894,\n                columnNumber: 21\n              }, this), \"Download\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 890,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 882,\n            columnNumber: 17\n          }, this)]\n        }, resume.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 825,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 823,\n        columnNumber: 11\n      }, this), totalPages > 1 && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"pagination-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"pagination-info\",\n          children: [\"Showing \", (currentPage - 1) * resultsPerPage + 1, \" to \", Math.min(currentPage * resultsPerPage, totalCount), \" of \", totalCount, \" results\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 905,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"pagination-controls\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"btn btn-secondary pagination-btn\",\n            onClick: () => handlePageChange(currentPage - 1),\n            disabled: currentPage === 1,\n            children: \"Previous\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 909,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"page-numbers\",\n            children: Array.from({\n              length: Math.min(5, totalPages)\n            }, (_, i) => {\n              let pageNum;\n              if (totalPages <= 5) {\n                pageNum = i + 1;\n              } else if (currentPage <= 3) {\n                pageNum = i + 1;\n              } else if (currentPage >= totalPages - 2) {\n                pageNum = totalPages - 4 + i;\n              } else {\n                pageNum = currentPage - 2 + i;\n              }\n              return /*#__PURE__*/_jsxDEV(\"button\", {\n                className: `btn pagination-btn ${currentPage === pageNum ? 'btn-primary' : 'btn-secondary'}`,\n                onClick: () => handlePageChange(pageNum),\n                children: pageNum\n              }, pageNum, false, {\n                fileName: _jsxFileName,\n                lineNumber: 931,\n                columnNumber: 23\n              }, this);\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 917,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"btn btn-secondary pagination-btn\",\n            onClick: () => handlePageChange(currentPage + 1),\n            disabled: currentPage === totalPages,\n            children: \"Next\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 942,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 908,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 904,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 808,\n      columnNumber: 9\n    }, this) : !isSearching ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"empty-state\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"empty-icon\",\n        children: /*#__PURE__*/_jsxDEV(FiSearch, {\n          size: 48\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 956,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 955,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n        children: \"Ready to Search!\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 958,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Use the search box above to find resumes using natural language, or click \\\"Show All\\\" to view all resumes in the database.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 959,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"search-examples\",\n        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n          children: \"Try searching for:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 961,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n          children: [/*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"\\\"Find Python developers\\\"\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 963,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"\\\"Show teachers with 5+ years experience\\\"\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 964,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"\\\"Software engineers in Mumbai\\\"\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 965,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 962,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 960,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 954,\n      columnNumber: 9\n    }, this) : null, /*#__PURE__*/_jsxDEV(ResumeModal, {\n      resume: selectedResume,\n      isOpen: isModalOpen,\n      onClose: handleCloseModal\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 972,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 601,\n    columnNumber: 5\n  }, this);\n};\n_s(SearchResumes, \"uvVHvTgqjBYZkn8TcWc0iS1zcVo=\");\n_c = SearchResumes;\nexport default SearchResumes;\nvar _c;\n$RefreshReg$(_c, \"SearchResumes\");", "map": {"version": 3, "names": ["React", "useState", "FiSearch", "<PERSON><PERSON><PERSON><PERSON>", "FiDownload", "FiEye", "FiUser", "FiMapPin", "FiMail", "FiPhone", "FiExternalLink", "FiStar", "FiX", "FiSettings", "FiChevronDown", "FiChevronUp", "toast", "ResumeModal", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "SearchResumes", "_s", "searchQuery", "setSearch<PERSON>uery", "isSearching", "setIsSearching", "searchResults", "setSearchResults", "filterQuery", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "showFilters", "setShowFilters", "advancedFilters", "setAdvancedFilters", "minExperience", "maxExperience", "education", "location", "skills", "minMarks", "institution", "graduationYear", "company", "selectedResume", "setSelectedResume", "isModalOpen", "setIsModalOpen", "currentPage", "setCurrentPage", "totalPages", "setTotalPages", "totalCount", "setTotalCount", "resultsPerPage", "mockResults", "id", "name", "title", "email", "phone", "experience", "summary", "score", "rawData", "Resume", "PersonalInformation", "FullName", "Email", "ContactNumber", "Address", "WorkExperience", "Role", "CompanyName", "StartYear", "EndYear", "Education", "Degree", "Institution", "GraduationYear", "Skills", "TotalWorkExperienceInYears", "handleSearch", "e", "preventDefault", "trim", "warning", "url", "URL", "searchParams", "append", "response", "fetch", "method", "headers", "data", "json", "ok", "transformedResults", "Array", "isArray", "map", "resume", "transformResumeData", "listOfDict", "filter", "toLowerCase", "includes", "some", "skill", "result", "success", "length", "query", "console", "log", "JSON", "stringify", "Error", "error", "fallback<PERSON><PERSON><PERSON>s", "handleShowAll", "page", "_resumeData$PersonalI", "_resumeData$PersonalI2", "_resumeData$PersonalI3", "_resumeData$PersonalI4", "_resumeData$PersonalI5", "_resumeData$WorkExper", "_resumeData$Education", "_resumeData$Education2", "resumeData", "_id", "Math", "random", "toString", "substr", "Designation", "Objective", "Summary", "ceil", "handlePageChange", "filteredResults", "matchesText", "filters", "_result$rawData", "_result$rawData$Resum", "experienceYears", "extractExperienceYears", "parseInt", "_result$rawData2", "_result$rawData2$Resu", "_result$rawData2$Resu2", "hasEducation", "edu", "matchesLocation", "_result$skills", "_result$rawData3", "_result$rawData3$Resu", "skillsText", "hasSkill", "_result$rawData4", "_result$rawData4$Resu", "_result$rawData4$Resu2", "hasInstitution", "School", "_result$rawData5", "_result$rawData5$Resu", "_result$rawData5$Resu2", "hasGradYear", "Year", "_result$rawData6", "_result$rawData6$Resu", "_result$rawData6$Resu2", "hasCompany", "exp", "Company", "Organization", "_result$rawData7", "_result$rawData7$Resu", "_result$rawData7$Resu2", "hasMinMarks", "marks", "parseFloat", "experienceText", "match", "handleAdvancedFilterChange", "filterName", "value", "prev", "clearAllFilters", "hasActiveFilters", "Object", "values", "handleDownload", "_resume$rawData", "_resume$rawData$Resum", "_resume$rawData2", "_resume$rawData2$Resu", "_resume$rawData3", "_resume$rawData3$Resu", "_resume$rawData4", "_resume$rawData4$Resu", "_resume$rawData5", "_resume$rawData5$Resu", "_resume$rawData6", "_resume$rawData6$Resu", "_resume$rawData7", "_resume$rawData7$Resu", "_resume$rawData8", "_resume$rawData8$Resu", "_resume$rawData8$Resu2", "<PERSON><PERSON><PERSON>nt", "JobTitle", "Position", "Duration", "Years", "Description", "Responsibilities", "join", "Languages", "Projects", "project", "Name", "Title", "Technologies", "Certifications", "cert", "IssuingOrganization", "IssueDate", "Achievements", "achievement", "AchievementName", "LinkedIn", "Date", "toLocaleDateString", "blob", "Blob", "type", "createObjectURL", "link", "document", "createElement", "href", "download", "replace", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL", "handleView", "info", "handleCloseModal", "handleEmailClick", "window", "open", "handlePhoneClick", "navigator", "userAgent", "clipboard", "writeText", "then", "catch", "handleExportAll", "exportData", "matchScore", "csv<PERSON><PERSON>nt", "row", "String", "toISOString", "split", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSubmit", "placeholder", "onChange", "target", "disabled", "size", "onClick", "min", "max", "index", "from", "_", "i", "pageNum", "isOpen", "onClose", "_c", "$RefreshReg$"], "sources": ["D:/Projects/SQL-Agent-Task-Submission-main/SQL-Agent-Task-Submission-main/src/pages/SearchResumes.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { <PERSON><PERSON>earch, Fi<PERSON>ilter, <PERSON>Down<PERSON>, <PERSON>Eye, Fi<PERSON>ser, FiMapPin, FiMail, FiPhone, FiExternalLink, FiStar, FiX, FiSettings, FiChevronDown, FiChevronUp } from 'react-icons/fi';\nimport { toast } from 'react-toastify';\nimport ResumeModal from '../components/ResumeModal';\nimport './SearchResumes.css';\n\nconst SearchResumes = () => {\n  const [searchQuery, setSearchQuery] = useState('');\n  const [isSearching, setIsSearching] = useState(false);\n  const [searchResults, setSearchResults] = useState([]);\n  const [filterQuery, setFilterQuery] = useState('');\n  const [showFilters, setShowFilters] = useState(false);\n  const [advancedFilters, setAdvancedFilters] = useState({\n    minExperience: '',\n    maxExperience: '',\n    education: '',\n    location: '',\n    skills: '',\n    minMarks: '',\n    institution: '',\n    graduationYear: '',\n    company: ''\n  });\n  const [selectedResume, setSelectedResume] = useState(null);\n  const [isModalOpen, setIsModalOpen] = useState(false);\n  const [currentPage, setCurrentPage] = useState(1);\n  const [totalPages, setTotalPages] = useState(1);\n  const [totalCount, setTotalCount] = useState(0);\n  const resultsPerPage = 20;\n\n  // Mock data for demonstration\n  const mockResults = [\n    {\n      id: 1,\n      name: 'John Doe',\n      title: 'Senior Software Engineer',\n      email: '<EMAIL>',\n      phone: '+****************',\n      location: 'San Francisco, CA',\n      experience: '5+ years',\n      skills: ['Python', 'React', 'Node.js', 'AWS'],\n      education: 'MS Computer Science',\n      summary: 'Experienced software engineer with expertise in full-stack development...',\n      score: 95,\n      rawData: {\n        Resume: {\n          PersonalInformation: {\n            FullName: 'John Doe',\n            Email: '<EMAIL>',\n            ContactNumber: '+****************',\n            Address: 'San Francisco, CA'\n          },\n          WorkExperience: [\n            {\n              Role: 'Senior Software Engineer',\n              CompanyName: 'Tech Corp',\n              StartYear: '2019',\n              EndYear: 'Present',\n              'Description/Responsibility': 'Led development of scalable web applications using React and Node.js'\n            },\n            {\n              Role: 'Software Engineer',\n              CompanyName: 'StartupXYZ',\n              StartYear: '2017',\n              EndYear: '2019',\n              'Description/Responsibility': 'Developed full-stack applications and improved system performance'\n            }\n          ],\n          Education: [\n            {\n              Degree: 'MS Computer Science',\n              Institution: 'Stanford University',\n              GraduationYear: '2017',\n              'GPA/Marks/%': 85\n            }\n          ],\n          Skills: ['Python', 'React', 'Node.js', 'AWS'],\n          TotalWorkExperienceInYears: 5\n        }\n      }\n    },\n    {\n      id: 2,\n      name: 'Jane Smith',\n      title: 'Frontend Developer',\n      email: '<EMAIL>',\n      phone: '+****************',\n      location: 'New York, NY',\n      experience: '3+ years',\n      skills: ['React', 'TypeScript', 'CSS', 'JavaScript'],\n      education: 'BS Computer Science',\n      summary: 'Creative frontend developer passionate about user experience...',\n      score: 88,\n      rawData: {\n        Resume: {\n          PersonalInformation: {\n            FullName: 'Jane Smith',\n            Email: '<EMAIL>',\n            ContactNumber: '+****************',\n            Address: 'New York, NY'\n          },\n          WorkExperience: [\n            {\n              Role: 'Frontend Developer',\n              CompanyName: 'Design Studio',\n              StartYear: '2021',\n              EndYear: 'Present',\n              'Description/Responsibility': 'Created responsive web interfaces using React and modern CSS'\n            }\n          ],\n          Education: [\n            {\n              Degree: 'BS Computer Science',\n              Institution: 'NYU',\n              GraduationYear: '2021',\n              'GPA/Marks/%': 78\n            }\n          ],\n          Skills: ['React', 'TypeScript', 'CSS', 'JavaScript'],\n          TotalWorkExperienceInYears: 3\n        }\n      }\n    },\n    {\n      id: 3,\n      name: 'Mike Johnson',\n      title: 'DevOps Engineer',\n      email: '<EMAIL>',\n      phone: '+****************',\n      location: 'Austin, TX',\n      experience: '4+ years',\n      skills: ['Docker', 'Kubernetes', 'AWS', 'Python'],\n      education: 'BS Information Technology',\n      summary: 'DevOps engineer specializing in cloud infrastructure and automation...',\n      score: 92,\n      rawData: {\n        Resume: {\n          PersonalInformation: {\n            FullName: 'Mike Johnson',\n            Email: '<EMAIL>',\n            ContactNumber: '+****************',\n            Address: 'Austin, TX'\n          },\n          WorkExperience: [\n            {\n              Role: 'DevOps Engineer',\n              CompanyName: 'Cloud Solutions Inc',\n              StartYear: '2020',\n              EndYear: 'Present',\n              'Description/Responsibility': 'Managed cloud infrastructure and automated deployment pipelines'\n            }\n          ],\n          Education: [\n            {\n              Degree: 'BS Information Technology',\n              Institution: 'UT Austin',\n              GraduationYear: '2020',\n              'GPA/Marks/%': 82\n            }\n          ],\n          Skills: ['Docker', 'Kubernetes', 'AWS', 'Python'],\n          TotalWorkExperienceInYears: 4\n        }\n      }\n    }\n  ];\n\n  const handleSearch = async (e) => {\n    e.preventDefault();\n    if (!searchQuery.trim()) {\n      toast.warning('Please enter a search query');\n      return;\n    }\n\n    setIsSearching(true);\n\n    try {\n      // Call the FastAPI backend exactly like frontendV3.py does\n      const url = new URL('http://localhost:8001/query/');\n      url.searchParams.append('naturalQuery', searchQuery);\n\n      const response = await fetch(url, {\n        method: 'GET',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n      });\n\n      const data = await response.json();\n\n      if (response.ok) {\n        // Handle FastAPI response format exactly like frontendV3.py: {query: [], listOfDict: [], result: \"\"}\n        let transformedResults = [];\n\n        // Check if API returns a direct list (like frontendV3.py handles)\n        if (Array.isArray(data)) {\n          transformedResults = data.map(resume => transformResumeData(resume));\n        } else if (data.listOfDict && Array.isArray(data.listOfDict)) {\n          // Transform the resume data from MongoDB to display format\n          transformedResults = data.listOfDict.map(resume => transformResumeData(resume));\n        } else {\n          // Fallback to mock data if API doesn't return results\n          transformedResults = mockResults.filter(resume =>\n            resume.name.toLowerCase().includes(searchQuery.toLowerCase()) ||\n            resume.title.toLowerCase().includes(searchQuery.toLowerCase()) ||\n            resume.skills.some(skill => skill.toLowerCase().includes(searchQuery.toLowerCase()))\n          );\n        }\n\n        setSearchResults(transformedResults);\n\n        // Show success message with GPT result (exactly like frontendV3.py)\n        if (data.result && data.result !== 'Data received successfully.') {\n          toast.success(`🤖 GPT Analysis: ${data.result}`);\n        } else {\n          toast.success(`🔍 Found ${transformedResults.length} matching resumes`);\n        }\n\n        // Log GPT response and MongoDB query for debugging (like frontendV3.py developer mode)\n        if (data.query) {\n          console.log('🤖 MongoDB Query Generated by GPT:', JSON.stringify(data.query, null, 2));\n          console.log('📊 GPT Result:', data.result);\n          console.log('📋 Full API Response:', data);\n        }\n      } else {\n        throw new Error(data.error || 'Search failed');\n      }\n    } catch (error) {\n      console.error('Search error:', error);\n\n      // Fallback to mock data on network error\n      const fallbackResults = mockResults.filter(resume =>\n        resume.name.toLowerCase().includes(searchQuery.toLowerCase()) ||\n        resume.title.toLowerCase().includes(searchQuery.toLowerCase()) ||\n        resume.skills.some(skill => skill.toLowerCase().includes(searchQuery.toLowerCase()))\n      );\n\n      setSearchResults(fallbackResults);\n      toast.warning(`Using demo data - Found ${fallbackResults.length} matching resumes`);\n    } finally {\n      setIsSearching(false);\n    }\n  };\n\n  const handleShowAll = async (page = 1) => {\n    setIsSearching(true);\n    try {\n      // Call the FastAPI backend directly to get all resumes\n      const response = await fetch('http://localhost:8001/showall');\n      const data = await response.json();\n\n      if (response.ok) {\n        let transformedResults = [];\n\n        if (Array.isArray(data)) {\n          // FastAPI /showall returns array directly\n          transformedResults = data.map(resume => {\n            // Handle the nested Resume structure from MongoDB\n            const resumeData = resume.Resume || resume;\n\n            return {\n              id: resume._id || Math.random().toString(36).substr(2, 9),\n              name: resumeData.PersonalInformation?.FullName || resumeData.name || 'Unknown',\n              title: resumeData.PersonalInformation?.Designation || resumeData.title || 'No title',\n              email: resumeData.PersonalInformation?.Email || resumeData.email || 'No email',\n              phone: resumeData.PersonalInformation?.ContactNumber || resumeData.phone || 'No phone',\n              location: resumeData.PersonalInformation?.Address || resumeData.location || 'No location',\n              experience: resumeData.TotalWorkExperienceInYears ?\n                `${resumeData.TotalWorkExperienceInYears} years` :\n                resumeData.WorkExperience?.length ?\n                  `${resumeData.WorkExperience.length}+ positions` :\n                  resumeData.experience || 'No experience data',\n              skills: resumeData.Skills || resumeData.skills || [],\n              education: resumeData.Education?.length ?\n                resumeData.Education[0]?.Degree || 'Education available' :\n                resumeData.education || 'No education data',\n              summary: resumeData.Objective || resumeData.Summary || resumeData.summary || 'No summary available',\n              score: 90, // Default score for show all\n              rawData: resume // Keep original data for reference\n            };\n          });\n        } else if (data.error) {\n          throw new Error(data.error);\n        } else {\n          // Fallback to mock data if API doesn't return proper results\n          transformedResults = mockResults;\n        }\n\n        setSearchResults(transformedResults);\n        setCurrentPage(1);\n        setTotalCount(transformedResults.length);\n        setTotalPages(Math.ceil(transformedResults.length / resultsPerPage));\n        setSearchQuery('');\n\n        toast.success(`📊 Loaded ${transformedResults.length} resumes from database`);\n      } else {\n        throw new Error(data.error || 'Failed to load resumes');\n      }\n    } catch (error) {\n      console.error('Load resumes error:', error);\n\n      // Fallback to mock data on network error\n      setSearchResults(mockResults);\n      setCurrentPage(1);\n      setTotalCount(mockResults.length);\n      setTotalPages(Math.ceil(mockResults.length / resultsPerPage));\n      setSearchQuery('');\n\n      toast.warning(`Using demo data - Showing ${mockResults.length} sample resumes`);\n    } finally {\n      setIsSearching(false);\n    }\n  };\n\n  const handlePageChange = (page) => {\n    if (page >= 1 && page <= totalPages) {\n      handleShowAll(page);\n    }\n  };\n\n  const filteredResults = searchResults.filter(result => {\n    // Basic text filter\n    if (filterQuery) {\n      const matchesText = result.name.toLowerCase().includes(filterQuery.toLowerCase()) ||\n                         result.title.toLowerCase().includes(filterQuery.toLowerCase()) ||\n                         result.location.toLowerCase().includes(filterQuery.toLowerCase());\n      if (!matchesText) return false;\n    }\n\n    // Advanced filters\n    const filters = advancedFilters;\n\n    // Experience filter\n    if (filters.minExperience || filters.maxExperience) {\n      const experienceYears = result.rawData?.Resume?.TotalWorkExperienceInYears ||\n                             extractExperienceYears(result.experience) || 0;\n\n      if (filters.minExperience && experienceYears < parseInt(filters.minExperience)) return false;\n      if (filters.maxExperience && experienceYears > parseInt(filters.maxExperience)) return false;\n    }\n\n    // Education filter\n    if (filters.education) {\n      const hasEducation = result.rawData?.Resume?.Education?.some(edu =>\n        (edu.Degree || '').toLowerCase().includes(filters.education.toLowerCase())\n      ) || (result.education || '').toLowerCase().includes(filters.education.toLowerCase());\n      if (!hasEducation) return false;\n    }\n\n    // Location filter\n    if (filters.location) {\n      const matchesLocation = (result.location || '').toLowerCase().includes(filters.location.toLowerCase());\n      if (!matchesLocation) return false;\n    }\n\n    // Skills filter\n    if (filters.skills) {\n      const skillsText = filters.skills.toLowerCase();\n      const hasSkill = result.skills?.some(skill => skill.toLowerCase().includes(skillsText)) ||\n                      (result.rawData?.Resume?.Skills || []).some(skill =>\n                        skill.toLowerCase().includes(skillsText)\n                      );\n      if (!hasSkill) return false;\n    }\n\n    // Institution filter\n    if (filters.institution) {\n      const hasInstitution = result.rawData?.Resume?.Education?.some(edu =>\n        (edu.Institution || edu.School || '').toLowerCase().includes(filters.institution.toLowerCase())\n      );\n      if (!hasInstitution) return false;\n    }\n\n    // Graduation year filter\n    if (filters.graduationYear) {\n      const hasGradYear = result.rawData?.Resume?.Education?.some(edu =>\n        (edu.GraduationYear || edu.Year || '').toString().includes(filters.graduationYear)\n      );\n      if (!hasGradYear) return false;\n    }\n\n    // Company filter\n    if (filters.company) {\n      const hasCompany = result.rawData?.Resume?.WorkExperience?.some(exp =>\n        (exp.Company || exp.CompanyName || exp.Organization || '').toLowerCase().includes(filters.company.toLowerCase())\n      );\n      if (!hasCompany) return false;\n    }\n\n    // Minimum marks filter\n    if (filters.minMarks) {\n      const hasMinMarks = result.rawData?.Resume?.Education?.some(edu => {\n        const marks = parseFloat(edu['GPA/Marks/%']) || 0;\n        return marks >= parseFloat(filters.minMarks);\n      });\n      if (!hasMinMarks) return false;\n    }\n\n    return true;\n  });\n\n  // Helper function to extract experience years from text\n  const extractExperienceYears = (experienceText) => {\n    if (!experienceText) return 0;\n    const match = experienceText.match(/(\\d+)\\+?\\s*years?/i);\n    return match ? parseInt(match[1]) : 0;\n  };\n\n  const handleAdvancedFilterChange = (filterName, value) => {\n    setAdvancedFilters(prev => ({\n      ...prev,\n      [filterName]: value\n    }));\n  };\n\n  const clearAllFilters = () => {\n    setFilterQuery('');\n    setAdvancedFilters({\n      minExperience: '',\n      maxExperience: '',\n      education: '',\n      location: '',\n      skills: '',\n      minMarks: '',\n      institution: '',\n      graduationYear: '',\n      company: ''\n    });\n  };\n\n  const hasActiveFilters = () => {\n    return filterQuery || Object.values(advancedFilters).some(value => value !== '');\n  };\n\n  const handleDownload = (resume) => {\n    try {\n      // Create a comprehensive text-based resume content\n      const resumeContent = `\n${resume.name}\n${resume.title}\n${resume.email} | ${resume.phone} | ${resume.location}\n\nPROFESSIONAL SUMMARY\n${resume.summary}\n\nWORK EXPERIENCE\n${resume.rawData?.Resume?.WorkExperience ?\n  resume.rawData.Resume.WorkExperience.map(exp =>\n    `${exp.JobTitle || exp.Position} at ${exp.Company || exp.Organization}\nDuration: ${exp.Duration || exp.Years || 'Not specified'}\n${exp.Description ? 'Description: ' + exp.Description : ''}\n${exp.Responsibilities ? 'Responsibilities: ' + exp.Responsibilities : ''}\n`\n  ).join('\\n') : resume.experience}\n\n${resume.rawData?.Resume?.TotalWorkExperienceInYears ?\n  `Total Experience: ${resume.rawData.Resume.TotalWorkExperienceInYears} years\\n` : ''}\n\nEDUCATION\n${resume.rawData?.Resume?.Education ?\n  resume.rawData.Resume.Education.map(edu =>\n    `${edu.Degree || 'Degree'} from ${edu.Institution || edu.School || 'Institution'}\nGraduation Year: ${edu.GraduationYear || edu.Year || 'Not specified'}\n${edu['GPA/Marks/%'] && edu['GPA/Marks/%'] > 0 ? 'Marks: ' + edu['GPA/Marks/%'] + '%' : ''}\n`\n  ).join('\\n') : resume.education}\n\nSKILLS & TECHNOLOGIES\n${resume.skills.join(', ')}\n\n${resume.rawData?.Resume?.Languages && resume.rawData.Resume.Languages.length > 0 ?\n  `LANGUAGES\\n${resume.rawData.Resume.Languages.join(', ')}\\n` : ''}\n\n${resume.rawData?.Resume?.Projects && resume.rawData.Resume.Projects.length > 0 ?\n  `PROJECTS\\n${resume.rawData.Resume.Projects.map(project =>\n    `${project.Name || project.Title || 'Project'}\n${project.Description ? 'Description: ' + project.Description : ''}\n${project.Technologies ? 'Technologies: ' + project.Technologies : ''}\n`\n  ).join('\\n')}` : ''}\n\n${resume.rawData?.Resume?.Certifications && resume.rawData.Resume.Certifications.length > 0 ?\n  `CERTIFICATIONS\\n${resume.rawData.Resume.Certifications.map(cert =>\n    `${cert.Name || cert.Title || 'Certification'}\n${cert.IssuingOrganization ? 'Issued by: ' + cert.IssuingOrganization : ''}\n${cert.IssueDate ? 'Date: ' + cert.IssueDate : ''}\n`\n  ).join('\\n')}` : ''}\n\n${resume.rawData?.Resume?.Achievements && resume.rawData.Resume.Achievements.length > 0 ?\n  `ACHIEVEMENTS\\n${resume.rawData.Resume.Achievements.map(achievement =>\n    typeof achievement === 'string' ? achievement :\n    `${achievement.AchievementName || achievement.Name || 'Achievement'}\n${achievement.IssueDate ? 'Date: ' + achievement.IssueDate : ''}\n${achievement.IssuingOrganization ? 'Organization: ' + achievement.IssuingOrganization : ''}\n${achievement.Description ? 'Description: ' + achievement.Description : ''}\n`\n  ).join('\\n')}` : ''}\n\n${resume.rawData?.Resume?.PersonalInformation?.LinkedIn ?\n  `ADDITIONAL INFORMATION\\nLinkedIn: ${resume.rawData.Resume.PersonalInformation.LinkedIn}` : ''}\n\nGenerated on: ${new Date().toLocaleDateString()}\n      `.trim();\n\n      // Create and download the file\n      const blob = new Blob([resumeContent], { type: 'text/plain' });\n      const url = URL.createObjectURL(blob);\n      const link = document.createElement('a');\n      link.href = url;\n      link.download = `${resume.name.replace(/\\s+/g, '_')}_Resume.txt`;\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n      URL.revokeObjectURL(url);\n\n      toast.success(`Downloaded resume for ${resume.name}`);\n    } catch (error) {\n      console.error('Download error:', error);\n      toast.error('Failed to download resume. Please try again.');\n    }\n  };\n\n  const handleView = (resume) => {\n    setSelectedResume(resume);\n    setIsModalOpen(true);\n    toast.info(`Opening detailed view for ${resume.name}`);\n  };\n\n  const handleCloseModal = () => {\n    setIsModalOpen(false);\n    setSelectedResume(null);\n  };\n\n  const handleEmailClick = (email, name) => {\n    window.open(`mailto:${email}?subject=Regarding your resume&body=Hi ${name},%0D%0A%0D%0AI found your resume and would like to discuss potential opportunities.%0D%0A%0D%0ABest regards`, '_blank');\n    toast.info(`Opening email client to contact ${name}`);\n  };\n\n  const handlePhoneClick = (phone, name) => {\n    if (navigator.userAgent.match(/Mobile|Android|iPhone|iPad/)) {\n      window.open(`tel:${phone}`, '_blank');\n    } else {\n      navigator.clipboard.writeText(phone).then(() => {\n        toast.success(`Phone number copied: ${phone}`);\n      }).catch(() => {\n        toast.info(`Phone: ${phone}`);\n      });\n    }\n  };\n\n\n\n  const handleExportAll = () => {\n    if (filteredResults.length === 0) {\n      toast.warning('No results to export');\n      return;\n    }\n\n    try {\n      const exportData = filteredResults.map(resume => ({\n        name: resume.name,\n        title: resume.title,\n        email: resume.email,\n        phone: resume.phone,\n        location: resume.location,\n        experience: resume.experience,\n        education: resume.education,\n        skills: resume.skills.join(', '),\n        summary: resume.summary,\n        matchScore: resume.score\n      }));\n\n      const csvContent = [\n        'Name,Title,Email,Phone,Location,Experience,Education,Skills,Summary,Match Score',\n        ...exportData.map(row =>\n          Object.values(row).map(value =>\n            `\"${String(value).replace(/\"/g, '\"\"')}\"`\n          ).join(',')\n        )\n      ].join('\\n');\n\n      const blob = new Blob([csvContent], { type: 'text/csv' });\n      const url = URL.createObjectURL(blob);\n      const link = document.createElement('a');\n      link.href = url;\n      link.download = `Resume_Search_Results_${new Date().toISOString().split('T')[0]}.csv`;\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n      URL.revokeObjectURL(url);\n\n      toast.success(`Exported ${filteredResults.length} resumes to CSV`);\n    } catch (error) {\n      console.error('Export error:', error);\n      toast.error('Failed to export results. Please try again.');\n    }\n  };\n\n  return (\n    <div className=\"search-page\">\n      <div className=\"page-header\">\n        <h1>Search Resumes</h1>\n        <p>Use natural language to find the perfect candidates for your needs</p>\n      </div>\n\n      {/* Search Section */}\n      <div className=\"search-section\">\n        <form onSubmit={handleSearch} className=\"search-form\">\n          <div className=\"search-input-group\">\n            <div className=\"search-input-wrapper\">\n              <FiSearch className=\"search-icon\" />\n              <input\n                type=\"text\"\n                placeholder=\"e.g., 'Find Python developers with 5+ years experience' or 'Show frontend engineers in California'\"\n                value={searchQuery}\n                onChange={(e) => setSearchQuery(e.target.value)}\n                className=\"search-input\"\n                disabled={isSearching}\n              />\n            </div>\n            <div className=\"search-buttons\">\n              <button\n                type=\"submit\"\n                className=\"btn btn-primary\"\n                disabled={isSearching}\n              >\n                {isSearching ? (\n                  <>\n                    <div className=\"loading-spinner\" />\n                    Searching...\n                  </>\n                ) : (\n                  <>\n                    <FiSearch size={16} />\n                    Search\n                  </>\n                )}\n              </button>\n              <button\n                type=\"button\"\n                className=\"btn btn-secondary\"\n                onClick={handleShowAll}\n                disabled={isSearching}\n              >\n                Show All\n              </button>\n            </div>\n          </div>\n        </form>\n\n        {/* Filter Section */}\n        {searchResults.length > 0 && (\n          <div className=\"filter-section\">\n            <div className=\"filter-header\">\n              <div className=\"basic-filter-wrapper\">\n                <FiFilter className=\"filter-icon\" />\n                <input\n                  type=\"text\"\n                  placeholder=\"Quick filter by name, title, or location...\"\n                  value={filterQuery}\n                  onChange={(e) => setFilterQuery(e.target.value)}\n                  className=\"filter-input\"\n                />\n              </div>\n              <div className=\"filter-controls\">\n                <button\n                  type=\"button\"\n                  className={`btn btn-secondary filter-toggle ${showFilters ? 'active' : ''}`}\n                  onClick={() => setShowFilters(!showFilters)}\n                >\n                  <FiSettings size={16} />\n                  Advanced Filters\n                  {showFilters ? <FiChevronUp size={16} /> : <FiChevronDown size={16} />}\n                </button>\n                {hasActiveFilters() && (\n                  <button\n                    type=\"button\"\n                    className=\"btn btn-outline clear-filters\"\n                    onClick={clearAllFilters}\n                  >\n                    <FiX size={16} />\n                    Clear All\n                  </button>\n                )}\n              </div>\n            </div>\n\n            {/* Advanced Filters Panel */}\n            {showFilters && (\n              <div className=\"advanced-filters\">\n                <div className=\"filters-grid\">\n                  <div className=\"filter-group\">\n                    <label>Experience (Years)</label>\n                    <div className=\"range-inputs\">\n                      <input\n                        type=\"number\"\n                        placeholder=\"Min\"\n                        value={advancedFilters.minExperience}\n                        onChange={(e) => handleAdvancedFilterChange('minExperience', e.target.value)}\n                        className=\"filter-input small\"\n                        min=\"0\"\n                      />\n                      <span>to</span>\n                      <input\n                        type=\"number\"\n                        placeholder=\"Max\"\n                        value={advancedFilters.maxExperience}\n                        onChange={(e) => handleAdvancedFilterChange('maxExperience', e.target.value)}\n                        className=\"filter-input small\"\n                        min=\"0\"\n                      />\n                    </div>\n                  </div>\n\n                  <div className=\"filter-group\">\n                    <label>Education Level</label>\n                    <input\n                      type=\"text\"\n                      placeholder=\"e.g., Bachelor, Master, PhD\"\n                      value={advancedFilters.education}\n                      onChange={(e) => handleAdvancedFilterChange('education', e.target.value)}\n                      className=\"filter-input\"\n                    />\n                  </div>\n\n                  <div className=\"filter-group\">\n                    <label>Location</label>\n                    <input\n                      type=\"text\"\n                      placeholder=\"e.g., Mumbai, Delhi, Bangalore\"\n                      value={advancedFilters.location}\n                      onChange={(e) => handleAdvancedFilterChange('location', e.target.value)}\n                      className=\"filter-input\"\n                    />\n                  </div>\n\n                  <div className=\"filter-group\">\n                    <label>Skills</label>\n                    <input\n                      type=\"text\"\n                      placeholder=\"e.g., Python, React, Java\"\n                      value={advancedFilters.skills}\n                      onChange={(e) => handleAdvancedFilterChange('skills', e.target.value)}\n                      className=\"filter-input\"\n                    />\n                  </div>\n\n                  <div className=\"filter-group\">\n                    <label>Institution</label>\n                    <input\n                      type=\"text\"\n                      placeholder=\"e.g., IIT, University name\"\n                      value={advancedFilters.institution}\n                      onChange={(e) => handleAdvancedFilterChange('institution', e.target.value)}\n                      className=\"filter-input\"\n                    />\n                  </div>\n\n                  <div className=\"filter-group\">\n                    <label>Graduation Year</label>\n                    <input\n                      type=\"text\"\n                      placeholder=\"e.g., 2020, 2021\"\n                      value={advancedFilters.graduationYear}\n                      onChange={(e) => handleAdvancedFilterChange('graduationYear', e.target.value)}\n                      className=\"filter-input\"\n                    />\n                  </div>\n\n                  <div className=\"filter-group\">\n                    <label>Company</label>\n                    <input\n                      type=\"text\"\n                      placeholder=\"e.g., Google, Microsoft, TCS\"\n                      value={advancedFilters.company}\n                      onChange={(e) => handleAdvancedFilterChange('company', e.target.value)}\n                      className=\"filter-input\"\n                    />\n                  </div>\n\n                  <div className=\"filter-group\">\n                    <label>Minimum Marks (%)</label>\n                    <input\n                      type=\"number\"\n                      placeholder=\"e.g., 75, 80\"\n                      value={advancedFilters.minMarks}\n                      onChange={(e) => handleAdvancedFilterChange('minMarks', e.target.value)}\n                      className=\"filter-input\"\n                      min=\"0\"\n                      max=\"100\"\n                    />\n                  </div>\n                </div>\n              </div>\n            )}\n\n            <div className=\"results-count\">\n              {filteredResults.length} of {searchResults.length} results\n              {hasActiveFilters() && <span className=\"filtered-indicator\"> (filtered)</span>}\n            </div>\n          </div>\n        )}\n      </div>\n\n      {/* Results Section */}\n      {searchResults.length > 0 ? (\n        <div className=\"results-section\">\n          <div className=\"results-header\">\n            <h2>Search Results</h2>\n            <div className=\"results-actions\">\n              <button\n                className=\"btn btn-secondary\"\n                onClick={handleExportAll}\n                title={`Export ${filteredResults.length} results to CSV`}\n              >\n                <FiDownload size={16} />\n                Export All ({filteredResults.length})\n              </button>\n            </div>\n          </div>\n\n          <div className=\"results-grid\">\n            {filteredResults.map((resume) => (\n              <div key={resume.id} className=\"resume-card\">\n                <div className=\"resume-header\">\n                  <div className=\"resume-avatar\">\n                    <FiUser size={24} />\n                  </div>\n                  <div className=\"resume-info\">\n                    <h3 className=\"resume-name\">{resume.name}</h3>\n                    <p className=\"resume-title\">{resume.title}</p>\n                  </div>\n                </div>\n\n                <div className=\"resume-details\">\n                  <div className=\"detail-item location-item\">\n                    <FiMapPin size={14} />\n                    <span>{resume.location}</span>\n                  </div>\n                  <div\n                    className=\"detail-item email-item clickable\"\n                    onClick={() => handleEmailClick(resume.email, resume.name)}\n                    title={`Send email to ${resume.name}`}\n                  >\n                    <FiMail size={14} />\n                    <span>{resume.email}</span>\n                    <FiExternalLink size={12} className=\"external-icon\" />\n                  </div>\n                  <div\n                    className=\"detail-item phone-item clickable\"\n                    onClick={() => handlePhoneClick(resume.phone, resume.name)}\n                    title={`Call ${resume.name}`}\n                  >\n                    <FiPhone size={14} />\n                    <span>{resume.phone}</span>\n                    <FiExternalLink size={12} className=\"external-icon\" />\n                  </div>\n                </div>\n\n                <div className=\"resume-content\">\n                  <div className=\"content-row\">\n                    <div className=\"content-section\">\n                      <h4>Experience</h4>\n                      <p>{resume.experience}</p>\n                    </div>\n                    <div className=\"content-section\">\n                      <h4>Education</h4>\n                      <p>{resume.education}</p>\n                    </div>\n                  </div>\n                  <div className=\"content-section\">\n                    <h4>Skills</h4>\n                    <div className=\"skills-list\">\n                      {resume.skills.map((skill, index) => (\n                        <span key={index} className=\"skill-tag\">{skill}</span>\n                      ))}\n                    </div>\n                  </div>\n                </div>\n\n                <div className=\"resume-actions\">\n                  <button\n                    className=\"btn btn-secondary\"\n                    onClick={() => handleView(resume)}\n                  >\n                    <FiEye size={16} />\n                    View Details\n                  </button>\n                  <button\n                    className=\"btn btn-primary\"\n                    onClick={() => handleDownload(resume)}\n                  >\n                    <FiDownload size={16} />\n                    Download\n                  </button>\n                </div>\n              </div>\n            ))}\n          </div>\n\n          {/* Pagination */}\n          {totalPages > 1 && (\n            <div className=\"pagination-section\">\n              <div className=\"pagination-info\">\n                Showing {((currentPage - 1) * resultsPerPage) + 1} to {Math.min(currentPage * resultsPerPage, totalCount)} of {totalCount} results\n              </div>\n              <div className=\"pagination-controls\">\n                <button\n                  className=\"btn btn-secondary pagination-btn\"\n                  onClick={() => handlePageChange(currentPage - 1)}\n                  disabled={currentPage === 1}\n                >\n                  Previous\n                </button>\n\n                <div className=\"page-numbers\">\n                  {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {\n                    let pageNum;\n                    if (totalPages <= 5) {\n                      pageNum = i + 1;\n                    } else if (currentPage <= 3) {\n                      pageNum = i + 1;\n                    } else if (currentPage >= totalPages - 2) {\n                      pageNum = totalPages - 4 + i;\n                    } else {\n                      pageNum = currentPage - 2 + i;\n                    }\n\n                    return (\n                      <button\n                        key={pageNum}\n                        className={`btn pagination-btn ${currentPage === pageNum ? 'btn-primary' : 'btn-secondary'}`}\n                        onClick={() => handlePageChange(pageNum)}\n                      >\n                        {pageNum}\n                      </button>\n                    );\n                  })}\n                </div>\n\n                <button\n                  className=\"btn btn-secondary pagination-btn\"\n                  onClick={() => handlePageChange(currentPage + 1)}\n                  disabled={currentPage === totalPages}\n                >\n                  Next\n                </button>\n              </div>\n            </div>\n          )}\n        </div>\n      ) : !isSearching ? (\n        <div className=\"empty-state\">\n          <div className=\"empty-icon\">\n            <FiSearch size={48} />\n          </div>\n          <h3>Ready to Search!</h3>\n          <p>Use the search box above to find resumes using natural language, or click \"Show All\" to view all resumes in the database.</p>\n          <div className=\"search-examples\">\n            <h4>Try searching for:</h4>\n            <ul>\n              <li>\"Find Python developers\"</li>\n              <li>\"Show teachers with 5+ years experience\"</li>\n              <li>\"Software engineers in Mumbai\"</li>\n            </ul>\n          </div>\n        </div>\n      ) : null}\n\n      {/* Resume Details Modal */}\n      <ResumeModal\n        resume={selectedResume}\n        isOpen={isModalOpen}\n        onClose={handleCloseModal}\n      />\n    </div>\n  );\n};\n\nexport default SearchResumes;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,QAAQ,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,KAAK,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,OAAO,EAAEC,cAAc,EAAEC,MAAM,EAAEC,GAAG,EAAEC,UAAU,EAAEC,aAAa,EAAEC,WAAW,QAAQ,gBAAgB;AAC9K,SAASC,KAAK,QAAQ,gBAAgB;AACtC,OAAOC,WAAW,MAAM,2BAA2B;AACnD,OAAO,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE7B,MAAMC,aAAa,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC1B,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACyB,WAAW,EAAEC,cAAc,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAAC2B,aAAa,EAAEC,gBAAgB,CAAC,GAAG5B,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAAC6B,WAAW,EAAEC,cAAc,CAAC,GAAG9B,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC+B,WAAW,EAAEC,cAAc,CAAC,GAAGhC,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACiC,eAAe,EAAEC,kBAAkB,CAAC,GAAGlC,QAAQ,CAAC;IACrDmC,aAAa,EAAE,EAAE;IACjBC,aAAa,EAAE,EAAE;IACjBC,SAAS,EAAE,EAAE;IACbC,QAAQ,EAAE,EAAE;IACZC,MAAM,EAAE,EAAE;IACVC,QAAQ,EAAE,EAAE;IACZC,WAAW,EAAE,EAAE;IACfC,cAAc,EAAE,EAAE;IAClBC,OAAO,EAAE;EACX,CAAC,CAAC;EACF,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAG7C,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAAC8C,WAAW,EAAEC,cAAc,CAAC,GAAG/C,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACgD,WAAW,EAAEC,cAAc,CAAC,GAAGjD,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAACkD,UAAU,EAAEC,aAAa,CAAC,GAAGnD,QAAQ,CAAC,CAAC,CAAC;EAC/C,MAAM,CAACoD,UAAU,EAAEC,aAAa,CAAC,GAAGrD,QAAQ,CAAC,CAAC,CAAC;EAC/C,MAAMsD,cAAc,GAAG,EAAE;;EAEzB;EACA,MAAMC,WAAW,GAAG,CAClB;IACEC,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,UAAU;IAChBC,KAAK,EAAE,0BAA0B;IACjCC,KAAK,EAAE,oBAAoB;IAC3BC,KAAK,EAAE,mBAAmB;IAC1BtB,QAAQ,EAAE,mBAAmB;IAC7BuB,UAAU,EAAE,UAAU;IACtBtB,MAAM,EAAE,CAAC,QAAQ,EAAE,OAAO,EAAE,SAAS,EAAE,KAAK,CAAC;IAC7CF,SAAS,EAAE,qBAAqB;IAChCyB,OAAO,EAAE,2EAA2E;IACpFC,KAAK,EAAE,EAAE;IACTC,OAAO,EAAE;MACPC,MAAM,EAAE;QACNC,mBAAmB,EAAE;UACnBC,QAAQ,EAAE,UAAU;UACpBC,KAAK,EAAE,oBAAoB;UAC3BC,aAAa,EAAE,mBAAmB;UAClCC,OAAO,EAAE;QACX,CAAC;QACDC,cAAc,EAAE,CACd;UACEC,IAAI,EAAE,0BAA0B;UAChCC,WAAW,EAAE,WAAW;UACxBC,SAAS,EAAE,MAAM;UACjBC,OAAO,EAAE,SAAS;UAClB,4BAA4B,EAAE;QAChC,CAAC,EACD;UACEH,IAAI,EAAE,mBAAmB;UACzBC,WAAW,EAAE,YAAY;UACzBC,SAAS,EAAE,MAAM;UACjBC,OAAO,EAAE,MAAM;UACf,4BAA4B,EAAE;QAChC,CAAC,CACF;QACDC,SAAS,EAAE,CACT;UACEC,MAAM,EAAE,qBAAqB;UAC7BC,WAAW,EAAE,qBAAqB;UAClCC,cAAc,EAAE,MAAM;UACtB,aAAa,EAAE;QACjB,CAAC,CACF;QACDC,MAAM,EAAE,CAAC,QAAQ,EAAE,OAAO,EAAE,SAAS,EAAE,KAAK,CAAC;QAC7CC,0BAA0B,EAAE;MAC9B;IACF;EACF,CAAC,EACD;IACEzB,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,YAAY;IAClBC,KAAK,EAAE,oBAAoB;IAC3BC,KAAK,EAAE,sBAAsB;IAC7BC,KAAK,EAAE,mBAAmB;IAC1BtB,QAAQ,EAAE,cAAc;IACxBuB,UAAU,EAAE,UAAU;IACtBtB,MAAM,EAAE,CAAC,OAAO,EAAE,YAAY,EAAE,KAAK,EAAE,YAAY,CAAC;IACpDF,SAAS,EAAE,qBAAqB;IAChCyB,OAAO,EAAE,iEAAiE;IAC1EC,KAAK,EAAE,EAAE;IACTC,OAAO,EAAE;MACPC,MAAM,EAAE;QACNC,mBAAmB,EAAE;UACnBC,QAAQ,EAAE,YAAY;UACtBC,KAAK,EAAE,sBAAsB;UAC7BC,aAAa,EAAE,mBAAmB;UAClCC,OAAO,EAAE;QACX,CAAC;QACDC,cAAc,EAAE,CACd;UACEC,IAAI,EAAE,oBAAoB;UAC1BC,WAAW,EAAE,eAAe;UAC5BC,SAAS,EAAE,MAAM;UACjBC,OAAO,EAAE,SAAS;UAClB,4BAA4B,EAAE;QAChC,CAAC,CACF;QACDC,SAAS,EAAE,CACT;UACEC,MAAM,EAAE,qBAAqB;UAC7BC,WAAW,EAAE,KAAK;UAClBC,cAAc,EAAE,MAAM;UACtB,aAAa,EAAE;QACjB,CAAC,CACF;QACDC,MAAM,EAAE,CAAC,OAAO,EAAE,YAAY,EAAE,KAAK,EAAE,YAAY,CAAC;QACpDC,0BAA0B,EAAE;MAC9B;IACF;EACF,CAAC,EACD;IACEzB,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,cAAc;IACpBC,KAAK,EAAE,iBAAiB;IACxBC,KAAK,EAAE,wBAAwB;IAC/BC,KAAK,EAAE,mBAAmB;IAC1BtB,QAAQ,EAAE,YAAY;IACtBuB,UAAU,EAAE,UAAU;IACtBtB,MAAM,EAAE,CAAC,QAAQ,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,CAAC;IACjDF,SAAS,EAAE,2BAA2B;IACtCyB,OAAO,EAAE,wEAAwE;IACjFC,KAAK,EAAE,EAAE;IACTC,OAAO,EAAE;MACPC,MAAM,EAAE;QACNC,mBAAmB,EAAE;UACnBC,QAAQ,EAAE,cAAc;UACxBC,KAAK,EAAE,wBAAwB;UAC/BC,aAAa,EAAE,mBAAmB;UAClCC,OAAO,EAAE;QACX,CAAC;QACDC,cAAc,EAAE,CACd;UACEC,IAAI,EAAE,iBAAiB;UACvBC,WAAW,EAAE,qBAAqB;UAClCC,SAAS,EAAE,MAAM;UACjBC,OAAO,EAAE,SAAS;UAClB,4BAA4B,EAAE;QAChC,CAAC,CACF;QACDC,SAAS,EAAE,CACT;UACEC,MAAM,EAAE,2BAA2B;UACnCC,WAAW,EAAE,WAAW;UACxBC,cAAc,EAAE,MAAM;UACtB,aAAa,EAAE;QACjB,CAAC,CACF;QACDC,MAAM,EAAE,CAAC,QAAQ,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,CAAC;QACjDC,0BAA0B,EAAE;MAC9B;IACF;EACF,CAAC,CACF;EAED,MAAMC,YAAY,GAAG,MAAOC,CAAC,IAAK;IAChCA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,IAAI,CAAC7D,WAAW,CAAC8D,IAAI,CAAC,CAAC,EAAE;MACvBtE,KAAK,CAACuE,OAAO,CAAC,6BAA6B,CAAC;MAC5C;IACF;IAEA5D,cAAc,CAAC,IAAI,CAAC;IAEpB,IAAI;MACF;MACA,MAAM6D,GAAG,GAAG,IAAIC,GAAG,CAAC,8BAA8B,CAAC;MACnDD,GAAG,CAACE,YAAY,CAACC,MAAM,CAAC,cAAc,EAAEnE,WAAW,CAAC;MAEpD,MAAMoE,QAAQ,GAAG,MAAMC,KAAK,CAACL,GAAG,EAAE;QAChCM,MAAM,EAAE,KAAK;QACbC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB;MACF,CAAC,CAAC;MAEF,MAAMC,IAAI,GAAG,MAAMJ,QAAQ,CAACK,IAAI,CAAC,CAAC;MAElC,IAAIL,QAAQ,CAACM,EAAE,EAAE;QACf;QACA,IAAIC,kBAAkB,GAAG,EAAE;;QAE3B;QACA,IAAIC,KAAK,CAACC,OAAO,CAACL,IAAI,CAAC,EAAE;UACvBG,kBAAkB,GAAGH,IAAI,CAACM,GAAG,CAACC,MAAM,IAAIC,mBAAmB,CAACD,MAAM,CAAC,CAAC;QACtE,CAAC,MAAM,IAAIP,IAAI,CAACS,UAAU,IAAIL,KAAK,CAACC,OAAO,CAACL,IAAI,CAACS,UAAU,CAAC,EAAE;UAC5D;UACAN,kBAAkB,GAAGH,IAAI,CAACS,UAAU,CAACH,GAAG,CAACC,MAAM,IAAIC,mBAAmB,CAACD,MAAM,CAAC,CAAC;QACjF,CAAC,MAAM;UACL;UACAJ,kBAAkB,GAAG3C,WAAW,CAACkD,MAAM,CAACH,MAAM,IAC5CA,MAAM,CAAC7C,IAAI,CAACiD,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACpF,WAAW,CAACmF,WAAW,CAAC,CAAC,CAAC,IAC7DJ,MAAM,CAAC5C,KAAK,CAACgD,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACpF,WAAW,CAACmF,WAAW,CAAC,CAAC,CAAC,IAC9DJ,MAAM,CAAC/D,MAAM,CAACqE,IAAI,CAACC,KAAK,IAAIA,KAAK,CAACH,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACpF,WAAW,CAACmF,WAAW,CAAC,CAAC,CAAC,CACrF,CAAC;QACH;QAEA9E,gBAAgB,CAACsE,kBAAkB,CAAC;;QAEpC;QACA,IAAIH,IAAI,CAACe,MAAM,IAAIf,IAAI,CAACe,MAAM,KAAK,6BAA6B,EAAE;UAChE/F,KAAK,CAACgG,OAAO,CAAC,oBAAoBhB,IAAI,CAACe,MAAM,EAAE,CAAC;QAClD,CAAC,MAAM;UACL/F,KAAK,CAACgG,OAAO,CAAC,YAAYb,kBAAkB,CAACc,MAAM,mBAAmB,CAAC;QACzE;;QAEA;QACA,IAAIjB,IAAI,CAACkB,KAAK,EAAE;UACdC,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAEC,IAAI,CAACC,SAAS,CAACtB,IAAI,CAACkB,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;UACtFC,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEpB,IAAI,CAACe,MAAM,CAAC;UAC1CI,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEpB,IAAI,CAAC;QAC5C;MACF,CAAC,MAAM;QACL,MAAM,IAAIuB,KAAK,CAACvB,IAAI,CAACwB,KAAK,IAAI,eAAe,CAAC;MAChD;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdL,OAAO,CAACK,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;;MAErC;MACA,MAAMC,eAAe,GAAGjE,WAAW,CAACkD,MAAM,CAACH,MAAM,IAC/CA,MAAM,CAAC7C,IAAI,CAACiD,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACpF,WAAW,CAACmF,WAAW,CAAC,CAAC,CAAC,IAC7DJ,MAAM,CAAC5C,KAAK,CAACgD,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACpF,WAAW,CAACmF,WAAW,CAAC,CAAC,CAAC,IAC9DJ,MAAM,CAAC/D,MAAM,CAACqE,IAAI,CAACC,KAAK,IAAIA,KAAK,CAACH,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACpF,WAAW,CAACmF,WAAW,CAAC,CAAC,CAAC,CACrF,CAAC;MAED9E,gBAAgB,CAAC4F,eAAe,CAAC;MACjCzG,KAAK,CAACuE,OAAO,CAAC,2BAA2BkC,eAAe,CAACR,MAAM,mBAAmB,CAAC;IACrF,CAAC,SAAS;MACRtF,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;EAED,MAAM+F,aAAa,GAAG,MAAAA,CAAOC,IAAI,GAAG,CAAC,KAAK;IACxChG,cAAc,CAAC,IAAI,CAAC;IACpB,IAAI;MACF;MACA,MAAMiE,QAAQ,GAAG,MAAMC,KAAK,CAAC,+BAA+B,CAAC;MAC7D,MAAMG,IAAI,GAAG,MAAMJ,QAAQ,CAACK,IAAI,CAAC,CAAC;MAElC,IAAIL,QAAQ,CAACM,EAAE,EAAE;QACf,IAAIC,kBAAkB,GAAG,EAAE;QAE3B,IAAIC,KAAK,CAACC,OAAO,CAACL,IAAI,CAAC,EAAE;UACvB;UACAG,kBAAkB,GAAGH,IAAI,CAACM,GAAG,CAACC,MAAM,IAAI;YAAA,IAAAqB,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,sBAAA;YACtC;YACA,MAAMC,UAAU,GAAG7B,MAAM,CAACrC,MAAM,IAAIqC,MAAM;YAE1C,OAAO;cACL9C,EAAE,EAAE8C,MAAM,CAAC8B,GAAG,IAAIC,IAAI,CAACC,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;cACzD/E,IAAI,EAAE,EAAAkE,qBAAA,GAAAQ,UAAU,CAACjE,mBAAmB,cAAAyD,qBAAA,uBAA9BA,qBAAA,CAAgCxD,QAAQ,KAAIgE,UAAU,CAAC1E,IAAI,IAAI,SAAS;cAC9EC,KAAK,EAAE,EAAAkE,sBAAA,GAAAO,UAAU,CAACjE,mBAAmB,cAAA0D,sBAAA,uBAA9BA,sBAAA,CAAgCa,WAAW,KAAIN,UAAU,CAACzE,KAAK,IAAI,UAAU;cACpFC,KAAK,EAAE,EAAAkE,sBAAA,GAAAM,UAAU,CAACjE,mBAAmB,cAAA2D,sBAAA,uBAA9BA,sBAAA,CAAgCzD,KAAK,KAAI+D,UAAU,CAACxE,KAAK,IAAI,UAAU;cAC9EC,KAAK,EAAE,EAAAkE,sBAAA,GAAAK,UAAU,CAACjE,mBAAmB,cAAA4D,sBAAA,uBAA9BA,sBAAA,CAAgCzD,aAAa,KAAI8D,UAAU,CAACvE,KAAK,IAAI,UAAU;cACtFtB,QAAQ,EAAE,EAAAyF,sBAAA,GAAAI,UAAU,CAACjE,mBAAmB,cAAA6D,sBAAA,uBAA9BA,sBAAA,CAAgCzD,OAAO,KAAI6D,UAAU,CAAC7F,QAAQ,IAAI,aAAa;cACzFuB,UAAU,EAAEsE,UAAU,CAAClD,0BAA0B,GAC/C,GAAGkD,UAAU,CAAClD,0BAA0B,QAAQ,GAChD,CAAA+C,qBAAA,GAAAG,UAAU,CAAC5D,cAAc,cAAAyD,qBAAA,eAAzBA,qBAAA,CAA2BhB,MAAM,GAC/B,GAAGmB,UAAU,CAAC5D,cAAc,CAACyC,MAAM,aAAa,GAChDmB,UAAU,CAACtE,UAAU,IAAI,oBAAoB;cACjDtB,MAAM,EAAE4F,UAAU,CAACnD,MAAM,IAAImD,UAAU,CAAC5F,MAAM,IAAI,EAAE;cACpDF,SAAS,EAAE,CAAA4F,qBAAA,GAAAE,UAAU,CAACvD,SAAS,cAAAqD,qBAAA,eAApBA,qBAAA,CAAsBjB,MAAM,GACrC,EAAAkB,sBAAA,GAAAC,UAAU,CAACvD,SAAS,CAAC,CAAC,CAAC,cAAAsD,sBAAA,uBAAvBA,sBAAA,CAAyBrD,MAAM,KAAI,qBAAqB,GACxDsD,UAAU,CAAC9F,SAAS,IAAI,mBAAmB;cAC7CyB,OAAO,EAAEqE,UAAU,CAACO,SAAS,IAAIP,UAAU,CAACQ,OAAO,IAAIR,UAAU,CAACrE,OAAO,IAAI,sBAAsB;cACnGC,KAAK,EAAE,EAAE;cAAE;cACXC,OAAO,EAAEsC,MAAM,CAAC;YAClB,CAAC;UACH,CAAC,CAAC;QACJ,CAAC,MAAM,IAAIP,IAAI,CAACwB,KAAK,EAAE;UACrB,MAAM,IAAID,KAAK,CAACvB,IAAI,CAACwB,KAAK,CAAC;QAC7B,CAAC,MAAM;UACL;UACArB,kBAAkB,GAAG3C,WAAW;QAClC;QAEA3B,gBAAgB,CAACsE,kBAAkB,CAAC;QACpCjD,cAAc,CAAC,CAAC,CAAC;QACjBI,aAAa,CAAC6C,kBAAkB,CAACc,MAAM,CAAC;QACxC7D,aAAa,CAACkF,IAAI,CAACO,IAAI,CAAC1C,kBAAkB,CAACc,MAAM,GAAG1D,cAAc,CAAC,CAAC;QACpE9B,cAAc,CAAC,EAAE,CAAC;QAElBT,KAAK,CAACgG,OAAO,CAAC,aAAab,kBAAkB,CAACc,MAAM,wBAAwB,CAAC;MAC/E,CAAC,MAAM;QACL,MAAM,IAAIM,KAAK,CAACvB,IAAI,CAACwB,KAAK,IAAI,wBAAwB,CAAC;MACzD;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdL,OAAO,CAACK,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;;MAE3C;MACA3F,gBAAgB,CAAC2B,WAAW,CAAC;MAC7BN,cAAc,CAAC,CAAC,CAAC;MACjBI,aAAa,CAACE,WAAW,CAACyD,MAAM,CAAC;MACjC7D,aAAa,CAACkF,IAAI,CAACO,IAAI,CAACrF,WAAW,CAACyD,MAAM,GAAG1D,cAAc,CAAC,CAAC;MAC7D9B,cAAc,CAAC,EAAE,CAAC;MAElBT,KAAK,CAACuE,OAAO,CAAC,6BAA6B/B,WAAW,CAACyD,MAAM,iBAAiB,CAAC;IACjF,CAAC,SAAS;MACRtF,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;EAED,MAAMmH,gBAAgB,GAAInB,IAAI,IAAK;IACjC,IAAIA,IAAI,IAAI,CAAC,IAAIA,IAAI,IAAIxE,UAAU,EAAE;MACnCuE,aAAa,CAACC,IAAI,CAAC;IACrB;EACF,CAAC;EAED,MAAMoB,eAAe,GAAGnH,aAAa,CAAC8E,MAAM,CAACK,MAAM,IAAI;IACrD;IACA,IAAIjF,WAAW,EAAE;MACf,MAAMkH,WAAW,GAAGjC,MAAM,CAACrD,IAAI,CAACiD,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC9E,WAAW,CAAC6E,WAAW,CAAC,CAAC,CAAC,IAC9DI,MAAM,CAACpD,KAAK,CAACgD,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC9E,WAAW,CAAC6E,WAAW,CAAC,CAAC,CAAC,IAC9DI,MAAM,CAACxE,QAAQ,CAACoE,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC9E,WAAW,CAAC6E,WAAW,CAAC,CAAC,CAAC;MACpF,IAAI,CAACqC,WAAW,EAAE,OAAO,KAAK;IAChC;;IAEA;IACA,MAAMC,OAAO,GAAG/G,eAAe;;IAE/B;IACA,IAAI+G,OAAO,CAAC7G,aAAa,IAAI6G,OAAO,CAAC5G,aAAa,EAAE;MAAA,IAAA6G,eAAA,EAAAC,qBAAA;MAClD,MAAMC,eAAe,GAAG,EAAAF,eAAA,GAAAnC,MAAM,CAAC9C,OAAO,cAAAiF,eAAA,wBAAAC,qBAAA,GAAdD,eAAA,CAAgBhF,MAAM,cAAAiF,qBAAA,uBAAtBA,qBAAA,CAAwBjE,0BAA0B,KACnDmE,sBAAsB,CAACtC,MAAM,CAACjD,UAAU,CAAC,IAAI,CAAC;MAErE,IAAImF,OAAO,CAAC7G,aAAa,IAAIgH,eAAe,GAAGE,QAAQ,CAACL,OAAO,CAAC7G,aAAa,CAAC,EAAE,OAAO,KAAK;MAC5F,IAAI6G,OAAO,CAAC5G,aAAa,IAAI+G,eAAe,GAAGE,QAAQ,CAACL,OAAO,CAAC5G,aAAa,CAAC,EAAE,OAAO,KAAK;IAC9F;;IAEA;IACA,IAAI4G,OAAO,CAAC3G,SAAS,EAAE;MAAA,IAAAiH,gBAAA,EAAAC,qBAAA,EAAAC,sBAAA;MACrB,MAAMC,YAAY,GAAG,EAAAH,gBAAA,GAAAxC,MAAM,CAAC9C,OAAO,cAAAsF,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBrF,MAAM,cAAAsF,qBAAA,wBAAAC,sBAAA,GAAtBD,qBAAA,CAAwB3E,SAAS,cAAA4E,sBAAA,uBAAjCA,sBAAA,CAAmC5C,IAAI,CAAC8C,GAAG,IAC9D,CAACA,GAAG,CAAC7E,MAAM,IAAI,EAAE,EAAE6B,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACqC,OAAO,CAAC3G,SAAS,CAACqE,WAAW,CAAC,CAAC,CAC3E,CAAC,KAAI,CAACI,MAAM,CAACzE,SAAS,IAAI,EAAE,EAAEqE,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACqC,OAAO,CAAC3G,SAAS,CAACqE,WAAW,CAAC,CAAC,CAAC;MACrF,IAAI,CAAC+C,YAAY,EAAE,OAAO,KAAK;IACjC;;IAEA;IACA,IAAIT,OAAO,CAAC1G,QAAQ,EAAE;MACpB,MAAMqH,eAAe,GAAG,CAAC7C,MAAM,CAACxE,QAAQ,IAAI,EAAE,EAAEoE,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACqC,OAAO,CAAC1G,QAAQ,CAACoE,WAAW,CAAC,CAAC,CAAC;MACtG,IAAI,CAACiD,eAAe,EAAE,OAAO,KAAK;IACpC;;IAEA;IACA,IAAIX,OAAO,CAACzG,MAAM,EAAE;MAAA,IAAAqH,cAAA,EAAAC,gBAAA,EAAAC,qBAAA;MAClB,MAAMC,UAAU,GAAGf,OAAO,CAACzG,MAAM,CAACmE,WAAW,CAAC,CAAC;MAC/C,MAAMsD,QAAQ,GAAG,EAAAJ,cAAA,GAAA9C,MAAM,CAACvE,MAAM,cAAAqH,cAAA,uBAAbA,cAAA,CAAehD,IAAI,CAACC,KAAK,IAAIA,KAAK,CAACH,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACoD,UAAU,CAAC,CAAC,KACvE,CAAC,EAAAF,gBAAA,GAAA/C,MAAM,CAAC9C,OAAO,cAAA6F,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgB5F,MAAM,cAAA6F,qBAAA,uBAAtBA,qBAAA,CAAwB9E,MAAM,KAAI,EAAE,EAAE4B,IAAI,CAACC,KAAK,IAC/CA,KAAK,CAACH,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACoD,UAAU,CACzC,CAAC;MACjB,IAAI,CAACC,QAAQ,EAAE,OAAO,KAAK;IAC7B;;IAEA;IACA,IAAIhB,OAAO,CAACvG,WAAW,EAAE;MAAA,IAAAwH,gBAAA,EAAAC,qBAAA,EAAAC,sBAAA;MACvB,MAAMC,cAAc,IAAAH,gBAAA,GAAGnD,MAAM,CAAC9C,OAAO,cAAAiG,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBhG,MAAM,cAAAiG,qBAAA,wBAAAC,sBAAA,GAAtBD,qBAAA,CAAwBtF,SAAS,cAAAuF,sBAAA,uBAAjCA,sBAAA,CAAmCvD,IAAI,CAAC8C,GAAG,IAChE,CAACA,GAAG,CAAC5E,WAAW,IAAI4E,GAAG,CAACW,MAAM,IAAI,EAAE,EAAE3D,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACqC,OAAO,CAACvG,WAAW,CAACiE,WAAW,CAAC,CAAC,CAChG,CAAC;MACD,IAAI,CAAC0D,cAAc,EAAE,OAAO,KAAK;IACnC;;IAEA;IACA,IAAIpB,OAAO,CAACtG,cAAc,EAAE;MAAA,IAAA4H,gBAAA,EAAAC,qBAAA,EAAAC,sBAAA;MAC1B,MAAMC,WAAW,IAAAH,gBAAA,GAAGxD,MAAM,CAAC9C,OAAO,cAAAsG,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBrG,MAAM,cAAAsG,qBAAA,wBAAAC,sBAAA,GAAtBD,qBAAA,CAAwB3F,SAAS,cAAA4F,sBAAA,uBAAjCA,sBAAA,CAAmC5D,IAAI,CAAC8C,GAAG,IAC7D,CAACA,GAAG,CAAC3E,cAAc,IAAI2E,GAAG,CAACgB,IAAI,IAAI,EAAE,EAAEnC,QAAQ,CAAC,CAAC,CAAC5B,QAAQ,CAACqC,OAAO,CAACtG,cAAc,CACnF,CAAC;MACD,IAAI,CAAC+H,WAAW,EAAE,OAAO,KAAK;IAChC;;IAEA;IACA,IAAIzB,OAAO,CAACrG,OAAO,EAAE;MAAA,IAAAgI,gBAAA,EAAAC,qBAAA,EAAAC,sBAAA;MACnB,MAAMC,UAAU,IAAAH,gBAAA,GAAG7D,MAAM,CAAC9C,OAAO,cAAA2G,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgB1G,MAAM,cAAA2G,qBAAA,wBAAAC,sBAAA,GAAtBD,qBAAA,CAAwBrG,cAAc,cAAAsG,sBAAA,uBAAtCA,sBAAA,CAAwCjE,IAAI,CAACmE,GAAG,IACjE,CAACA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACtG,WAAW,IAAIsG,GAAG,CAACE,YAAY,IAAI,EAAE,EAAEvE,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACqC,OAAO,CAACrG,OAAO,CAAC+D,WAAW,CAAC,CAAC,CACjH,CAAC;MACD,IAAI,CAACoE,UAAU,EAAE,OAAO,KAAK;IAC/B;;IAEA;IACA,IAAI9B,OAAO,CAACxG,QAAQ,EAAE;MAAA,IAAA0I,gBAAA,EAAAC,qBAAA,EAAAC,sBAAA;MACpB,MAAMC,WAAW,IAAAH,gBAAA,GAAGpE,MAAM,CAAC9C,OAAO,cAAAkH,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBjH,MAAM,cAAAkH,qBAAA,wBAAAC,sBAAA,GAAtBD,qBAAA,CAAwBvG,SAAS,cAAAwG,sBAAA,uBAAjCA,sBAAA,CAAmCxE,IAAI,CAAC8C,GAAG,IAAI;QACjE,MAAM4B,KAAK,GAAGC,UAAU,CAAC7B,GAAG,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC;QACjD,OAAO4B,KAAK,IAAIC,UAAU,CAACvC,OAAO,CAACxG,QAAQ,CAAC;MAC9C,CAAC,CAAC;MACF,IAAI,CAAC6I,WAAW,EAAE,OAAO,KAAK;IAChC;IAEA,OAAO,IAAI;EACb,CAAC,CAAC;;EAEF;EACA,MAAMjC,sBAAsB,GAAIoC,cAAc,IAAK;IACjD,IAAI,CAACA,cAAc,EAAE,OAAO,CAAC;IAC7B,MAAMC,KAAK,GAAGD,cAAc,CAACC,KAAK,CAAC,oBAAoB,CAAC;IACxD,OAAOA,KAAK,GAAGpC,QAAQ,CAACoC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;EACvC,CAAC;EAED,MAAMC,0BAA0B,GAAGA,CAACC,UAAU,EAAEC,KAAK,KAAK;IACxD1J,kBAAkB,CAAC2J,IAAI,KAAK;MAC1B,GAAGA,IAAI;MACP,CAACF,UAAU,GAAGC;IAChB,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAME,eAAe,GAAGA,CAAA,KAAM;IAC5BhK,cAAc,CAAC,EAAE,CAAC;IAClBI,kBAAkB,CAAC;MACjBC,aAAa,EAAE,EAAE;MACjBC,aAAa,EAAE,EAAE;MACjBC,SAAS,EAAE,EAAE;MACbC,QAAQ,EAAE,EAAE;MACZC,MAAM,EAAE,EAAE;MACVC,QAAQ,EAAE,EAAE;MACZC,WAAW,EAAE,EAAE;MACfC,cAAc,EAAE,EAAE;MAClBC,OAAO,EAAE;IACX,CAAC,CAAC;EACJ,CAAC;EAED,MAAMoJ,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,OAAOlK,WAAW,IAAImK,MAAM,CAACC,MAAM,CAAChK,eAAe,CAAC,CAAC2E,IAAI,CAACgF,KAAK,IAAIA,KAAK,KAAK,EAAE,CAAC;EAClF,CAAC;EAED,MAAMM,cAAc,GAAI5F,MAAM,IAAK;IACjC,IAAI;MAAA,IAAA6F,eAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,qBAAA,EAAAC,sBAAA;MACF;MACA,MAAMC,aAAa,GAAG;AAC5B,EAAE9G,MAAM,CAAC7C,IAAI;AACb,EAAE6C,MAAM,CAAC5C,KAAK;AACd,EAAE4C,MAAM,CAAC3C,KAAK,MAAM2C,MAAM,CAAC1C,KAAK,MAAM0C,MAAM,CAAChE,QAAQ;AACrD;AACA;AACA,EAAEgE,MAAM,CAACxC,OAAO;AAChB;AACA;AACA,EAAE,CAAAqI,eAAA,GAAA7F,MAAM,CAACtC,OAAO,cAAAmI,eAAA,gBAAAC,qBAAA,GAAdD,eAAA,CAAgBlI,MAAM,cAAAmI,qBAAA,eAAtBA,qBAAA,CAAwB7H,cAAc,GACtC+B,MAAM,CAACtC,OAAO,CAACC,MAAM,CAACM,cAAc,CAAC8B,GAAG,CAAC0E,GAAG,IAC1C,GAAGA,GAAG,CAACsC,QAAQ,IAAItC,GAAG,CAACuC,QAAQ,OAAOvC,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,YAAY;AACzE,YAAYF,GAAG,CAACwC,QAAQ,IAAIxC,GAAG,CAACyC,KAAK,IAAI,eAAe;AACxD,EAAEzC,GAAG,CAAC0C,WAAW,GAAG,eAAe,GAAG1C,GAAG,CAAC0C,WAAW,GAAG,EAAE;AAC1D,EAAE1C,GAAG,CAAC2C,gBAAgB,GAAG,oBAAoB,GAAG3C,GAAG,CAAC2C,gBAAgB,GAAG,EAAE;AACzE,CACE,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC,GAAGrH,MAAM,CAACzC,UAAU;AAClC;AACA,EAAE,CAAAwI,gBAAA,GAAA/F,MAAM,CAACtC,OAAO,cAAAqI,gBAAA,gBAAAC,qBAAA,GAAdD,gBAAA,CAAgBpI,MAAM,cAAAqI,qBAAA,eAAtBA,qBAAA,CAAwBrH,0BAA0B,GAClD,qBAAqBqB,MAAM,CAACtC,OAAO,CAACC,MAAM,CAACgB,0BAA0B,UAAU,GAAG,EAAE;AACtF;AACA;AACA,EAAE,CAAAsH,gBAAA,GAAAjG,MAAM,CAACtC,OAAO,cAAAuI,gBAAA,gBAAAC,qBAAA,GAAdD,gBAAA,CAAgBtI,MAAM,cAAAuI,qBAAA,eAAtBA,qBAAA,CAAwB5H,SAAS,GACjC0B,MAAM,CAACtC,OAAO,CAACC,MAAM,CAACW,SAAS,CAACyB,GAAG,CAACqD,GAAG,IACrC,GAAGA,GAAG,CAAC7E,MAAM,IAAI,QAAQ,SAAS6E,GAAG,CAAC5E,WAAW,IAAI4E,GAAG,CAACW,MAAM,IAAI,aAAa;AACpF,mBAAmBX,GAAG,CAAC3E,cAAc,IAAI2E,GAAG,CAACgB,IAAI,IAAI,eAAe;AACpE,EAAEhB,GAAG,CAAC,aAAa,CAAC,IAAIA,GAAG,CAAC,aAAa,CAAC,GAAG,CAAC,GAAG,SAAS,GAAGA,GAAG,CAAC,aAAa,CAAC,GAAG,GAAG,GAAG,EAAE;AAC1F,CACE,CAAC,CAACiE,IAAI,CAAC,IAAI,CAAC,GAAGrH,MAAM,CAACjE,SAAS;AACjC;AACA;AACA,EAAEiE,MAAM,CAAC/D,MAAM,CAACoL,IAAI,CAAC,IAAI,CAAC;AAC1B;AACA,EAAE,CAAAlB,gBAAA,GAAAnG,MAAM,CAACtC,OAAO,cAAAyI,gBAAA,gBAAAC,qBAAA,GAAdD,gBAAA,CAAgBxI,MAAM,cAAAyI,qBAAA,eAAtBA,qBAAA,CAAwBkB,SAAS,IAAItH,MAAM,CAACtC,OAAO,CAACC,MAAM,CAAC2J,SAAS,CAAC5G,MAAM,GAAG,CAAC,GAC/E,cAAcV,MAAM,CAACtC,OAAO,CAACC,MAAM,CAAC2J,SAAS,CAACD,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,EAAE;AACnE;AACA,EAAE,CAAAhB,gBAAA,GAAArG,MAAM,CAACtC,OAAO,cAAA2I,gBAAA,gBAAAC,qBAAA,GAAdD,gBAAA,CAAgB1I,MAAM,cAAA2I,qBAAA,eAAtBA,qBAAA,CAAwBiB,QAAQ,IAAIvH,MAAM,CAACtC,OAAO,CAACC,MAAM,CAAC4J,QAAQ,CAAC7G,MAAM,GAAG,CAAC,GAC7E,aAAaV,MAAM,CAACtC,OAAO,CAACC,MAAM,CAAC4J,QAAQ,CAACxH,GAAG,CAACyH,OAAO,IACrD,GAAGA,OAAO,CAACC,IAAI,IAAID,OAAO,CAACE,KAAK,IAAI,SAAS;AACjD,EAAEF,OAAO,CAACL,WAAW,GAAG,eAAe,GAAGK,OAAO,CAACL,WAAW,GAAG,EAAE;AAClE,EAAEK,OAAO,CAACG,YAAY,GAAG,gBAAgB,GAAGH,OAAO,CAACG,YAAY,GAAG,EAAE;AACrE,CACE,CAAC,CAACN,IAAI,CAAC,IAAI,CAAC,EAAE,GAAG,EAAE;AACrB;AACA,EAAE,CAAAd,gBAAA,GAAAvG,MAAM,CAACtC,OAAO,cAAA6I,gBAAA,gBAAAC,qBAAA,GAAdD,gBAAA,CAAgB5I,MAAM,cAAA6I,qBAAA,eAAtBA,qBAAA,CAAwBoB,cAAc,IAAI5H,MAAM,CAACtC,OAAO,CAACC,MAAM,CAACiK,cAAc,CAAClH,MAAM,GAAG,CAAC,GACzF,mBAAmBV,MAAM,CAACtC,OAAO,CAACC,MAAM,CAACiK,cAAc,CAAC7H,GAAG,CAAC8H,IAAI,IAC9D,GAAGA,IAAI,CAACJ,IAAI,IAAII,IAAI,CAACH,KAAK,IAAI,eAAe;AACjD,EAAEG,IAAI,CAACC,mBAAmB,GAAG,aAAa,GAAGD,IAAI,CAACC,mBAAmB,GAAG,EAAE;AAC1E,EAAED,IAAI,CAACE,SAAS,GAAG,QAAQ,GAAGF,IAAI,CAACE,SAAS,GAAG,EAAE;AACjD,CACE,CAAC,CAACV,IAAI,CAAC,IAAI,CAAC,EAAE,GAAG,EAAE;AACrB;AACA,EAAE,CAAAZ,gBAAA,GAAAzG,MAAM,CAACtC,OAAO,cAAA+I,gBAAA,gBAAAC,qBAAA,GAAdD,gBAAA,CAAgB9I,MAAM,cAAA+I,qBAAA,eAAtBA,qBAAA,CAAwBsB,YAAY,IAAIhI,MAAM,CAACtC,OAAO,CAACC,MAAM,CAACqK,YAAY,CAACtH,MAAM,GAAG,CAAC,GACrF,iBAAiBV,MAAM,CAACtC,OAAO,CAACC,MAAM,CAACqK,YAAY,CAACjI,GAAG,CAACkI,WAAW,IACjE,OAAOA,WAAW,KAAK,QAAQ,GAAGA,WAAW,GAC7C,GAAGA,WAAW,CAACC,eAAe,IAAID,WAAW,CAACR,IAAI,IAAI,aAAa;AACvE,EAAEQ,WAAW,CAACF,SAAS,GAAG,QAAQ,GAAGE,WAAW,CAACF,SAAS,GAAG,EAAE;AAC/D,EAAEE,WAAW,CAACH,mBAAmB,GAAG,gBAAgB,GAAGG,WAAW,CAACH,mBAAmB,GAAG,EAAE;AAC3F,EAAEG,WAAW,CAACd,WAAW,GAAG,eAAe,GAAGc,WAAW,CAACd,WAAW,GAAG,EAAE;AAC1E,CACE,CAAC,CAACE,IAAI,CAAC,IAAI,CAAC,EAAE,GAAG,EAAE;AACrB;AACA,EAAE,CAAAV,gBAAA,GAAA3G,MAAM,CAACtC,OAAO,cAAAiJ,gBAAA,gBAAAC,qBAAA,GAAdD,gBAAA,CAAgBhJ,MAAM,cAAAiJ,qBAAA,gBAAAC,sBAAA,GAAtBD,qBAAA,CAAwBhJ,mBAAmB,cAAAiJ,sBAAA,eAA3CA,sBAAA,CAA6CsB,QAAQ,GACrD,qCAAqCnI,MAAM,CAACtC,OAAO,CAACC,MAAM,CAACC,mBAAmB,CAACuK,QAAQ,EAAE,GAAG,EAAE;AAChG;AACA,gBAAgB,IAAIC,IAAI,CAAC,CAAC,CAACC,kBAAkB,CAAC,CAAC;AAC/C,OAAO,CAACtJ,IAAI,CAAC,CAAC;;MAER;MACA,MAAMuJ,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACzB,aAAa,CAAC,EAAE;QAAE0B,IAAI,EAAE;MAAa,CAAC,CAAC;MAC9D,MAAMvJ,GAAG,GAAGC,GAAG,CAACuJ,eAAe,CAACH,IAAI,CAAC;MACrC,MAAMI,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;MACxCF,IAAI,CAACG,IAAI,GAAG5J,GAAG;MACfyJ,IAAI,CAACI,QAAQ,GAAG,GAAG9I,MAAM,CAAC7C,IAAI,CAAC4L,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,aAAa;MAChEJ,QAAQ,CAACK,IAAI,CAACC,WAAW,CAACP,IAAI,CAAC;MAC/BA,IAAI,CAACQ,KAAK,CAAC,CAAC;MACZP,QAAQ,CAACK,IAAI,CAACG,WAAW,CAACT,IAAI,CAAC;MAC/BxJ,GAAG,CAACkK,eAAe,CAACnK,GAAG,CAAC;MAExBxE,KAAK,CAACgG,OAAO,CAAC,yBAAyBT,MAAM,CAAC7C,IAAI,EAAE,CAAC;IACvD,CAAC,CAAC,OAAO8D,KAAK,EAAE;MACdL,OAAO,CAACK,KAAK,CAAC,iBAAiB,EAAEA,KAAK,CAAC;MACvCxG,KAAK,CAACwG,KAAK,CAAC,8CAA8C,CAAC;IAC7D;EACF,CAAC;EAED,MAAMoI,UAAU,GAAIrJ,MAAM,IAAK;IAC7BzD,iBAAiB,CAACyD,MAAM,CAAC;IACzBvD,cAAc,CAAC,IAAI,CAAC;IACpBhC,KAAK,CAAC6O,IAAI,CAAC,6BAA6BtJ,MAAM,CAAC7C,IAAI,EAAE,CAAC;EACxD,CAAC;EAED,MAAMoM,gBAAgB,GAAGA,CAAA,KAAM;IAC7B9M,cAAc,CAAC,KAAK,CAAC;IACrBF,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;EAED,MAAMiN,gBAAgB,GAAGA,CAACnM,KAAK,EAAEF,IAAI,KAAK;IACxCsM,MAAM,CAACC,IAAI,CAAC,UAAUrM,KAAK,0CAA0CF,IAAI,6GAA6G,EAAE,QAAQ,CAAC;IACjM1C,KAAK,CAAC6O,IAAI,CAAC,mCAAmCnM,IAAI,EAAE,CAAC;EACvD,CAAC;EAED,MAAMwM,gBAAgB,GAAGA,CAACrM,KAAK,EAAEH,IAAI,KAAK;IACxC,IAAIyM,SAAS,CAACC,SAAS,CAAC1E,KAAK,CAAC,4BAA4B,CAAC,EAAE;MAC3DsE,MAAM,CAACC,IAAI,CAAC,OAAOpM,KAAK,EAAE,EAAE,QAAQ,CAAC;IACvC,CAAC,MAAM;MACLsM,SAAS,CAACE,SAAS,CAACC,SAAS,CAACzM,KAAK,CAAC,CAAC0M,IAAI,CAAC,MAAM;QAC9CvP,KAAK,CAACgG,OAAO,CAAC,wBAAwBnD,KAAK,EAAE,CAAC;MAChD,CAAC,CAAC,CAAC2M,KAAK,CAAC,MAAM;QACbxP,KAAK,CAAC6O,IAAI,CAAC,UAAUhM,KAAK,EAAE,CAAC;MAC/B,CAAC,CAAC;IACJ;EACF,CAAC;EAID,MAAM4M,eAAe,GAAGA,CAAA,KAAM;IAC5B,IAAI1H,eAAe,CAAC9B,MAAM,KAAK,CAAC,EAAE;MAChCjG,KAAK,CAACuE,OAAO,CAAC,sBAAsB,CAAC;MACrC;IACF;IAEA,IAAI;MACF,MAAMmL,UAAU,GAAG3H,eAAe,CAACzC,GAAG,CAACC,MAAM,KAAK;QAChD7C,IAAI,EAAE6C,MAAM,CAAC7C,IAAI;QACjBC,KAAK,EAAE4C,MAAM,CAAC5C,KAAK;QACnBC,KAAK,EAAE2C,MAAM,CAAC3C,KAAK;QACnBC,KAAK,EAAE0C,MAAM,CAAC1C,KAAK;QACnBtB,QAAQ,EAAEgE,MAAM,CAAChE,QAAQ;QACzBuB,UAAU,EAAEyC,MAAM,CAACzC,UAAU;QAC7BxB,SAAS,EAAEiE,MAAM,CAACjE,SAAS;QAC3BE,MAAM,EAAE+D,MAAM,CAAC/D,MAAM,CAACoL,IAAI,CAAC,IAAI,CAAC;QAChC7J,OAAO,EAAEwC,MAAM,CAACxC,OAAO;QACvB4M,UAAU,EAAEpK,MAAM,CAACvC;MACrB,CAAC,CAAC,CAAC;MAEH,MAAM4M,UAAU,GAAG,CACjB,iFAAiF,EACjF,GAAGF,UAAU,CAACpK,GAAG,CAACuK,GAAG,IACnB5E,MAAM,CAACC,MAAM,CAAC2E,GAAG,CAAC,CAACvK,GAAG,CAACuF,KAAK,IAC1B,IAAIiF,MAAM,CAACjF,KAAK,CAAC,CAACyD,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,GACvC,CAAC,CAAC1B,IAAI,CAAC,GAAG,CACZ,CAAC,CACF,CAACA,IAAI,CAAC,IAAI,CAAC;MAEZ,MAAMiB,IAAI,GAAG,IAAIC,IAAI,CAAC,CAAC8B,UAAU,CAAC,EAAE;QAAE7B,IAAI,EAAE;MAAW,CAAC,CAAC;MACzD,MAAMvJ,GAAG,GAAGC,GAAG,CAACuJ,eAAe,CAACH,IAAI,CAAC;MACrC,MAAMI,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;MACxCF,IAAI,CAACG,IAAI,GAAG5J,GAAG;MACfyJ,IAAI,CAACI,QAAQ,GAAG,yBAAyB,IAAIV,IAAI,CAAC,CAAC,CAACoC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM;MACrF9B,QAAQ,CAACK,IAAI,CAACC,WAAW,CAACP,IAAI,CAAC;MAC/BA,IAAI,CAACQ,KAAK,CAAC,CAAC;MACZP,QAAQ,CAACK,IAAI,CAACG,WAAW,CAACT,IAAI,CAAC;MAC/BxJ,GAAG,CAACkK,eAAe,CAACnK,GAAG,CAAC;MAExBxE,KAAK,CAACgG,OAAO,CAAC,YAAY+B,eAAe,CAAC9B,MAAM,iBAAiB,CAAC;IACpE,CAAC,CAAC,OAAOO,KAAK,EAAE;MACdL,OAAO,CAACK,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;MACrCxG,KAAK,CAACwG,KAAK,CAAC,6CAA6C,CAAC;IAC5D;EACF,CAAC;EAED,oBACErG,OAAA;IAAK8P,SAAS,EAAC,aAAa;IAAAC,QAAA,gBAC1B/P,OAAA;MAAK8P,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1B/P,OAAA;QAAA+P,QAAA,EAAI;MAAc;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACvBnQ,OAAA;QAAA+P,QAAA,EAAG;MAAkE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtE,CAAC,eAGNnQ,OAAA;MAAK8P,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAC7B/P,OAAA;QAAMoQ,QAAQ,EAAEpM,YAAa;QAAC8L,SAAS,EAAC,aAAa;QAAAC,QAAA,eACnD/P,OAAA;UAAK8P,SAAS,EAAC,oBAAoB;UAAAC,QAAA,gBACjC/P,OAAA;YAAK8P,SAAS,EAAC,sBAAsB;YAAAC,QAAA,gBACnC/P,OAAA,CAACjB,QAAQ;cAAC+Q,SAAS,EAAC;YAAa;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACpCnQ,OAAA;cACE4N,IAAI,EAAC,MAAM;cACXyC,WAAW,EAAC,oGAAoG;cAChH3F,KAAK,EAAErK,WAAY;cACnBiQ,QAAQ,EAAGrM,CAAC,IAAK3D,cAAc,CAAC2D,CAAC,CAACsM,MAAM,CAAC7F,KAAK,CAAE;cAChDoF,SAAS,EAAC,cAAc;cACxBU,QAAQ,EAAEjQ;YAAY;cAAAyP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNnQ,OAAA;YAAK8P,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7B/P,OAAA;cACE4N,IAAI,EAAC,QAAQ;cACbkC,SAAS,EAAC,iBAAiB;cAC3BU,QAAQ,EAAEjQ,WAAY;cAAAwP,QAAA,EAErBxP,WAAW,gBACVP,OAAA,CAAAE,SAAA;gBAAA6P,QAAA,gBACE/P,OAAA;kBAAK8P,SAAS,EAAC;gBAAiB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gBAErC;cAAA,eAAE,CAAC,gBAEHnQ,OAAA,CAAAE,SAAA;gBAAA6P,QAAA,gBACE/P,OAAA,CAACjB,QAAQ;kBAAC0R,IAAI,EAAE;gBAAG;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,UAExB;cAAA,eAAE;YACH;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK,CAAC,eACTnQ,OAAA;cACE4N,IAAI,EAAC,QAAQ;cACbkC,SAAS,EAAC,mBAAmB;cAC7BY,OAAO,EAAEnK,aAAc;cACvBiK,QAAQ,EAAEjQ,WAAY;cAAAwP,QAAA,EACvB;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,EAGN1P,aAAa,CAACqF,MAAM,GAAG,CAAC,iBACvB9F,OAAA;QAAK8P,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7B/P,OAAA;UAAK8P,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5B/P,OAAA;YAAK8P,SAAS,EAAC,sBAAsB;YAAAC,QAAA,gBACnC/P,OAAA,CAAChB,QAAQ;cAAC8Q,SAAS,EAAC;YAAa;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACpCnQ,OAAA;cACE4N,IAAI,EAAC,MAAM;cACXyC,WAAW,EAAC,6CAA6C;cACzD3F,KAAK,EAAE/J,WAAY;cACnB2P,QAAQ,EAAGrM,CAAC,IAAKrD,cAAc,CAACqD,CAAC,CAACsM,MAAM,CAAC7F,KAAK,CAAE;cAChDoF,SAAS,EAAC;YAAc;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNnQ,OAAA;YAAK8P,SAAS,EAAC,iBAAiB;YAAAC,QAAA,gBAC9B/P,OAAA;cACE4N,IAAI,EAAC,QAAQ;cACbkC,SAAS,EAAE,mCAAmCjP,WAAW,GAAG,QAAQ,GAAG,EAAE,EAAG;cAC5E6P,OAAO,EAAEA,CAAA,KAAM5P,cAAc,CAAC,CAACD,WAAW,CAAE;cAAAkP,QAAA,gBAE5C/P,OAAA,CAACN,UAAU;gBAAC+Q,IAAI,EAAE;cAAG;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,oBAExB,EAACtP,WAAW,gBAAGb,OAAA,CAACJ,WAAW;gBAAC6Q,IAAI,EAAE;cAAG;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAAGnQ,OAAA,CAACL,aAAa;gBAAC8Q,IAAI,EAAE;cAAG;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChE,CAAC,EACRtF,gBAAgB,CAAC,CAAC,iBACjB7K,OAAA;cACE4N,IAAI,EAAC,QAAQ;cACbkC,SAAS,EAAC,+BAA+B;cACzCY,OAAO,EAAE9F,eAAgB;cAAAmF,QAAA,gBAEzB/P,OAAA,CAACP,GAAG;gBAACgR,IAAI,EAAE;cAAG;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,aAEnB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CACT;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAGLtP,WAAW,iBACVb,OAAA;UAAK8P,SAAS,EAAC,kBAAkB;UAAAC,QAAA,eAC/B/P,OAAA;YAAK8P,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3B/P,OAAA;cAAK8P,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3B/P,OAAA;gBAAA+P,QAAA,EAAO;cAAkB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACjCnQ,OAAA;gBAAK8P,SAAS,EAAC,cAAc;gBAAAC,QAAA,gBAC3B/P,OAAA;kBACE4N,IAAI,EAAC,QAAQ;kBACbyC,WAAW,EAAC,KAAK;kBACjB3F,KAAK,EAAE3J,eAAe,CAACE,aAAc;kBACrCqP,QAAQ,EAAGrM,CAAC,IAAKuG,0BAA0B,CAAC,eAAe,EAAEvG,CAAC,CAACsM,MAAM,CAAC7F,KAAK,CAAE;kBAC7EoF,SAAS,EAAC,oBAAoB;kBAC9Ba,GAAG,EAAC;gBAAG;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR,CAAC,eACFnQ,OAAA;kBAAA+P,QAAA,EAAM;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACfnQ,OAAA;kBACE4N,IAAI,EAAC,QAAQ;kBACbyC,WAAW,EAAC,KAAK;kBACjB3F,KAAK,EAAE3J,eAAe,CAACG,aAAc;kBACrCoP,QAAQ,EAAGrM,CAAC,IAAKuG,0BAA0B,CAAC,eAAe,EAAEvG,CAAC,CAACsM,MAAM,CAAC7F,KAAK,CAAE;kBAC7EoF,SAAS,EAAC,oBAAoB;kBAC9Ba,GAAG,EAAC;gBAAG;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENnQ,OAAA;cAAK8P,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3B/P,OAAA;gBAAA+P,QAAA,EAAO;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC9BnQ,OAAA;gBACE4N,IAAI,EAAC,MAAM;gBACXyC,WAAW,EAAC,6BAA6B;gBACzC3F,KAAK,EAAE3J,eAAe,CAACI,SAAU;gBACjCmP,QAAQ,EAAGrM,CAAC,IAAKuG,0BAA0B,CAAC,WAAW,EAAEvG,CAAC,CAACsM,MAAM,CAAC7F,KAAK,CAAE;gBACzEoF,SAAS,EAAC;cAAc;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAENnQ,OAAA;cAAK8P,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3B/P,OAAA;gBAAA+P,QAAA,EAAO;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACvBnQ,OAAA;gBACE4N,IAAI,EAAC,MAAM;gBACXyC,WAAW,EAAC,gCAAgC;gBAC5C3F,KAAK,EAAE3J,eAAe,CAACK,QAAS;gBAChCkP,QAAQ,EAAGrM,CAAC,IAAKuG,0BAA0B,CAAC,UAAU,EAAEvG,CAAC,CAACsM,MAAM,CAAC7F,KAAK,CAAE;gBACxEoF,SAAS,EAAC;cAAc;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAENnQ,OAAA;cAAK8P,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3B/P,OAAA;gBAAA+P,QAAA,EAAO;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACrBnQ,OAAA;gBACE4N,IAAI,EAAC,MAAM;gBACXyC,WAAW,EAAC,2BAA2B;gBACvC3F,KAAK,EAAE3J,eAAe,CAACM,MAAO;gBAC9BiP,QAAQ,EAAGrM,CAAC,IAAKuG,0BAA0B,CAAC,QAAQ,EAAEvG,CAAC,CAACsM,MAAM,CAAC7F,KAAK,CAAE;gBACtEoF,SAAS,EAAC;cAAc;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAENnQ,OAAA;cAAK8P,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3B/P,OAAA;gBAAA+P,QAAA,EAAO;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC1BnQ,OAAA;gBACE4N,IAAI,EAAC,MAAM;gBACXyC,WAAW,EAAC,4BAA4B;gBACxC3F,KAAK,EAAE3J,eAAe,CAACQ,WAAY;gBACnC+O,QAAQ,EAAGrM,CAAC,IAAKuG,0BAA0B,CAAC,aAAa,EAAEvG,CAAC,CAACsM,MAAM,CAAC7F,KAAK,CAAE;gBAC3EoF,SAAS,EAAC;cAAc;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAENnQ,OAAA;cAAK8P,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3B/P,OAAA;gBAAA+P,QAAA,EAAO;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC9BnQ,OAAA;gBACE4N,IAAI,EAAC,MAAM;gBACXyC,WAAW,EAAC,kBAAkB;gBAC9B3F,KAAK,EAAE3J,eAAe,CAACS,cAAe;gBACtC8O,QAAQ,EAAGrM,CAAC,IAAKuG,0BAA0B,CAAC,gBAAgB,EAAEvG,CAAC,CAACsM,MAAM,CAAC7F,KAAK,CAAE;gBAC9EoF,SAAS,EAAC;cAAc;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAENnQ,OAAA;cAAK8P,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3B/P,OAAA;gBAAA+P,QAAA,EAAO;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACtBnQ,OAAA;gBACE4N,IAAI,EAAC,MAAM;gBACXyC,WAAW,EAAC,8BAA8B;gBAC1C3F,KAAK,EAAE3J,eAAe,CAACU,OAAQ;gBAC/B6O,QAAQ,EAAGrM,CAAC,IAAKuG,0BAA0B,CAAC,SAAS,EAAEvG,CAAC,CAACsM,MAAM,CAAC7F,KAAK,CAAE;gBACvEoF,SAAS,EAAC;cAAc;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAENnQ,OAAA;cAAK8P,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3B/P,OAAA;gBAAA+P,QAAA,EAAO;cAAiB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAChCnQ,OAAA;gBACE4N,IAAI,EAAC,QAAQ;gBACbyC,WAAW,EAAC,cAAc;gBAC1B3F,KAAK,EAAE3J,eAAe,CAACO,QAAS;gBAChCgP,QAAQ,EAAGrM,CAAC,IAAKuG,0BAA0B,CAAC,UAAU,EAAEvG,CAAC,CAACsM,MAAM,CAAC7F,KAAK,CAAE;gBACxEoF,SAAS,EAAC,cAAc;gBACxBa,GAAG,EAAC,GAAG;gBACPC,GAAG,EAAC;cAAK;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,eAEDnQ,OAAA;UAAK8P,SAAS,EAAC,eAAe;UAAAC,QAAA,GAC3BnI,eAAe,CAAC9B,MAAM,EAAC,MAAI,EAACrF,aAAa,CAACqF,MAAM,EAAC,UAClD,EAAC+E,gBAAgB,CAAC,CAAC,iBAAI7K,OAAA;YAAM8P,SAAS,EAAC,oBAAoB;YAAAC,QAAA,EAAC;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3E,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAGL1P,aAAa,CAACqF,MAAM,GAAG,CAAC,gBACvB9F,OAAA;MAAK8P,SAAS,EAAC,iBAAiB;MAAAC,QAAA,gBAC9B/P,OAAA;QAAK8P,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7B/P,OAAA;UAAA+P,QAAA,EAAI;QAAc;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACvBnQ,OAAA;UAAK8P,SAAS,EAAC,iBAAiB;UAAAC,QAAA,eAC9B/P,OAAA;YACE8P,SAAS,EAAC,mBAAmB;YAC7BY,OAAO,EAAEpB,eAAgB;YACzB9M,KAAK,EAAE,UAAUoF,eAAe,CAAC9B,MAAM,iBAAkB;YAAAiK,QAAA,gBAEzD/P,OAAA,CAACf,UAAU;cAACwR,IAAI,EAAE;YAAG;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBACZ,EAACvI,eAAe,CAAC9B,MAAM,EAAC,GACtC;UAAA;YAAAkK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENnQ,OAAA;QAAK8P,SAAS,EAAC,cAAc;QAAAC,QAAA,EAC1BnI,eAAe,CAACzC,GAAG,CAAEC,MAAM,iBAC1BpF,OAAA;UAAqB8P,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1C/P,OAAA;YAAK8P,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5B/P,OAAA;cAAK8P,SAAS,EAAC,eAAe;cAAAC,QAAA,eAC5B/P,OAAA,CAACb,MAAM;gBAACsR,IAAI,EAAE;cAAG;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjB,CAAC,eACNnQ,OAAA;cAAK8P,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1B/P,OAAA;gBAAI8P,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAE3K,MAAM,CAAC7C;cAAI;gBAAAyN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC9CnQ,OAAA;gBAAG8P,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAE3K,MAAM,CAAC5C;cAAK;gBAAAwN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENnQ,OAAA;YAAK8P,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7B/P,OAAA;cAAK8P,SAAS,EAAC,2BAA2B;cAAAC,QAAA,gBACxC/P,OAAA,CAACZ,QAAQ;gBAACqR,IAAI,EAAE;cAAG;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACtBnQ,OAAA;gBAAA+P,QAAA,EAAO3K,MAAM,CAAChE;cAAQ;gBAAA4O,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3B,CAAC,eACNnQ,OAAA;cACE8P,SAAS,EAAC,kCAAkC;cAC5CY,OAAO,EAAEA,CAAA,KAAM9B,gBAAgB,CAACxJ,MAAM,CAAC3C,KAAK,EAAE2C,MAAM,CAAC7C,IAAI,CAAE;cAC3DC,KAAK,EAAE,iBAAiB4C,MAAM,CAAC7C,IAAI,EAAG;cAAAwN,QAAA,gBAEtC/P,OAAA,CAACX,MAAM;gBAACoR,IAAI,EAAE;cAAG;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACpBnQ,OAAA;gBAAA+P,QAAA,EAAO3K,MAAM,CAAC3C;cAAK;gBAAAuN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC3BnQ,OAAA,CAACT,cAAc;gBAACkR,IAAI,EAAE,EAAG;gBAACX,SAAS,EAAC;cAAe;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnD,CAAC,eACNnQ,OAAA;cACE8P,SAAS,EAAC,kCAAkC;cAC5CY,OAAO,EAAEA,CAAA,KAAM3B,gBAAgB,CAAC3J,MAAM,CAAC1C,KAAK,EAAE0C,MAAM,CAAC7C,IAAI,CAAE;cAC3DC,KAAK,EAAE,QAAQ4C,MAAM,CAAC7C,IAAI,EAAG;cAAAwN,QAAA,gBAE7B/P,OAAA,CAACV,OAAO;gBAACmR,IAAI,EAAE;cAAG;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACrBnQ,OAAA;gBAAA+P,QAAA,EAAO3K,MAAM,CAAC1C;cAAK;gBAAAsN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC3BnQ,OAAA,CAACT,cAAc;gBAACkR,IAAI,EAAE,EAAG;gBAACX,SAAS,EAAC;cAAe;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENnQ,OAAA;YAAK8P,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7B/P,OAAA;cAAK8P,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1B/P,OAAA;gBAAK8P,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,gBAC9B/P,OAAA;kBAAA+P,QAAA,EAAI;gBAAU;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACnBnQ,OAAA;kBAAA+P,QAAA,EAAI3K,MAAM,CAACzC;gBAAU;kBAAAqN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvB,CAAC,eACNnQ,OAAA;gBAAK8P,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,gBAC9B/P,OAAA;kBAAA+P,QAAA,EAAI;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAClBnQ,OAAA;kBAAA+P,QAAA,EAAI3K,MAAM,CAACjE;gBAAS;kBAAA6O,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNnQ,OAAA;cAAK8P,SAAS,EAAC,iBAAiB;cAAAC,QAAA,gBAC9B/P,OAAA;gBAAA+P,QAAA,EAAI;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACfnQ,OAAA;gBAAK8P,SAAS,EAAC,aAAa;gBAAAC,QAAA,EACzB3K,MAAM,CAAC/D,MAAM,CAAC8D,GAAG,CAAC,CAACQ,KAAK,EAAEkL,KAAK,kBAC9B7Q,OAAA;kBAAkB8P,SAAS,EAAC,WAAW;kBAAAC,QAAA,EAAEpK;gBAAK,GAAnCkL,KAAK;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAqC,CACtD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENnQ,OAAA;YAAK8P,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7B/P,OAAA;cACE8P,SAAS,EAAC,mBAAmB;cAC7BY,OAAO,EAAEA,CAAA,KAAMjC,UAAU,CAACrJ,MAAM,CAAE;cAAA2K,QAAA,gBAElC/P,OAAA,CAACd,KAAK;gBAACuR,IAAI,EAAE;cAAG;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAErB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTnQ,OAAA;cACE8P,SAAS,EAAC,iBAAiB;cAC3BY,OAAO,EAAEA,CAAA,KAAM1F,cAAc,CAAC5F,MAAM,CAAE;cAAA2K,QAAA,gBAEtC/P,OAAA,CAACf,UAAU;gBAACwR,IAAI,EAAE;cAAG;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,YAE1B;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA,GAxEE/K,MAAM,CAAC9C,EAAE;UAAA0N,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAyEd,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,EAGLnO,UAAU,GAAG,CAAC,iBACbhC,OAAA;QAAK8P,SAAS,EAAC,oBAAoB;QAAAC,QAAA,gBACjC/P,OAAA;UAAK8P,SAAS,EAAC,iBAAiB;UAAAC,QAAA,GAAC,UACvB,EAAE,CAACjO,WAAW,GAAG,CAAC,IAAIM,cAAc,GAAI,CAAC,EAAC,MAAI,EAAC+E,IAAI,CAACwJ,GAAG,CAAC7O,WAAW,GAAGM,cAAc,EAAEF,UAAU,CAAC,EAAC,MAAI,EAACA,UAAU,EAAC,UAC5H;QAAA;UAAA8N,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACNnQ,OAAA;UAAK8P,SAAS,EAAC,qBAAqB;UAAAC,QAAA,gBAClC/P,OAAA;YACE8P,SAAS,EAAC,kCAAkC;YAC5CY,OAAO,EAAEA,CAAA,KAAM/I,gBAAgB,CAAC7F,WAAW,GAAG,CAAC,CAAE;YACjD0O,QAAQ,EAAE1O,WAAW,KAAK,CAAE;YAAAiO,QAAA,EAC7B;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAETnQ,OAAA;YAAK8P,SAAS,EAAC,cAAc;YAAAC,QAAA,EAC1B9K,KAAK,CAAC6L,IAAI,CAAC;cAAEhL,MAAM,EAAEqB,IAAI,CAACwJ,GAAG,CAAC,CAAC,EAAE3O,UAAU;YAAE,CAAC,EAAE,CAAC+O,CAAC,EAAEC,CAAC,KAAK;cACzD,IAAIC,OAAO;cACX,IAAIjP,UAAU,IAAI,CAAC,EAAE;gBACnBiP,OAAO,GAAGD,CAAC,GAAG,CAAC;cACjB,CAAC,MAAM,IAAIlP,WAAW,IAAI,CAAC,EAAE;gBAC3BmP,OAAO,GAAGD,CAAC,GAAG,CAAC;cACjB,CAAC,MAAM,IAAIlP,WAAW,IAAIE,UAAU,GAAG,CAAC,EAAE;gBACxCiP,OAAO,GAAGjP,UAAU,GAAG,CAAC,GAAGgP,CAAC;cAC9B,CAAC,MAAM;gBACLC,OAAO,GAAGnP,WAAW,GAAG,CAAC,GAAGkP,CAAC;cAC/B;cAEA,oBACEhR,OAAA;gBAEE8P,SAAS,EAAE,sBAAsBhO,WAAW,KAAKmP,OAAO,GAAG,aAAa,GAAG,eAAe,EAAG;gBAC7FP,OAAO,EAAEA,CAAA,KAAM/I,gBAAgB,CAACsJ,OAAO,CAAE;gBAAAlB,QAAA,EAExCkB;cAAO,GAJHA,OAAO;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAKN,CAAC;YAEb,CAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAENnQ,OAAA;YACE8P,SAAS,EAAC,kCAAkC;YAC5CY,OAAO,EAAEA,CAAA,KAAM/I,gBAAgB,CAAC7F,WAAW,GAAG,CAAC,CAAE;YACjD0O,QAAQ,EAAE1O,WAAW,KAAKE,UAAW;YAAA+N,QAAA,EACtC;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,GACJ,CAAC5P,WAAW,gBACdP,OAAA;MAAK8P,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1B/P,OAAA;QAAK8P,SAAS,EAAC,YAAY;QAAAC,QAAA,eACzB/P,OAAA,CAACjB,QAAQ;UAAC0R,IAAI,EAAE;QAAG;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnB,CAAC,eACNnQ,OAAA;QAAA+P,QAAA,EAAI;MAAgB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACzBnQ,OAAA;QAAA+P,QAAA,EAAG;MAAyH;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eAChInQ,OAAA;QAAK8P,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9B/P,OAAA;UAAA+P,QAAA,EAAI;QAAkB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC3BnQ,OAAA;UAAA+P,QAAA,gBACE/P,OAAA;YAAA+P,QAAA,EAAI;UAAwB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACjCnQ,OAAA;YAAA+P,QAAA,EAAI;UAAwC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACjDnQ,OAAA;YAAA+P,QAAA,EAAI;UAA8B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,GACJ,IAAI,eAGRnQ,OAAA,CAACF,WAAW;MACVsF,MAAM,EAAE1D,cAAe;MACvBwP,MAAM,EAAEtP,WAAY;MACpBuP,OAAO,EAAExC;IAAiB;MAAAqB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3B,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAAC/P,EAAA,CA58BID,aAAa;AAAAiR,EAAA,GAAbjR,aAAa;AA88BnB,eAAeA,aAAa;AAAC,IAAAiR,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}