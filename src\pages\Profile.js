import React, { useState } from 'react';
import { FiUser, FiMail, FiPhone, FiMapPin, FiEdit, FiSave, FiX } from 'react-icons/fi';
import { toast } from 'react-toastify';
import './Profile.css';

const Profile = () => {
  const [isEditing, setIsEditing] = useState(false);
  const [profile, setProfile] = useState({
    name: '<PERSON><PERSON><PERSON>',
    email: '<EMAIL>',
    phone: '+****************',
    location: 'San Francisco, CA',
    role: 'Administrator',
    company: 'Resume AI Corp',
    bio: 'Experienced administrator managing AI-powered resume processing systems.'
  });

  const [editedProfile, setEditedProfile] = useState({ ...profile });

  const handleEdit = () => {
    setIsEditing(true);
    setEditedProfile({ ...profile });
  };

  const handleSave = () => {
    setProfile({ ...editedProfile });
    setIsEditing(false);
    toast.success('Profile updated successfully!');
  };

  const handleCancel = () => {
    setEditedProfile({ ...profile });
    setIsEditing(false);
  };

  const handleChange = (field, value) => {
    setEditedProfile(prev => ({ ...prev, [field]: value }));
  };

  return (
    <div className="profile-page">
      <div className="page-header">
        <h1>Profile Settings</h1>
        <p>Manage your account information and preferences</p>
      </div>

      <div className="profile-container">
        <div className="profile-card">
          <div className="profile-header">
            <div className="profile-avatar">
              <FiUser size={48} />
            </div>
            <div className="profile-actions">
              {!isEditing ? (
                <button className="btn btn-primary" onClick={handleEdit}>
                  <FiEdit size={16} />
                  Edit Profile
                </button>
              ) : (
                <div className="edit-actions">
                  <button className="btn btn-secondary" onClick={handleCancel}>
                    <FiX size={16} />
                    Cancel
                  </button>
                  <button className="btn btn-primary" onClick={handleSave}>
                    <FiSave size={16} />
                    Save Changes
                  </button>
                </div>
              )}
            </div>
          </div>

          <div className="profile-content">
            <div className="form-group">
              <label>Full Name</label>
              {isEditing ? (
                <input
                  type="text"
                  value={editedProfile.name}
                  onChange={(e) => handleChange('name', e.target.value)}
                  className="input"
                />
              ) : (
                <div className="field-value">{profile.name}</div>
              )}
            </div>

            <div className="form-group">
              <label>Email Address</label>
              {isEditing ? (
                <input
                  type="email"
                  value={editedProfile.email}
                  onChange={(e) => handleChange('email', e.target.value)}
                  className="input"
                />
              ) : (
                <div className="field-value">{profile.email}</div>
              )}
            </div>

            <div className="form-group">
              <label>Phone Number</label>
              {isEditing ? (
                <input
                  type="tel"
                  value={editedProfile.phone}
                  onChange={(e) => handleChange('phone', e.target.value)}
                  className="input"
                />
              ) : (
                <div className="field-value">{profile.phone}</div>
              )}
            </div>

            <div className="form-group">
              <label>Location</label>
              {isEditing ? (
                <input
                  type="text"
                  value={editedProfile.location}
                  onChange={(e) => handleChange('location', e.target.value)}
                  className="input"
                />
              ) : (
                <div className="field-value">{profile.location}</div>
              )}
            </div>

            <div className="form-group">
              <label>Role</label>
              <div className="field-value">{profile.role}</div>
            </div>

            <div className="form-group">
              <label>Company</label>
              {isEditing ? (
                <input
                  type="text"
                  value={editedProfile.company}
                  onChange={(e) => handleChange('company', e.target.value)}
                  className="input"
                />
              ) : (
                <div className="field-value">{profile.company}</div>
              )}
            </div>

            <div className="form-group">
              <label>Bio</label>
              {isEditing ? (
                <textarea
                  value={editedProfile.bio}
                  onChange={(e) => handleChange('bio', e.target.value)}
                  className="input"
                  rows={3}
                />
              ) : (
                <div className="field-value">{profile.bio}</div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Profile;
