# 🤖 Resume AI Agent - Enhanced Version 2.0

An intelligent resume search and management system powered by OpenAI GPT-4, featuring a modern Streamlit frontend and FastAPI backend.

## ✨ Features

### 🎨 Enhanced UI/UX
- **Modern Design**: Beautiful gradient backgrounds, glassmorphism effects, and smooth animations
- **Responsive Layout**: Works perfectly on desktop and mobile devices
- **Interactive Elements**: Hover effects, smooth transitions, and intuitive navigation
- **Professional Styling**: Clean typography, consistent color scheme, and modern card layouts

### 🔍 Intelligent Search
- **Natural Language Processing**: Search resumes using plain English queries
- **AI-Powered Results**: GPT-4 processes and understands complex search criteria
- **Real-time Filtering**: Filter search results with instant feedback
- **Smart Sorting**: Automatically sorts results by relevance and academic performance

### 📁 Document Management
- **PDF Upload**: Drag-and-drop multiple PDF resumes for processing
- **Duplicate Detection**: Automatic checksum-based duplicate prevention
- **AWS Textract Integration**: Advanced text extraction from PDF documents
- **Batch Processing**: Process multiple resumes simultaneously

### 🗄️ Database Integration
- **MongoDB Storage**: Scalable NoSQL database for resume data
- **Real-time Operations**: Live CRUD operations with instant UI updates
- **Data Integrity**: Robust error handling and data validation

## 🚀 Quick Start

### Prerequisites
- Python 3.8+
- MongoDB (running on localhost:27017)
- OpenAI API key
- AWS credentials (for Textract)

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd SQL-Agent-Task-Submission-main
   ```

2. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

3. **Configure environment**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

4. **Start the application**
   ```bash
   python start_app.py
   ```

### Manual Start (Alternative)

1. **Start the backend API**
   ```bash
   uvicorn app:app --port 8001 --reload
   ```

2. **Start the frontend (in another terminal)**
   ```bash
   streamlit run frontendV3.py --server.port 9024
   ```

3. **Open your browser**
   - Frontend: http://localhost:9024
   - API Docs: http://localhost:8001/docs

## 🔧 Configuration

### Environment Variables (.env)
```env
# Database Configuration
CONNECTION_URI=mongodb://localhost:27017/
DATABASE_NAME=db-production
COLLECTION_NAME=collection-resumes

# API Configuration
STR_API_URL=http://127.0.0.1:8001/query/

# OpenAI Configuration (add your key)
OPENAI_API_KEY=your_openai_api_key_here

# AWS Configuration (for Textract)
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
AWS_DEFAULT_REGION=us-east-1
```

## 📖 Usage Guide

### 🔍 Searching Resumes

1. **Natural Language Search**
   - Enter queries like: "Find Python developers with 5+ years experience"
   - "Show teachers with Mathematics background"
   - "Software engineers in Mumbai"

2. **Filter Results**
   - Use the filter box to narrow down search results
   - Results are automatically sorted by academic performance

3. **View All Resumes**
   - Click "Show All" to display all resumes in the database

### 📁 Uploading Resumes

1. **Select PDF Files**
   - Use the file uploader to select multiple PDF resumes
   - Drag and drop files for easy upload

2. **Process Documents**
   - Click "Save & Process PDFs" to upload and extract data
   - AI will automatically extract structured information

3. **Monitor Progress**
   - Real-time progress indicators show processing status
   - Toast notifications confirm successful operations

### 🗑️ Managing Resumes

1. **Delete Resumes**
   - Click the delete button on any resume card
   - Confirmation and feedback provided

2. **Real-time Updates**
   - UI updates immediately after operations
   - No page refresh required

## 🏗️ Architecture

### Frontend (Streamlit)
- **frontendV3.py**: Main application interface
- **DisplayJson.py**: Resume display and formatting
- Modern CSS styling with animations and responsive design

### Backend (FastAPI)
- **app.py**: API endpoints and business logic
- **helperMongoDb.py**: Database operations
- **extractDataParallel.py**: Document processing

### Data Processing
- **AWS_Async.py**: AWS Textract integration
- **utils.py**: Utility functions
- **config/**: Configuration files

## 🧪 Testing

Run the comprehensive test suite:
```bash
python test_functionality.py
```

Tests include:
- ✅ Import validation
- ✅ File structure verification
- ✅ Environment configuration
- ✅ MongoDB connectivity
- ✅ API endpoint accessibility

## 🎨 UI/UX Enhancements

### Visual Improvements
- **Gradient Backgrounds**: Beautiful purple-blue gradients
- **Glassmorphism**: Translucent cards with backdrop blur
- **Modern Typography**: Inter font family for clean readability
- **Consistent Spacing**: Proper margins and padding throughout

### Interactive Elements
- **Hover Effects**: Smooth transitions on buttons and cards
- **Loading States**: Progress indicators for long operations
- **Toast Notifications**: User-friendly feedback messages
- **Responsive Design**: Mobile-first approach

### User Experience
- **Intuitive Navigation**: Clear visual hierarchy
- **Helpful Tooltips**: Guidance for user actions
- **Error Handling**: Graceful error messages and recovery
- **Performance**: Optimized for fast loading and smooth interactions

## 🔧 Troubleshooting

### Common Issues

1. **MongoDB Connection Failed**
   ```bash
   # Start MongoDB service
   sudo systemctl start mongod
   # Or on macOS
   brew services start mongodb-community
   ```

2. **API Not Accessible**
   ```bash
   # Check if backend is running
   curl http://localhost:8001/
   ```

3. **Import Errors**
   ```bash
   # Reinstall dependencies
   pip install -r requirements.txt --force-reinstall
   ```

### Performance Tips
- Ensure MongoDB has sufficient memory allocation
- Use SSD storage for better database performance
- Monitor API response times in browser dev tools

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- OpenAI for GPT-4 API
- Streamlit for the amazing web framework
- FastAPI for the high-performance backend
- MongoDB for flexible data storage
- AWS Textract for document processing

---

**Version 2.0** - Enhanced UI/UX, improved functionality, comprehensive testing
