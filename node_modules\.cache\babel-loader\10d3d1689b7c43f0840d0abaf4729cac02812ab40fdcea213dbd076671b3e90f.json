{"ast": null, "code": "var _jsxFileName = \"D:\\\\Projects\\\\SQL-Agent-Task-Submission-main\\\\SQL-Agent-Task-Submission-main\\\\src\\\\components\\\\ResumeModal.js\";\nimport React from 'react';\nimport { FiX, FiMail, FiPhone, FiMapPin, FiDownload, FiExternalLink, FiUser, FiBriefcase, FiBookOpen, FiAward } from 'react-icons/fi';\nimport './ResumeModal.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ResumeModal = ({\n  resume,\n  isOpen,\n  onClose\n}) => {\n  var _resume$rawData$Resum, _resume$rawData$Resum2;\n  if (!isOpen || !resume) return null;\n  const handleDownload = () => {\n    // Create a simple text-based resume content\n    const resumeContent = `\n${resume.name}\n${resume.title}\n${resume.email} | ${resume.phone} | ${resume.location}\n\nSUMMARY\n${resume.summary}\n\nEXPERIENCE\n${resume.experience}\n\nEDUCATION\n${resume.education}\n\nSKILLS\n${resume.skills.join(', ')}\n    `.trim();\n\n    // Create and download the file\n    const blob = new Blob([resumeContent], {\n      type: 'text/plain'\n    });\n    const url = URL.createObjectURL(blob);\n    const link = document.createElement('a');\n    link.href = url;\n    link.download = `${resume.name.replace(/\\s+/g, '_')}_Resume.txt`;\n    document.body.appendChild(link);\n    link.click();\n    document.body.removeChild(link);\n    URL.revokeObjectURL(url);\n  };\n  const handleEmailClick = () => {\n    window.open(`mailto:${resume.email}`, '_blank');\n  };\n  const handlePhoneClick = () => {\n    window.open(`tel:${resume.phone}`, '_blank');\n  };\n  const handleBackdropClick = e => {\n    if (e.target === e.currentTarget) {\n      onClose();\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"modal-backdrop\",\n    onClick: handleBackdropClick,\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"resume-modal\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"modal-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"modal-title\",\n          children: [/*#__PURE__*/_jsxDEV(FiUser, {\n            size: 24\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 59,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n            children: \"Resume Details\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 60,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 58,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"modal-close\",\n          onClick: onClose,\n          children: /*#__PURE__*/_jsxDEV(FiX, {\n            size: 24\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 63,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 62,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 57,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"modal-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"modal-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"section-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"resume-avatar-large\",\n              children: /*#__PURE__*/_jsxDEV(FiUser, {\n                size: 32\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 72,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 71,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"personal-info\",\n              children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                className: \"candidate-name\",\n                children: resume.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 75,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n                className: \"candidate-title\",\n                children: resume.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 76,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"match-score-large\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"score-label\",\n                  children: \"Match Score:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 78,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"score-value\",\n                  children: [resume.score, \"%\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 79,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"score-bar\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"score-fill\",\n                    style: {\n                      width: `${resume.score}%`\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 81,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 80,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 77,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 74,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 70,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"contact-actions\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"contact-btn email-btn\",\n              onClick: handleEmailClick,\n              title: `Email ${resume.name}`,\n              children: [/*#__PURE__*/_jsxDEV(FiMail, {\n                size: 16\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 96,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: resume.email\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 97,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(FiExternalLink, {\n                size: 12\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 98,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 91,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"contact-btn phone-btn\",\n              onClick: handlePhoneClick,\n              title: `Call ${resume.name}`,\n              children: [/*#__PURE__*/_jsxDEV(FiPhone, {\n                size: 16\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 105,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: resume.phone\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 106,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(FiExternalLink, {\n                size: 12\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 107,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 100,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"contact-info location-info\",\n              children: [/*#__PURE__*/_jsxDEV(FiMapPin, {\n                size: 16\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 110,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: resume.location\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 111,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 109,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 90,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 69,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"modal-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"section-title\",\n            children: [/*#__PURE__*/_jsxDEV(FiUser, {\n              size: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 119,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"Professional Summary\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 120,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 118,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"section-content\",\n            children: /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"summary-text\",\n              children: resume.summary\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 123,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 122,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 117,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"modal-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"section-title\",\n            children: [/*#__PURE__*/_jsxDEV(FiBriefcase, {\n              size: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 130,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"Experience\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 131,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 129,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"section-content\",\n            children: /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"experience-text\",\n              children: resume.experience\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 134,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 133,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 128,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"modal-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"section-title\",\n            children: [/*#__PURE__*/_jsxDEV(FiBookOpen, {\n              size: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 141,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"Education\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 142,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 140,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"section-content\",\n            children: /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"education-text\",\n              children: resume.education\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 145,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 144,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 139,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"modal-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"section-title\",\n            children: [/*#__PURE__*/_jsxDEV(FiAward, {\n              size: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 152,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"Skills & Technologies\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 153,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 151,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"section-content\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"skills-grid\",\n              children: resume.skills.map((skill, index) => /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"skill-tag-large\",\n                children: skill\n              }, index, false, {\n                fileName: _jsxFileName,\n                lineNumber: 158,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 156,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 155,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 150,\n          columnNumber: 11\n        }, this), resume.rawData && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"modal-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"section-title\",\n            children: /*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"Additional Information\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 168,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 167,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"section-content\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"raw-data-info\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Database ID:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 172,\n                  columnNumber: 22\n                }, this), \" \", resume.rawData._id || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 172,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Data Source:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 173,\n                  columnNumber: 22\n                }, this), \" \", resume.rawData.Resume ? 'MongoDB' : 'Mock Data']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 173,\n                columnNumber: 19\n              }, this), ((_resume$rawData$Resum = resume.rawData.Resume) === null || _resume$rawData$Resum === void 0 ? void 0 : (_resume$rawData$Resum2 = _resume$rawData$Resum.PersonalInformation) === null || _resume$rawData$Resum2 === void 0 ? void 0 : _resume$rawData$Resum2.LinkedIn) && /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"LinkedIn:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 175,\n                  columnNumber: 24\n                }, this), \" \", resume.rawData.Resume.PersonalInformation.LinkedIn]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 175,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 171,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 170,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 166,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 67,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"modal-footer\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-secondary\",\n          onClick: onClose,\n          children: \"Close\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 184,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-primary\",\n          onClick: handleDownload,\n          children: [/*#__PURE__*/_jsxDEV(FiDownload, {\n            size: 16\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 188,\n            columnNumber: 13\n          }, this), \"Download Resume\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 187,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 183,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 56,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 55,\n    columnNumber: 5\n  }, this);\n};\n_c = ResumeModal;\nexport default ResumeModal;\nvar _c;\n$RefreshReg$(_c, \"ResumeModal\");", "map": {"version": 3, "names": ["React", "FiX", "FiMail", "FiPhone", "FiMapPin", "FiDownload", "FiExternalLink", "FiUser", "FiBriefcase", "FiBookOpen", "FiAward", "jsxDEV", "_jsxDEV", "ResumeModal", "resume", "isOpen", "onClose", "_resume$rawData$Resum", "_resume$rawData$Resum2", "handleDownload", "<PERSON><PERSON><PERSON>nt", "name", "title", "email", "phone", "location", "summary", "experience", "education", "skills", "join", "trim", "blob", "Blob", "type", "url", "URL", "createObjectURL", "link", "document", "createElement", "href", "download", "replace", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL", "handleEmailClick", "window", "open", "handlePhoneClick", "handleBackdropClick", "e", "target", "currentTarget", "className", "onClick", "children", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "score", "style", "width", "map", "skill", "index", "rawData", "_id", "Resume", "PersonalInformation", "LinkedIn", "_c", "$RefreshReg$"], "sources": ["D:/Projects/SQL-Agent-Task-Submission-main/SQL-Agent-Task-Submission-main/src/components/ResumeModal.js"], "sourcesContent": ["import React from 'react';\nimport { FiX, FiMail, FiPhone, FiMapPin, FiDownload, FiExternalLink, FiUser, FiBriefcase, FiBookOpen, FiAward } from 'react-icons/fi';\nimport './ResumeModal.css';\n\nconst ResumeModal = ({ resume, isOpen, onClose }) => {\n  if (!isOpen || !resume) return null;\n\n  const handleDownload = () => {\n    // Create a simple text-based resume content\n    const resumeContent = `\n${resume.name}\n${resume.title}\n${resume.email} | ${resume.phone} | ${resume.location}\n\nSUMMARY\n${resume.summary}\n\nEXPERIENCE\n${resume.experience}\n\nEDUCATION\n${resume.education}\n\nSKILLS\n${resume.skills.join(', ')}\n    `.trim();\n\n    // Create and download the file\n    const blob = new Blob([resumeContent], { type: 'text/plain' });\n    const url = URL.createObjectURL(blob);\n    const link = document.createElement('a');\n    link.href = url;\n    link.download = `${resume.name.replace(/\\s+/g, '_')}_Resume.txt`;\n    document.body.appendChild(link);\n    link.click();\n    document.body.removeChild(link);\n    URL.revokeObjectURL(url);\n  };\n\n  const handleEmailClick = () => {\n    window.open(`mailto:${resume.email}`, '_blank');\n  };\n\n  const handlePhoneClick = () => {\n    window.open(`tel:${resume.phone}`, '_blank');\n  };\n\n  const handleBackdropClick = (e) => {\n    if (e.target === e.currentTarget) {\n      onClose();\n    }\n  };\n\n  return (\n    <div className=\"modal-backdrop\" onClick={handleBackdropClick}>\n      <div className=\"resume-modal\">\n        <div className=\"modal-header\">\n          <div className=\"modal-title\">\n            <FiUser size={24} />\n            <h2>Resume Details</h2>\n          </div>\n          <button className=\"modal-close\" onClick={onClose}>\n            <FiX size={24} />\n          </button>\n        </div>\n\n        <div className=\"modal-content\">\n          {/* Personal Information Section */}\n          <div className=\"modal-section\">\n            <div className=\"section-header\">\n              <div className=\"resume-avatar-large\">\n                <FiUser size={32} />\n              </div>\n              <div className=\"personal-info\">\n                <h1 className=\"candidate-name\">{resume.name}</h1>\n                <h2 className=\"candidate-title\">{resume.title}</h2>\n                <div className=\"match-score-large\">\n                  <span className=\"score-label\">Match Score:</span>\n                  <span className=\"score-value\">{resume.score}%</span>\n                  <div className=\"score-bar\">\n                    <div\n                      className=\"score-fill\"\n                      style={{ width: `${resume.score}%` }}\n                    ></div>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"contact-actions\">\n              <button\n                className=\"contact-btn email-btn\"\n                onClick={handleEmailClick}\n                title={`Email ${resume.name}`}\n              >\n                <FiMail size={16} />\n                <span>{resume.email}</span>\n                <FiExternalLink size={12} />\n              </button>\n              <button\n                className=\"contact-btn phone-btn\"\n                onClick={handlePhoneClick}\n                title={`Call ${resume.name}`}\n              >\n                <FiPhone size={16} />\n                <span>{resume.phone}</span>\n                <FiExternalLink size={12} />\n              </button>\n              <div className=\"contact-info location-info\">\n                <FiMapPin size={16} />\n                <span>{resume.location}</span>\n              </div>\n            </div>\n          </div>\n\n          {/* Summary Section */}\n          <div className=\"modal-section\">\n            <div className=\"section-title\">\n              <FiUser size={20} />\n              <h3>Professional Summary</h3>\n            </div>\n            <div className=\"section-content\">\n              <p className=\"summary-text\">{resume.summary}</p>\n            </div>\n          </div>\n\n          {/* Experience Section */}\n          <div className=\"modal-section\">\n            <div className=\"section-title\">\n              <FiBriefcase size={20} />\n              <h3>Experience</h3>\n            </div>\n            <div className=\"section-content\">\n              <p className=\"experience-text\">{resume.experience}</p>\n            </div>\n          </div>\n\n          {/* Education Section */}\n          <div className=\"modal-section\">\n            <div className=\"section-title\">\n              <FiBookOpen size={20} />\n              <h3>Education</h3>\n            </div>\n            <div className=\"section-content\">\n              <p className=\"education-text\">{resume.education}</p>\n            </div>\n          </div>\n\n          {/* Skills Section */}\n          <div className=\"modal-section\">\n            <div className=\"section-title\">\n              <FiAward size={20} />\n              <h3>Skills & Technologies</h3>\n            </div>\n            <div className=\"section-content\">\n              <div className=\"skills-grid\">\n                {resume.skills.map((skill, index) => (\n                  <span key={index} className=\"skill-tag-large\">{skill}</span>\n                ))}\n              </div>\n            </div>\n          </div>\n\n          {/* Raw Data Section (for debugging) */}\n          {resume.rawData && (\n            <div className=\"modal-section\">\n              <div className=\"section-title\">\n                <h3>Additional Information</h3>\n              </div>\n              <div className=\"section-content\">\n                <div className=\"raw-data-info\">\n                  <p><strong>Database ID:</strong> {resume.rawData._id || 'N/A'}</p>\n                  <p><strong>Data Source:</strong> {resume.rawData.Resume ? 'MongoDB' : 'Mock Data'}</p>\n                  {resume.rawData.Resume?.PersonalInformation?.LinkedIn && (\n                    <p><strong>LinkedIn:</strong> {resume.rawData.Resume.PersonalInformation.LinkedIn}</p>\n                  )}\n                </div>\n              </div>\n            </div>\n          )}\n        </div>\n\n        <div className=\"modal-footer\">\n          <button className=\"btn btn-secondary\" onClick={onClose}>\n            Close\n          </button>\n          <button className=\"btn btn-primary\" onClick={handleDownload}>\n            <FiDownload size={16} />\n            Download Resume\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default ResumeModal;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,GAAG,EAAEC,MAAM,EAAEC,OAAO,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,cAAc,EAAEC,MAAM,EAAEC,WAAW,EAAEC,UAAU,EAAEC,OAAO,QAAQ,gBAAgB;AACrI,OAAO,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3B,MAAMC,WAAW,GAAGA,CAAC;EAAEC,MAAM;EAAEC,MAAM;EAAEC;AAAQ,CAAC,KAAK;EAAA,IAAAC,qBAAA,EAAAC,sBAAA;EACnD,IAAI,CAACH,MAAM,IAAI,CAACD,MAAM,EAAE,OAAO,IAAI;EAEnC,MAAMK,cAAc,GAAGA,CAAA,KAAM;IAC3B;IACA,MAAMC,aAAa,GAAG;AAC1B,EAAEN,MAAM,CAACO,IAAI;AACb,EAAEP,MAAM,CAACQ,KAAK;AACd,EAAER,MAAM,CAACS,KAAK,MAAMT,MAAM,CAACU,KAAK,MAAMV,MAAM,CAACW,QAAQ;AACrD;AACA;AACA,EAAEX,MAAM,CAACY,OAAO;AAChB;AACA;AACA,EAAEZ,MAAM,CAACa,UAAU;AACnB;AACA;AACA,EAAEb,MAAM,CAACc,SAAS;AAClB;AACA;AACA,EAAEd,MAAM,CAACe,MAAM,CAACC,IAAI,CAAC,IAAI,CAAC;AAC1B,KAAK,CAACC,IAAI,CAAC,CAAC;;IAER;IACA,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACb,aAAa,CAAC,EAAE;MAAEc,IAAI,EAAE;IAAa,CAAC,CAAC;IAC9D,MAAMC,GAAG,GAAGC,GAAG,CAACC,eAAe,CAACL,IAAI,CAAC;IACrC,MAAMM,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;IACxCF,IAAI,CAACG,IAAI,GAAGN,GAAG;IACfG,IAAI,CAACI,QAAQ,GAAG,GAAG5B,MAAM,CAACO,IAAI,CAACsB,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,aAAa;IAChEJ,QAAQ,CAACK,IAAI,CAACC,WAAW,CAACP,IAAI,CAAC;IAC/BA,IAAI,CAACQ,KAAK,CAAC,CAAC;IACZP,QAAQ,CAACK,IAAI,CAACG,WAAW,CAACT,IAAI,CAAC;IAC/BF,GAAG,CAACY,eAAe,CAACb,GAAG,CAAC;EAC1B,CAAC;EAED,MAAMc,gBAAgB,GAAGA,CAAA,KAAM;IAC7BC,MAAM,CAACC,IAAI,CAAC,UAAUrC,MAAM,CAACS,KAAK,EAAE,EAAE,QAAQ,CAAC;EACjD,CAAC;EAED,MAAM6B,gBAAgB,GAAGA,CAAA,KAAM;IAC7BF,MAAM,CAACC,IAAI,CAAC,OAAOrC,MAAM,CAACU,KAAK,EAAE,EAAE,QAAQ,CAAC;EAC9C,CAAC;EAED,MAAM6B,mBAAmB,GAAIC,CAAC,IAAK;IACjC,IAAIA,CAAC,CAACC,MAAM,KAAKD,CAAC,CAACE,aAAa,EAAE;MAChCxC,OAAO,CAAC,CAAC;IACX;EACF,CAAC;EAED,oBACEJ,OAAA;IAAK6C,SAAS,EAAC,gBAAgB;IAACC,OAAO,EAAEL,mBAAoB;IAAAM,QAAA,eAC3D/C,OAAA;MAAK6C,SAAS,EAAC,cAAc;MAAAE,QAAA,gBAC3B/C,OAAA;QAAK6C,SAAS,EAAC,cAAc;QAAAE,QAAA,gBAC3B/C,OAAA;UAAK6C,SAAS,EAAC,aAAa;UAAAE,QAAA,gBAC1B/C,OAAA,CAACL,MAAM;YAACqD,IAAI,EAAE;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACpBpD,OAAA;YAAA+C,QAAA,EAAI;UAAc;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpB,CAAC,eACNpD,OAAA;UAAQ6C,SAAS,EAAC,aAAa;UAACC,OAAO,EAAE1C,OAAQ;UAAA2C,QAAA,eAC/C/C,OAAA,CAACX,GAAG;YAAC2D,IAAI,EAAE;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAENpD,OAAA;QAAK6C,SAAS,EAAC,eAAe;QAAAE,QAAA,gBAE5B/C,OAAA;UAAK6C,SAAS,EAAC,eAAe;UAAAE,QAAA,gBAC5B/C,OAAA;YAAK6C,SAAS,EAAC,gBAAgB;YAAAE,QAAA,gBAC7B/C,OAAA;cAAK6C,SAAS,EAAC,qBAAqB;cAAAE,QAAA,eAClC/C,OAAA,CAACL,MAAM;gBAACqD,IAAI,EAAE;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjB,CAAC,eACNpD,OAAA;cAAK6C,SAAS,EAAC,eAAe;cAAAE,QAAA,gBAC5B/C,OAAA;gBAAI6C,SAAS,EAAC,gBAAgB;gBAAAE,QAAA,EAAE7C,MAAM,CAACO;cAAI;gBAAAwC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACjDpD,OAAA;gBAAI6C,SAAS,EAAC,iBAAiB;gBAAAE,QAAA,EAAE7C,MAAM,CAACQ;cAAK;gBAAAuC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACnDpD,OAAA;gBAAK6C,SAAS,EAAC,mBAAmB;gBAAAE,QAAA,gBAChC/C,OAAA;kBAAM6C,SAAS,EAAC,aAAa;kBAAAE,QAAA,EAAC;gBAAY;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACjDpD,OAAA;kBAAM6C,SAAS,EAAC,aAAa;kBAAAE,QAAA,GAAE7C,MAAM,CAACmD,KAAK,EAAC,GAAC;gBAAA;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACpDpD,OAAA;kBAAK6C,SAAS,EAAC,WAAW;kBAAAE,QAAA,eACxB/C,OAAA;oBACE6C,SAAS,EAAC,YAAY;oBACtBS,KAAK,EAAE;sBAAEC,KAAK,EAAE,GAAGrD,MAAM,CAACmD,KAAK;oBAAI;kBAAE;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENpD,OAAA;YAAK6C,SAAS,EAAC,iBAAiB;YAAAE,QAAA,gBAC9B/C,OAAA;cACE6C,SAAS,EAAC,uBAAuB;cACjCC,OAAO,EAAET,gBAAiB;cAC1B3B,KAAK,EAAE,SAASR,MAAM,CAACO,IAAI,EAAG;cAAAsC,QAAA,gBAE9B/C,OAAA,CAACV,MAAM;gBAAC0D,IAAI,EAAE;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACpBpD,OAAA;gBAAA+C,QAAA,EAAO7C,MAAM,CAACS;cAAK;gBAAAsC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC3BpD,OAAA,CAACN,cAAc;gBAACsD,IAAI,EAAE;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB,CAAC,eACTpD,OAAA;cACE6C,SAAS,EAAC,uBAAuB;cACjCC,OAAO,EAAEN,gBAAiB;cAC1B9B,KAAK,EAAE,QAAQR,MAAM,CAACO,IAAI,EAAG;cAAAsC,QAAA,gBAE7B/C,OAAA,CAACT,OAAO;gBAACyD,IAAI,EAAE;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACrBpD,OAAA;gBAAA+C,QAAA,EAAO7C,MAAM,CAACU;cAAK;gBAAAqC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC3BpD,OAAA,CAACN,cAAc;gBAACsD,IAAI,EAAE;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB,CAAC,eACTpD,OAAA;cAAK6C,SAAS,EAAC,4BAA4B;cAAAE,QAAA,gBACzC/C,OAAA,CAACR,QAAQ;gBAACwD,IAAI,EAAE;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACtBpD,OAAA;gBAAA+C,QAAA,EAAO7C,MAAM,CAACW;cAAQ;gBAAAoC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNpD,OAAA;UAAK6C,SAAS,EAAC,eAAe;UAAAE,QAAA,gBAC5B/C,OAAA;YAAK6C,SAAS,EAAC,eAAe;YAAAE,QAAA,gBAC5B/C,OAAA,CAACL,MAAM;cAACqD,IAAI,EAAE;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACpBpD,OAAA;cAAA+C,QAAA,EAAI;YAAoB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B,CAAC,eACNpD,OAAA;YAAK6C,SAAS,EAAC,iBAAiB;YAAAE,QAAA,eAC9B/C,OAAA;cAAG6C,SAAS,EAAC,cAAc;cAAAE,QAAA,EAAE7C,MAAM,CAACY;YAAO;cAAAmC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNpD,OAAA;UAAK6C,SAAS,EAAC,eAAe;UAAAE,QAAA,gBAC5B/C,OAAA;YAAK6C,SAAS,EAAC,eAAe;YAAAE,QAAA,gBAC5B/C,OAAA,CAACJ,WAAW;cAACoD,IAAI,EAAE;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACzBpD,OAAA;cAAA+C,QAAA,EAAI;YAAU;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB,CAAC,eACNpD,OAAA;YAAK6C,SAAS,EAAC,iBAAiB;YAAAE,QAAA,eAC9B/C,OAAA;cAAG6C,SAAS,EAAC,iBAAiB;cAAAE,QAAA,EAAE7C,MAAM,CAACa;YAAU;cAAAkC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNpD,OAAA;UAAK6C,SAAS,EAAC,eAAe;UAAAE,QAAA,gBAC5B/C,OAAA;YAAK6C,SAAS,EAAC,eAAe;YAAAE,QAAA,gBAC5B/C,OAAA,CAACH,UAAU;cAACmD,IAAI,EAAE;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACxBpD,OAAA;cAAA+C,QAAA,EAAI;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC,eACNpD,OAAA;YAAK6C,SAAS,EAAC,iBAAiB;YAAAE,QAAA,eAC9B/C,OAAA;cAAG6C,SAAS,EAAC,gBAAgB;cAAAE,QAAA,EAAE7C,MAAM,CAACc;YAAS;cAAAiC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNpD,OAAA;UAAK6C,SAAS,EAAC,eAAe;UAAAE,QAAA,gBAC5B/C,OAAA;YAAK6C,SAAS,EAAC,eAAe;YAAAE,QAAA,gBAC5B/C,OAAA,CAACF,OAAO;cAACkD,IAAI,EAAE;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACrBpD,OAAA;cAAA+C,QAAA,EAAI;YAAqB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B,CAAC,eACNpD,OAAA;YAAK6C,SAAS,EAAC,iBAAiB;YAAAE,QAAA,eAC9B/C,OAAA;cAAK6C,SAAS,EAAC,aAAa;cAAAE,QAAA,EACzB7C,MAAM,CAACe,MAAM,CAACuC,GAAG,CAAC,CAACC,KAAK,EAAEC,KAAK,kBAC9B1D,OAAA;gBAAkB6C,SAAS,EAAC,iBAAiB;gBAAAE,QAAA,EAAEU;cAAK,GAAzCC,KAAK;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAA2C,CAC5D;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAGLlD,MAAM,CAACyD,OAAO,iBACb3D,OAAA;UAAK6C,SAAS,EAAC,eAAe;UAAAE,QAAA,gBAC5B/C,OAAA;YAAK6C,SAAS,EAAC,eAAe;YAAAE,QAAA,eAC5B/C,OAAA;cAAA+C,QAAA,EAAI;YAAsB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B,CAAC,eACNpD,OAAA;YAAK6C,SAAS,EAAC,iBAAiB;YAAAE,QAAA,eAC9B/C,OAAA;cAAK6C,SAAS,EAAC,eAAe;cAAAE,QAAA,gBAC5B/C,OAAA;gBAAA+C,QAAA,gBAAG/C,OAAA;kBAAA+C,QAAA,EAAQ;gBAAY;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAClD,MAAM,CAACyD,OAAO,CAACC,GAAG,IAAI,KAAK;cAAA;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAClEpD,OAAA;gBAAA+C,QAAA,gBAAG/C,OAAA;kBAAA+C,QAAA,EAAQ;gBAAY;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAClD,MAAM,CAACyD,OAAO,CAACE,MAAM,GAAG,SAAS,GAAG,WAAW;cAAA;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,EACrF,EAAA/C,qBAAA,GAAAH,MAAM,CAACyD,OAAO,CAACE,MAAM,cAAAxD,qBAAA,wBAAAC,sBAAA,GAArBD,qBAAA,CAAuByD,mBAAmB,cAAAxD,sBAAA,uBAA1CA,sBAAA,CAA4CyD,QAAQ,kBACnD/D,OAAA;gBAAA+C,QAAA,gBAAG/C,OAAA;kBAAA+C,QAAA,EAAQ;gBAAS;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAClD,MAAM,CAACyD,OAAO,CAACE,MAAM,CAACC,mBAAmB,CAACC,QAAQ;cAAA;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CACtF;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAENpD,OAAA;QAAK6C,SAAS,EAAC,cAAc;QAAAE,QAAA,gBAC3B/C,OAAA;UAAQ6C,SAAS,EAAC,mBAAmB;UAACC,OAAO,EAAE1C,OAAQ;UAAA2C,QAAA,EAAC;QAExD;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTpD,OAAA;UAAQ6C,SAAS,EAAC,iBAAiB;UAACC,OAAO,EAAEvC,cAAe;UAAAwC,QAAA,gBAC1D/C,OAAA,CAACP,UAAU;YAACuD,IAAI,EAAE;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,mBAE1B;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACY,EAAA,GA9LI/D,WAAW;AAgMjB,eAAeA,WAAW;AAAC,IAAA+D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}