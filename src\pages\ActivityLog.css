/* ActivityLog.css */
.activity-log-page {
  animation: fadeIn 0.5s ease-in;
}

.activity-container {
  background: white;
  border-radius: 16px;
  border: 1px solid #e2e8f0;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.activity-stats {
  padding: 24px 32px;
  border-bottom: 1px solid #e2e8f0;
  background: #f8fafc;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #6b7280;
  font-weight: 500;
}

.activity-list {
  padding: 0;
}

.activity-item {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  padding: 20px 32px;
  border-bottom: 1px solid #f1f5f9;
  transition: background-color 0.2s ease;
}

.activity-item:last-child {
  border-bottom: none;
}

.activity-item:hover {
  background: #f8fafc;
}

.activity-icon-wrapper {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  background: #f1f5f9;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.activity-icon {
  width: 20px;
  height: 20px;
}

.activity-icon.upload {
  color: #10b981;
}

.activity-icon.search {
  color: #f59e0b;
}

.activity-icon.process {
  color: #8b5cf6;
}

.activity-icon.bulk {
  color: #3b82f6;
}

.activity-icon.default {
  color: #6b7280;
}

.activity-content {
  flex: 1;
}

.activity-main {
  font-size: 14px;
  color: #374151;
  margin-bottom: 4px;
}

.activity-action {
  font-weight: 500;
}

.activity-detail {
  color: #6b7280;
}

.activity-meta {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 12px;
  color: #9ca3af;
}

.activity-user {
  font-weight: 500;
}

/* Responsive Design */
@media (max-width: 768px) {
  .activity-stats {
    padding: 20px;
  }
  
  .activity-item {
    padding: 16px 20px;
  }
  
  .activity-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
}
