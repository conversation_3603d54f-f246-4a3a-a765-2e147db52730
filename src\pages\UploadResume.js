import React, { useState, useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import { FiUpload, FiFile, FiX, <PERSON><PERSON>heck, FiAlertCircle } from 'react-icons/fi';
import { toast } from 'react-toastify';
import './UploadResume.css';

const UploadResume = () => {
  const [files, setFiles] = useState([]);
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState({});

  const onDrop = useCallback((acceptedFiles, rejectedFiles) => {
    // Handle rejected files
    if (rejectedFiles.length > 0) {
      rejectedFiles.forEach(({ file, errors }) => {
        errors.forEach(error => {
          if (error.code === 'file-too-large') {
            toast.error(`File ${file.name} is too large. Maximum size is 10MB.`);
          } else if (error.code === 'file-invalid-type') {
            toast.error(`File ${file.name} is not a valid PDF file.`);
          }
        });
      });
    }

    // Handle accepted files
    const newFiles = acceptedFiles.map(file => ({
      id: Math.random().toString(36).substr(2, 9),
      file,
      status: 'pending', // pending, uploading, success, error
      progress: 0,
      error: null
    }));

    setFiles(prev => [...prev, ...newFiles]);
  }, []);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'application/pdf': ['.pdf']
    },
    maxSize: 10 * 1024 * 1024, // 10MB
    multiple: true
  });

  const removeFile = (fileId) => {
    setFiles(prev => prev.filter(f => f.id !== fileId));
  };

  const uploadFiles = async () => {
    if (files.length === 0) {
      toast.warning('Please select files to upload');
      return;
    }

    setUploading(true);
    
    for (const fileItem of files) {
      if (fileItem.status !== 'pending') continue;

      try {
        // Update status to uploading
        setFiles(prev => prev.map(f => 
          f.id === fileItem.id 
            ? { ...f, status: 'uploading', progress: 0 }
            : f
        ));

        // Simulate upload progress
        for (let progress = 0; progress <= 100; progress += 10) {
          await new Promise(resolve => setTimeout(resolve, 100));
          setFiles(prev => prev.map(f => 
            f.id === fileItem.id 
              ? { ...f, progress }
              : f
          ));
        }

        // Simulate API call
        const formData = new FormData();
        formData.append('file', fileItem.file);

        // Replace with actual API call
        await new Promise(resolve => setTimeout(resolve, 500));

        // Update status to success
        setFiles(prev => prev.map(f => 
          f.id === fileItem.id 
            ? { ...f, status: 'success', progress: 100 }
            : f
        ));

        toast.success(`${fileItem.file.name} uploaded successfully!`);

      } catch (error) {
        // Update status to error
        setFiles(prev => prev.map(f => 
          f.id === fileItem.id 
            ? { ...f, status: 'error', error: error.message }
            : f
        ));

        toast.error(`Failed to upload ${fileItem.file.name}`);
      }
    }

    setUploading(false);
  };

  const clearAll = () => {
    setFiles([]);
  };

  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'success':
        return <FiCheck className="status-icon success" />;
      case 'error':
        return <FiAlertCircle className="status-icon error" />;
      case 'uploading':
        return <div className="loading-spinner" />;
      default:
        return <FiFile className="status-icon pending" />;
    }
  };

  return (
    <div className="upload-page">
      <div className="page-header">
        <h1>Upload Resume</h1>
        <p>Upload a resume to extract information and add it to your collection. Our AI will automatically analyze and structure the data.</p>
      </div>

      <div className="upload-container">
        {/* Upload Area */}
        <div className="upload-section">
          <div 
            {...getRootProps()} 
            className={`dropzone ${isDragActive ? 'active' : ''}`}
          >
            <input {...getInputProps()} />
            <div className="dropzone-content">
              <div className="upload-icon">
                <FiUpload size={48} />
              </div>
              <h3>Click or drag to upload your resume</h3>
              <p>Supported formats: PDF, DOCX, TXT, JPG, PNG</p>
              <div className="upload-specs">
                <span>Maximum file size: 10MB</span>
                <span>•</span>
                <span>Multiple files supported</span>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          {files.length > 0 && (
            <div className="upload-actions">
              <button 
                className="btn btn-secondary"
                onClick={clearAll}
                disabled={uploading}
              >
                Clear All
              </button>
              <button 
                className="btn btn-primary"
                onClick={uploadFiles}
                disabled={uploading || files.every(f => f.status === 'success')}
              >
                {uploading ? 'Processing...' : 'Extract Data'}
              </button>
            </div>
          )}
        </div>

        {/* File List */}
        {files.length > 0 && (
          <div className="file-list-section">
            <div className="section-header">
              <h3>Selected Files ({files.length})</h3>
            </div>
            
            <div className="file-list">
              {files.map((fileItem) => (
                <div key={fileItem.id} className={`file-item ${fileItem.status}`}>
                  <div className="file-info">
                    <div className="file-icon">
                      {getStatusIcon(fileItem.status)}
                    </div>
                    <div className="file-details">
                      <div className="file-name">{fileItem.file.name}</div>
                      <div className="file-meta">
                        <span>{formatFileSize(fileItem.file.size)}</span>
                        {fileItem.status === 'error' && fileItem.error && (
                          <>
                            <span>•</span>
                            <span className="error-text">{fileItem.error}</span>
                          </>
                        )}
                      </div>
                    </div>
                  </div>

                  <div className="file-actions">
                    {fileItem.status === 'uploading' && (
                      <div className="progress-container">
                        <div className="progress-bar">
                          <div 
                            className="progress-fill"
                            style={{ width: `${fileItem.progress}%` }}
                          />
                        </div>
                        <span className="progress-text">{fileItem.progress}%</span>
                      </div>
                    )}
                    
                    {fileItem.status !== 'uploading' && (
                      <button
                        className="remove-btn"
                        onClick={() => removeFile(fileItem.id)}
                        title="Remove file"
                      >
                        <FiX size={16} />
                      </button>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default UploadResume;
