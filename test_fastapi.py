#!/usr/bin/env python3
"""
Test script to verify FastAPI endpoints are working correctly
"""

import requests
import json

def test_showall():
    """Test show all endpoint"""
    try:
        response = requests.get('http://localhost:8001/showall', timeout=10)

        if response.status_code == 200:
            data = response.json()
            print(f"✅ Show All endpoint working - Found {len(data)} resumes")
            if data:
                first_resume = data[0]
                resume_data = first_resume.get('Resume', first_resume)
                name = resume_data.get('PersonalInformation', {}).get('FullName', 'Unknown')
                print(f"   First resume: {name}")
            return True
        else:
            print(f"❌ Show All endpoint failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Show All endpoint failed: {e}")
        return False

def test_search():
    """Test search endpoint with multiple queries"""
    test_queries = [
        'medical',
        'show all resumes',
        'engineer',
        'computer science'
    ]

    for query in test_queries:
        try:
            response = requests.get('http://localhost:8001/query/', params={'naturalQuery': query}, timeout=10)

            if response.status_code == 200:
                data = response.json()
                results = data.get('listOfDict', [])
                print(f"✅ Query '{query}' - Found {len(results)} results")
                print(f"   GPT Result: {data.get('result', 'No result')}")
                print(f"   MongoDB Query: {data.get('query', [])}")
            else:
                print(f"❌ Query '{query}' failed: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ Query '{query}' failed: {e}")
            return False

    return True

def main():
    """Run all tests"""
    print("🧪 Testing FastAPI Backend with Sample Data")
    print("=" * 50)

    # Test show all endpoint
    test_showall()

    print()

    # Test search endpoint
    test_search()

    print("\n" + "=" * 50)
    print("🎯 FastAPI Backend is ready!")
    print("   Backend URL: http://localhost:8001")
    print("   Frontend URL: http://localhost:3000")

if __name__ == "__main__":
    main()
