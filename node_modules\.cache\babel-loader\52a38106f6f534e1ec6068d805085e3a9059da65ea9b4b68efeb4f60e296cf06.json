{"ast": null, "code": "var _jsxFileName = \"D:\\\\Projects\\\\SQL-Agent-Task-Submission-main\\\\SQL-Agent-Task-Submission-main\\\\src\\\\components\\\\ResumeModal.js\";\nimport React, { useRef } from 'react';\nimport { FiX, FiMail, FiPhone, FiMapPin, FiDownload, FiExternalLink, FiUser, FiBriefcase, FiBookOpen, FiAward } from 'react-icons/fi';\nimport jsPDF from 'jspdf';\nimport html2canvas from 'html2canvas';\nimport './ResumeModal.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ResumeModal = ({\n  resume,\n  isOpen,\n  onClose\n}) => {\n  var _resume$rawData, _resume$rawData$Resum, _resume$rawData2, _resume$rawData2$Resu, _resume$rawData3, _resume$rawData3$Resu, _resume$rawData4, _resume$rawData4$Resu, _resume$rawData5, _resume$rawData5$Resu, _resume$rawData6, _resume$rawData6$Resu, _resume$rawData7, _resume$rawData7$Resu, _resume$rawData$Resum2, _resume$rawData$Resum3;\n  if (!isOpen || !resume) return null;\n  const handleDownload = () => {\n    // Create a simple text-based resume content\n    const resumeContent = `\n${resume.name}\n${resume.title}\n${resume.email} | ${resume.phone} | ${resume.location}\n\nSUMMARY\n${resume.summary}\n\nEXPERIENCE\n${resume.experience}\n\nEDUCATION\n${resume.education}\n\nSKILLS\n${resume.skills.join(', ')}\n    `.trim();\n\n    // Create and download the file\n    const blob = new Blob([resumeContent], {\n      type: 'text/plain'\n    });\n    const url = URL.createObjectURL(blob);\n    const link = document.createElement('a');\n    link.href = url;\n    link.download = `${resume.name.replace(/\\s+/g, '_')}_Resume.txt`;\n    document.body.appendChild(link);\n    link.click();\n    document.body.removeChild(link);\n    URL.revokeObjectURL(url);\n  };\n  const handleEmailClick = () => {\n    window.open(`mailto:${resume.email}`, '_blank');\n  };\n  const handlePhoneClick = () => {\n    window.open(`tel:${resume.phone}`, '_blank');\n  };\n  const handleBackdropClick = e => {\n    if (e.target === e.currentTarget) {\n      onClose();\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"modal-backdrop\",\n    onClick: handleBackdropClick,\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"resume-modal\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"modal-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"modal-title\",\n          children: [/*#__PURE__*/_jsxDEV(FiUser, {\n            size: 24\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 61,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n            children: \"Resume Details\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 62,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 60,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"modal-close\",\n          onClick: onClose,\n          children: /*#__PURE__*/_jsxDEV(FiX, {\n            size: 24\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 65,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 64,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 59,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"modal-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"modal-content-full\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"modal-section\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"section-header\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"resume-avatar-large\",\n                children: /*#__PURE__*/_jsxDEV(FiUser, {\n                  size: 32\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 75,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 74,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"personal-info\",\n                children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                  className: \"candidate-name\",\n                  children: resume.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 78,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n                  className: \"candidate-title\",\n                  children: resume.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 79,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"match-score-large\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"score-label\",\n                    children: \"Match Score:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 81,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"score-value\",\n                    children: [resume.score, \"%\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 82,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"score-bar\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"score-fill\",\n                      style: {\n                        width: `${resume.score}%`\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 84,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 83,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 80,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 77,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"contact-actions\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"contact-btn email-btn\",\n                  onClick: handleEmailClick,\n                  title: `Email ${resume.name}`,\n                  children: [/*#__PURE__*/_jsxDEV(FiMail, {\n                    size: 16\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 97,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: resume.email\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 98,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(FiExternalLink, {\n                    size: 12\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 99,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 92,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"contact-btn phone-btn\",\n                  onClick: handlePhoneClick,\n                  title: `Call ${resume.name}`,\n                  children: [/*#__PURE__*/_jsxDEV(FiPhone, {\n                    size: 16\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 106,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: resume.phone\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 107,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(FiExternalLink, {\n                    size: 12\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 108,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 101,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"contact-info location-info\",\n                  children: [/*#__PURE__*/_jsxDEV(FiMapPin, {\n                    size: 16\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 111,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: resume.location\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 112,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 110,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 91,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 73,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 72,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 71,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"modal-content-left\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"modal-section\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"section-title\",\n              children: [/*#__PURE__*/_jsxDEV(FiUser, {\n                size: 20\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 124,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                children: \"Professional Summary\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 125,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 123,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"section-content\",\n              children: /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"summary-text\",\n                children: resume.summary\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 128,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 127,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 122,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"modal-section\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"section-title\",\n              children: [/*#__PURE__*/_jsxDEV(FiBriefcase, {\n                size: 20\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 135,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                children: \"Work Experience\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 136,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 134,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"section-content\",\n              children: [(_resume$rawData = resume.rawData) !== null && _resume$rawData !== void 0 && (_resume$rawData$Resum = _resume$rawData.Resume) !== null && _resume$rawData$Resum !== void 0 && _resume$rawData$Resum.WorkExperience && resume.rawData.Resume.WorkExperience.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"experience-list\",\n                children: resume.rawData.Resume.WorkExperience.map((exp, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"experience-item\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    children: exp.JobTitle || exp.Position || 'Position'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 143,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"company-name\",\n                    children: exp.Company || exp.Organization || 'Company'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 144,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"duration\",\n                    children: exp.Duration || exp.Years || 'Duration not specified'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 145,\n                    columnNumber: 25\n                  }, this), exp.Description && /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"description\",\n                    children: exp.Description\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 146,\n                    columnNumber: 45\n                  }, this), exp.Responsibilities && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"responsibilities\",\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Responsibilities:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 149,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      children: exp.Responsibilities\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 150,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 148,\n                    columnNumber: 27\n                  }, this)]\n                }, index, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 142,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 140,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"experience-text\",\n                children: resume.experience\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 157,\n                columnNumber: 19\n              }, this), ((_resume$rawData2 = resume.rawData) === null || _resume$rawData2 === void 0 ? void 0 : (_resume$rawData2$Resu = _resume$rawData2.Resume) === null || _resume$rawData2$Resu === void 0 ? void 0 : _resume$rawData2$Resu.TotalWorkExperienceInYears) && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"total-experience\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Total Experience:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 161,\n                  columnNumber: 21\n                }, this), \" \", resume.rawData.Resume.TotalWorkExperienceInYears, \" years\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 160,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 138,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 133,\n            columnNumber: 13\n          }, this), ((_resume$rawData3 = resume.rawData) === null || _resume$rawData3 === void 0 ? void 0 : (_resume$rawData3$Resu = _resume$rawData3.Resume) === null || _resume$rawData3$Resu === void 0 ? void 0 : _resume$rawData3$Resu.Projects) && resume.rawData.Resume.Projects.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"modal-section\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"section-title\",\n              children: /*#__PURE__*/_jsxDEV(\"h3\", {\n                children: \"Projects\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 171,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 170,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"section-content\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"projects-list\",\n                children: resume.rawData.Resume.Projects.map((project, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"project-item\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    children: project.Name || project.Title || `Project ${index + 1}`\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 177,\n                    columnNumber: 25\n                  }, this), project.Description && /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: project.Description\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 178,\n                    columnNumber: 49\n                  }, this), project.Technologies && /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Technologies:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 180,\n                      columnNumber: 30\n                    }, this), \" \", project.Technologies]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 180,\n                    columnNumber: 27\n                  }, this)]\n                }, index, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 176,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 174,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 173,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 169,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 120,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"modal-content-right\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"modal-section\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"section-title\",\n              children: [/*#__PURE__*/_jsxDEV(FiBookOpen, {\n                size: 20\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 195,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                children: \"Education\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 196,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 194,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"section-content\",\n              children: (_resume$rawData4 = resume.rawData) !== null && _resume$rawData4 !== void 0 && (_resume$rawData4$Resu = _resume$rawData4.Resume) !== null && _resume$rawData4$Resu !== void 0 && _resume$rawData4$Resu.Education && resume.rawData.Resume.Education.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"education-list\",\n                children: resume.rawData.Resume.Education.map((edu, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"education-item\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    children: edu.Degree || 'Degree'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 203,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"institution\",\n                    children: edu.Institution || edu.School || 'Institution'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 204,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"graduation-year\",\n                    children: edu.GraduationYear || edu.Year || 'Year not specified'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 205,\n                    columnNumber: 25\n                  }, this), edu['GPA/Marks/%'] && edu['GPA/Marks/%'] > 0 && /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"marks\",\n                    children: [\"Marks: \", edu['GPA/Marks/%'], \"%\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 209,\n                    columnNumber: 27\n                  }, this)]\n                }, index, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 202,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 200,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"education-text\",\n                children: resume.education\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 215,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 198,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 193,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"modal-section\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"section-title\",\n              children: [/*#__PURE__*/_jsxDEV(FiAward, {\n                size: 20\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 223,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                children: \"Skills & Technologies\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 224,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 222,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"section-content\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"skills-grid\",\n                children: resume.skills.map((skill, index) => /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"skill-tag-large\",\n                  children: skill\n                }, index, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 229,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 227,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 226,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 221,\n            columnNumber: 13\n          }, this), ((_resume$rawData5 = resume.rawData) === null || _resume$rawData5 === void 0 ? void 0 : (_resume$rawData5$Resu = _resume$rawData5.Resume) === null || _resume$rawData5$Resu === void 0 ? void 0 : _resume$rawData5$Resu.Languages) && resume.rawData.Resume.Languages.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"modal-section\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"section-title\",\n              children: /*#__PURE__*/_jsxDEV(\"h3\", {\n                children: \"Languages\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 239,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 238,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"section-content\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"languages-list\",\n                children: resume.rawData.Resume.Languages.map((lang, index) => /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"language-tag\",\n                  children: lang\n                }, index, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 244,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 242,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 241,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 237,\n            columnNumber: 15\n          }, this), ((_resume$rawData6 = resume.rawData) === null || _resume$rawData6 === void 0 ? void 0 : (_resume$rawData6$Resu = _resume$rawData6.Resume) === null || _resume$rawData6$Resu === void 0 ? void 0 : _resume$rawData6$Resu.Certifications) && resume.rawData.Resume.Certifications.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"modal-section\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"section-title\",\n              children: /*#__PURE__*/_jsxDEV(\"h3\", {\n                children: \"Certifications\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 255,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 254,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"section-content\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"certifications-list\",\n                children: resume.rawData.Resume.Certifications.map((cert, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"certification-item\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    children: cert.Name || cert.Title || `Certification ${index + 1}`\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 261,\n                    columnNumber: 25\n                  }, this), cert.IssuingOrganization && /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [\"Issued by: \", cert.IssuingOrganization]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 262,\n                    columnNumber: 54\n                  }, this), cert.IssueDate && /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [\"Date: \", cert.IssueDate]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 263,\n                    columnNumber: 44\n                  }, this)]\n                }, index, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 260,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 258,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 257,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 253,\n            columnNumber: 15\n          }, this), ((_resume$rawData7 = resume.rawData) === null || _resume$rawData7 === void 0 ? void 0 : (_resume$rawData7$Resu = _resume$rawData7.Resume) === null || _resume$rawData7$Resu === void 0 ? void 0 : _resume$rawData7$Resu.Achievements) && resume.rawData.Resume.Achievements.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"modal-section\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"section-title\",\n              children: /*#__PURE__*/_jsxDEV(\"h3\", {\n                children: \"Achievements\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 275,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 274,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"section-content\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"achievements-list\",\n                children: resume.rawData.Resume.Achievements.map((achievement, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"achievement-item\",\n                  children: typeof achievement === 'string' ? /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: achievement\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 282,\n                    columnNumber: 27\n                  }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                      children: achievement.AchievementName || achievement.Name || `Achievement ${index + 1}`\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 285,\n                      columnNumber: 29\n                    }, this), achievement.IssueDate && /*#__PURE__*/_jsxDEV(\"p\", {\n                      children: [\"Date: \", achievement.IssueDate]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 286,\n                      columnNumber: 55\n                    }, this), achievement.IssuingOrganization && /*#__PURE__*/_jsxDEV(\"p\", {\n                      children: [\"Organization: \", achievement.IssuingOrganization]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 287,\n                      columnNumber: 65\n                    }, this), achievement.Description && /*#__PURE__*/_jsxDEV(\"p\", {\n                      children: achievement.Description\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 288,\n                      columnNumber: 57\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 284,\n                    columnNumber: 27\n                  }, this)\n                }, index, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 280,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 278,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 277,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 273,\n            columnNumber: 15\n          }, this), resume.rawData && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"modal-section\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"section-title\",\n              children: /*#__PURE__*/_jsxDEV(\"h3\", {\n                children: \"Additional Information\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 302,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 301,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"section-content\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"raw-data-info\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"Database ID:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 306,\n                    columnNumber: 24\n                  }, this), \" \", resume.rawData._id || 'N/A']\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 306,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"Data Source:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 307,\n                    columnNumber: 24\n                  }, this), \" \", resume.rawData.Resume ? 'MongoDB' : 'Mock Data']\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 307,\n                  columnNumber: 21\n                }, this), ((_resume$rawData$Resum2 = resume.rawData.Resume) === null || _resume$rawData$Resum2 === void 0 ? void 0 : (_resume$rawData$Resum3 = _resume$rawData$Resum2.PersonalInformation) === null || _resume$rawData$Resum3 === void 0 ? void 0 : _resume$rawData$Resum3.LinkedIn) && /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"LinkedIn:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 309,\n                    columnNumber: 26\n                  }, this), \" \", resume.rawData.Resume.PersonalInformation.LinkedIn]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 309,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 305,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 304,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 300,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 191,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 69,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"modal-footer\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-secondary\",\n          onClick: onClose,\n          children: \"Close\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 319,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-primary\",\n          onClick: handleDownload,\n          children: [/*#__PURE__*/_jsxDEV(FiDownload, {\n            size: 16\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 323,\n            columnNumber: 13\n          }, this), \"Download Resume\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 322,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 318,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 58,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 57,\n    columnNumber: 5\n  }, this);\n};\n_c = ResumeModal;\nexport default ResumeModal;\nvar _c;\n$RefreshReg$(_c, \"ResumeModal\");", "map": {"version": 3, "names": ["React", "useRef", "FiX", "FiMail", "FiPhone", "FiMapPin", "FiDownload", "FiExternalLink", "FiUser", "FiBriefcase", "FiBookOpen", "FiAward", "jsPDF", "html2canvas", "jsxDEV", "_jsxDEV", "ResumeModal", "resume", "isOpen", "onClose", "_resume$rawData", "_resume$rawData$Resum", "_resume$rawData2", "_resume$rawData2$Resu", "_resume$rawData3", "_resume$rawData3$Resu", "_resume$rawData4", "_resume$rawData4$Resu", "_resume$rawData5", "_resume$rawData5$Resu", "_resume$rawData6", "_resume$rawData6$Resu", "_resume$rawData7", "_resume$rawData7$Resu", "_resume$rawData$Resum2", "_resume$rawData$Resum3", "handleDownload", "<PERSON><PERSON><PERSON>nt", "name", "title", "email", "phone", "location", "summary", "experience", "education", "skills", "join", "trim", "blob", "Blob", "type", "url", "URL", "createObjectURL", "link", "document", "createElement", "href", "download", "replace", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL", "handleEmailClick", "window", "open", "handlePhoneClick", "handleBackdropClick", "e", "target", "currentTarget", "className", "onClick", "children", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "score", "style", "width", "rawData", "Resume", "WorkExperience", "length", "map", "exp", "index", "JobTitle", "Position", "Company", "Organization", "Duration", "Years", "Description", "Responsibilities", "TotalWorkExperienceInYears", "Projects", "project", "Name", "Title", "Technologies", "Education", "edu", "Degree", "Institution", "School", "GraduationYear", "Year", "skill", "Languages", "lang", "Certifications", "cert", "IssuingOrganization", "IssueDate", "Achievements", "achievement", "AchievementName", "_id", "PersonalInformation", "LinkedIn", "_c", "$RefreshReg$"], "sources": ["D:/Projects/SQL-Agent-Task-Submission-main/SQL-Agent-Task-Submission-main/src/components/ResumeModal.js"], "sourcesContent": ["import React, { useRef } from 'react';\nimport { FiX, FiMail, FiPhone, FiMapPin, FiDownload, FiExternalLink, FiUser, FiBriefcase, FiBookOpen, FiAward } from 'react-icons/fi';\nimport jsPDF from 'jspdf';\nimport html2canvas from 'html2canvas';\nimport './ResumeModal.css';\n\nconst ResumeModal = ({ resume, isOpen, onClose }) => {\n  if (!isOpen || !resume) return null;\n\n  const handleDownload = () => {\n    // Create a simple text-based resume content\n    const resumeContent = `\n${resume.name}\n${resume.title}\n${resume.email} | ${resume.phone} | ${resume.location}\n\nSUMMARY\n${resume.summary}\n\nEXPERIENCE\n${resume.experience}\n\nEDUCATION\n${resume.education}\n\nSKILLS\n${resume.skills.join(', ')}\n    `.trim();\n\n    // Create and download the file\n    const blob = new Blob([resumeContent], { type: 'text/plain' });\n    const url = URL.createObjectURL(blob);\n    const link = document.createElement('a');\n    link.href = url;\n    link.download = `${resume.name.replace(/\\s+/g, '_')}_Resume.txt`;\n    document.body.appendChild(link);\n    link.click();\n    document.body.removeChild(link);\n    URL.revokeObjectURL(url);\n  };\n\n  const handleEmailClick = () => {\n    window.open(`mailto:${resume.email}`, '_blank');\n  };\n\n  const handlePhoneClick = () => {\n    window.open(`tel:${resume.phone}`, '_blank');\n  };\n\n  const handleBackdropClick = (e) => {\n    if (e.target === e.currentTarget) {\n      onClose();\n    }\n  };\n\n  return (\n    <div className=\"modal-backdrop\" onClick={handleBackdropClick}>\n      <div className=\"resume-modal\">\n        <div className=\"modal-header\">\n          <div className=\"modal-title\">\n            <FiUser size={24} />\n            <h2>Resume Details</h2>\n          </div>\n          <button className=\"modal-close\" onClick={onClose}>\n            <FiX size={24} />\n          </button>\n        </div>\n\n        <div className=\"modal-content\">\n          {/* Personal Information Section - Full Width */}\n          <div className=\"modal-content-full\">\n            <div className=\"modal-section\">\n              <div className=\"section-header\">\n                <div className=\"resume-avatar-large\">\n                  <FiUser size={32} />\n                </div>\n                <div className=\"personal-info\">\n                  <h1 className=\"candidate-name\">{resume.name}</h1>\n                  <h2 className=\"candidate-title\">{resume.title}</h2>\n                  <div className=\"match-score-large\">\n                    <span className=\"score-label\">Match Score:</span>\n                    <span className=\"score-value\">{resume.score}%</span>\n                    <div className=\"score-bar\">\n                      <div\n                        className=\"score-fill\"\n                        style={{ width: `${resume.score}%` }}\n                      ></div>\n                    </div>\n                  </div>\n                </div>\n                <div className=\"contact-actions\">\n                  <button\n                    className=\"contact-btn email-btn\"\n                    onClick={handleEmailClick}\n                    title={`Email ${resume.name}`}\n                  >\n                    <FiMail size={16} />\n                    <span>{resume.email}</span>\n                    <FiExternalLink size={12} />\n                  </button>\n                  <button\n                    className=\"contact-btn phone-btn\"\n                    onClick={handlePhoneClick}\n                    title={`Call ${resume.name}`}\n                  >\n                    <FiPhone size={16} />\n                    <span>{resume.phone}</span>\n                    <FiExternalLink size={12} />\n                  </button>\n                  <div className=\"contact-info location-info\">\n                    <FiMapPin size={16} />\n                    <span>{resume.location}</span>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Left Column */}\n          <div className=\"modal-content-left\">\n            {/* Summary/Objective Section */}\n            <div className=\"modal-section\">\n              <div className=\"section-title\">\n                <FiUser size={20} />\n                <h3>Professional Summary</h3>\n              </div>\n              <div className=\"section-content\">\n                <p className=\"summary-text\">{resume.summary}</p>\n              </div>\n            </div>\n\n            {/* Experience Section */}\n            <div className=\"modal-section\">\n              <div className=\"section-title\">\n                <FiBriefcase size={20} />\n                <h3>Work Experience</h3>\n              </div>\n              <div className=\"section-content\">\n                {resume.rawData?.Resume?.WorkExperience && resume.rawData.Resume.WorkExperience.length > 0 ? (\n                  <div className=\"experience-list\">\n                    {resume.rawData.Resume.WorkExperience.map((exp, index) => (\n                      <div key={index} className=\"experience-item\">\n                        <h4>{exp.JobTitle || exp.Position || 'Position'}</h4>\n                        <p className=\"company-name\">{exp.Company || exp.Organization || 'Company'}</p>\n                        <p className=\"duration\">{exp.Duration || exp.Years || 'Duration not specified'}</p>\n                        {exp.Description && <p className=\"description\">{exp.Description}</p>}\n                        {exp.Responsibilities && (\n                          <div className=\"responsibilities\">\n                            <strong>Responsibilities:</strong>\n                            <p>{exp.Responsibilities}</p>\n                          </div>\n                        )}\n                      </div>\n                    ))}\n                  </div>\n                ) : (\n                  <p className=\"experience-text\">{resume.experience}</p>\n                )}\n                {resume.rawData?.Resume?.TotalWorkExperienceInYears && (\n                  <p className=\"total-experience\">\n                    <strong>Total Experience:</strong> {resume.rawData.Resume.TotalWorkExperienceInYears} years\n                  </p>\n                )}\n              </div>\n            </div>\n\n            {/* Projects Section */}\n            {resume.rawData?.Resume?.Projects && resume.rawData.Resume.Projects.length > 0 && (\n              <div className=\"modal-section\">\n                <div className=\"section-title\">\n                  <h3>Projects</h3>\n                </div>\n                <div className=\"section-content\">\n                  <div className=\"projects-list\">\n                    {resume.rawData.Resume.Projects.map((project, index) => (\n                      <div key={index} className=\"project-item\">\n                        <h4>{project.Name || project.Title || `Project ${index + 1}`}</h4>\n                        {project.Description && <p>{project.Description}</p>}\n                        {project.Technologies && (\n                          <p><strong>Technologies:</strong> {project.Technologies}</p>\n                        )}\n                      </div>\n                    ))}\n                  </div>\n                </div>\n              </div>\n            )}\n          </div>\n\n          {/* Right Column */}\n          <div className=\"modal-content-right\">\n            {/* Education Section */}\n            <div className=\"modal-section\">\n              <div className=\"section-title\">\n                <FiBookOpen size={20} />\n                <h3>Education</h3>\n              </div>\n              <div className=\"section-content\">\n                {resume.rawData?.Resume?.Education && resume.rawData.Resume.Education.length > 0 ? (\n                  <div className=\"education-list\">\n                    {resume.rawData.Resume.Education.map((edu, index) => (\n                      <div key={index} className=\"education-item\">\n                        <h4>{edu.Degree || 'Degree'}</h4>\n                        <p className=\"institution\">{edu.Institution || edu.School || 'Institution'}</p>\n                        <p className=\"graduation-year\">\n                          {edu.GraduationYear || edu.Year || 'Year not specified'}\n                        </p>\n                        {edu['GPA/Marks/%'] && edu['GPA/Marks/%'] > 0 && (\n                          <p className=\"marks\">Marks: {edu['GPA/Marks/%']}%</p>\n                        )}\n                      </div>\n                    ))}\n                  </div>\n                ) : (\n                  <p className=\"education-text\">{resume.education}</p>\n                )}\n              </div>\n            </div>\n\n            {/* Skills Section */}\n            <div className=\"modal-section\">\n              <div className=\"section-title\">\n                <FiAward size={20} />\n                <h3>Skills & Technologies</h3>\n              </div>\n              <div className=\"section-content\">\n                <div className=\"skills-grid\">\n                  {resume.skills.map((skill, index) => (\n                    <span key={index} className=\"skill-tag-large\">{skill}</span>\n                  ))}\n                </div>\n              </div>\n            </div>\n\n            {/* Languages Section */}\n            {resume.rawData?.Resume?.Languages && resume.rawData.Resume.Languages.length > 0 && (\n              <div className=\"modal-section\">\n                <div className=\"section-title\">\n                  <h3>Languages</h3>\n                </div>\n                <div className=\"section-content\">\n                  <div className=\"languages-list\">\n                    {resume.rawData.Resume.Languages.map((lang, index) => (\n                      <span key={index} className=\"language-tag\">{lang}</span>\n                    ))}\n                  </div>\n                </div>\n              </div>\n            )}\n\n            {/* Certifications Section */}\n            {resume.rawData?.Resume?.Certifications && resume.rawData.Resume.Certifications.length > 0 && (\n              <div className=\"modal-section\">\n                <div className=\"section-title\">\n                  <h3>Certifications</h3>\n                </div>\n                <div className=\"section-content\">\n                  <div className=\"certifications-list\">\n                    {resume.rawData.Resume.Certifications.map((cert, index) => (\n                      <div key={index} className=\"certification-item\">\n                        <h4>{cert.Name || cert.Title || `Certification ${index + 1}`}</h4>\n                        {cert.IssuingOrganization && <p>Issued by: {cert.IssuingOrganization}</p>}\n                        {cert.IssueDate && <p>Date: {cert.IssueDate}</p>}\n                      </div>\n                    ))}\n                  </div>\n                </div>\n              </div>\n            )}\n\n            {/* Achievements Section */}\n            {resume.rawData?.Resume?.Achievements && resume.rawData.Resume.Achievements.length > 0 && (\n              <div className=\"modal-section\">\n                <div className=\"section-title\">\n                  <h3>Achievements</h3>\n                </div>\n                <div className=\"section-content\">\n                  <div className=\"achievements-list\">\n                    {resume.rawData.Resume.Achievements.map((achievement, index) => (\n                      <div key={index} className=\"achievement-item\">\n                        {typeof achievement === 'string' ? (\n                          <p>{achievement}</p>\n                        ) : (\n                          <div>\n                            <h4>{achievement.AchievementName || achievement.Name || `Achievement ${index + 1}`}</h4>\n                            {achievement.IssueDate && <p>Date: {achievement.IssueDate}</p>}\n                            {achievement.IssuingOrganization && <p>Organization: {achievement.IssuingOrganization}</p>}\n                            {achievement.Description && <p>{achievement.Description}</p>}\n                          </div>\n                        )}\n                      </div>\n                    ))}\n                  </div>\n                </div>\n              </div>\n            )}\n\n            {/* Raw Data Section (for debugging) */}\n            {resume.rawData && (\n              <div className=\"modal-section\">\n                <div className=\"section-title\">\n                  <h3>Additional Information</h3>\n                </div>\n                <div className=\"section-content\">\n                  <div className=\"raw-data-info\">\n                    <p><strong>Database ID:</strong> {resume.rawData._id || 'N/A'}</p>\n                    <p><strong>Data Source:</strong> {resume.rawData.Resume ? 'MongoDB' : 'Mock Data'}</p>\n                    {resume.rawData.Resume?.PersonalInformation?.LinkedIn && (\n                      <p><strong>LinkedIn:</strong> {resume.rawData.Resume.PersonalInformation.LinkedIn}</p>\n                    )}\n                  </div>\n                </div>\n              </div>\n            )}\n          </div>\n        </div>\n\n        <div className=\"modal-footer\">\n          <button className=\"btn btn-secondary\" onClick={onClose}>\n            Close\n          </button>\n          <button className=\"btn btn-primary\" onClick={handleDownload}>\n            <FiDownload size={16} />\n            Download Resume\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default ResumeModal;\n"], "mappings": ";AAAA,OAAOA,KAAK,IAAIC,MAAM,QAAQ,OAAO;AACrC,SAASC,GAAG,EAAEC,MAAM,EAAEC,OAAO,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,cAAc,EAAEC,MAAM,EAAEC,WAAW,EAAEC,UAAU,EAAEC,OAAO,QAAQ,gBAAgB;AACrI,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,WAAW,MAAM,aAAa;AACrC,OAAO,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3B,MAAMC,WAAW,GAAGA,CAAC;EAAEC,MAAM;EAAEC,MAAM;EAAEC;AAAQ,CAAC,KAAK;EAAA,IAAAC,eAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA;EACnD,IAAI,CAACjB,MAAM,IAAI,CAACD,MAAM,EAAE,OAAO,IAAI;EAEnC,MAAMmB,cAAc,GAAGA,CAAA,KAAM;IAC3B;IACA,MAAMC,aAAa,GAAG;AAC1B,EAAEpB,MAAM,CAACqB,IAAI;AACb,EAAErB,MAAM,CAACsB,KAAK;AACd,EAAEtB,MAAM,CAACuB,KAAK,MAAMvB,MAAM,CAACwB,KAAK,MAAMxB,MAAM,CAACyB,QAAQ;AACrD;AACA;AACA,EAAEzB,MAAM,CAAC0B,OAAO;AAChB;AACA;AACA,EAAE1B,MAAM,CAAC2B,UAAU;AACnB;AACA;AACA,EAAE3B,MAAM,CAAC4B,SAAS;AAClB;AACA;AACA,EAAE5B,MAAM,CAAC6B,MAAM,CAACC,IAAI,CAAC,IAAI,CAAC;AAC1B,KAAK,CAACC,IAAI,CAAC,CAAC;;IAER;IACA,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACb,aAAa,CAAC,EAAE;MAAEc,IAAI,EAAE;IAAa,CAAC,CAAC;IAC9D,MAAMC,GAAG,GAAGC,GAAG,CAACC,eAAe,CAACL,IAAI,CAAC;IACrC,MAAMM,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;IACxCF,IAAI,CAACG,IAAI,GAAGN,GAAG;IACfG,IAAI,CAACI,QAAQ,GAAG,GAAG1C,MAAM,CAACqB,IAAI,CAACsB,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,aAAa;IAChEJ,QAAQ,CAACK,IAAI,CAACC,WAAW,CAACP,IAAI,CAAC;IAC/BA,IAAI,CAACQ,KAAK,CAAC,CAAC;IACZP,QAAQ,CAACK,IAAI,CAACG,WAAW,CAACT,IAAI,CAAC;IAC/BF,GAAG,CAACY,eAAe,CAACb,GAAG,CAAC;EAC1B,CAAC;EAED,MAAMc,gBAAgB,GAAGA,CAAA,KAAM;IAC7BC,MAAM,CAACC,IAAI,CAAC,UAAUnD,MAAM,CAACuB,KAAK,EAAE,EAAE,QAAQ,CAAC;EACjD,CAAC;EAED,MAAM6B,gBAAgB,GAAGA,CAAA,KAAM;IAC7BF,MAAM,CAACC,IAAI,CAAC,OAAOnD,MAAM,CAACwB,KAAK,EAAE,EAAE,QAAQ,CAAC;EAC9C,CAAC;EAED,MAAM6B,mBAAmB,GAAIC,CAAC,IAAK;IACjC,IAAIA,CAAC,CAACC,MAAM,KAAKD,CAAC,CAACE,aAAa,EAAE;MAChCtD,OAAO,CAAC,CAAC;IACX;EACF,CAAC;EAED,oBACEJ,OAAA;IAAK2D,SAAS,EAAC,gBAAgB;IAACC,OAAO,EAAEL,mBAAoB;IAAAM,QAAA,eAC3D7D,OAAA;MAAK2D,SAAS,EAAC,cAAc;MAAAE,QAAA,gBAC3B7D,OAAA;QAAK2D,SAAS,EAAC,cAAc;QAAAE,QAAA,gBAC3B7D,OAAA;UAAK2D,SAAS,EAAC,aAAa;UAAAE,QAAA,gBAC1B7D,OAAA,CAACP,MAAM;YAACqE,IAAI,EAAE;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACpBlE,OAAA;YAAA6D,QAAA,EAAI;UAAc;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpB,CAAC,eACNlE,OAAA;UAAQ2D,SAAS,EAAC,aAAa;UAACC,OAAO,EAAExD,OAAQ;UAAAyD,QAAA,eAC/C7D,OAAA,CAACb,GAAG;YAAC2E,IAAI,EAAE;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAENlE,OAAA;QAAK2D,SAAS,EAAC,eAAe;QAAAE,QAAA,gBAE5B7D,OAAA;UAAK2D,SAAS,EAAC,oBAAoB;UAAAE,QAAA,eACjC7D,OAAA;YAAK2D,SAAS,EAAC,eAAe;YAAAE,QAAA,eAC5B7D,OAAA;cAAK2D,SAAS,EAAC,gBAAgB;cAAAE,QAAA,gBAC7B7D,OAAA;gBAAK2D,SAAS,EAAC,qBAAqB;gBAAAE,QAAA,eAClC7D,OAAA,CAACP,MAAM;kBAACqE,IAAI,EAAE;gBAAG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjB,CAAC,eACNlE,OAAA;gBAAK2D,SAAS,EAAC,eAAe;gBAAAE,QAAA,gBAC5B7D,OAAA;kBAAI2D,SAAS,EAAC,gBAAgB;kBAAAE,QAAA,EAAE3D,MAAM,CAACqB;gBAAI;kBAAAwC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACjDlE,OAAA;kBAAI2D,SAAS,EAAC,iBAAiB;kBAAAE,QAAA,EAAE3D,MAAM,CAACsB;gBAAK;kBAAAuC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACnDlE,OAAA;kBAAK2D,SAAS,EAAC,mBAAmB;kBAAAE,QAAA,gBAChC7D,OAAA;oBAAM2D,SAAS,EAAC,aAAa;oBAAAE,QAAA,EAAC;kBAAY;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACjDlE,OAAA;oBAAM2D,SAAS,EAAC,aAAa;oBAAAE,QAAA,GAAE3D,MAAM,CAACiE,KAAK,EAAC,GAAC;kBAAA;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACpDlE,OAAA;oBAAK2D,SAAS,EAAC,WAAW;oBAAAE,QAAA,eACxB7D,OAAA;sBACE2D,SAAS,EAAC,YAAY;sBACtBS,KAAK,EAAE;wBAAEC,KAAK,EAAE,GAAGnE,MAAM,CAACiE,KAAK;sBAAI;oBAAE;sBAAAJ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNlE,OAAA;gBAAK2D,SAAS,EAAC,iBAAiB;gBAAAE,QAAA,gBAC9B7D,OAAA;kBACE2D,SAAS,EAAC,uBAAuB;kBACjCC,OAAO,EAAET,gBAAiB;kBAC1B3B,KAAK,EAAE,SAAStB,MAAM,CAACqB,IAAI,EAAG;kBAAAsC,QAAA,gBAE9B7D,OAAA,CAACZ,MAAM;oBAAC0E,IAAI,EAAE;kBAAG;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACpBlE,OAAA;oBAAA6D,QAAA,EAAO3D,MAAM,CAACuB;kBAAK;oBAAAsC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC3BlE,OAAA,CAACR,cAAc;oBAACsE,IAAI,EAAE;kBAAG;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtB,CAAC,eACTlE,OAAA;kBACE2D,SAAS,EAAC,uBAAuB;kBACjCC,OAAO,EAAEN,gBAAiB;kBAC1B9B,KAAK,EAAE,QAAQtB,MAAM,CAACqB,IAAI,EAAG;kBAAAsC,QAAA,gBAE7B7D,OAAA,CAACX,OAAO;oBAACyE,IAAI,EAAE;kBAAG;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACrBlE,OAAA;oBAAA6D,QAAA,EAAO3D,MAAM,CAACwB;kBAAK;oBAAAqC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC3BlE,OAAA,CAACR,cAAc;oBAACsE,IAAI,EAAE;kBAAG;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtB,CAAC,eACTlE,OAAA;kBAAK2D,SAAS,EAAC,4BAA4B;kBAAAE,QAAA,gBACzC7D,OAAA,CAACV,QAAQ;oBAACwE,IAAI,EAAE;kBAAG;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACtBlE,OAAA;oBAAA6D,QAAA,EAAO3D,MAAM,CAACyB;kBAAQ;oBAAAoC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNlE,OAAA;UAAK2D,SAAS,EAAC,oBAAoB;UAAAE,QAAA,gBAEjC7D,OAAA;YAAK2D,SAAS,EAAC,eAAe;YAAAE,QAAA,gBAC5B7D,OAAA;cAAK2D,SAAS,EAAC,eAAe;cAAAE,QAAA,gBAC5B7D,OAAA,CAACP,MAAM;gBAACqE,IAAI,EAAE;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACpBlE,OAAA;gBAAA6D,QAAA,EAAI;cAAoB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1B,CAAC,eACNlE,OAAA;cAAK2D,SAAS,EAAC,iBAAiB;cAAAE,QAAA,eAC9B7D,OAAA;gBAAG2D,SAAS,EAAC,cAAc;gBAAAE,QAAA,EAAE3D,MAAM,CAAC0B;cAAO;gBAAAmC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNlE,OAAA;YAAK2D,SAAS,EAAC,eAAe;YAAAE,QAAA,gBAC5B7D,OAAA;cAAK2D,SAAS,EAAC,eAAe;cAAAE,QAAA,gBAC5B7D,OAAA,CAACN,WAAW;gBAACoE,IAAI,EAAE;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACzBlE,OAAA;gBAAA6D,QAAA,EAAI;cAAe;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB,CAAC,eACNlE,OAAA;cAAK2D,SAAS,EAAC,iBAAiB;cAAAE,QAAA,GAC7B,CAAAxD,eAAA,GAAAH,MAAM,CAACoE,OAAO,cAAAjE,eAAA,gBAAAC,qBAAA,GAAdD,eAAA,CAAgBkE,MAAM,cAAAjE,qBAAA,eAAtBA,qBAAA,CAAwBkE,cAAc,IAAItE,MAAM,CAACoE,OAAO,CAACC,MAAM,CAACC,cAAc,CAACC,MAAM,GAAG,CAAC,gBACxFzE,OAAA;gBAAK2D,SAAS,EAAC,iBAAiB;gBAAAE,QAAA,EAC7B3D,MAAM,CAACoE,OAAO,CAACC,MAAM,CAACC,cAAc,CAACE,GAAG,CAAC,CAACC,GAAG,EAAEC,KAAK,kBACnD5E,OAAA;kBAAiB2D,SAAS,EAAC,iBAAiB;kBAAAE,QAAA,gBAC1C7D,OAAA;oBAAA6D,QAAA,EAAKc,GAAG,CAACE,QAAQ,IAAIF,GAAG,CAACG,QAAQ,IAAI;kBAAU;oBAAAf,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACrDlE,OAAA;oBAAG2D,SAAS,EAAC,cAAc;oBAAAE,QAAA,EAAEc,GAAG,CAACI,OAAO,IAAIJ,GAAG,CAACK,YAAY,IAAI;kBAAS;oBAAAjB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC9ElE,OAAA;oBAAG2D,SAAS,EAAC,UAAU;oBAAAE,QAAA,EAAEc,GAAG,CAACM,QAAQ,IAAIN,GAAG,CAACO,KAAK,IAAI;kBAAwB;oBAAAnB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,EAClFS,GAAG,CAACQ,WAAW,iBAAInF,OAAA;oBAAG2D,SAAS,EAAC,aAAa;oBAAAE,QAAA,EAAEc,GAAG,CAACQ;kBAAW;oBAAApB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,EACnES,GAAG,CAACS,gBAAgB,iBACnBpF,OAAA;oBAAK2D,SAAS,EAAC,kBAAkB;oBAAAE,QAAA,gBAC/B7D,OAAA;sBAAA6D,QAAA,EAAQ;oBAAiB;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eAClClE,OAAA;sBAAA6D,QAAA,EAAIc,GAAG,CAACS;oBAAgB;sBAAArB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1B,CACN;gBAAA,GAVOU,KAAK;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAWV,CACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,gBAENlE,OAAA;gBAAG2D,SAAS,EAAC,iBAAiB;gBAAAE,QAAA,EAAE3D,MAAM,CAAC2B;cAAU;gBAAAkC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CACtD,EACA,EAAA3D,gBAAA,GAAAL,MAAM,CAACoE,OAAO,cAAA/D,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBgE,MAAM,cAAA/D,qBAAA,uBAAtBA,qBAAA,CAAwB6E,0BAA0B,kBACjDrF,OAAA;gBAAG2D,SAAS,EAAC,kBAAkB;gBAAAE,QAAA,gBAC7B7D,OAAA;kBAAA6D,QAAA,EAAQ;gBAAiB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAChE,MAAM,CAACoE,OAAO,CAACC,MAAM,CAACc,0BAA0B,EAAC,QACvF;cAAA;gBAAAtB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CACJ;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EAGL,EAAAzD,gBAAA,GAAAP,MAAM,CAACoE,OAAO,cAAA7D,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgB8D,MAAM,cAAA7D,qBAAA,uBAAtBA,qBAAA,CAAwB4E,QAAQ,KAAIpF,MAAM,CAACoE,OAAO,CAACC,MAAM,CAACe,QAAQ,CAACb,MAAM,GAAG,CAAC,iBAC5EzE,OAAA;YAAK2D,SAAS,EAAC,eAAe;YAAAE,QAAA,gBAC5B7D,OAAA;cAAK2D,SAAS,EAAC,eAAe;cAAAE,QAAA,eAC5B7D,OAAA;gBAAA6D,QAAA,EAAI;cAAQ;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACd,CAAC,eACNlE,OAAA;cAAK2D,SAAS,EAAC,iBAAiB;cAAAE,QAAA,eAC9B7D,OAAA;gBAAK2D,SAAS,EAAC,eAAe;gBAAAE,QAAA,EAC3B3D,MAAM,CAACoE,OAAO,CAACC,MAAM,CAACe,QAAQ,CAACZ,GAAG,CAAC,CAACa,OAAO,EAAEX,KAAK,kBACjD5E,OAAA;kBAAiB2D,SAAS,EAAC,cAAc;kBAAAE,QAAA,gBACvC7D,OAAA;oBAAA6D,QAAA,EAAK0B,OAAO,CAACC,IAAI,IAAID,OAAO,CAACE,KAAK,IAAI,WAAWb,KAAK,GAAG,CAAC;kBAAE;oBAAAb,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,EACjEqB,OAAO,CAACJ,WAAW,iBAAInF,OAAA;oBAAA6D,QAAA,EAAI0B,OAAO,CAACJ;kBAAW;oBAAApB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,EACnDqB,OAAO,CAACG,YAAY,iBACnB1F,OAAA;oBAAA6D,QAAA,gBAAG7D,OAAA;sBAAA6D,QAAA,EAAQ;oBAAa;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAACqB,OAAO,CAACG,YAAY;kBAAA;oBAAA3B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAC5D;gBAAA,GALOU,KAAK;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAMV,CACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAGNlE,OAAA;UAAK2D,SAAS,EAAC,qBAAqB;UAAAE,QAAA,gBAElC7D,OAAA;YAAK2D,SAAS,EAAC,eAAe;YAAAE,QAAA,gBAC5B7D,OAAA;cAAK2D,SAAS,EAAC,eAAe;cAAAE,QAAA,gBAC5B7D,OAAA,CAACL,UAAU;gBAACmE,IAAI,EAAE;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACxBlE,OAAA;gBAAA6D,QAAA,EAAI;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf,CAAC,eACNlE,OAAA;cAAK2D,SAAS,EAAC,iBAAiB;cAAAE,QAAA,EAC7B,CAAAlD,gBAAA,GAAAT,MAAM,CAACoE,OAAO,cAAA3D,gBAAA,gBAAAC,qBAAA,GAAdD,gBAAA,CAAgB4D,MAAM,cAAA3D,qBAAA,eAAtBA,qBAAA,CAAwB+E,SAAS,IAAIzF,MAAM,CAACoE,OAAO,CAACC,MAAM,CAACoB,SAAS,CAAClB,MAAM,GAAG,CAAC,gBAC9EzE,OAAA;gBAAK2D,SAAS,EAAC,gBAAgB;gBAAAE,QAAA,EAC5B3D,MAAM,CAACoE,OAAO,CAACC,MAAM,CAACoB,SAAS,CAACjB,GAAG,CAAC,CAACkB,GAAG,EAAEhB,KAAK,kBAC9C5E,OAAA;kBAAiB2D,SAAS,EAAC,gBAAgB;kBAAAE,QAAA,gBACzC7D,OAAA;oBAAA6D,QAAA,EAAK+B,GAAG,CAACC,MAAM,IAAI;kBAAQ;oBAAA9B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACjClE,OAAA;oBAAG2D,SAAS,EAAC,aAAa;oBAAAE,QAAA,EAAE+B,GAAG,CAACE,WAAW,IAAIF,GAAG,CAACG,MAAM,IAAI;kBAAa;oBAAAhC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC/ElE,OAAA;oBAAG2D,SAAS,EAAC,iBAAiB;oBAAAE,QAAA,EAC3B+B,GAAG,CAACI,cAAc,IAAIJ,GAAG,CAACK,IAAI,IAAI;kBAAoB;oBAAAlC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtD,CAAC,EACH0B,GAAG,CAAC,aAAa,CAAC,IAAIA,GAAG,CAAC,aAAa,CAAC,GAAG,CAAC,iBAC3C5F,OAAA;oBAAG2D,SAAS,EAAC,OAAO;oBAAAE,QAAA,GAAC,SAAO,EAAC+B,GAAG,CAAC,aAAa,CAAC,EAAC,GAAC;kBAAA;oBAAA7B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CACrD;gBAAA,GAROU,KAAK;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OASV,CACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,gBAENlE,OAAA;gBAAG2D,SAAS,EAAC,gBAAgB;gBAAAE,QAAA,EAAE3D,MAAM,CAAC4B;cAAS;gBAAAiC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI;YACpD;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNlE,OAAA;YAAK2D,SAAS,EAAC,eAAe;YAAAE,QAAA,gBAC5B7D,OAAA;cAAK2D,SAAS,EAAC,eAAe;cAAAE,QAAA,gBAC5B7D,OAAA,CAACJ,OAAO;gBAACkE,IAAI,EAAE;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACrBlE,OAAA;gBAAA6D,QAAA,EAAI;cAAqB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3B,CAAC,eACNlE,OAAA;cAAK2D,SAAS,EAAC,iBAAiB;cAAAE,QAAA,eAC9B7D,OAAA;gBAAK2D,SAAS,EAAC,aAAa;gBAAAE,QAAA,EACzB3D,MAAM,CAAC6B,MAAM,CAAC2C,GAAG,CAAC,CAACwB,KAAK,EAAEtB,KAAK,kBAC9B5E,OAAA;kBAAkB2D,SAAS,EAAC,iBAAiB;kBAAAE,QAAA,EAAEqC;gBAAK,GAAzCtB,KAAK;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAA2C,CAC5D;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EAGL,EAAArD,gBAAA,GAAAX,MAAM,CAACoE,OAAO,cAAAzD,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgB0D,MAAM,cAAAzD,qBAAA,uBAAtBA,qBAAA,CAAwBqF,SAAS,KAAIjG,MAAM,CAACoE,OAAO,CAACC,MAAM,CAAC4B,SAAS,CAAC1B,MAAM,GAAG,CAAC,iBAC9EzE,OAAA;YAAK2D,SAAS,EAAC,eAAe;YAAAE,QAAA,gBAC5B7D,OAAA;cAAK2D,SAAS,EAAC,eAAe;cAAAE,QAAA,eAC5B7D,OAAA;gBAAA6D,QAAA,EAAI;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf,CAAC,eACNlE,OAAA;cAAK2D,SAAS,EAAC,iBAAiB;cAAAE,QAAA,eAC9B7D,OAAA;gBAAK2D,SAAS,EAAC,gBAAgB;gBAAAE,QAAA,EAC5B3D,MAAM,CAACoE,OAAO,CAACC,MAAM,CAAC4B,SAAS,CAACzB,GAAG,CAAC,CAAC0B,IAAI,EAAExB,KAAK,kBAC/C5E,OAAA;kBAAkB2D,SAAS,EAAC,cAAc;kBAAAE,QAAA,EAAEuC;gBAAI,GAArCxB,KAAK;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAuC,CACxD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,EAGA,EAAAnD,gBAAA,GAAAb,MAAM,CAACoE,OAAO,cAAAvD,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBwD,MAAM,cAAAvD,qBAAA,uBAAtBA,qBAAA,CAAwBqF,cAAc,KAAInG,MAAM,CAACoE,OAAO,CAACC,MAAM,CAAC8B,cAAc,CAAC5B,MAAM,GAAG,CAAC,iBACxFzE,OAAA;YAAK2D,SAAS,EAAC,eAAe;YAAAE,QAAA,gBAC5B7D,OAAA;cAAK2D,SAAS,EAAC,eAAe;cAAAE,QAAA,eAC5B7D,OAAA;gBAAA6D,QAAA,EAAI;cAAc;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpB,CAAC,eACNlE,OAAA;cAAK2D,SAAS,EAAC,iBAAiB;cAAAE,QAAA,eAC9B7D,OAAA;gBAAK2D,SAAS,EAAC,qBAAqB;gBAAAE,QAAA,EACjC3D,MAAM,CAACoE,OAAO,CAACC,MAAM,CAAC8B,cAAc,CAAC3B,GAAG,CAAC,CAAC4B,IAAI,EAAE1B,KAAK,kBACpD5E,OAAA;kBAAiB2D,SAAS,EAAC,oBAAoB;kBAAAE,QAAA,gBAC7C7D,OAAA;oBAAA6D,QAAA,EAAKyC,IAAI,CAACd,IAAI,IAAIc,IAAI,CAACb,KAAK,IAAI,iBAAiBb,KAAK,GAAG,CAAC;kBAAE;oBAAAb,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,EACjEoC,IAAI,CAACC,mBAAmB,iBAAIvG,OAAA;oBAAA6D,QAAA,GAAG,aAAW,EAACyC,IAAI,CAACC,mBAAmB;kBAAA;oBAAAxC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,EACxEoC,IAAI,CAACE,SAAS,iBAAIxG,OAAA;oBAAA6D,QAAA,GAAG,QAAM,EAACyC,IAAI,CAACE,SAAS;kBAAA;oBAAAzC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA,GAHxCU,KAAK;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAIV,CACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,EAGA,EAAAjD,gBAAA,GAAAf,MAAM,CAACoE,OAAO,cAAArD,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBsD,MAAM,cAAArD,qBAAA,uBAAtBA,qBAAA,CAAwBuF,YAAY,KAAIvG,MAAM,CAACoE,OAAO,CAACC,MAAM,CAACkC,YAAY,CAAChC,MAAM,GAAG,CAAC,iBACpFzE,OAAA;YAAK2D,SAAS,EAAC,eAAe;YAAAE,QAAA,gBAC5B7D,OAAA;cAAK2D,SAAS,EAAC,eAAe;cAAAE,QAAA,eAC5B7D,OAAA;gBAAA6D,QAAA,EAAI;cAAY;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClB,CAAC,eACNlE,OAAA;cAAK2D,SAAS,EAAC,iBAAiB;cAAAE,QAAA,eAC9B7D,OAAA;gBAAK2D,SAAS,EAAC,mBAAmB;gBAAAE,QAAA,EAC/B3D,MAAM,CAACoE,OAAO,CAACC,MAAM,CAACkC,YAAY,CAAC/B,GAAG,CAAC,CAACgC,WAAW,EAAE9B,KAAK,kBACzD5E,OAAA;kBAAiB2D,SAAS,EAAC,kBAAkB;kBAAAE,QAAA,EAC1C,OAAO6C,WAAW,KAAK,QAAQ,gBAC9B1G,OAAA;oBAAA6D,QAAA,EAAI6C;kBAAW;oBAAA3C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,gBAEpBlE,OAAA;oBAAA6D,QAAA,gBACE7D,OAAA;sBAAA6D,QAAA,EAAK6C,WAAW,CAACC,eAAe,IAAID,WAAW,CAAClB,IAAI,IAAI,eAAeZ,KAAK,GAAG,CAAC;oBAAE;sBAAAb,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,EACvFwC,WAAW,CAACF,SAAS,iBAAIxG,OAAA;sBAAA6D,QAAA,GAAG,QAAM,EAAC6C,WAAW,CAACF,SAAS;oBAAA;sBAAAzC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,EAC7DwC,WAAW,CAACH,mBAAmB,iBAAIvG,OAAA;sBAAA6D,QAAA,GAAG,gBAAc,EAAC6C,WAAW,CAACH,mBAAmB;oBAAA;sBAAAxC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,EACzFwC,WAAW,CAACvB,WAAW,iBAAInF,OAAA;sBAAA6D,QAAA,EAAI6C,WAAW,CAACvB;oBAAW;sBAAApB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzD;gBACN,GAVOU,KAAK;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAWV,CACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,EAGAhE,MAAM,CAACoE,OAAO,iBACbtE,OAAA;YAAK2D,SAAS,EAAC,eAAe;YAAAE,QAAA,gBAC5B7D,OAAA;cAAK2D,SAAS,EAAC,eAAe;cAAAE,QAAA,eAC5B7D,OAAA;gBAAA6D,QAAA,EAAI;cAAsB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5B,CAAC,eACNlE,OAAA;cAAK2D,SAAS,EAAC,iBAAiB;cAAAE,QAAA,eAC9B7D,OAAA;gBAAK2D,SAAS,EAAC,eAAe;gBAAAE,QAAA,gBAC5B7D,OAAA;kBAAA6D,QAAA,gBAAG7D,OAAA;oBAAA6D,QAAA,EAAQ;kBAAY;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,KAAC,EAAChE,MAAM,CAACoE,OAAO,CAACsC,GAAG,IAAI,KAAK;gBAAA;kBAAA7C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAClElE,OAAA;kBAAA6D,QAAA,gBAAG7D,OAAA;oBAAA6D,QAAA,EAAQ;kBAAY;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,KAAC,EAAChE,MAAM,CAACoE,OAAO,CAACC,MAAM,GAAG,SAAS,GAAG,WAAW;gBAAA;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,EACrF,EAAA/C,sBAAA,GAAAjB,MAAM,CAACoE,OAAO,CAACC,MAAM,cAAApD,sBAAA,wBAAAC,sBAAA,GAArBD,sBAAA,CAAuB0F,mBAAmB,cAAAzF,sBAAA,uBAA1CA,sBAAA,CAA4C0F,QAAQ,kBACnD9G,OAAA;kBAAA6D,QAAA,gBAAG7D,OAAA;oBAAA6D,QAAA,EAAQ;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,KAAC,EAAChE,MAAM,CAACoE,OAAO,CAACC,MAAM,CAACsC,mBAAmB,CAACC,QAAQ;gBAAA;kBAAA/C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CACtF;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENlE,OAAA;QAAK2D,SAAS,EAAC,cAAc;QAAAE,QAAA,gBAC3B7D,OAAA;UAAQ2D,SAAS,EAAC,mBAAmB;UAACC,OAAO,EAAExD,OAAQ;UAAAyD,QAAA,EAAC;QAExD;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTlE,OAAA;UAAQ2D,SAAS,EAAC,iBAAiB;UAACC,OAAO,EAAEvC,cAAe;UAAAwC,QAAA,gBAC1D7D,OAAA,CAACT,UAAU;YAACuE,IAAI,EAAE;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,mBAE1B;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC6C,EAAA,GAnUI9G,WAAW;AAqUjB,eAAeA,WAAW;AAAC,IAAA8G,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}