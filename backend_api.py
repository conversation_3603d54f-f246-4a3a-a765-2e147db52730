from flask import Flask, request, jsonify
from flask_cors import CORS
import os
import json
from datetime import datetime
import uuid

app = Flask(__name__)
CORS(app)  # Enable CORS for React frontend

# Mock data storage (in production, use a proper database)
resumes_data = []
activity_log = []

# Configuration
UPLOAD_FOLDER = './uploads'
os.makedirs(UPLOAD_FOLDER, exist_ok=True)

def log_activity(action, detail, user="Admin"):
    """Log an activity to the activity log"""
    activity = {
        'id': str(uuid.uuid4()),
        'action': action,
        'detail': detail,
        'user': user,
        'timestamp': datetime.now().isoformat(),
        'time_ago': 'Just now'
    }
    activity_log.insert(0, activity)  # Add to beginning
    # Keep only last 100 activities
    if len(activity_log) > 100:
        activity_log.pop()

@app.route('/api/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({'status': 'healthy', 'message': 'Resume AI Agent API is running'})

@app.route('/api/upload', methods=['POST'])
def upload_resume():
    """Upload and process resume files"""
    try:
        if 'files' not in request.files:
            return jsonify({'error': 'No files provided'}), 400
        
        files = request.files.getlist('files')
        processed_files = []
        
        for file in files:
            if file.filename == '':
                continue
                
            # Save file
            filename = f"{uuid.uuid4()}_{file.filename}"
            filepath = os.path.join(UPLOAD_FOLDER, filename)
            file.save(filepath)
            
            # Mock resume data extraction
            resume_data = {
                'id': str(uuid.uuid4()),
                'filename': file.filename,
                'filepath': filepath,
                'name': f"John Doe {len(resumes_data) + 1}",
                'title': 'Software Engineer',
                'email': f'john.doe{len(resumes_data) + 1}@email.com',
                'phone': '+****************',
                'location': 'San Francisco, CA',
                'experience': '5+ years',
                'skills': ['Python', 'React', 'Node.js', 'AWS'],
                'education': 'MS Computer Science',
                'summary': 'Experienced software engineer with expertise in full-stack development...',
                'uploaded_at': datetime.now().isoformat()
            }
            
            resumes_data.append(resume_data)
            processed_files.append(resume_data)
            
            # Log activity
            log_activity('Resume uploaded', file.filename)
        
        return jsonify({
            'message': f'Successfully processed {len(processed_files)} files',
            'files': processed_files
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/search', methods=['POST'])
def search_resumes():
    """Search resumes using natural language query"""
    try:
        data = request.get_json()
        query = data.get('query', '').lower()
        
        if not query:
            return jsonify({'error': 'Query is required'}), 400
        
        # Mock search logic - filter resumes based on query
        filtered_resumes = []
        for resume in resumes_data:
            # Simple keyword matching
            if (query in resume['name'].lower() or 
                query in resume['title'].lower() or 
                any(query in skill.lower() for skill in resume['skills']) or
                query in resume['location'].lower()):
                
                # Add match score
                resume_copy = resume.copy()
                resume_copy['score'] = 95  # Mock score
                filtered_resumes.append(resume_copy)
        
        # Log activity
        log_activity('Search completed', f'"{query}" ({len(filtered_resumes)} results)')
        
        return jsonify({
            'query': query,
            'results': filtered_resumes,
            'count': len(filtered_resumes)
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/resumes', methods=['GET'])
def get_all_resumes():
    """Get all resumes"""
    try:
        # Log activity
        log_activity('Viewed all resumes', f'{len(resumes_data)} resumes')
        
        return jsonify({
            'resumes': resumes_data,
            'count': len(resumes_data)
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/resumes/<resume_id>', methods=['GET'])
def get_resume(resume_id):
    """Get a specific resume by ID"""
    try:
        resume = next((r for r in resumes_data if r['id'] == resume_id), None)
        
        if not resume:
            return jsonify({'error': 'Resume not found'}), 404
        
        return jsonify(resume)
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/resumes/<resume_id>', methods=['DELETE'])
def delete_resume(resume_id):
    """Delete a resume"""
    try:
        global resumes_data
        resume = next((r for r in resumes_data if r['id'] == resume_id), None)
        
        if not resume:
            return jsonify({'error': 'Resume not found'}), 404
        
        # Remove from data
        resumes_data = [r for r in resumes_data if r['id'] != resume_id]
        
        # Delete file if exists
        if os.path.exists(resume['filepath']):
            os.remove(resume['filepath'])
        
        # Log activity
        log_activity('Resume deleted', resume['filename'])
        
        return jsonify({'message': 'Resume deleted successfully'})
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/activity', methods=['GET'])
def get_activity_log():
    """Get activity log"""
    try:
        return jsonify({
            'activities': activity_log,
            'count': len(activity_log)
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/stats', methods=['GET'])
def get_stats():
    """Get dashboard statistics"""
    try:
        stats = {
            'total_resumes': len(resumes_data),
            'recent_uploads': len([r for r in resumes_data if 
                                 (datetime.now() - datetime.fromisoformat(r['uploaded_at'])).days < 7]),
            'total_searches': len([a for a in activity_log if 'Search completed' in a['action']]),
            'total_activities': len(activity_log)
        }
        
        return jsonify(stats)
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/export', methods=['POST'])
def export_resumes():
    """Export resumes data"""
    try:
        data = request.get_json()
        format_type = data.get('format', 'json')
        resume_ids = data.get('resume_ids', [])
        
        # Filter resumes if specific IDs provided
        if resume_ids:
            export_data = [r for r in resumes_data if r['id'] in resume_ids]
        else:
            export_data = resumes_data
        
        # Log activity
        log_activity('Data exported', f'{len(export_data)} resumes in {format_type} format')
        
        return jsonify({
            'data': export_data,
            'format': format_type,
            'count': len(export_data),
            'exported_at': datetime.now().isoformat()
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

if __name__ == '__main__':
    # Add some sample data for demonstration
    sample_resumes = [
        {
            'id': str(uuid.uuid4()),
            'filename': 'john_doe_resume.pdf',
            'filepath': './sample/john_doe_resume.pdf',
            'name': 'John Doe',
            'title': 'Senior Software Engineer',
            'email': '<EMAIL>',
            'phone': '+****************',
            'location': 'San Francisco, CA',
            'experience': '5+ years',
            'skills': ['Python', 'React', 'Node.js', 'AWS'],
            'education': 'MS Computer Science',
            'summary': 'Experienced software engineer with expertise in full-stack development...',
            'uploaded_at': datetime.now().isoformat()
        },
        {
            'id': str(uuid.uuid4()),
            'filename': 'jane_smith_cv.pdf',
            'filepath': './sample/jane_smith_cv.pdf',
            'name': 'Jane Smith',
            'title': 'Frontend Developer',
            'email': '<EMAIL>',
            'phone': '+****************',
            'location': 'New York, NY',
            'experience': '3+ years',
            'skills': ['React', 'TypeScript', 'CSS', 'JavaScript'],
            'education': 'BS Computer Science',
            'summary': 'Creative frontend developer passionate about user experience...',
            'uploaded_at': datetime.now().isoformat()
        }
    ]
    
    resumes_data.extend(sample_resumes)
    
    # Add sample activities
    log_activity('System started', 'Resume AI Agent backend initialized')
    log_activity('Sample data loaded', f'{len(sample_resumes)} sample resumes')
    
    print("🚀 Resume AI Agent Backend API starting...")
    print("📊 Dashboard: http://localhost:3000")
    print("🔗 API Base URL: http://localhost:8001/api")
    print("📋 Health Check: http://localhost:8001/api/health")
    
    app.run(debug=True, host='0.0.0.0', port=8001)
