{"ast": null, "code": "var _jsxFileName = \"D:\\\\Projects\\\\SQL-Agent-Task-Submission-main\\\\SQL-Agent-Task-Submission-main\\\\src\\\\App.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { BrowserRouter as Router, Routes, Route } from 'react-router-dom';\nimport { ToastContainer } from 'react-toastify';\nimport 'react-toastify/dist/ReactToastify.css';\nimport Sidebar from './components/Sidebar';\nimport Header from './components/Header';\nimport Dashboard from './pages/Dashboard';\nimport UploadResume from './pages/UploadResume';\nimport SearchResumes from './pages/SearchResumes';\nimport AllResumes from './pages/AllResumes';\nimport ActivityLog from './pages/ActivityLog';\nimport Profile from './pages/Profile';\nimport Support from './pages/Support';\nimport './App.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  _s();\n  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);\n  const [currentUser] = useState({\n    name: '<PERSON><PERSON><PERSON>',\n    email: '<EMAIL>',\n    avatar: null\n  });\n  return /*#__PURE__*/_jsxDEV(Router, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"app\",\n      children: [/*#__PURE__*/_jsxDEV(Sidebar, {\n        collapsed: sidebarCollapsed,\n        onToggle: () => setSidebarCollapsed(!sidebarCollapsed)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 29,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `main-content ${sidebarCollapsed ? 'sidebar-collapsed' : ''}`,\n        children: [/*#__PURE__*/_jsxDEV(Header, {\n          user: currentUser\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 34,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"content\",\n          children: /*#__PURE__*/_jsxDEV(Routes, {\n            children: [/*#__PURE__*/_jsxDEV(Route, {\n              path: \"/\",\n              element: /*#__PURE__*/_jsxDEV(Dashboard, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 37,\n                columnNumber: 40\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 37,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/upload\",\n              element: /*#__PURE__*/_jsxDEV(UploadResume, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 38,\n                columnNumber: 46\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 38,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/search\",\n              element: /*#__PURE__*/_jsxDEV(SearchResumes, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 39,\n                columnNumber: 46\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 39,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/resumes\",\n              element: /*#__PURE__*/_jsxDEV(AllResumes, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 40,\n                columnNumber: 47\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 40,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/activity\",\n              element: /*#__PURE__*/_jsxDEV(ActivityLog, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 41,\n                columnNumber: 48\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 41,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/profile\",\n              element: /*#__PURE__*/_jsxDEV(Profile, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 42,\n                columnNumber: 47\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 42,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/support\",\n              element: /*#__PURE__*/_jsxDEV(Support, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 43,\n                columnNumber: 47\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 43,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 36,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 35,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 33,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ToastContainer, {\n        position: \"top-right\",\n        autoClose: 3000,\n        hideProgressBar: false,\n        newestOnTop: false,\n        closeOnClick: true,\n        rtl: false,\n        pauseOnFocusLoss: true,\n        draggable: true,\n        pauseOnHover: true,\n        theme: \"light\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 47,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 28,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 27,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"6bsnKZHPyRen4fKXYk6Il4mLVMM=\");\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "useState", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "ToastContainer", "Sidebar", "Header", "Dashboard", "UploadResume", "SearchResumes", "AllResumes", "ActivityLog", "Profile", "Support", "jsxDEV", "_jsxDEV", "App", "_s", "sidebarCollapsed", "setSidebarCollapsed", "currentUser", "name", "email", "avatar", "children", "className", "collapsed", "onToggle", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "user", "path", "element", "position", "autoClose", "hideProgressBar", "newestOnTop", "closeOnClick", "rtl", "pauseOnFocusLoss", "draggable", "pauseOnHover", "theme", "_c", "$RefreshReg$"], "sources": ["D:/Projects/SQL-Agent-Task-Submission-main/SQL-Agent-Task-Submission-main/src/App.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { BrowserRouter as Router, Routes, Route } from 'react-router-dom';\nimport { ToastContainer } from 'react-toastify';\nimport 'react-toastify/dist/ReactToastify.css';\n\nimport Sidebar from './components/Sidebar';\nimport Header from './components/Header';\nimport Dashboard from './pages/Dashboard';\nimport UploadResume from './pages/UploadResume';\nimport SearchResumes from './pages/SearchResumes';\nimport AllResumes from './pages/AllResumes';\nimport ActivityLog from './pages/ActivityLog';\nimport Profile from './pages/Profile';\nimport Support from './pages/Support';\n\nimport './App.css';\n\nfunction App() {\n  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);\n  const [currentUser] = useState({\n    name: '<PERSON><PERSON><PERSON><PERSON>',\n    email: '<EMAIL>',\n    avatar: null\n  });\n\n  return (\n    <Router>\n      <div className=\"app\">\n        <Sidebar \n          collapsed={sidebarCollapsed} \n          onToggle={() => setSidebarCollapsed(!sidebarCollapsed)}\n        />\n        <div className={`main-content ${sidebarCollapsed ? 'sidebar-collapsed' : ''}`}>\n          <Header user={currentUser} />\n          <div className=\"content\">\n            <Routes>\n              <Route path=\"/\" element={<Dashboard />} />\n              <Route path=\"/upload\" element={<UploadResume />} />\n              <Route path=\"/search\" element={<SearchResumes />} />\n              <Route path=\"/resumes\" element={<AllResumes />} />\n              <Route path=\"/activity\" element={<ActivityLog />} />\n              <Route path=\"/profile\" element={<Profile />} />\n              <Route path=\"/support\" element={<Support />} />\n            </Routes>\n          </div>\n        </div>\n        <ToastContainer\n          position=\"top-right\"\n          autoClose={3000}\n          hideProgressBar={false}\n          newestOnTop={false}\n          closeOnClick\n          rtl={false}\n          pauseOnFocusLoss\n          draggable\n          pauseOnHover\n          theme=\"light\"\n        />\n      </div>\n    </Router>\n  );\n}\n\nexport default App;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,QAAQ,kBAAkB;AACzE,SAASC,cAAc,QAAQ,gBAAgB;AAC/C,OAAO,uCAAuC;AAE9C,OAAOC,OAAO,MAAM,sBAAsB;AAC1C,OAAOC,MAAM,MAAM,qBAAqB;AACxC,OAAOC,SAAS,MAAM,mBAAmB;AACzC,OAAOC,YAAY,MAAM,sBAAsB;AAC/C,OAAOC,aAAa,MAAM,uBAAuB;AACjD,OAAOC,UAAU,MAAM,oBAAoB;AAC3C,OAAOC,WAAW,MAAM,qBAAqB;AAC7C,OAAOC,OAAO,MAAM,iBAAiB;AACrC,OAAOC,OAAO,MAAM,iBAAiB;AAErC,OAAO,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnB,SAASC,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACb,MAAM,CAACC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGpB,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACqB,WAAW,CAAC,GAAGrB,QAAQ,CAAC;IAC7BsB,IAAI,EAAE,iBAAiB;IACvBC,KAAK,EAAE,qBAAqB;IAC5BC,MAAM,EAAE;EACV,CAAC,CAAC;EAEF,oBACER,OAAA,CAACd,MAAM;IAAAuB,QAAA,eACLT,OAAA;MAAKU,SAAS,EAAC,KAAK;MAAAD,QAAA,gBAClBT,OAAA,CAACV,OAAO;QACNqB,SAAS,EAAER,gBAAiB;QAC5BS,QAAQ,EAAEA,CAAA,KAAMR,mBAAmB,CAAC,CAACD,gBAAgB;MAAE;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxD,CAAC,eACFhB,OAAA;QAAKU,SAAS,EAAE,gBAAgBP,gBAAgB,GAAG,mBAAmB,GAAG,EAAE,EAAG;QAAAM,QAAA,gBAC5ET,OAAA,CAACT,MAAM;UAAC0B,IAAI,EAAEZ;QAAY;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC7BhB,OAAA;UAAKU,SAAS,EAAC,SAAS;UAAAD,QAAA,eACtBT,OAAA,CAACb,MAAM;YAAAsB,QAAA,gBACLT,OAAA,CAACZ,KAAK;cAAC8B,IAAI,EAAC,GAAG;cAACC,OAAO,eAAEnB,OAAA,CAACR,SAAS;gBAAAqB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC1ChB,OAAA,CAACZ,KAAK;cAAC8B,IAAI,EAAC,SAAS;cAACC,OAAO,eAAEnB,OAAA,CAACP,YAAY;gBAAAoB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACnDhB,OAAA,CAACZ,KAAK;cAAC8B,IAAI,EAAC,SAAS;cAACC,OAAO,eAAEnB,OAAA,CAACN,aAAa;gBAAAmB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACpDhB,OAAA,CAACZ,KAAK;cAAC8B,IAAI,EAAC,UAAU;cAACC,OAAO,eAAEnB,OAAA,CAACL,UAAU;gBAAAkB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAClDhB,OAAA,CAACZ,KAAK;cAAC8B,IAAI,EAAC,WAAW;cAACC,OAAO,eAAEnB,OAAA,CAACJ,WAAW;gBAAAiB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACpDhB,OAAA,CAACZ,KAAK;cAAC8B,IAAI,EAAC,UAAU;cAACC,OAAO,eAAEnB,OAAA,CAACH,OAAO;gBAAAgB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC/ChB,OAAA,CAACZ,KAAK;cAAC8B,IAAI,EAAC,UAAU;cAACC,OAAO,eAAEnB,OAAA,CAACF,OAAO;gBAAAe,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNhB,OAAA,CAACX,cAAc;QACb+B,QAAQ,EAAC,WAAW;QACpBC,SAAS,EAAE,IAAK;QAChBC,eAAe,EAAE,KAAM;QACvBC,WAAW,EAAE,KAAM;QACnBC,YAAY;QACZC,GAAG,EAAE,KAAM;QACXC,gBAAgB;QAChBC,SAAS;QACTC,YAAY;QACZC,KAAK,EAAC;MAAO;QAAAhB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACd,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEb;AAACd,EAAA,CA5CQD,GAAG;AAAA6B,EAAA,GAAH7B,GAAG;AA8CZ,eAAeA,GAAG;AAAC,IAAA6B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}