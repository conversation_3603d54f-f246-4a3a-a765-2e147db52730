[{"D:\\Projects\\SQL-Agent-Task-Submission-main\\SQL-Agent-Task-Submission-main\\src\\index.js": "1", "D:\\Projects\\SQL-Agent-Task-Submission-main\\SQL-Agent-Task-Submission-main\\src\\App.js": "2", "D:\\Projects\\SQL-Agent-Task-Submission-main\\SQL-Agent-Task-Submission-main\\src\\pages\\SearchResumes.js": "3", "D:\\Projects\\SQL-Agent-Task-Submission-main\\SQL-Agent-Task-Submission-main\\src\\pages\\ActivityLog.js": "4", "D:\\Projects\\SQL-Agent-Task-Submission-main\\SQL-Agent-Task-Submission-main\\src\\components\\Sidebar.js": "5", "D:\\Projects\\SQL-Agent-Task-Submission-main\\SQL-Agent-Task-Submission-main\\src\\pages\\AllResumes.js": "6", "D:\\Projects\\SQL-Agent-Task-Submission-main\\SQL-Agent-Task-Submission-main\\src\\pages\\Support.js": "7", "D:\\Projects\\SQL-Agent-Task-Submission-main\\SQL-Agent-Task-Submission-main\\src\\pages\\Profile.js": "8", "D:\\Projects\\SQL-Agent-Task-Submission-main\\SQL-Agent-Task-Submission-main\\src\\pages\\Dashboard.js": "9", "D:\\Projects\\SQL-Agent-Task-Submission-main\\SQL-Agent-Task-Submission-main\\src\\components\\Header.js": "10", "D:\\Projects\\SQL-Agent-Task-Submission-main\\SQL-Agent-Task-Submission-main\\src\\pages\\UploadResume.js": "11", "D:\\Projects\\SQL-Agent-Task-Submission-main\\SQL-Agent-Task-Submission-main\\src\\components\\ResumeModal.js": "12"}, {"size": 254, "mtime": 1748607364261, "results": "13", "hashOfConfig": "14"}, {"size": 2064, "mtime": 1748607402512, "results": "15", "hashOfConfig": "14"}, {"size": 18655, "mtime": 1748614270982, "results": "16", "hashOfConfig": "14"}, {"size": 3157, "mtime": 1748607869034, "results": "17", "hashOfConfig": "14"}, {"size": 3615, "mtime": 1748607451550, "results": "18", "hashOfConfig": "14"}, {"size": 3086, "mtime": 1748607848456, "results": "19", "hashOfConfig": "14"}, {"size": 5424, "mtime": 1748607928251, "results": "20", "hashOfConfig": "14"}, {"size": 5340, "mtime": 1748607895419, "results": "21", "hashOfConfig": "14"}, {"size": 8001, "mtime": 1748607719070, "results": "22", "hashOfConfig": "14"}, {"size": 6079, "mtime": 1748607558832, "results": "23", "hashOfConfig": "14"}, {"size": 7946, "mtime": 1748607644613, "results": "24", "hashOfConfig": "14"}, {"size": 11878, "mtime": 1748614363851, "results": "25", "hashOfConfig": "14"}, {"filePath": "26", "messages": "27", "suppressedMessages": "28", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "fmxtqe", {"filePath": "29", "messages": "30", "suppressedMessages": "31", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "32", "messages": "33", "suppressedMessages": "34", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "35", "messages": "36", "suppressedMessages": "37", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\Projects\\SQL-Agent-Task-Submission-main\\SQL-Agent-Task-Submission-main\\src\\index.js", [], [], "D:\\Projects\\SQL-Agent-Task-Submission-main\\SQL-Agent-Task-Submission-main\\src\\App.js", [], [], "D:\\Projects\\SQL-Agent-Task-Submission-main\\SQL-Agent-Task-Submission-main\\src\\pages\\SearchResumes.js", ["62", "63", "64", "65"], [], "D:\\Projects\\SQL-Agent-Task-Submission-main\\SQL-Agent-Task-Submission-main\\src\\pages\\ActivityLog.js", [], [], "D:\\Projects\\SQL-Agent-Task-Submission-main\\SQL-Agent-Task-Submission-main\\src\\components\\Sidebar.js", [], [], "D:\\Projects\\SQL-Agent-Task-Submission-main\\SQL-Agent-Task-Submission-main\\src\\pages\\AllResumes.js", [], [], "D:\\Projects\\SQL-Agent-Task-Submission-main\\SQL-Agent-Task-Submission-main\\src\\pages\\Support.js", [], [], "D:\\Projects\\SQL-Agent-Task-Submission-main\\SQL-Agent-Task-Submission-main\\src\\pages\\Profile.js", ["66", "67", "68"], [], "D:\\Projects\\SQL-Agent-Task-Submission-main\\SQL-Agent-Task-Submission-main\\src\\pages\\Dashboard.js", [], [], "D:\\Projects\\SQL-Agent-Task-Submission-main\\SQL-Agent-Task-Submission-main\\src\\components\\Header.js", [], [], "D:\\Projects\\SQL-Agent-Task-Submission-main\\SQL-Agent-Task-Submission-main\\src\\pages\\UploadResume.js", ["69", "70"], [], "D:\\Projects\\SQL-Agent-Task-Submission-main\\SQL-Agent-Task-Submission-main\\src\\components\\ResumeModal.js", [], [], {"ruleId": "71", "severity": 1, "message": "72", "line": 2, "column": 100, "nodeType": "73", "messageId": "74", "endLine": 2, "endColumn": 106}, {"ruleId": "71", "severity": 1, "message": "75", "line": 12, "column": 10, "nodeType": "73", "messageId": "74", "endLine": 12, "endColumn": 21}, {"ruleId": "71", "severity": 1, "message": "76", "line": 12, "column": 23, "nodeType": "73", "messageId": "74", "endLine": 12, "endColumn": 37}, {"ruleId": "71", "severity": 1, "message": "77", "line": 17, "column": 9, "nodeType": "73", "messageId": "74", "endLine": 17, "endColumn": 20}, {"ruleId": "71", "severity": 1, "message": "78", "line": 2, "column": 18, "nodeType": "73", "messageId": "74", "endLine": 2, "endColumn": 24}, {"ruleId": "71", "severity": 1, "message": "79", "line": 2, "column": 26, "nodeType": "73", "messageId": "74", "endLine": 2, "endColumn": 33}, {"ruleId": "71", "severity": 1, "message": "80", "line": 2, "column": 35, "nodeType": "73", "messageId": "74", "endLine": 2, "endColumn": 43}, {"ruleId": "71", "severity": 1, "message": "81", "line": 10, "column": 10, "nodeType": "73", "messageId": "74", "endLine": 10, "endColumn": 24}, {"ruleId": "71", "severity": 1, "message": "82", "line": 10, "column": 26, "nodeType": "73", "messageId": "74", "endLine": 10, "endColumn": 43}, "no-unused-vars", "'FiStar' is defined but never used.", "Identifier", "unusedVar", "'showFilters' is assigned a value but never used.", "'setShowFilters' is assigned a value but never used.", "'mockResults' is assigned a value but never used.", "'FiMail' is defined but never used.", "'FiPhone' is defined but never used.", "'FiMapPin' is defined but never used.", "'uploadProgress' is assigned a value but never used.", "'setUploadProgress' is assigned a value but never used."]