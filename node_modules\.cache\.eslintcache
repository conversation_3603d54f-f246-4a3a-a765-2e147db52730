[{"D:\\Projects\\SQL-Agent-Task-Submission-main\\SQL-Agent-Task-Submission-main\\src\\index.js": "1", "D:\\Projects\\SQL-Agent-Task-Submission-main\\SQL-Agent-Task-Submission-main\\src\\App.js": "2", "D:\\Projects\\SQL-Agent-Task-Submission-main\\SQL-Agent-Task-Submission-main\\src\\pages\\SearchResumes.js": "3", "D:\\Projects\\SQL-Agent-Task-Submission-main\\SQL-Agent-Task-Submission-main\\src\\pages\\ActivityLog.js": "4", "D:\\Projects\\SQL-Agent-Task-Submission-main\\SQL-Agent-Task-Submission-main\\src\\components\\Sidebar.js": "5", "D:\\Projects\\SQL-Agent-Task-Submission-main\\SQL-Agent-Task-Submission-main\\src\\pages\\AllResumes.js": "6", "D:\\Projects\\SQL-Agent-Task-Submission-main\\SQL-Agent-Task-Submission-main\\src\\pages\\Support.js": "7", "D:\\Projects\\SQL-Agent-Task-Submission-main\\SQL-Agent-Task-Submission-main\\src\\pages\\Profile.js": "8", "D:\\Projects\\SQL-Agent-Task-Submission-main\\SQL-Agent-Task-Submission-main\\src\\pages\\Dashboard.js": "9", "D:\\Projects\\SQL-Agent-Task-Submission-main\\SQL-Agent-Task-Submission-main\\src\\components\\Header.js": "10", "D:\\Projects\\SQL-Agent-Task-Submission-main\\SQL-Agent-Task-Submission-main\\src\\pages\\UploadResume.js": "11"}, {"size": 254, "mtime": 1748607364261, "results": "12", "hashOfConfig": "13"}, {"size": 2064, "mtime": 1748607402512, "results": "14", "hashOfConfig": "13"}, {"size": 10148, "mtime": 1748607824149, "results": "15", "hashOfConfig": "13"}, {"size": 3157, "mtime": 1748607869034, "results": "16", "hashOfConfig": "13"}, {"size": 3615, "mtime": 1748607451550, "results": "17", "hashOfConfig": "13"}, {"size": 3086, "mtime": 1748607848456, "results": "18", "hashOfConfig": "13"}, {"size": 5424, "mtime": 1748607928251, "results": "19", "hashOfConfig": "13"}, {"size": 5340, "mtime": 1748607895419, "results": "20", "hashOfConfig": "13"}, {"size": 8001, "mtime": 1748607719070, "results": "21", "hashOfConfig": "13"}, {"size": 6079, "mtime": 1748607558832, "results": "22", "hashOfConfig": "13"}, {"size": 7946, "mtime": 1748607644613, "results": "23", "hashOfConfig": "13"}, {"filePath": "24", "messages": "25", "suppressedMessages": "26", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "fmxtqe", {"filePath": "27", "messages": "28", "suppressedMessages": "29", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "30", "messages": "31", "suppressedMessages": "32", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "33", "messages": "34", "suppressedMessages": "35", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "D:\\Projects\\SQL-Agent-Task-Submission-main\\SQL-Agent-Task-Submission-main\\src\\index.js", [], [], "D:\\Projects\\SQL-Agent-Task-Submission-main\\SQL-Agent-Task-Submission-main\\src\\App.js", [], [], "D:\\Projects\\SQL-Agent-Task-Submission-main\\SQL-Agent-Task-Submission-main\\src\\pages\\SearchResumes.js", ["57", "58"], [], "D:\\Projects\\SQL-Agent-Task-Submission-main\\SQL-Agent-Task-Submission-main\\src\\pages\\ActivityLog.js", [], [], "D:\\Projects\\SQL-Agent-Task-Submission-main\\SQL-Agent-Task-Submission-main\\src\\components\\Sidebar.js", [], [], "D:\\Projects\\SQL-Agent-Task-Submission-main\\SQL-Agent-Task-Submission-main\\src\\pages\\AllResumes.js", [], [], "D:\\Projects\\SQL-Agent-Task-Submission-main\\SQL-Agent-Task-Submission-main\\src\\pages\\Support.js", [], [], "D:\\Projects\\SQL-Agent-Task-Submission-main\\SQL-Agent-Task-Submission-main\\src\\pages\\Profile.js", ["59", "60", "61"], [], "D:\\Projects\\SQL-Agent-Task-Submission-main\\SQL-Agent-Task-Submission-main\\src\\pages\\Dashboard.js", [], [], "D:\\Projects\\SQL-Agent-Task-Submission-main\\SQL-Agent-Task-Submission-main\\src\\components\\Header.js", [], [], "D:\\Projects\\SQL-Agent-Task-Submission-main\\SQL-Agent-Task-Submission-main\\src\\pages\\UploadResume.js", ["62", "63"], [], {"ruleId": "64", "severity": 1, "message": "65", "line": 11, "column": 10, "nodeType": "66", "messageId": "67", "endLine": 11, "endColumn": 21}, {"ruleId": "64", "severity": 1, "message": "68", "line": 11, "column": 23, "nodeType": "66", "messageId": "67", "endLine": 11, "endColumn": 37}, {"ruleId": "64", "severity": 1, "message": "69", "line": 2, "column": 18, "nodeType": "66", "messageId": "67", "endLine": 2, "endColumn": 24}, {"ruleId": "64", "severity": 1, "message": "70", "line": 2, "column": 26, "nodeType": "66", "messageId": "67", "endLine": 2, "endColumn": 33}, {"ruleId": "64", "severity": 1, "message": "71", "line": 2, "column": 35, "nodeType": "66", "messageId": "67", "endLine": 2, "endColumn": 43}, {"ruleId": "64", "severity": 1, "message": "72", "line": 10, "column": 10, "nodeType": "66", "messageId": "67", "endLine": 10, "endColumn": 24}, {"ruleId": "64", "severity": 1, "message": "73", "line": 10, "column": 26, "nodeType": "66", "messageId": "67", "endLine": 10, "endColumn": 43}, "no-unused-vars", "'showFilters' is assigned a value but never used.", "Identifier", "unusedVar", "'setShowFilters' is assigned a value but never used.", "'FiMail' is defined but never used.", "'FiPhone' is defined but never used.", "'FiMapPin' is defined but never used.", "'uploadProgress' is assigned a value but never used.", "'setUploadProgress' is assigned a value but never used."]