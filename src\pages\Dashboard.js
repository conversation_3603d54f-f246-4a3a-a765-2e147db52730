import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { 
  FiUsers, 
  FiUpload, 
  FiSearch, 
  FiActivity,
  FiTrendingUp,
  FiClock,
  FiFileText,
  FiArrowRight
} from 'react-icons/fi';
import './Dashboard.css';

const Dashboard = () => {
  const [stats, setStats] = useState({
    totalResumes: 0,
    searchResults: 0,
    aiPowered: 'GPT-4',
    version: 'v2.0'
  });

  const [recentActivity, setRecentActivity] = useState([]);
  const [quickStats, setQuickStats] = useState([]);

  useEffect(() => {
    // Simulate loading data
    setStats({
      totalResumes: 247,
      searchResults: 0,
      aiPowered: 'GPT-4',
      version: 'v2.0'
    });

    setRecentActivity([
      { id: 1, action: 'Resume uploaded', file: 'john_doe_resume.pdf', time: '2 minutes ago', type: 'upload' },
      { id: 2, action: 'Search completed', query: 'Python developers', results: 15, time: '5 minutes ago', type: 'search' },
      { id: 3, action: 'Resume processed', file: 'jane_smith_cv.pdf', time: '10 minutes ago', type: 'process' },
      { id: 4, action: 'Bulk upload completed', count: 5, time: '1 hour ago', type: 'bulk' },
      { id: 5, action: 'Search completed', query: 'UI/UX designers', results: 8, time: '2 hours ago', type: 'search' }
    ]);

    setQuickStats([
      { label: 'This Week', value: '23', change: '+12%', trend: 'up' },
      { label: 'This Month', value: '156', change: '+8%', trend: 'up' },
      { label: 'Success Rate', value: '98.5%', change: '+0.3%', trend: 'up' },
      { label: 'Avg. Process Time', value: '2.3s', change: '-0.5s', trend: 'up' }
    ]);
  }, []);

  const quickActions = [
    {
      title: 'Upload Resume',
      description: 'Add new resumes to your collection',
      icon: FiUpload,
      color: '#10b981',
      link: '/upload'
    },
    {
      title: 'Search Resumes',
      description: 'Find candidates using natural language',
      icon: FiSearch,
      color: '#f59e0b',
      link: '/search'
    },
    {
      title: 'View All Resumes',
      description: 'Browse your complete resume database',
      icon: FiUsers,
      color: '#8b5cf6',
      link: '/resumes'
    },
    {
      title: 'Activity Log',
      description: 'Track all system activities',
      icon: FiActivity,
      color: '#ef4444',
      link: '/activity'
    }
  ];

  const getActivityIcon = (type) => {
    switch (type) {
      case 'upload':
        return <FiUpload className="activity-icon upload" />;
      case 'search':
        return <FiSearch className="activity-icon search" />;
      case 'process':
        return <FiFileText className="activity-icon process" />;
      case 'bulk':
        return <FiUsers className="activity-icon bulk" />;
      default:
        return <FiActivity className="activity-icon default" />;
    }
  };

  return (
    <div className="dashboard">
      {/* Welcome Section */}
      <div className="welcome-section">
        <div className="welcome-content">
          <h1>Welcome back! 👋</h1>
          <p>Here's what's happening with your Resume AI Agent today.</p>
        </div>
        <div className="welcome-stats">
          <div className="stat-item">
            <FiClock className="stat-icon" />
            <span>Last activity: 2 min ago</span>
          </div>
        </div>
      </div>

      {/* Main Stats */}
      <div className="stats-grid">
        <div className="stat-card primary">
          <div className="stat-header">
            <FiUsers className="stat-icon" />
            <span className="stat-label">Total Resumes</span>
          </div>
          <div className="stat-value">{stats.totalResumes}</div>
          <div className="stat-change positive">
            <FiTrendingUp size={14} />
            <span>+12 this week</span>
          </div>
        </div>

        <div className="stat-card secondary">
          <div className="stat-header">
            <FiSearch className="stat-icon" />
            <span className="stat-label">Search Results</span>
          </div>
          <div className="stat-value">{stats.searchResults}</div>
          <div className="stat-change neutral">
            <span>Ready to search</span>
          </div>
        </div>

        <div className="stat-card accent">
          <div className="stat-header">
            <span className="ai-icon">⚡</span>
            <span className="stat-label">AI Powered</span>
          </div>
          <div className="stat-value">{stats.aiPowered}</div>
          <div className="stat-change positive">
            <span>Latest model</span>
          </div>
        </div>

        <div className="stat-card info">
          <div className="stat-header">
            <span className="version-icon">🚀</span>
            <span className="stat-label">Version</span>
          </div>
          <div className="stat-value">{stats.version}</div>
          <div className="stat-change positive">
            <span>Up to date</span>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="quick-actions-section">
        <h2>Quick Actions</h2>
        <div className="quick-actions-grid">
          {quickActions.map((action, index) => {
            const Icon = action.icon;
            return (
              <Link key={index} to={action.link} className="action-card">
                <div className="action-icon" style={{ backgroundColor: `${action.color}15`, color: action.color }}>
                  <Icon size={24} />
                </div>
                <div className="action-content">
                  <h3>{action.title}</h3>
                  <p>{action.description}</p>
                </div>
                <FiArrowRight className="action-arrow" />
              </Link>
            );
          })}
        </div>
      </div>

      {/* Bottom Grid */}
      <div className="bottom-grid">
        {/* Recent Activity */}
        <div className="activity-section">
          <div className="section-header">
            <h2>Recent Activity</h2>
            <Link to="/activity" className="view-all-link">View all</Link>
          </div>
          <div className="activity-list">
            {recentActivity.map((activity) => (
              <div key={activity.id} className="activity-item">
                <div className="activity-icon-wrapper">
                  {getActivityIcon(activity.type)}
                </div>
                <div className="activity-content">
                  <div className="activity-text">
                    <span className="activity-action">{activity.action}</span>
                    {activity.file && <span className="activity-detail">: {activity.file}</span>}
                    {activity.query && <span className="activity-detail">: "{activity.query}"</span>}
                    {activity.results && <span className="activity-detail"> ({activity.results} results)</span>}
                    {activity.count && <span className="activity-detail"> ({activity.count} files)</span>}
                  </div>
                  <div className="activity-time">{activity.time}</div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Quick Stats */}
        <div className="quick-stats-section">
          <div className="section-header">
            <h2>Quick Stats</h2>
          </div>
          <div className="quick-stats-grid">
            {quickStats.map((stat, index) => (
              <div key={index} className="quick-stat-item">
                <div className="quick-stat-label">{stat.label}</div>
                <div className="quick-stat-value">{stat.value}</div>
                <div className={`quick-stat-change ${stat.trend}`}>
                  {stat.trend === 'up' && <FiTrendingUp size={12} />}
                  <span>{stat.change}</span>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
