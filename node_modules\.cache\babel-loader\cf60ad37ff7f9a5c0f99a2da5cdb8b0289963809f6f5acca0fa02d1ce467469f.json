{"ast": null, "code": "var _jsxFileName = \"D:\\\\Projects\\\\SQL-Agent-Task-Submission-main\\\\SQL-Agent-Task-Submission-main\\\\src\\\\pages\\\\UploadResume.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useCallback } from 'react';\nimport { useDropzone } from 'react-dropzone';\nimport { FiUpload, FiFile, FiX, FiCheck, FiAlertCircle } from 'react-icons/fi';\nimport { toast } from 'react-toastify';\nimport './UploadResume.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst UploadResume = () => {\n  _s();\n  const [files, setFiles] = useState([]);\n  const [uploading, setUploading] = useState(false);\n  const [uploadProgress, setUploadProgress] = useState({});\n  const onDrop = useCallback((acceptedFiles, rejectedFiles) => {\n    // Handle rejected files\n    if (rejectedFiles.length > 0) {\n      rejectedFiles.forEach(({\n        file,\n        errors\n      }) => {\n        errors.forEach(error => {\n          if (error.code === 'file-too-large') {\n            toast.error(`File ${file.name} is too large. Maximum size is 10MB.`);\n          } else if (error.code === 'file-invalid-type') {\n            toast.error(`File ${file.name} is not a valid PDF file.`);\n          }\n        });\n      });\n    }\n\n    // Handle accepted files\n    const newFiles = acceptedFiles.map(file => ({\n      id: Math.random().toString(36).substr(2, 9),\n      file,\n      status: 'pending',\n      // pending, uploading, success, error\n      progress: 0,\n      error: null\n    }));\n    setFiles(prev => [...prev, ...newFiles]);\n  }, []);\n  const {\n    getRootProps,\n    getInputProps,\n    isDragActive\n  } = useDropzone({\n    onDrop,\n    accept: {\n      'application/pdf': ['.pdf']\n    },\n    maxSize: 10 * 1024 * 1024,\n    // 10MB\n    multiple: true\n  });\n  const removeFile = fileId => {\n    setFiles(prev => prev.filter(f => f.id !== fileId));\n  };\n  const uploadFiles = async () => {\n    if (files.length === 0) {\n      toast.warning('Please select files to upload');\n      return;\n    }\n    setUploading(true);\n    for (const fileItem of files) {\n      if (fileItem.status !== 'pending') continue;\n      try {\n        // Update status to uploading\n        setFiles(prev => prev.map(f => f.id === fileItem.id ? {\n          ...f,\n          status: 'uploading',\n          progress: 0\n        } : f));\n\n        // Simulate upload progress\n        for (let progress = 0; progress <= 100; progress += 10) {\n          await new Promise(resolve => setTimeout(resolve, 100));\n          setFiles(prev => prev.map(f => f.id === fileItem.id ? {\n            ...f,\n            progress\n          } : f));\n        }\n\n        // Simulate API call\n        const formData = new FormData();\n        formData.append('file', fileItem.file);\n\n        // Replace with actual API call\n        await new Promise(resolve => setTimeout(resolve, 500));\n\n        // Update status to success\n        setFiles(prev => prev.map(f => f.id === fileItem.id ? {\n          ...f,\n          status: 'success',\n          progress: 100\n        } : f));\n        toast.success(`${fileItem.file.name} uploaded successfully!`);\n      } catch (error) {\n        // Update status to error\n        setFiles(prev => prev.map(f => f.id === fileItem.id ? {\n          ...f,\n          status: 'error',\n          error: error.message\n        } : f));\n        toast.error(`Failed to upload ${fileItem.file.name}`);\n      }\n    }\n    setUploading(false);\n  };\n  const clearAll = () => {\n    setFiles([]);\n  };\n  const formatFileSize = bytes => {\n    if (bytes === 0) return '0 Bytes';\n    const k = 1024;\n    const sizes = ['Bytes', 'KB', 'MB', 'GB'];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n  };\n  const getStatusIcon = status => {\n    switch (status) {\n      case 'success':\n        return /*#__PURE__*/_jsxDEV(FiCheck, {\n          className: \"status-icon success\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 126,\n          columnNumber: 16\n        }, this);\n      case 'error':\n        return /*#__PURE__*/_jsxDEV(FiAlertCircle, {\n          className: \"status-icon error\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 128,\n          columnNumber: 16\n        }, this);\n      case 'uploading':\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"loading-spinner\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 130,\n          columnNumber: 16\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(FiFile, {\n          className: \"status-icon pending\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 132,\n          columnNumber: 16\n        }, this);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"upload-page\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"page-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        children: \"Upload Resume\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 139,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Upload a resume to extract information and add it to your collection. Our AI will automatically analyze and structure the data.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 140,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 138,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"upload-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"upload-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          ...getRootProps(),\n          className: `dropzone ${isDragActive ? 'active' : ''}`,\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            ...getInputProps()\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 150,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"dropzone-content\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"upload-icon\",\n              children: /*#__PURE__*/_jsxDEV(FiUpload, {\n                size: 48\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 153,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 152,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"Click or drag to upload your resume\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 155,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Supported formats: PDF, DOCX, TXT, JPG, PNG\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 156,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"upload-specs\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Maximum file size: 10MB\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 158,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"\\u2022\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 159,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Multiple files supported\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 160,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 157,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 151,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 146,\n          columnNumber: 11\n        }, this), files.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"upload-actions\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"btn btn-secondary\",\n            onClick: clearAll,\n            disabled: uploading,\n            children: \"Clear All\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 168,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"btn btn-primary\",\n            onClick: uploadFiles,\n            disabled: uploading || files.every(f => f.status === 'success'),\n            children: uploading ? 'Processing...' : 'Extract Data'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 175,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 167,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 145,\n        columnNumber: 9\n      }, this), files.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"file-list-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"section-header\",\n          children: /*#__PURE__*/_jsxDEV(\"h3\", {\n            children: [\"Selected Files (\", files.length, \")\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 190,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 189,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"file-list\",\n          children: files.map(fileItem => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `file-item ${fileItem.status}`,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"file-info\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"file-icon\",\n                children: getStatusIcon(fileItem.status)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 197,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"file-details\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"file-name\",\n                  children: fileItem.file.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 201,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"file-meta\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: formatFileSize(fileItem.file.size)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 203,\n                    columnNumber: 25\n                  }, this), fileItem.status === 'error' && fileItem.error && /*#__PURE__*/_jsxDEV(_Fragment, {\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      children: \"\\u2022\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 206,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"error-text\",\n                      children: fileItem.error\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 207,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 202,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 200,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 196,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"file-actions\",\n              children: [fileItem.status === 'uploading' && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"progress-container\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"progress-bar\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"progress-fill\",\n                    style: {\n                      width: `${fileItem.progress}%`\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 218,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 217,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"progress-text\",\n                  children: [fileItem.progress, \"%\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 223,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 216,\n                columnNumber: 23\n              }, this), fileItem.status !== 'uploading' && /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"remove-btn\",\n                onClick: () => removeFile(fileItem.id),\n                title: \"Remove file\",\n                children: /*#__PURE__*/_jsxDEV(FiX, {\n                  size: 16\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 233,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 228,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 214,\n              columnNumber: 19\n            }, this)]\n          }, fileItem.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 195,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 193,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 188,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 143,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 137,\n    columnNumber: 5\n  }, this);\n};\n_s(UploadResume, \"8YF6AmKImW1aHfbmsH7gPSEAZBc=\", false, function () {\n  return [useDropzone];\n});\n_c = UploadResume;\nexport default UploadResume;\nvar _c;\n$RefreshReg$(_c, \"UploadResume\");", "map": {"version": 3, "names": ["React", "useState", "useCallback", "useDropzone", "FiUpload", "FiFile", "FiX", "<PERSON><PERSON><PERSON><PERSON>", "FiAlertCircle", "toast", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "UploadResume", "_s", "files", "setFiles", "uploading", "setUploading", "uploadProgress", "setUploadProgress", "onDrop", "acceptedFiles", "rejectedFiles", "length", "for<PERSON>ach", "file", "errors", "error", "code", "name", "newFiles", "map", "id", "Math", "random", "toString", "substr", "status", "progress", "prev", "getRootProps", "getInputProps", "isDragActive", "accept", "maxSize", "multiple", "removeFile", "fileId", "filter", "f", "uploadFiles", "warning", "fileItem", "Promise", "resolve", "setTimeout", "formData", "FormData", "append", "success", "message", "clearAll", "formatFileSize", "bytes", "k", "sizes", "i", "floor", "log", "parseFloat", "pow", "toFixed", "getStatusIcon", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "children", "size", "onClick", "disabled", "every", "style", "width", "title", "_c", "$RefreshReg$"], "sources": ["D:/Projects/SQL-Agent-Task-Submission-main/SQL-Agent-Task-Submission-main/src/pages/UploadResume.js"], "sourcesContent": ["import React, { useState, useCallback } from 'react';\nimport { useDropzone } from 'react-dropzone';\nimport { FiUpload, FiFile, FiX, <PERSON><PERSON>heck, FiAlertCircle } from 'react-icons/fi';\nimport { toast } from 'react-toastify';\nimport './UploadResume.css';\n\nconst UploadResume = () => {\n  const [files, setFiles] = useState([]);\n  const [uploading, setUploading] = useState(false);\n  const [uploadProgress, setUploadProgress] = useState({});\n\n  const onDrop = useCallback((acceptedFiles, rejectedFiles) => {\n    // Handle rejected files\n    if (rejectedFiles.length > 0) {\n      rejectedFiles.forEach(({ file, errors }) => {\n        errors.forEach(error => {\n          if (error.code === 'file-too-large') {\n            toast.error(`File ${file.name} is too large. Maximum size is 10MB.`);\n          } else if (error.code === 'file-invalid-type') {\n            toast.error(`File ${file.name} is not a valid PDF file.`);\n          }\n        });\n      });\n    }\n\n    // Handle accepted files\n    const newFiles = acceptedFiles.map(file => ({\n      id: Math.random().toString(36).substr(2, 9),\n      file,\n      status: 'pending', // pending, uploading, success, error\n      progress: 0,\n      error: null\n    }));\n\n    setFiles(prev => [...prev, ...newFiles]);\n  }, []);\n\n  const { getRootProps, getInputProps, isDragActive } = useDropzone({\n    onDrop,\n    accept: {\n      'application/pdf': ['.pdf']\n    },\n    maxSize: 10 * 1024 * 1024, // 10MB\n    multiple: true\n  });\n\n  const removeFile = (fileId) => {\n    setFiles(prev => prev.filter(f => f.id !== fileId));\n  };\n\n  const uploadFiles = async () => {\n    if (files.length === 0) {\n      toast.warning('Please select files to upload');\n      return;\n    }\n\n    setUploading(true);\n    \n    for (const fileItem of files) {\n      if (fileItem.status !== 'pending') continue;\n\n      try {\n        // Update status to uploading\n        setFiles(prev => prev.map(f => \n          f.id === fileItem.id \n            ? { ...f, status: 'uploading', progress: 0 }\n            : f\n        ));\n\n        // Simulate upload progress\n        for (let progress = 0; progress <= 100; progress += 10) {\n          await new Promise(resolve => setTimeout(resolve, 100));\n          setFiles(prev => prev.map(f => \n            f.id === fileItem.id \n              ? { ...f, progress }\n              : f\n          ));\n        }\n\n        // Simulate API call\n        const formData = new FormData();\n        formData.append('file', fileItem.file);\n\n        // Replace with actual API call\n        await new Promise(resolve => setTimeout(resolve, 500));\n\n        // Update status to success\n        setFiles(prev => prev.map(f => \n          f.id === fileItem.id \n            ? { ...f, status: 'success', progress: 100 }\n            : f\n        ));\n\n        toast.success(`${fileItem.file.name} uploaded successfully!`);\n\n      } catch (error) {\n        // Update status to error\n        setFiles(prev => prev.map(f => \n          f.id === fileItem.id \n            ? { ...f, status: 'error', error: error.message }\n            : f\n        ));\n\n        toast.error(`Failed to upload ${fileItem.file.name}`);\n      }\n    }\n\n    setUploading(false);\n  };\n\n  const clearAll = () => {\n    setFiles([]);\n  };\n\n  const formatFileSize = (bytes) => {\n    if (bytes === 0) return '0 Bytes';\n    const k = 1024;\n    const sizes = ['Bytes', 'KB', 'MB', 'GB'];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n  };\n\n  const getStatusIcon = (status) => {\n    switch (status) {\n      case 'success':\n        return <FiCheck className=\"status-icon success\" />;\n      case 'error':\n        return <FiAlertCircle className=\"status-icon error\" />;\n      case 'uploading':\n        return <div className=\"loading-spinner\" />;\n      default:\n        return <FiFile className=\"status-icon pending\" />;\n    }\n  };\n\n  return (\n    <div className=\"upload-page\">\n      <div className=\"page-header\">\n        <h1>Upload Resume</h1>\n        <p>Upload a resume to extract information and add it to your collection. Our AI will automatically analyze and structure the data.</p>\n      </div>\n\n      <div className=\"upload-container\">\n        {/* Upload Area */}\n        <div className=\"upload-section\">\n          <div \n            {...getRootProps()} \n            className={`dropzone ${isDragActive ? 'active' : ''}`}\n          >\n            <input {...getInputProps()} />\n            <div className=\"dropzone-content\">\n              <div className=\"upload-icon\">\n                <FiUpload size={48} />\n              </div>\n              <h3>Click or drag to upload your resume</h3>\n              <p>Supported formats: PDF, DOCX, TXT, JPG, PNG</p>\n              <div className=\"upload-specs\">\n                <span>Maximum file size: 10MB</span>\n                <span>•</span>\n                <span>Multiple files supported</span>\n              </div>\n            </div>\n          </div>\n\n          {/* Action Buttons */}\n          {files.length > 0 && (\n            <div className=\"upload-actions\">\n              <button \n                className=\"btn btn-secondary\"\n                onClick={clearAll}\n                disabled={uploading}\n              >\n                Clear All\n              </button>\n              <button \n                className=\"btn btn-primary\"\n                onClick={uploadFiles}\n                disabled={uploading || files.every(f => f.status === 'success')}\n              >\n                {uploading ? 'Processing...' : 'Extract Data'}\n              </button>\n            </div>\n          )}\n        </div>\n\n        {/* File List */}\n        {files.length > 0 && (\n          <div className=\"file-list-section\">\n            <div className=\"section-header\">\n              <h3>Selected Files ({files.length})</h3>\n            </div>\n            \n            <div className=\"file-list\">\n              {files.map((fileItem) => (\n                <div key={fileItem.id} className={`file-item ${fileItem.status}`}>\n                  <div className=\"file-info\">\n                    <div className=\"file-icon\">\n                      {getStatusIcon(fileItem.status)}\n                    </div>\n                    <div className=\"file-details\">\n                      <div className=\"file-name\">{fileItem.file.name}</div>\n                      <div className=\"file-meta\">\n                        <span>{formatFileSize(fileItem.file.size)}</span>\n                        {fileItem.status === 'error' && fileItem.error && (\n                          <>\n                            <span>•</span>\n                            <span className=\"error-text\">{fileItem.error}</span>\n                          </>\n                        )}\n                      </div>\n                    </div>\n                  </div>\n\n                  <div className=\"file-actions\">\n                    {fileItem.status === 'uploading' && (\n                      <div className=\"progress-container\">\n                        <div className=\"progress-bar\">\n                          <div \n                            className=\"progress-fill\"\n                            style={{ width: `${fileItem.progress}%` }}\n                          />\n                        </div>\n                        <span className=\"progress-text\">{fileItem.progress}%</span>\n                      </div>\n                    )}\n                    \n                    {fileItem.status !== 'uploading' && (\n                      <button\n                        className=\"remove-btn\"\n                        onClick={() => removeFile(fileItem.id)}\n                        title=\"Remove file\"\n                      >\n                        <FiX size={16} />\n                      </button>\n                    )}\n                  </div>\n                </div>\n              ))}\n            </div>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default UploadResume;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,WAAW,QAAQ,OAAO;AACpD,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,QAAQ,EAAEC,MAAM,EAAEC,GAAG,EAAEC,OAAO,EAAEC,aAAa,QAAQ,gBAAgB;AAC9E,SAASC,KAAK,QAAQ,gBAAgB;AACtC,OAAO,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE5B,MAAMC,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzB,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGhB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACiB,SAAS,EAAEC,YAAY,CAAC,GAAGlB,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACmB,cAAc,EAAEC,iBAAiB,CAAC,GAAGpB,QAAQ,CAAC,CAAC,CAAC,CAAC;EAExD,MAAMqB,MAAM,GAAGpB,WAAW,CAAC,CAACqB,aAAa,EAAEC,aAAa,KAAK;IAC3D;IACA,IAAIA,aAAa,CAACC,MAAM,GAAG,CAAC,EAAE;MAC5BD,aAAa,CAACE,OAAO,CAAC,CAAC;QAAEC,IAAI;QAAEC;MAAO,CAAC,KAAK;QAC1CA,MAAM,CAACF,OAAO,CAACG,KAAK,IAAI;UACtB,IAAIA,KAAK,CAACC,IAAI,KAAK,gBAAgB,EAAE;YACnCrB,KAAK,CAACoB,KAAK,CAAC,QAAQF,IAAI,CAACI,IAAI,sCAAsC,CAAC;UACtE,CAAC,MAAM,IAAIF,KAAK,CAACC,IAAI,KAAK,mBAAmB,EAAE;YAC7CrB,KAAK,CAACoB,KAAK,CAAC,QAAQF,IAAI,CAACI,IAAI,2BAA2B,CAAC;UAC3D;QACF,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ;;IAEA;IACA,MAAMC,QAAQ,GAAGT,aAAa,CAACU,GAAG,CAACN,IAAI,KAAK;MAC1CO,EAAE,EAAEC,IAAI,CAACC,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;MAC3CX,IAAI;MACJY,MAAM,EAAE,SAAS;MAAE;MACnBC,QAAQ,EAAE,CAAC;MACXX,KAAK,EAAE;IACT,CAAC,CAAC,CAAC;IAEHZ,QAAQ,CAACwB,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE,GAAGT,QAAQ,CAAC,CAAC;EAC1C,CAAC,EAAE,EAAE,CAAC;EAEN,MAAM;IAAEU,YAAY;IAAEC,aAAa;IAAEC;EAAa,CAAC,GAAGzC,WAAW,CAAC;IAChEmB,MAAM;IACNuB,MAAM,EAAE;MACN,iBAAiB,EAAE,CAAC,MAAM;IAC5B,CAAC;IACDC,OAAO,EAAE,EAAE,GAAG,IAAI,GAAG,IAAI;IAAE;IAC3BC,QAAQ,EAAE;EACZ,CAAC,CAAC;EAEF,MAAMC,UAAU,GAAIC,MAAM,IAAK;IAC7BhC,QAAQ,CAACwB,IAAI,IAAIA,IAAI,CAACS,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACjB,EAAE,KAAKe,MAAM,CAAC,CAAC;EACrD,CAAC;EAED,MAAMG,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9B,IAAIpC,KAAK,CAACS,MAAM,KAAK,CAAC,EAAE;MACtBhB,KAAK,CAAC4C,OAAO,CAAC,+BAA+B,CAAC;MAC9C;IACF;IAEAlC,YAAY,CAAC,IAAI,CAAC;IAElB,KAAK,MAAMmC,QAAQ,IAAItC,KAAK,EAAE;MAC5B,IAAIsC,QAAQ,CAACf,MAAM,KAAK,SAAS,EAAE;MAEnC,IAAI;QACF;QACAtB,QAAQ,CAACwB,IAAI,IAAIA,IAAI,CAACR,GAAG,CAACkB,CAAC,IACzBA,CAAC,CAACjB,EAAE,KAAKoB,QAAQ,CAACpB,EAAE,GAChB;UAAE,GAAGiB,CAAC;UAAEZ,MAAM,EAAE,WAAW;UAAEC,QAAQ,EAAE;QAAE,CAAC,GAC1CW,CACN,CAAC,CAAC;;QAEF;QACA,KAAK,IAAIX,QAAQ,GAAG,CAAC,EAAEA,QAAQ,IAAI,GAAG,EAAEA,QAAQ,IAAI,EAAE,EAAE;UACtD,MAAM,IAAIe,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC;UACtDvC,QAAQ,CAACwB,IAAI,IAAIA,IAAI,CAACR,GAAG,CAACkB,CAAC,IACzBA,CAAC,CAACjB,EAAE,KAAKoB,QAAQ,CAACpB,EAAE,GAChB;YAAE,GAAGiB,CAAC;YAAEX;UAAS,CAAC,GAClBW,CACN,CAAC,CAAC;QACJ;;QAEA;QACA,MAAMO,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;QAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAEN,QAAQ,CAAC3B,IAAI,CAAC;;QAEtC;QACA,MAAM,IAAI4B,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC;;QAEtD;QACAvC,QAAQ,CAACwB,IAAI,IAAIA,IAAI,CAACR,GAAG,CAACkB,CAAC,IACzBA,CAAC,CAACjB,EAAE,KAAKoB,QAAQ,CAACpB,EAAE,GAChB;UAAE,GAAGiB,CAAC;UAAEZ,MAAM,EAAE,SAAS;UAAEC,QAAQ,EAAE;QAAI,CAAC,GAC1CW,CACN,CAAC,CAAC;QAEF1C,KAAK,CAACoD,OAAO,CAAC,GAAGP,QAAQ,CAAC3B,IAAI,CAACI,IAAI,yBAAyB,CAAC;MAE/D,CAAC,CAAC,OAAOF,KAAK,EAAE;QACd;QACAZ,QAAQ,CAACwB,IAAI,IAAIA,IAAI,CAACR,GAAG,CAACkB,CAAC,IACzBA,CAAC,CAACjB,EAAE,KAAKoB,QAAQ,CAACpB,EAAE,GAChB;UAAE,GAAGiB,CAAC;UAAEZ,MAAM,EAAE,OAAO;UAAEV,KAAK,EAAEA,KAAK,CAACiC;QAAQ,CAAC,GAC/CX,CACN,CAAC,CAAC;QAEF1C,KAAK,CAACoB,KAAK,CAAC,oBAAoByB,QAAQ,CAAC3B,IAAI,CAACI,IAAI,EAAE,CAAC;MACvD;IACF;IAEAZ,YAAY,CAAC,KAAK,CAAC;EACrB,CAAC;EAED,MAAM4C,QAAQ,GAAGA,CAAA,KAAM;IACrB9C,QAAQ,CAAC,EAAE,CAAC;EACd,CAAC;EAED,MAAM+C,cAAc,GAAIC,KAAK,IAAK;IAChC,IAAIA,KAAK,KAAK,CAAC,EAAE,OAAO,SAAS;IACjC,MAAMC,CAAC,GAAG,IAAI;IACd,MAAMC,KAAK,GAAG,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IACzC,MAAMC,CAAC,GAAGjC,IAAI,CAACkC,KAAK,CAAClC,IAAI,CAACmC,GAAG,CAACL,KAAK,CAAC,GAAG9B,IAAI,CAACmC,GAAG,CAACJ,CAAC,CAAC,CAAC;IACnD,OAAOK,UAAU,CAAC,CAACN,KAAK,GAAG9B,IAAI,CAACqC,GAAG,CAACN,CAAC,EAAEE,CAAC,CAAC,EAAEK,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,GAAGN,KAAK,CAACC,CAAC,CAAC;EACzE,CAAC;EAED,MAAMM,aAAa,GAAInC,MAAM,IAAK;IAChC,QAAQA,MAAM;MACZ,KAAK,SAAS;QACZ,oBAAO5B,OAAA,CAACJ,OAAO;UAACoE,SAAS,EAAC;QAAqB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACpD,KAAK,OAAO;QACV,oBAAOpE,OAAA,CAACH,aAAa;UAACmE,SAAS,EAAC;QAAmB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACxD,KAAK,WAAW;QACd,oBAAOpE,OAAA;UAAKgE,SAAS,EAAC;QAAiB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC5C;QACE,oBAAOpE,OAAA,CAACN,MAAM;UAACsE,SAAS,EAAC;QAAqB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;IACrD;EACF,CAAC;EAED,oBACEpE,OAAA;IAAKgE,SAAS,EAAC,aAAa;IAAAK,QAAA,gBAC1BrE,OAAA;MAAKgE,SAAS,EAAC,aAAa;MAAAK,QAAA,gBAC1BrE,OAAA;QAAAqE,QAAA,EAAI;MAAa;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACtBpE,OAAA;QAAAqE,QAAA,EAAG;MAA+H;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnI,CAAC,eAENpE,OAAA;MAAKgE,SAAS,EAAC,kBAAkB;MAAAK,QAAA,gBAE/BrE,OAAA;QAAKgE,SAAS,EAAC,gBAAgB;QAAAK,QAAA,gBAC7BrE,OAAA;UAAA,GACM+B,YAAY,CAAC,CAAC;UAClBiC,SAAS,EAAE,YAAY/B,YAAY,GAAG,QAAQ,GAAG,EAAE,EAAG;UAAAoC,QAAA,gBAEtDrE,OAAA;YAAA,GAAWgC,aAAa,CAAC;UAAC;YAAAiC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAC9BpE,OAAA;YAAKgE,SAAS,EAAC,kBAAkB;YAAAK,QAAA,gBAC/BrE,OAAA;cAAKgE,SAAS,EAAC,aAAa;cAAAK,QAAA,eAC1BrE,OAAA,CAACP,QAAQ;gBAAC6E,IAAI,EAAE;cAAG;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB,CAAC,eACNpE,OAAA;cAAAqE,QAAA,EAAI;YAAmC;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC5CpE,OAAA;cAAAqE,QAAA,EAAG;YAA2C;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAClDpE,OAAA;cAAKgE,SAAS,EAAC,cAAc;cAAAK,QAAA,gBAC3BrE,OAAA;gBAAAqE,QAAA,EAAM;cAAuB;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACpCpE,OAAA;gBAAAqE,QAAA,EAAM;cAAC;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACdpE,OAAA;gBAAAqE,QAAA,EAAM;cAAwB;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAGL/D,KAAK,CAACS,MAAM,GAAG,CAAC,iBACfd,OAAA;UAAKgE,SAAS,EAAC,gBAAgB;UAAAK,QAAA,gBAC7BrE,OAAA;YACEgE,SAAS,EAAC,mBAAmB;YAC7BO,OAAO,EAAEnB,QAAS;YAClBoB,QAAQ,EAAEjE,SAAU;YAAA8D,QAAA,EACrB;UAED;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTpE,OAAA;YACEgE,SAAS,EAAC,iBAAiB;YAC3BO,OAAO,EAAE9B,WAAY;YACrB+B,QAAQ,EAAEjE,SAAS,IAAIF,KAAK,CAACoE,KAAK,CAACjC,CAAC,IAAIA,CAAC,CAACZ,MAAM,KAAK,SAAS,CAAE;YAAAyC,QAAA,EAE/D9D,SAAS,GAAG,eAAe,GAAG;UAAc;YAAA0D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,EAGL/D,KAAK,CAACS,MAAM,GAAG,CAAC,iBACfd,OAAA;QAAKgE,SAAS,EAAC,mBAAmB;QAAAK,QAAA,gBAChCrE,OAAA;UAAKgE,SAAS,EAAC,gBAAgB;UAAAK,QAAA,eAC7BrE,OAAA;YAAAqE,QAAA,GAAI,kBAAgB,EAAChE,KAAK,CAACS,MAAM,EAAC,GAAC;UAAA;YAAAmD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrC,CAAC,eAENpE,OAAA;UAAKgE,SAAS,EAAC,WAAW;UAAAK,QAAA,EACvBhE,KAAK,CAACiB,GAAG,CAAEqB,QAAQ,iBAClB3C,OAAA;YAAuBgE,SAAS,EAAE,aAAarB,QAAQ,CAACf,MAAM,EAAG;YAAAyC,QAAA,gBAC/DrE,OAAA;cAAKgE,SAAS,EAAC,WAAW;cAAAK,QAAA,gBACxBrE,OAAA;gBAAKgE,SAAS,EAAC,WAAW;gBAAAK,QAAA,EACvBN,aAAa,CAACpB,QAAQ,CAACf,MAAM;cAAC;gBAAAqC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B,CAAC,eACNpE,OAAA;gBAAKgE,SAAS,EAAC,cAAc;gBAAAK,QAAA,gBAC3BrE,OAAA;kBAAKgE,SAAS,EAAC,WAAW;kBAAAK,QAAA,EAAE1B,QAAQ,CAAC3B,IAAI,CAACI;gBAAI;kBAAA6C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACrDpE,OAAA;kBAAKgE,SAAS,EAAC,WAAW;kBAAAK,QAAA,gBACxBrE,OAAA;oBAAAqE,QAAA,EAAOhB,cAAc,CAACV,QAAQ,CAAC3B,IAAI,CAACsD,IAAI;kBAAC;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,EAChDzB,QAAQ,CAACf,MAAM,KAAK,OAAO,IAAIe,QAAQ,CAACzB,KAAK,iBAC5ClB,OAAA,CAAAE,SAAA;oBAAAmE,QAAA,gBACErE,OAAA;sBAAAqE,QAAA,EAAM;oBAAC;sBAAAJ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACdpE,OAAA;sBAAMgE,SAAS,EAAC,YAAY;sBAAAK,QAAA,EAAE1B,QAAQ,CAACzB;oBAAK;sBAAA+C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA,eACpD,CACH;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENpE,OAAA;cAAKgE,SAAS,EAAC,cAAc;cAAAK,QAAA,GAC1B1B,QAAQ,CAACf,MAAM,KAAK,WAAW,iBAC9B5B,OAAA;gBAAKgE,SAAS,EAAC,oBAAoB;gBAAAK,QAAA,gBACjCrE,OAAA;kBAAKgE,SAAS,EAAC,cAAc;kBAAAK,QAAA,eAC3BrE,OAAA;oBACEgE,SAAS,EAAC,eAAe;oBACzBU,KAAK,EAAE;sBAAEC,KAAK,EAAE,GAAGhC,QAAQ,CAACd,QAAQ;oBAAI;kBAAE;oBAAAoC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3C;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACNpE,OAAA;kBAAMgE,SAAS,EAAC,eAAe;kBAAAK,QAAA,GAAE1B,QAAQ,CAACd,QAAQ,EAAC,GAAC;gBAAA;kBAAAoC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxD,CACN,EAEAzB,QAAQ,CAACf,MAAM,KAAK,WAAW,iBAC9B5B,OAAA;gBACEgE,SAAS,EAAC,YAAY;gBACtBO,OAAO,EAAEA,CAAA,KAAMlC,UAAU,CAACM,QAAQ,CAACpB,EAAE,CAAE;gBACvCqD,KAAK,EAAC,aAAa;gBAAAP,QAAA,eAEnBrE,OAAA,CAACL,GAAG;kBAAC2E,IAAI,EAAE;gBAAG;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACX,CACT;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA,GAzCEzB,QAAQ,CAACpB,EAAE;YAAA0C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OA0ChB,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAChE,EAAA,CA9OID,YAAY;EAAA,QA+BsCX,WAAW;AAAA;AAAAqF,EAAA,GA/B7D1E,YAAY;AAgPlB,eAAeA,YAAY;AAAC,IAAA0E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}