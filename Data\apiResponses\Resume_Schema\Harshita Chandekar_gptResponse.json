{"id": "chatcmpl-B3eNnJ5gzk9IDFNGdEzuFrNaBkseW", "choices": [{"finish_reason": "stop", "index": 0, "logprobs": null, "message": {"content": "{\"Resume\":{\"PersonalInformation\":{\"FullName\":\"<PERSON><PERSON><PERSON> L <PERSON>\",\"Gender\":\"Female\",\"BirthDate\":19970529,\"Email\":\"<EMAIL>\",\"Address\":\"B/H Vishnu Complex, Chandekar area, C. A. Road Nagpur-440008\",\"ContactNumber\":\"9975995768\",\"LinkedInProfile\":\"\"},\"Objective\":\"To get an opportunity where I can make the best of my potential and contribute to the organization's growth.\",\"Education\":[{\"Degree\":\"Master of Business Administration\",\"Institution\":\"RTM Nagpur University\",\"GraduationYear\":\"Pursuing\",\"GPA/Marks/%\":0},{\"Degree\":\"BE in Electronics & Telecommunication Engineering\",\"Institution\":\"Savitri Bai Phule University\",\"GraduationYear\":\"\",\"GPA/Marks/%\":0},{\"Degree\":\"Diploma in Electronic Engineering\",\"Institution\":\"MSBTE\",\"GraduationYear\":\"\",\"GPA/Marks/%\":0},{\"Degree\":\"Xth\",\"Institution\":\"Maharashtra State Board\",\"GraduationYear\":\"\",\"GPA/Marks/%\":0}],\"WorkExperience\":[{\"CompanyName\":\"JAFS Engineering Service PVT. LTD.\",\"Role\":\"Intern\",\"StartYear\":201707,\"EndYear\":201707,\"Description/Responsibility\":\"Get exposure to practical knowledge of working industry in CCTV and Networking department\"},{\"CompanyName\":\"BSNL RTTC NAGPUR\",\"Role\":\"Intern\",\"StartYear\":201505,\"EndYear\":201505,\"Description/Responsibility\":\"Get to know various aspects of Telecommunication\"},{\"CompanyName\":\"MTA's DIGTECH\",\"Role\":\"Intern\",\"StartYear\":201405,\"EndYear\":201405,\"Description/Responsibility\":\"Ethical Hacking and Networking Security\"}],\"TotalWorkExperienceInYears\":0.1,\"Skills\":[\"MS Suite (Excel, Power Point, Word, Outlook)\",\"Verbal Communication\",\"Time Management\",\"Critical thinking\",\"Basic knowledge of C and Python Language\"],\"Certifications\":[{\"CertificationName\":\"MSCIT\",\"IssuingOrganization\":\"\",\"IssueDate\":0,\"ExpiryDate\":0}],\"Achievements\":[{\"AchievementName\":\"1st prize in Skit Competition\",\"IssuingOrganization\":\"Green Heaven Institute of Research and Management\",\"IssueDate\":0},{\"AchievementName\":\"2nd prize at National Level Project Competition\",\"IssuingOrganization\":\"PAROKSHA\",\"IssueDate\":0}],\"Languages\":[\"Marathi\",\"Hindi\",\"English\"],\"Projects\":[{\"ProjectName\":\"Smart Watch for Senior/Elderly\",\"Description\":\"Developed a smart watch using microcontroller for senior citizens.\",\"TechnologiesUsed\":[\"Arduino IDE\",\"Proteus Simulation\"],\"Role\":\"Team Lead, presentation, fabrication\"},{\"ProjectName\":\"Dual Power Supply Trainer Kit\",\"Description\":\"Developed a mini project for dual power supply.\",\"TechnologiesUsed\":[\"Proteus Simulation\"],\"Role\":\"Marketing Survey, soldering of components, real-time implementation\"},{\"ProjectName\":\"GPS Based Soldier Tracking and Health Monitoring System\",\"Description\":\"Developed a system for tracking and monitoring soldiers' health.\",\"TechnologiesUsed\":[\"Proteus Simulation\"],\"Role\":\"Team lead, market survey\"}]}}", "refusal": null, "role": "assistant", "audio": null, "function_call": null, "tool_calls": null, "parsed": null}}], "created": **********, "model": "gpt-4o-2024-08-06", "object": "chat.completion", "service_tier": "default", "system_fingerprint": "fp_eb9dce56a8", "usage": {"completion_tokens": 623, "prompt_tokens": 3948, "total_tokens": 4571, "completion_tokens_details": {"accepted_prediction_tokens": 0, "audio_tokens": 0, "reasoning_tokens": 0, "rejected_prediction_tokens": 0}, "prompt_tokens_details": {"audio_tokens": 0, "cached_tokens": 0}}}