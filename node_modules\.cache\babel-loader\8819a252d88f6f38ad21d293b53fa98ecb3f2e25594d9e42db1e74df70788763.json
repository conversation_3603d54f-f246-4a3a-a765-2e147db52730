{"ast": null, "code": "var _jsxFileName = \"D:\\\\Projects\\\\SQL-Agent-Task-Submission-main\\\\SQL-Agent-Task-Submission-main\\\\src\\\\pages\\\\SearchResumes.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { FiSearch, FiFilter, FiDownload, FiEye, FiUser, FiMapPin, FiMail, FiPhone, FiExternalLink, FiStar, FiX, FiSettings, FiChevronDown, FiChevronUp } from 'react-icons/fi';\nimport { toast } from 'react-toastify';\nimport ResumeModal from '../components/ResumeModal';\nimport './SearchResumes.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst SearchResumes = () => {\n  _s();\n  const [searchQuery, setSearchQuery] = useState('');\n  const [isSearching, setIsSearching] = useState(false);\n  const [searchResults, setSearchResults] = useState([]);\n  const [filterQuery, setFilterQuery] = useState('');\n  const [showFilters, setShowFilters] = useState(false);\n  const [advancedFilters, setAdvancedFilters] = useState({\n    minExperience: '',\n    maxExperience: '',\n    education: '',\n    location: '',\n    skills: '',\n    minMarks: '',\n    institution: '',\n    graduationYear: '',\n    company: ''\n  });\n  const [selectedResume, setSelectedResume] = useState(null);\n  const [isModalOpen, setIsModalOpen] = useState(false);\n  const [currentPage, setCurrentPage] = useState(1);\n  const [totalPages, setTotalPages] = useState(1);\n  const [totalCount, setTotalCount] = useState(0);\n  const resultsPerPage = 20;\n\n  // Mock data for demonstration\n  const mockResults = [{\n    id: 1,\n    name: 'John Doe',\n    title: 'Senior Software Engineer',\n    email: '<EMAIL>',\n    phone: '+****************',\n    location: 'San Francisco, CA',\n    experience: '5+ years',\n    skills: ['Python', 'React', 'Node.js', 'AWS'],\n    education: 'MS Computer Science',\n    summary: 'Experienced software engineer with expertise in full-stack development...',\n    score: 95,\n    rawData: {\n      Resume: {\n        PersonalInformation: {\n          FullName: 'John Doe',\n          Email: '<EMAIL>',\n          ContactNumber: '+****************',\n          Address: 'San Francisco, CA'\n        },\n        WorkExperience: [{\n          Role: 'Senior Software Engineer',\n          CompanyName: 'Tech Corp',\n          StartYear: '2019',\n          EndYear: 'Present',\n          'Description/Responsibility': 'Led development of scalable web applications using React and Node.js'\n        }, {\n          Role: 'Software Engineer',\n          CompanyName: 'StartupXYZ',\n          StartYear: '2017',\n          EndYear: '2019',\n          'Description/Responsibility': 'Developed full-stack applications and improved system performance'\n        }],\n        Education: [{\n          Degree: 'MS Computer Science',\n          Institution: 'Stanford University',\n          GraduationYear: '2017',\n          'GPA/Marks/%': 85\n        }],\n        Skills: ['Python', 'React', 'Node.js', 'AWS'],\n        TotalWorkExperienceInYears: 5\n      }\n    }\n  }, {\n    id: 2,\n    name: 'Jane Smith',\n    title: 'Frontend Developer',\n    email: '<EMAIL>',\n    phone: '+****************',\n    location: 'New York, NY',\n    experience: '3+ years',\n    skills: ['React', 'TypeScript', 'CSS', 'JavaScript'],\n    education: 'BS Computer Science',\n    summary: 'Creative frontend developer passionate about user experience...',\n    score: 88,\n    rawData: {\n      Resume: {\n        PersonalInformation: {\n          FullName: 'Jane Smith',\n          Email: '<EMAIL>',\n          ContactNumber: '+****************',\n          Address: 'New York, NY'\n        },\n        WorkExperience: [{\n          Role: 'Frontend Developer',\n          CompanyName: 'Design Studio',\n          StartYear: '2021',\n          EndYear: 'Present',\n          'Description/Responsibility': 'Created responsive web interfaces using React and modern CSS'\n        }],\n        Education: [{\n          Degree: 'BS Computer Science',\n          Institution: 'NYU',\n          GraduationYear: '2021',\n          'GPA/Marks/%': 78\n        }],\n        Skills: ['React', 'TypeScript', 'CSS', 'JavaScript'],\n        TotalWorkExperienceInYears: 3\n      }\n    }\n  }, {\n    id: 3,\n    name: 'Mike Johnson',\n    title: 'DevOps Engineer',\n    email: '<EMAIL>',\n    phone: '+****************',\n    location: 'Austin, TX',\n    experience: '4+ years',\n    skills: ['Docker', 'Kubernetes', 'AWS', 'Python'],\n    education: 'BS Information Technology',\n    summary: 'DevOps engineer specializing in cloud infrastructure and automation...',\n    score: 92,\n    rawData: {\n      Resume: {\n        PersonalInformation: {\n          FullName: 'Mike Johnson',\n          Email: '<EMAIL>',\n          ContactNumber: '+****************',\n          Address: 'Austin, TX'\n        },\n        WorkExperience: [{\n          Role: 'DevOps Engineer',\n          CompanyName: 'Cloud Solutions Inc',\n          StartYear: '2020',\n          EndYear: 'Present',\n          'Description/Responsibility': 'Managed cloud infrastructure and automated deployment pipelines'\n        }],\n        Education: [{\n          Degree: 'BS Information Technology',\n          Institution: 'UT Austin',\n          GraduationYear: '2020',\n          'GPA/Marks/%': 82\n        }],\n        Skills: ['Docker', 'Kubernetes', 'AWS', 'Python'],\n        TotalWorkExperienceInYears: 4\n      }\n    }\n  }];\n  const handleSearch = async e => {\n    e.preventDefault();\n    if (!searchQuery.trim()) {\n      toast.warning('Please enter a search query');\n      return;\n    }\n    setIsSearching(true);\n    try {\n      // Call the FastAPI backend directly for GPT-powered search\n      const response = await fetch(`http://localhost:8001/query/?naturalQuery=${encodeURIComponent(searchQuery)}`, {\n        method: 'GET',\n        headers: {\n          'Content-Type': 'application/json'\n        }\n      });\n      const data = await response.json();\n      if (response.ok) {\n        // Handle FastAPI response format: {query: [], listOfDict: [], result: \"\"}\n        let transformedResults = [];\n        if (data.listOfDict && Array.isArray(data.listOfDict)) {\n          // Transform the resume data from MongoDB to display format\n          transformedResults = data.listOfDict.map(resume => {\n            var _resumeData$PersonalI, _resumeData$PersonalI2, _resumeData$PersonalI3, _resumeData$PersonalI4, _resumeData$PersonalI5, _resumeData$WorkExper, _resumeData$Education, _resumeData$Education2;\n            // Handle the nested Resume structure from MongoDB\n            const resumeData = resume.Resume || resume;\n            return {\n              id: resume._id || Math.random().toString(36).substr(2, 9),\n              name: ((_resumeData$PersonalI = resumeData.PersonalInformation) === null || _resumeData$PersonalI === void 0 ? void 0 : _resumeData$PersonalI.FullName) || resumeData.name || 'Unknown',\n              title: ((_resumeData$PersonalI2 = resumeData.PersonalInformation) === null || _resumeData$PersonalI2 === void 0 ? void 0 : _resumeData$PersonalI2.Designation) || resumeData.title || 'No title',\n              email: ((_resumeData$PersonalI3 = resumeData.PersonalInformation) === null || _resumeData$PersonalI3 === void 0 ? void 0 : _resumeData$PersonalI3.Email) || resumeData.email || 'No email',\n              phone: ((_resumeData$PersonalI4 = resumeData.PersonalInformation) === null || _resumeData$PersonalI4 === void 0 ? void 0 : _resumeData$PersonalI4.ContactNumber) || resumeData.phone || 'No phone',\n              location: ((_resumeData$PersonalI5 = resumeData.PersonalInformation) === null || _resumeData$PersonalI5 === void 0 ? void 0 : _resumeData$PersonalI5.Address) || resumeData.location || 'No location',\n              experience: resumeData.TotalWorkExperienceInYears ? `${resumeData.TotalWorkExperienceInYears} years` : (_resumeData$WorkExper = resumeData.WorkExperience) !== null && _resumeData$WorkExper !== void 0 && _resumeData$WorkExper.length ? `${resumeData.WorkExperience.length}+ positions` : resumeData.experience || 'No experience data',\n              skills: resumeData.Skills || resumeData.skills || [],\n              education: (_resumeData$Education = resumeData.Education) !== null && _resumeData$Education !== void 0 && _resumeData$Education.length ? ((_resumeData$Education2 = resumeData.Education[0]) === null || _resumeData$Education2 === void 0 ? void 0 : _resumeData$Education2.Degree) || 'Education available' : resumeData.education || 'No education data',\n              summary: resumeData.Objective || resumeData.Summary || resumeData.summary || 'No summary available',\n              score: 95,\n              // Default score\n              rawData: resume // Keep original data for reference\n            };\n          });\n        } else {\n          // Fallback to mock data if API doesn't return results\n          transformedResults = mockResults.filter(resume => resume.name.toLowerCase().includes(searchQuery.toLowerCase()) || resume.title.toLowerCase().includes(searchQuery.toLowerCase()) || resume.skills.some(skill => skill.toLowerCase().includes(searchQuery.toLowerCase())));\n        }\n        setSearchResults(transformedResults);\n\n        // Show success message with GPT result\n        if (data.result) {\n          toast.success(`🤖 AI Search: ${data.result}`);\n        } else {\n          toast.success(`Found ${transformedResults.length} matching resumes`);\n        }\n\n        // Log GPT response and MongoDB query for debugging\n        if (data.query) {\n          console.log('MongoDB Query Generated by GPT:', data.query);\n          console.log('GPT Result:', data.result);\n        }\n      } else {\n        throw new Error(data.error || 'Search failed');\n      }\n    } catch (error) {\n      console.error('Search error:', error);\n\n      // Fallback to mock data on network error\n      const fallbackResults = mockResults.filter(resume => resume.name.toLowerCase().includes(searchQuery.toLowerCase()) || resume.title.toLowerCase().includes(searchQuery.toLowerCase()) || resume.skills.some(skill => skill.toLowerCase().includes(searchQuery.toLowerCase())));\n      setSearchResults(fallbackResults);\n      toast.warning(`Using demo data - Found ${fallbackResults.length} matching resumes`);\n    } finally {\n      setIsSearching(false);\n    }\n  };\n  const handleShowAll = async (page = 1) => {\n    setIsSearching(true);\n    try {\n      // Call the FastAPI backend directly to get all resumes\n      const response = await fetch('http://localhost:8001/showall');\n      const data = await response.json();\n      if (response.ok) {\n        let transformedResults = [];\n        if (Array.isArray(data)) {\n          // FastAPI /showall returns array directly\n          transformedResults = data.map(resume => {\n            var _resumeData$PersonalI6, _resumeData$PersonalI7, _resumeData$PersonalI8, _resumeData$PersonalI9, _resumeData$PersonalI0, _resumeData$WorkExper2, _resumeData$Education3, _resumeData$Education4;\n            // Handle the nested Resume structure from MongoDB\n            const resumeData = resume.Resume || resume;\n            return {\n              id: resume._id || Math.random().toString(36).substr(2, 9),\n              name: ((_resumeData$PersonalI6 = resumeData.PersonalInformation) === null || _resumeData$PersonalI6 === void 0 ? void 0 : _resumeData$PersonalI6.FullName) || resumeData.name || 'Unknown',\n              title: ((_resumeData$PersonalI7 = resumeData.PersonalInformation) === null || _resumeData$PersonalI7 === void 0 ? void 0 : _resumeData$PersonalI7.Designation) || resumeData.title || 'No title',\n              email: ((_resumeData$PersonalI8 = resumeData.PersonalInformation) === null || _resumeData$PersonalI8 === void 0 ? void 0 : _resumeData$PersonalI8.Email) || resumeData.email || 'No email',\n              phone: ((_resumeData$PersonalI9 = resumeData.PersonalInformation) === null || _resumeData$PersonalI9 === void 0 ? void 0 : _resumeData$PersonalI9.ContactNumber) || resumeData.phone || 'No phone',\n              location: ((_resumeData$PersonalI0 = resumeData.PersonalInformation) === null || _resumeData$PersonalI0 === void 0 ? void 0 : _resumeData$PersonalI0.Address) || resumeData.location || 'No location',\n              experience: resumeData.TotalWorkExperienceInYears ? `${resumeData.TotalWorkExperienceInYears} years` : (_resumeData$WorkExper2 = resumeData.WorkExperience) !== null && _resumeData$WorkExper2 !== void 0 && _resumeData$WorkExper2.length ? `${resumeData.WorkExperience.length}+ positions` : resumeData.experience || 'No experience data',\n              skills: resumeData.Skills || resumeData.skills || [],\n              education: (_resumeData$Education3 = resumeData.Education) !== null && _resumeData$Education3 !== void 0 && _resumeData$Education3.length ? ((_resumeData$Education4 = resumeData.Education[0]) === null || _resumeData$Education4 === void 0 ? void 0 : _resumeData$Education4.Degree) || 'Education available' : resumeData.education || 'No education data',\n              summary: resumeData.Objective || resumeData.Summary || resumeData.summary || 'No summary available',\n              score: 90,\n              // Default score for show all\n              rawData: resume // Keep original data for reference\n            };\n          });\n        } else if (data.error) {\n          throw new Error(data.error);\n        } else {\n          // Fallback to mock data if API doesn't return proper results\n          transformedResults = mockResults;\n        }\n        setSearchResults(transformedResults);\n        setCurrentPage(1);\n        setTotalCount(transformedResults.length);\n        setTotalPages(Math.ceil(transformedResults.length / resultsPerPage));\n        setSearchQuery('');\n        toast.success(`📊 Loaded ${transformedResults.length} resumes from database`);\n      } else {\n        throw new Error(data.error || 'Failed to load resumes');\n      }\n    } catch (error) {\n      console.error('Load resumes error:', error);\n\n      // Fallback to mock data on network error\n      setSearchResults(mockResults);\n      setCurrentPage(1);\n      setTotalCount(mockResults.length);\n      setTotalPages(Math.ceil(mockResults.length / resultsPerPage));\n      setSearchQuery('');\n      toast.warning(`Using demo data - Showing ${mockResults.length} sample resumes`);\n    } finally {\n      setIsSearching(false);\n    }\n  };\n  const handlePageChange = page => {\n    if (page >= 1 && page <= totalPages) {\n      handleShowAll(page);\n    }\n  };\n  const filteredResults = searchResults.filter(result => {\n    // Basic text filter\n    if (filterQuery) {\n      const matchesText = result.name.toLowerCase().includes(filterQuery.toLowerCase()) || result.title.toLowerCase().includes(filterQuery.toLowerCase()) || result.location.toLowerCase().includes(filterQuery.toLowerCase());\n      if (!matchesText) return false;\n    }\n\n    // Advanced filters\n    const filters = advancedFilters;\n\n    // Experience filter\n    if (filters.minExperience || filters.maxExperience) {\n      var _result$rawData, _result$rawData$Resum;\n      const experienceYears = ((_result$rawData = result.rawData) === null || _result$rawData === void 0 ? void 0 : (_result$rawData$Resum = _result$rawData.Resume) === null || _result$rawData$Resum === void 0 ? void 0 : _result$rawData$Resum.TotalWorkExperienceInYears) || extractExperienceYears(result.experience) || 0;\n      if (filters.minExperience && experienceYears < parseInt(filters.minExperience)) return false;\n      if (filters.maxExperience && experienceYears > parseInt(filters.maxExperience)) return false;\n    }\n\n    // Education filter\n    if (filters.education) {\n      var _result$rawData2, _result$rawData2$Resu, _result$rawData2$Resu2;\n      const hasEducation = ((_result$rawData2 = result.rawData) === null || _result$rawData2 === void 0 ? void 0 : (_result$rawData2$Resu = _result$rawData2.Resume) === null || _result$rawData2$Resu === void 0 ? void 0 : (_result$rawData2$Resu2 = _result$rawData2$Resu.Education) === null || _result$rawData2$Resu2 === void 0 ? void 0 : _result$rawData2$Resu2.some(edu => (edu.Degree || '').toLowerCase().includes(filters.education.toLowerCase()))) || (result.education || '').toLowerCase().includes(filters.education.toLowerCase());\n      if (!hasEducation) return false;\n    }\n\n    // Location filter\n    if (filters.location) {\n      const matchesLocation = (result.location || '').toLowerCase().includes(filters.location.toLowerCase());\n      if (!matchesLocation) return false;\n    }\n\n    // Skills filter\n    if (filters.skills) {\n      var _result$skills, _result$rawData3, _result$rawData3$Resu;\n      const skillsText = filters.skills.toLowerCase();\n      const hasSkill = ((_result$skills = result.skills) === null || _result$skills === void 0 ? void 0 : _result$skills.some(skill => skill.toLowerCase().includes(skillsText))) || (((_result$rawData3 = result.rawData) === null || _result$rawData3 === void 0 ? void 0 : (_result$rawData3$Resu = _result$rawData3.Resume) === null || _result$rawData3$Resu === void 0 ? void 0 : _result$rawData3$Resu.Skills) || []).some(skill => skill.toLowerCase().includes(skillsText));\n      if (!hasSkill) return false;\n    }\n\n    // Institution filter\n    if (filters.institution) {\n      var _result$rawData4, _result$rawData4$Resu, _result$rawData4$Resu2;\n      const hasInstitution = (_result$rawData4 = result.rawData) === null || _result$rawData4 === void 0 ? void 0 : (_result$rawData4$Resu = _result$rawData4.Resume) === null || _result$rawData4$Resu === void 0 ? void 0 : (_result$rawData4$Resu2 = _result$rawData4$Resu.Education) === null || _result$rawData4$Resu2 === void 0 ? void 0 : _result$rawData4$Resu2.some(edu => (edu.Institution || edu.School || '').toLowerCase().includes(filters.institution.toLowerCase()));\n      if (!hasInstitution) return false;\n    }\n\n    // Graduation year filter\n    if (filters.graduationYear) {\n      var _result$rawData5, _result$rawData5$Resu, _result$rawData5$Resu2;\n      const hasGradYear = (_result$rawData5 = result.rawData) === null || _result$rawData5 === void 0 ? void 0 : (_result$rawData5$Resu = _result$rawData5.Resume) === null || _result$rawData5$Resu === void 0 ? void 0 : (_result$rawData5$Resu2 = _result$rawData5$Resu.Education) === null || _result$rawData5$Resu2 === void 0 ? void 0 : _result$rawData5$Resu2.some(edu => (edu.GraduationYear || edu.Year || '').toString().includes(filters.graduationYear));\n      if (!hasGradYear) return false;\n    }\n\n    // Company filter\n    if (filters.company) {\n      var _result$rawData6, _result$rawData6$Resu, _result$rawData6$Resu2;\n      const hasCompany = (_result$rawData6 = result.rawData) === null || _result$rawData6 === void 0 ? void 0 : (_result$rawData6$Resu = _result$rawData6.Resume) === null || _result$rawData6$Resu === void 0 ? void 0 : (_result$rawData6$Resu2 = _result$rawData6$Resu.WorkExperience) === null || _result$rawData6$Resu2 === void 0 ? void 0 : _result$rawData6$Resu2.some(exp => (exp.Company || exp.CompanyName || exp.Organization || '').toLowerCase().includes(filters.company.toLowerCase()));\n      if (!hasCompany) return false;\n    }\n\n    // Minimum marks filter\n    if (filters.minMarks) {\n      var _result$rawData7, _result$rawData7$Resu, _result$rawData7$Resu2;\n      const hasMinMarks = (_result$rawData7 = result.rawData) === null || _result$rawData7 === void 0 ? void 0 : (_result$rawData7$Resu = _result$rawData7.Resume) === null || _result$rawData7$Resu === void 0 ? void 0 : (_result$rawData7$Resu2 = _result$rawData7$Resu.Education) === null || _result$rawData7$Resu2 === void 0 ? void 0 : _result$rawData7$Resu2.some(edu => {\n        const marks = parseFloat(edu['GPA/Marks/%']) || 0;\n        return marks >= parseFloat(filters.minMarks);\n      });\n      if (!hasMinMarks) return false;\n    }\n    return true;\n  });\n\n  // Helper function to extract experience years from text\n  const extractExperienceYears = experienceText => {\n    if (!experienceText) return 0;\n    const match = experienceText.match(/(\\d+)\\+?\\s*years?/i);\n    return match ? parseInt(match[1]) : 0;\n  };\n  const handleAdvancedFilterChange = (filterName, value) => {\n    setAdvancedFilters(prev => ({\n      ...prev,\n      [filterName]: value\n    }));\n  };\n  const clearAllFilters = () => {\n    setFilterQuery('');\n    setAdvancedFilters({\n      minExperience: '',\n      maxExperience: '',\n      education: '',\n      location: '',\n      skills: '',\n      minMarks: '',\n      institution: '',\n      graduationYear: '',\n      company: ''\n    });\n  };\n  const hasActiveFilters = () => {\n    return filterQuery || Object.values(advancedFilters).some(value => value !== '');\n  };\n  const handleDownload = resume => {\n    try {\n      var _resume$rawData, _resume$rawData$Resum, _resume$rawData2, _resume$rawData2$Resu, _resume$rawData3, _resume$rawData3$Resu, _resume$rawData4, _resume$rawData4$Resu, _resume$rawData5, _resume$rawData5$Resu, _resume$rawData6, _resume$rawData6$Resu, _resume$rawData7, _resume$rawData7$Resu, _resume$rawData8, _resume$rawData8$Resu, _resume$rawData8$Resu2;\n      // Create a comprehensive text-based resume content\n      const resumeContent = `\n${resume.name}\n${resume.title}\n${resume.email} | ${resume.phone} | ${resume.location}\n\nPROFESSIONAL SUMMARY\n${resume.summary}\n\nWORK EXPERIENCE\n${(_resume$rawData = resume.rawData) !== null && _resume$rawData !== void 0 && (_resume$rawData$Resum = _resume$rawData.Resume) !== null && _resume$rawData$Resum !== void 0 && _resume$rawData$Resum.WorkExperience ? resume.rawData.Resume.WorkExperience.map(exp => `${exp.JobTitle || exp.Position} at ${exp.Company || exp.Organization}\nDuration: ${exp.Duration || exp.Years || 'Not specified'}\n${exp.Description ? 'Description: ' + exp.Description : ''}\n${exp.Responsibilities ? 'Responsibilities: ' + exp.Responsibilities : ''}\n`).join('\\n') : resume.experience}\n\n${(_resume$rawData2 = resume.rawData) !== null && _resume$rawData2 !== void 0 && (_resume$rawData2$Resu = _resume$rawData2.Resume) !== null && _resume$rawData2$Resu !== void 0 && _resume$rawData2$Resu.TotalWorkExperienceInYears ? `Total Experience: ${resume.rawData.Resume.TotalWorkExperienceInYears} years\\n` : ''}\n\nEDUCATION\n${(_resume$rawData3 = resume.rawData) !== null && _resume$rawData3 !== void 0 && (_resume$rawData3$Resu = _resume$rawData3.Resume) !== null && _resume$rawData3$Resu !== void 0 && _resume$rawData3$Resu.Education ? resume.rawData.Resume.Education.map(edu => `${edu.Degree || 'Degree'} from ${edu.Institution || edu.School || 'Institution'}\nGraduation Year: ${edu.GraduationYear || edu.Year || 'Not specified'}\n${edu['GPA/Marks/%'] && edu['GPA/Marks/%'] > 0 ? 'Marks: ' + edu['GPA/Marks/%'] + '%' : ''}\n`).join('\\n') : resume.education}\n\nSKILLS & TECHNOLOGIES\n${resume.skills.join(', ')}\n\n${(_resume$rawData4 = resume.rawData) !== null && _resume$rawData4 !== void 0 && (_resume$rawData4$Resu = _resume$rawData4.Resume) !== null && _resume$rawData4$Resu !== void 0 && _resume$rawData4$Resu.Languages && resume.rawData.Resume.Languages.length > 0 ? `LANGUAGES\\n${resume.rawData.Resume.Languages.join(', ')}\\n` : ''}\n\n${(_resume$rawData5 = resume.rawData) !== null && _resume$rawData5 !== void 0 && (_resume$rawData5$Resu = _resume$rawData5.Resume) !== null && _resume$rawData5$Resu !== void 0 && _resume$rawData5$Resu.Projects && resume.rawData.Resume.Projects.length > 0 ? `PROJECTS\\n${resume.rawData.Resume.Projects.map(project => `${project.Name || project.Title || 'Project'}\n${project.Description ? 'Description: ' + project.Description : ''}\n${project.Technologies ? 'Technologies: ' + project.Technologies : ''}\n`).join('\\n')}` : ''}\n\n${(_resume$rawData6 = resume.rawData) !== null && _resume$rawData6 !== void 0 && (_resume$rawData6$Resu = _resume$rawData6.Resume) !== null && _resume$rawData6$Resu !== void 0 && _resume$rawData6$Resu.Certifications && resume.rawData.Resume.Certifications.length > 0 ? `CERTIFICATIONS\\n${resume.rawData.Resume.Certifications.map(cert => `${cert.Name || cert.Title || 'Certification'}\n${cert.IssuingOrganization ? 'Issued by: ' + cert.IssuingOrganization : ''}\n${cert.IssueDate ? 'Date: ' + cert.IssueDate : ''}\n`).join('\\n')}` : ''}\n\n${(_resume$rawData7 = resume.rawData) !== null && _resume$rawData7 !== void 0 && (_resume$rawData7$Resu = _resume$rawData7.Resume) !== null && _resume$rawData7$Resu !== void 0 && _resume$rawData7$Resu.Achievements && resume.rawData.Resume.Achievements.length > 0 ? `ACHIEVEMENTS\\n${resume.rawData.Resume.Achievements.map(achievement => typeof achievement === 'string' ? achievement : `${achievement.AchievementName || achievement.Name || 'Achievement'}\n${achievement.IssueDate ? 'Date: ' + achievement.IssueDate : ''}\n${achievement.IssuingOrganization ? 'Organization: ' + achievement.IssuingOrganization : ''}\n${achievement.Description ? 'Description: ' + achievement.Description : ''}\n`).join('\\n')}` : ''}\n\n${(_resume$rawData8 = resume.rawData) !== null && _resume$rawData8 !== void 0 && (_resume$rawData8$Resu = _resume$rawData8.Resume) !== null && _resume$rawData8$Resu !== void 0 && (_resume$rawData8$Resu2 = _resume$rawData8$Resu.PersonalInformation) !== null && _resume$rawData8$Resu2 !== void 0 && _resume$rawData8$Resu2.LinkedIn ? `ADDITIONAL INFORMATION\\nLinkedIn: ${resume.rawData.Resume.PersonalInformation.LinkedIn}` : ''}\n\nGenerated on: ${new Date().toLocaleDateString()}\n      `.trim();\n\n      // Create and download the file\n      const blob = new Blob([resumeContent], {\n        type: 'text/plain'\n      });\n      const url = URL.createObjectURL(blob);\n      const link = document.createElement('a');\n      link.href = url;\n      link.download = `${resume.name.replace(/\\s+/g, '_')}_Resume.txt`;\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n      URL.revokeObjectURL(url);\n      toast.success(`Downloaded resume for ${resume.name}`);\n    } catch (error) {\n      console.error('Download error:', error);\n      toast.error('Failed to download resume. Please try again.');\n    }\n  };\n  const handleView = resume => {\n    setSelectedResume(resume);\n    setIsModalOpen(true);\n    toast.info(`Opening detailed view for ${resume.name}`);\n  };\n  const handleCloseModal = () => {\n    setIsModalOpen(false);\n    setSelectedResume(null);\n  };\n  const handleEmailClick = (email, name) => {\n    window.open(`mailto:${email}?subject=Regarding your resume&body=Hi ${name},%0D%0A%0D%0AI found your resume and would like to discuss potential opportunities.%0D%0A%0D%0ABest regards`, '_blank');\n    toast.info(`Opening email client to contact ${name}`);\n  };\n  const handlePhoneClick = (phone, name) => {\n    if (navigator.userAgent.match(/Mobile|Android|iPhone|iPad/)) {\n      window.open(`tel:${phone}`, '_blank');\n    } else {\n      navigator.clipboard.writeText(phone).then(() => {\n        toast.success(`Phone number copied: ${phone}`);\n      }).catch(() => {\n        toast.info(`Phone: ${phone}`);\n      });\n    }\n  };\n  const handleExportAll = () => {\n    if (filteredResults.length === 0) {\n      toast.warning('No results to export');\n      return;\n    }\n    try {\n      const exportData = filteredResults.map(resume => ({\n        name: resume.name,\n        title: resume.title,\n        email: resume.email,\n        phone: resume.phone,\n        location: resume.location,\n        experience: resume.experience,\n        education: resume.education,\n        skills: resume.skills.join(', '),\n        summary: resume.summary,\n        matchScore: resume.score\n      }));\n      const csvContent = ['Name,Title,Email,Phone,Location,Experience,Education,Skills,Summary,Match Score', ...exportData.map(row => Object.values(row).map(value => `\"${String(value).replace(/\"/g, '\"\"')}\"`).join(','))].join('\\n');\n      const blob = new Blob([csvContent], {\n        type: 'text/csv'\n      });\n      const url = URL.createObjectURL(blob);\n      const link = document.createElement('a');\n      link.href = url;\n      link.download = `Resume_Search_Results_${new Date().toISOString().split('T')[0]}.csv`;\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n      URL.revokeObjectURL(url);\n      toast.success(`Exported ${filteredResults.length} resumes to CSV`);\n    } catch (error) {\n      console.error('Export error:', error);\n      toast.error('Failed to export results. Please try again.');\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"search-page\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"page-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        children: \"Search Resumes\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 620,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Use natural language to find the perfect candidates for your needs\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 621,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 619,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"search-section\",\n      children: [/*#__PURE__*/_jsxDEV(\"form\", {\n        onSubmit: handleSearch,\n        className: \"search-form\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"search-input-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"search-input-wrapper\",\n            children: [/*#__PURE__*/_jsxDEV(FiSearch, {\n              className: \"search-icon\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 629,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              placeholder: \"e.g., 'Find Python developers with 5+ years experience' or 'Show frontend engineers in California'\",\n              value: searchQuery,\n              onChange: e => setSearchQuery(e.target.value),\n              className: \"search-input\",\n              disabled: isSearching\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 630,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 628,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"search-buttons\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"submit\",\n              className: \"btn btn-primary\",\n              disabled: isSearching,\n              children: isSearching ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"loading-spinner\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 647,\n                  columnNumber: 21\n                }, this), \"Searching...\"]\n              }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(FiSearch, {\n                  size: 16\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 652,\n                  columnNumber: 21\n                }, this), \"Search\"]\n              }, void 0, true)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 640,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              className: \"btn btn-secondary\",\n              onClick: handleShowAll,\n              disabled: isSearching,\n              children: \"Show All\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 657,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 639,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 627,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 626,\n        columnNumber: 9\n      }, this), searchResults.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"filter-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"filter-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"basic-filter-wrapper\",\n            children: [/*#__PURE__*/_jsxDEV(FiFilter, {\n              className: \"filter-icon\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 674,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              placeholder: \"Quick filter by name, title, or location...\",\n              value: filterQuery,\n              onChange: e => setFilterQuery(e.target.value),\n              className: \"filter-input\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 675,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 673,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"filter-controls\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              className: `btn btn-secondary filter-toggle ${showFilters ? 'active' : ''}`,\n              onClick: () => setShowFilters(!showFilters),\n              children: [/*#__PURE__*/_jsxDEV(FiSettings, {\n                size: 16\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 689,\n                columnNumber: 19\n              }, this), \"Advanced Filters\", showFilters ? /*#__PURE__*/_jsxDEV(FiChevronUp, {\n                size: 16\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 691,\n                columnNumber: 34\n              }, this) : /*#__PURE__*/_jsxDEV(FiChevronDown, {\n                size: 16\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 691,\n                columnNumber: 62\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 684,\n              columnNumber: 17\n            }, this), hasActiveFilters() && /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              className: \"btn btn-outline clear-filters\",\n              onClick: clearAllFilters,\n              children: [/*#__PURE__*/_jsxDEV(FiX, {\n                size: 16\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 699,\n                columnNumber: 21\n              }, this), \"Clear All\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 694,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 683,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 672,\n          columnNumber: 13\n        }, this), showFilters && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"advanced-filters\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"filters-grid\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"filter-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Experience (Years)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 711,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"range-inputs\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"number\",\n                  placeholder: \"Min\",\n                  value: advancedFilters.minExperience,\n                  onChange: e => handleAdvancedFilterChange('minExperience', e.target.value),\n                  className: \"filter-input small\",\n                  min: \"0\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 713,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"to\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 721,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"number\",\n                  placeholder: \"Max\",\n                  value: advancedFilters.maxExperience,\n                  onChange: e => handleAdvancedFilterChange('maxExperience', e.target.value),\n                  className: \"filter-input small\",\n                  min: \"0\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 722,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 712,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 710,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"filter-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Education Level\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 734,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                placeholder: \"e.g., Bachelor, Master, PhD\",\n                value: advancedFilters.education,\n                onChange: e => handleAdvancedFilterChange('education', e.target.value),\n                className: \"filter-input\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 735,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 733,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"filter-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Location\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 745,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                placeholder: \"e.g., Mumbai, Delhi, Bangalore\",\n                value: advancedFilters.location,\n                onChange: e => handleAdvancedFilterChange('location', e.target.value),\n                className: \"filter-input\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 746,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 744,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"filter-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Skills\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 756,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                placeholder: \"e.g., Python, React, Java\",\n                value: advancedFilters.skills,\n                onChange: e => handleAdvancedFilterChange('skills', e.target.value),\n                className: \"filter-input\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 757,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 755,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"filter-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Institution\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 767,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                placeholder: \"e.g., IIT, University name\",\n                value: advancedFilters.institution,\n                onChange: e => handleAdvancedFilterChange('institution', e.target.value),\n                className: \"filter-input\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 768,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 766,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"filter-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Graduation Year\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 778,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                placeholder: \"e.g., 2020, 2021\",\n                value: advancedFilters.graduationYear,\n                onChange: e => handleAdvancedFilterChange('graduationYear', e.target.value),\n                className: \"filter-input\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 779,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 777,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"filter-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Company\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 789,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                placeholder: \"e.g., Google, Microsoft, TCS\",\n                value: advancedFilters.company,\n                onChange: e => handleAdvancedFilterChange('company', e.target.value),\n                className: \"filter-input\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 790,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 788,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"filter-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Minimum Marks (%)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 800,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"number\",\n                placeholder: \"e.g., 75, 80\",\n                value: advancedFilters.minMarks,\n                onChange: e => handleAdvancedFilterChange('minMarks', e.target.value),\n                className: \"filter-input\",\n                min: \"0\",\n                max: \"100\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 801,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 799,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 709,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 708,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"results-count\",\n          children: [filteredResults.length, \" of \", searchResults.length, \" results\", hasActiveFilters() && /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"filtered-indicator\",\n            children: \" (filtered)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 817,\n            columnNumber: 38\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 815,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 671,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 625,\n      columnNumber: 7\n    }, this), searchResults.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"results-section\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"results-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Search Results\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 827,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"results-actions\",\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"btn btn-secondary\",\n            onClick: handleExportAll,\n            title: `Export ${filteredResults.length} results to CSV`,\n            children: [/*#__PURE__*/_jsxDEV(FiDownload, {\n              size: 16\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 834,\n              columnNumber: 17\n            }, this), \"Export All (\", filteredResults.length, \")\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 829,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 828,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 826,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"results-grid\",\n        children: filteredResults.map(resume => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"resume-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"resume-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"resume-avatar\",\n              children: /*#__PURE__*/_jsxDEV(FiUser, {\n                size: 24\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 845,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 844,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"resume-info\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"resume-name\",\n                children: resume.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 848,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"resume-title\",\n                children: resume.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 849,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 847,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 843,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"resume-details\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"detail-item location-item\",\n              children: [/*#__PURE__*/_jsxDEV(FiMapPin, {\n                size: 14\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 855,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: resume.location\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 856,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 854,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"detail-item email-item clickable\",\n              onClick: () => handleEmailClick(resume.email, resume.name),\n              title: `Send email to ${resume.name}`,\n              children: [/*#__PURE__*/_jsxDEV(FiMail, {\n                size: 14\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 863,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: resume.email\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 864,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(FiExternalLink, {\n                size: 12,\n                className: \"external-icon\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 865,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 858,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"detail-item phone-item clickable\",\n              onClick: () => handlePhoneClick(resume.phone, resume.name),\n              title: `Call ${resume.name}`,\n              children: [/*#__PURE__*/_jsxDEV(FiPhone, {\n                size: 14\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 872,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: resume.phone\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 873,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(FiExternalLink, {\n                size: 12,\n                className: \"external-icon\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 874,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 867,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 853,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"resume-content\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"content-row\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"content-section\",\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  children: \"Experience\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 881,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: resume.experience\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 882,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 880,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"content-section\",\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  children: \"Education\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 885,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: resume.education\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 886,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 884,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 879,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"content-section\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                children: \"Skills\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 890,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"skills-list\",\n                children: resume.skills.map((skill, index) => /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"skill-tag\",\n                  children: skill\n                }, index, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 893,\n                  columnNumber: 25\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 891,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 889,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 878,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"resume-actions\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"btn btn-secondary\",\n              onClick: () => handleView(resume),\n              children: [/*#__PURE__*/_jsxDEV(FiEye, {\n                size: 16\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 904,\n                columnNumber: 21\n              }, this), \"View Details\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 900,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"btn btn-primary\",\n              onClick: () => handleDownload(resume),\n              children: [/*#__PURE__*/_jsxDEV(FiDownload, {\n                size: 16\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 911,\n                columnNumber: 21\n              }, this), \"Download\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 907,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 899,\n            columnNumber: 17\n          }, this)]\n        }, resume.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 842,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 840,\n        columnNumber: 11\n      }, this), totalPages > 1 && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"pagination-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"pagination-info\",\n          children: [\"Showing \", (currentPage - 1) * resultsPerPage + 1, \" to \", Math.min(currentPage * resultsPerPage, totalCount), \" of \", totalCount, \" results\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 922,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"pagination-controls\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"btn btn-secondary pagination-btn\",\n            onClick: () => handlePageChange(currentPage - 1),\n            disabled: currentPage === 1,\n            children: \"Previous\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 926,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"page-numbers\",\n            children: Array.from({\n              length: Math.min(5, totalPages)\n            }, (_, i) => {\n              let pageNum;\n              if (totalPages <= 5) {\n                pageNum = i + 1;\n              } else if (currentPage <= 3) {\n                pageNum = i + 1;\n              } else if (currentPage >= totalPages - 2) {\n                pageNum = totalPages - 4 + i;\n              } else {\n                pageNum = currentPage - 2 + i;\n              }\n              return /*#__PURE__*/_jsxDEV(\"button\", {\n                className: `btn pagination-btn ${currentPage === pageNum ? 'btn-primary' : 'btn-secondary'}`,\n                onClick: () => handlePageChange(pageNum),\n                children: pageNum\n              }, pageNum, false, {\n                fileName: _jsxFileName,\n                lineNumber: 948,\n                columnNumber: 23\n              }, this);\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 934,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"btn btn-secondary pagination-btn\",\n            onClick: () => handlePageChange(currentPage + 1),\n            disabled: currentPage === totalPages,\n            children: \"Next\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 959,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 925,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 921,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 825,\n      columnNumber: 9\n    }, this) : !isSearching ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"empty-state\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"empty-icon\",\n        children: /*#__PURE__*/_jsxDEV(FiSearch, {\n          size: 48\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 973,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 972,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n        children: \"Ready to Search!\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 975,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Use the search box above to find resumes using natural language, or click \\\"Show All\\\" to view all resumes in the database.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 976,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"search-examples\",\n        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n          children: \"Try searching for:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 978,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n          children: [/*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"\\\"Find Python developers\\\"\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 980,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"\\\"Show teachers with 5+ years experience\\\"\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 981,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"\\\"Software engineers in Mumbai\\\"\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 982,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 979,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 977,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 971,\n      columnNumber: 9\n    }, this) : null, /*#__PURE__*/_jsxDEV(ResumeModal, {\n      resume: selectedResume,\n      isOpen: isModalOpen,\n      onClose: handleCloseModal\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 989,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 618,\n    columnNumber: 5\n  }, this);\n};\n_s(SearchResumes, \"uvVHvTgqjBYZkn8TcWc0iS1zcVo=\");\n_c = SearchResumes;\nexport default SearchResumes;\nvar _c;\n$RefreshReg$(_c, \"SearchResumes\");", "map": {"version": 3, "names": ["React", "useState", "FiSearch", "<PERSON><PERSON><PERSON><PERSON>", "FiDownload", "FiEye", "FiUser", "FiMapPin", "FiMail", "FiPhone", "FiExternalLink", "FiStar", "FiX", "FiSettings", "FiChevronDown", "FiChevronUp", "toast", "ResumeModal", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "SearchResumes", "_s", "searchQuery", "setSearch<PERSON>uery", "isSearching", "setIsSearching", "searchResults", "setSearchResults", "filterQuery", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "showFilters", "setShowFilters", "advancedFilters", "setAdvancedFilters", "minExperience", "maxExperience", "education", "location", "skills", "minMarks", "institution", "graduationYear", "company", "selectedResume", "setSelectedResume", "isModalOpen", "setIsModalOpen", "currentPage", "setCurrentPage", "totalPages", "setTotalPages", "totalCount", "setTotalCount", "resultsPerPage", "mockResults", "id", "name", "title", "email", "phone", "experience", "summary", "score", "rawData", "Resume", "PersonalInformation", "FullName", "Email", "ContactNumber", "Address", "WorkExperience", "Role", "CompanyName", "StartYear", "EndYear", "Education", "Degree", "Institution", "GraduationYear", "Skills", "TotalWorkExperienceInYears", "handleSearch", "e", "preventDefault", "trim", "warning", "response", "fetch", "encodeURIComponent", "method", "headers", "data", "json", "ok", "transformedResults", "listOfDict", "Array", "isArray", "map", "resume", "_resumeData$PersonalI", "_resumeData$PersonalI2", "_resumeData$PersonalI3", "_resumeData$PersonalI4", "_resumeData$PersonalI5", "_resumeData$WorkExper", "_resumeData$Education", "_resumeData$Education2", "resumeData", "_id", "Math", "random", "toString", "substr", "Designation", "length", "Objective", "Summary", "filter", "toLowerCase", "includes", "some", "skill", "result", "success", "query", "console", "log", "Error", "error", "fallback<PERSON><PERSON><PERSON>s", "handleShowAll", "page", "_resumeData$PersonalI6", "_resumeData$PersonalI7", "_resumeData$PersonalI8", "_resumeData$PersonalI9", "_resumeData$PersonalI0", "_resumeData$WorkExper2", "_resumeData$Education3", "_resumeData$Education4", "ceil", "handlePageChange", "filteredResults", "matchesText", "filters", "_result$rawData", "_result$rawData$Resum", "experienceYears", "extractExperienceYears", "parseInt", "_result$rawData2", "_result$rawData2$Resu", "_result$rawData2$Resu2", "hasEducation", "edu", "matchesLocation", "_result$skills", "_result$rawData3", "_result$rawData3$Resu", "skillsText", "hasSkill", "_result$rawData4", "_result$rawData4$Resu", "_result$rawData4$Resu2", "hasInstitution", "School", "_result$rawData5", "_result$rawData5$Resu", "_result$rawData5$Resu2", "hasGradYear", "Year", "_result$rawData6", "_result$rawData6$Resu", "_result$rawData6$Resu2", "hasCompany", "exp", "Company", "Organization", "_result$rawData7", "_result$rawData7$Resu", "_result$rawData7$Resu2", "hasMinMarks", "marks", "parseFloat", "experienceText", "match", "handleAdvancedFilterChange", "filterName", "value", "prev", "clearAllFilters", "hasActiveFilters", "Object", "values", "handleDownload", "_resume$rawData", "_resume$rawData$Resum", "_resume$rawData2", "_resume$rawData2$Resu", "_resume$rawData3", "_resume$rawData3$Resu", "_resume$rawData4", "_resume$rawData4$Resu", "_resume$rawData5", "_resume$rawData5$Resu", "_resume$rawData6", "_resume$rawData6$Resu", "_resume$rawData7", "_resume$rawData7$Resu", "_resume$rawData8", "_resume$rawData8$Resu", "_resume$rawData8$Resu2", "<PERSON><PERSON><PERSON>nt", "JobTitle", "Position", "Duration", "Years", "Description", "Responsibilities", "join", "Languages", "Projects", "project", "Name", "Title", "Technologies", "Certifications", "cert", "IssuingOrganization", "IssueDate", "Achievements", "achievement", "AchievementName", "LinkedIn", "Date", "toLocaleDateString", "blob", "Blob", "type", "url", "URL", "createObjectURL", "link", "document", "createElement", "href", "download", "replace", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL", "handleView", "info", "handleCloseModal", "handleEmailClick", "window", "open", "handlePhoneClick", "navigator", "userAgent", "clipboard", "writeText", "then", "catch", "handleExportAll", "exportData", "matchScore", "csv<PERSON><PERSON>nt", "row", "String", "toISOString", "split", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSubmit", "placeholder", "onChange", "target", "disabled", "size", "onClick", "min", "max", "index", "from", "_", "i", "pageNum", "isOpen", "onClose", "_c", "$RefreshReg$"], "sources": ["D:/Projects/SQL-Agent-Task-Submission-main/SQL-Agent-Task-Submission-main/src/pages/SearchResumes.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { <PERSON><PERSON>earch, Fi<PERSON>ilter, <PERSON>Down<PERSON>, <PERSON>Eye, Fi<PERSON>ser, FiMapPin, FiMail, FiPhone, FiExternalLink, FiStar, FiX, FiSettings, FiChevronDown, FiChevronUp } from 'react-icons/fi';\nimport { toast } from 'react-toastify';\nimport ResumeModal from '../components/ResumeModal';\nimport './SearchResumes.css';\n\nconst SearchResumes = () => {\n  const [searchQuery, setSearchQuery] = useState('');\n  const [isSearching, setIsSearching] = useState(false);\n  const [searchResults, setSearchResults] = useState([]);\n  const [filterQuery, setFilterQuery] = useState('');\n  const [showFilters, setShowFilters] = useState(false);\n  const [advancedFilters, setAdvancedFilters] = useState({\n    minExperience: '',\n    maxExperience: '',\n    education: '',\n    location: '',\n    skills: '',\n    minMarks: '',\n    institution: '',\n    graduationYear: '',\n    company: ''\n  });\n  const [selectedResume, setSelectedResume] = useState(null);\n  const [isModalOpen, setIsModalOpen] = useState(false);\n  const [currentPage, setCurrentPage] = useState(1);\n  const [totalPages, setTotalPages] = useState(1);\n  const [totalCount, setTotalCount] = useState(0);\n  const resultsPerPage = 20;\n\n  // Mock data for demonstration\n  const mockResults = [\n    {\n      id: 1,\n      name: 'John Doe',\n      title: 'Senior Software Engineer',\n      email: '<EMAIL>',\n      phone: '+****************',\n      location: 'San Francisco, CA',\n      experience: '5+ years',\n      skills: ['Python', 'React', 'Node.js', 'AWS'],\n      education: 'MS Computer Science',\n      summary: 'Experienced software engineer with expertise in full-stack development...',\n      score: 95,\n      rawData: {\n        Resume: {\n          PersonalInformation: {\n            FullName: 'John Doe',\n            Email: '<EMAIL>',\n            ContactNumber: '+****************',\n            Address: 'San Francisco, CA'\n          },\n          WorkExperience: [\n            {\n              Role: 'Senior Software Engineer',\n              CompanyName: 'Tech Corp',\n              StartYear: '2019',\n              EndYear: 'Present',\n              'Description/Responsibility': 'Led development of scalable web applications using React and Node.js'\n            },\n            {\n              Role: 'Software Engineer',\n              CompanyName: 'StartupXYZ',\n              StartYear: '2017',\n              EndYear: '2019',\n              'Description/Responsibility': 'Developed full-stack applications and improved system performance'\n            }\n          ],\n          Education: [\n            {\n              Degree: 'MS Computer Science',\n              Institution: 'Stanford University',\n              GraduationYear: '2017',\n              'GPA/Marks/%': 85\n            }\n          ],\n          Skills: ['Python', 'React', 'Node.js', 'AWS'],\n          TotalWorkExperienceInYears: 5\n        }\n      }\n    },\n    {\n      id: 2,\n      name: 'Jane Smith',\n      title: 'Frontend Developer',\n      email: '<EMAIL>',\n      phone: '+****************',\n      location: 'New York, NY',\n      experience: '3+ years',\n      skills: ['React', 'TypeScript', 'CSS', 'JavaScript'],\n      education: 'BS Computer Science',\n      summary: 'Creative frontend developer passionate about user experience...',\n      score: 88,\n      rawData: {\n        Resume: {\n          PersonalInformation: {\n            FullName: 'Jane Smith',\n            Email: '<EMAIL>',\n            ContactNumber: '+****************',\n            Address: 'New York, NY'\n          },\n          WorkExperience: [\n            {\n              Role: 'Frontend Developer',\n              CompanyName: 'Design Studio',\n              StartYear: '2021',\n              EndYear: 'Present',\n              'Description/Responsibility': 'Created responsive web interfaces using React and modern CSS'\n            }\n          ],\n          Education: [\n            {\n              Degree: 'BS Computer Science',\n              Institution: 'NYU',\n              GraduationYear: '2021',\n              'GPA/Marks/%': 78\n            }\n          ],\n          Skills: ['React', 'TypeScript', 'CSS', 'JavaScript'],\n          TotalWorkExperienceInYears: 3\n        }\n      }\n    },\n    {\n      id: 3,\n      name: 'Mike Johnson',\n      title: 'DevOps Engineer',\n      email: '<EMAIL>',\n      phone: '+****************',\n      location: 'Austin, TX',\n      experience: '4+ years',\n      skills: ['Docker', 'Kubernetes', 'AWS', 'Python'],\n      education: 'BS Information Technology',\n      summary: 'DevOps engineer specializing in cloud infrastructure and automation...',\n      score: 92,\n      rawData: {\n        Resume: {\n          PersonalInformation: {\n            FullName: 'Mike Johnson',\n            Email: '<EMAIL>',\n            ContactNumber: '+****************',\n            Address: 'Austin, TX'\n          },\n          WorkExperience: [\n            {\n              Role: 'DevOps Engineer',\n              CompanyName: 'Cloud Solutions Inc',\n              StartYear: '2020',\n              EndYear: 'Present',\n              'Description/Responsibility': 'Managed cloud infrastructure and automated deployment pipelines'\n            }\n          ],\n          Education: [\n            {\n              Degree: 'BS Information Technology',\n              Institution: 'UT Austin',\n              GraduationYear: '2020',\n              'GPA/Marks/%': 82\n            }\n          ],\n          Skills: ['Docker', 'Kubernetes', 'AWS', 'Python'],\n          TotalWorkExperienceInYears: 4\n        }\n      }\n    }\n  ];\n\n  const handleSearch = async (e) => {\n    e.preventDefault();\n    if (!searchQuery.trim()) {\n      toast.warning('Please enter a search query');\n      return;\n    }\n\n    setIsSearching(true);\n\n    try {\n      // Call the FastAPI backend directly for GPT-powered search\n      const response = await fetch(`http://localhost:8001/query/?naturalQuery=${encodeURIComponent(searchQuery)}`, {\n        method: 'GET',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n      });\n\n      const data = await response.json();\n\n      if (response.ok) {\n        // Handle FastAPI response format: {query: [], listOfDict: [], result: \"\"}\n        let transformedResults = [];\n\n        if (data.listOfDict && Array.isArray(data.listOfDict)) {\n          // Transform the resume data from MongoDB to display format\n          transformedResults = data.listOfDict.map(resume => {\n            // Handle the nested Resume structure from MongoDB\n            const resumeData = resume.Resume || resume;\n\n            return {\n              id: resume._id || Math.random().toString(36).substr(2, 9),\n              name: resumeData.PersonalInformation?.FullName || resumeData.name || 'Unknown',\n              title: resumeData.PersonalInformation?.Designation || resumeData.title || 'No title',\n              email: resumeData.PersonalInformation?.Email || resumeData.email || 'No email',\n              phone: resumeData.PersonalInformation?.ContactNumber || resumeData.phone || 'No phone',\n              location: resumeData.PersonalInformation?.Address || resumeData.location || 'No location',\n              experience: resumeData.TotalWorkExperienceInYears ?\n                `${resumeData.TotalWorkExperienceInYears} years` :\n                resumeData.WorkExperience?.length ?\n                  `${resumeData.WorkExperience.length}+ positions` :\n                  resumeData.experience || 'No experience data',\n              skills: resumeData.Skills || resumeData.skills || [],\n              education: resumeData.Education?.length ?\n                resumeData.Education[0]?.Degree || 'Education available' :\n                resumeData.education || 'No education data',\n              summary: resumeData.Objective || resumeData.Summary || resumeData.summary || 'No summary available',\n              score: 95, // Default score\n              rawData: resume // Keep original data for reference\n            };\n          });\n        } else {\n          // Fallback to mock data if API doesn't return results\n          transformedResults = mockResults.filter(resume =>\n            resume.name.toLowerCase().includes(searchQuery.toLowerCase()) ||\n            resume.title.toLowerCase().includes(searchQuery.toLowerCase()) ||\n            resume.skills.some(skill => skill.toLowerCase().includes(searchQuery.toLowerCase()))\n          );\n        }\n\n        setSearchResults(transformedResults);\n\n        // Show success message with GPT result\n        if (data.result) {\n          toast.success(`🤖 AI Search: ${data.result}`);\n        } else {\n          toast.success(`Found ${transformedResults.length} matching resumes`);\n        }\n\n        // Log GPT response and MongoDB query for debugging\n        if (data.query) {\n          console.log('MongoDB Query Generated by GPT:', data.query);\n          console.log('GPT Result:', data.result);\n        }\n      } else {\n        throw new Error(data.error || 'Search failed');\n      }\n    } catch (error) {\n      console.error('Search error:', error);\n\n      // Fallback to mock data on network error\n      const fallbackResults = mockResults.filter(resume =>\n        resume.name.toLowerCase().includes(searchQuery.toLowerCase()) ||\n        resume.title.toLowerCase().includes(searchQuery.toLowerCase()) ||\n        resume.skills.some(skill => skill.toLowerCase().includes(searchQuery.toLowerCase()))\n      );\n\n      setSearchResults(fallbackResults);\n      toast.warning(`Using demo data - Found ${fallbackResults.length} matching resumes`);\n    } finally {\n      setIsSearching(false);\n    }\n  };\n\n  const handleShowAll = async (page = 1) => {\n    setIsSearching(true);\n    try {\n      // Call the FastAPI backend directly to get all resumes\n      const response = await fetch('http://localhost:8001/showall');\n      const data = await response.json();\n\n      if (response.ok) {\n        let transformedResults = [];\n\n        if (Array.isArray(data)) {\n          // FastAPI /showall returns array directly\n          transformedResults = data.map(resume => {\n            // Handle the nested Resume structure from MongoDB\n            const resumeData = resume.Resume || resume;\n\n            return {\n              id: resume._id || Math.random().toString(36).substr(2, 9),\n              name: resumeData.PersonalInformation?.FullName || resumeData.name || 'Unknown',\n              title: resumeData.PersonalInformation?.Designation || resumeData.title || 'No title',\n              email: resumeData.PersonalInformation?.Email || resumeData.email || 'No email',\n              phone: resumeData.PersonalInformation?.ContactNumber || resumeData.phone || 'No phone',\n              location: resumeData.PersonalInformation?.Address || resumeData.location || 'No location',\n              experience: resumeData.TotalWorkExperienceInYears ?\n                `${resumeData.TotalWorkExperienceInYears} years` :\n                resumeData.WorkExperience?.length ?\n                  `${resumeData.WorkExperience.length}+ positions` :\n                  resumeData.experience || 'No experience data',\n              skills: resumeData.Skills || resumeData.skills || [],\n              education: resumeData.Education?.length ?\n                resumeData.Education[0]?.Degree || 'Education available' :\n                resumeData.education || 'No education data',\n              summary: resumeData.Objective || resumeData.Summary || resumeData.summary || 'No summary available',\n              score: 90, // Default score for show all\n              rawData: resume // Keep original data for reference\n            };\n          });\n        } else if (data.error) {\n          throw new Error(data.error);\n        } else {\n          // Fallback to mock data if API doesn't return proper results\n          transformedResults = mockResults;\n        }\n\n        setSearchResults(transformedResults);\n        setCurrentPage(1);\n        setTotalCount(transformedResults.length);\n        setTotalPages(Math.ceil(transformedResults.length / resultsPerPage));\n        setSearchQuery('');\n\n        toast.success(`📊 Loaded ${transformedResults.length} resumes from database`);\n      } else {\n        throw new Error(data.error || 'Failed to load resumes');\n      }\n    } catch (error) {\n      console.error('Load resumes error:', error);\n\n      // Fallback to mock data on network error\n      setSearchResults(mockResults);\n      setCurrentPage(1);\n      setTotalCount(mockResults.length);\n      setTotalPages(Math.ceil(mockResults.length / resultsPerPage));\n      setSearchQuery('');\n\n      toast.warning(`Using demo data - Showing ${mockResults.length} sample resumes`);\n    } finally {\n      setIsSearching(false);\n    }\n  };\n\n  const handlePageChange = (page) => {\n    if (page >= 1 && page <= totalPages) {\n      handleShowAll(page);\n    }\n  };\n\n  const filteredResults = searchResults.filter(result => {\n    // Basic text filter\n    if (filterQuery) {\n      const matchesText = result.name.toLowerCase().includes(filterQuery.toLowerCase()) ||\n                         result.title.toLowerCase().includes(filterQuery.toLowerCase()) ||\n                         result.location.toLowerCase().includes(filterQuery.toLowerCase());\n      if (!matchesText) return false;\n    }\n\n    // Advanced filters\n    const filters = advancedFilters;\n\n    // Experience filter\n    if (filters.minExperience || filters.maxExperience) {\n      const experienceYears = result.rawData?.Resume?.TotalWorkExperienceInYears ||\n                             extractExperienceYears(result.experience) || 0;\n\n      if (filters.minExperience && experienceYears < parseInt(filters.minExperience)) return false;\n      if (filters.maxExperience && experienceYears > parseInt(filters.maxExperience)) return false;\n    }\n\n    // Education filter\n    if (filters.education) {\n      const hasEducation = result.rawData?.Resume?.Education?.some(edu =>\n        (edu.Degree || '').toLowerCase().includes(filters.education.toLowerCase())\n      ) || (result.education || '').toLowerCase().includes(filters.education.toLowerCase());\n      if (!hasEducation) return false;\n    }\n\n    // Location filter\n    if (filters.location) {\n      const matchesLocation = (result.location || '').toLowerCase().includes(filters.location.toLowerCase());\n      if (!matchesLocation) return false;\n    }\n\n    // Skills filter\n    if (filters.skills) {\n      const skillsText = filters.skills.toLowerCase();\n      const hasSkill = result.skills?.some(skill => skill.toLowerCase().includes(skillsText)) ||\n                      (result.rawData?.Resume?.Skills || []).some(skill =>\n                        skill.toLowerCase().includes(skillsText)\n                      );\n      if (!hasSkill) return false;\n    }\n\n    // Institution filter\n    if (filters.institution) {\n      const hasInstitution = result.rawData?.Resume?.Education?.some(edu =>\n        (edu.Institution || edu.School || '').toLowerCase().includes(filters.institution.toLowerCase())\n      );\n      if (!hasInstitution) return false;\n    }\n\n    // Graduation year filter\n    if (filters.graduationYear) {\n      const hasGradYear = result.rawData?.Resume?.Education?.some(edu =>\n        (edu.GraduationYear || edu.Year || '').toString().includes(filters.graduationYear)\n      );\n      if (!hasGradYear) return false;\n    }\n\n    // Company filter\n    if (filters.company) {\n      const hasCompany = result.rawData?.Resume?.WorkExperience?.some(exp =>\n        (exp.Company || exp.CompanyName || exp.Organization || '').toLowerCase().includes(filters.company.toLowerCase())\n      );\n      if (!hasCompany) return false;\n    }\n\n    // Minimum marks filter\n    if (filters.minMarks) {\n      const hasMinMarks = result.rawData?.Resume?.Education?.some(edu => {\n        const marks = parseFloat(edu['GPA/Marks/%']) || 0;\n        return marks >= parseFloat(filters.minMarks);\n      });\n      if (!hasMinMarks) return false;\n    }\n\n    return true;\n  });\n\n  // Helper function to extract experience years from text\n  const extractExperienceYears = (experienceText) => {\n    if (!experienceText) return 0;\n    const match = experienceText.match(/(\\d+)\\+?\\s*years?/i);\n    return match ? parseInt(match[1]) : 0;\n  };\n\n  const handleAdvancedFilterChange = (filterName, value) => {\n    setAdvancedFilters(prev => ({\n      ...prev,\n      [filterName]: value\n    }));\n  };\n\n  const clearAllFilters = () => {\n    setFilterQuery('');\n    setAdvancedFilters({\n      minExperience: '',\n      maxExperience: '',\n      education: '',\n      location: '',\n      skills: '',\n      minMarks: '',\n      institution: '',\n      graduationYear: '',\n      company: ''\n    });\n  };\n\n  const hasActiveFilters = () => {\n    return filterQuery || Object.values(advancedFilters).some(value => value !== '');\n  };\n\n  const handleDownload = (resume) => {\n    try {\n      // Create a comprehensive text-based resume content\n      const resumeContent = `\n${resume.name}\n${resume.title}\n${resume.email} | ${resume.phone} | ${resume.location}\n\nPROFESSIONAL SUMMARY\n${resume.summary}\n\nWORK EXPERIENCE\n${resume.rawData?.Resume?.WorkExperience ?\n  resume.rawData.Resume.WorkExperience.map(exp =>\n    `${exp.JobTitle || exp.Position} at ${exp.Company || exp.Organization}\nDuration: ${exp.Duration || exp.Years || 'Not specified'}\n${exp.Description ? 'Description: ' + exp.Description : ''}\n${exp.Responsibilities ? 'Responsibilities: ' + exp.Responsibilities : ''}\n`\n  ).join('\\n') : resume.experience}\n\n${resume.rawData?.Resume?.TotalWorkExperienceInYears ?\n  `Total Experience: ${resume.rawData.Resume.TotalWorkExperienceInYears} years\\n` : ''}\n\nEDUCATION\n${resume.rawData?.Resume?.Education ?\n  resume.rawData.Resume.Education.map(edu =>\n    `${edu.Degree || 'Degree'} from ${edu.Institution || edu.School || 'Institution'}\nGraduation Year: ${edu.GraduationYear || edu.Year || 'Not specified'}\n${edu['GPA/Marks/%'] && edu['GPA/Marks/%'] > 0 ? 'Marks: ' + edu['GPA/Marks/%'] + '%' : ''}\n`\n  ).join('\\n') : resume.education}\n\nSKILLS & TECHNOLOGIES\n${resume.skills.join(', ')}\n\n${resume.rawData?.Resume?.Languages && resume.rawData.Resume.Languages.length > 0 ?\n  `LANGUAGES\\n${resume.rawData.Resume.Languages.join(', ')}\\n` : ''}\n\n${resume.rawData?.Resume?.Projects && resume.rawData.Resume.Projects.length > 0 ?\n  `PROJECTS\\n${resume.rawData.Resume.Projects.map(project =>\n    `${project.Name || project.Title || 'Project'}\n${project.Description ? 'Description: ' + project.Description : ''}\n${project.Technologies ? 'Technologies: ' + project.Technologies : ''}\n`\n  ).join('\\n')}` : ''}\n\n${resume.rawData?.Resume?.Certifications && resume.rawData.Resume.Certifications.length > 0 ?\n  `CERTIFICATIONS\\n${resume.rawData.Resume.Certifications.map(cert =>\n    `${cert.Name || cert.Title || 'Certification'}\n${cert.IssuingOrganization ? 'Issued by: ' + cert.IssuingOrganization : ''}\n${cert.IssueDate ? 'Date: ' + cert.IssueDate : ''}\n`\n  ).join('\\n')}` : ''}\n\n${resume.rawData?.Resume?.Achievements && resume.rawData.Resume.Achievements.length > 0 ?\n  `ACHIEVEMENTS\\n${resume.rawData.Resume.Achievements.map(achievement =>\n    typeof achievement === 'string' ? achievement :\n    `${achievement.AchievementName || achievement.Name || 'Achievement'}\n${achievement.IssueDate ? 'Date: ' + achievement.IssueDate : ''}\n${achievement.IssuingOrganization ? 'Organization: ' + achievement.IssuingOrganization : ''}\n${achievement.Description ? 'Description: ' + achievement.Description : ''}\n`\n  ).join('\\n')}` : ''}\n\n${resume.rawData?.Resume?.PersonalInformation?.LinkedIn ?\n  `ADDITIONAL INFORMATION\\nLinkedIn: ${resume.rawData.Resume.PersonalInformation.LinkedIn}` : ''}\n\nGenerated on: ${new Date().toLocaleDateString()}\n      `.trim();\n\n      // Create and download the file\n      const blob = new Blob([resumeContent], { type: 'text/plain' });\n      const url = URL.createObjectURL(blob);\n      const link = document.createElement('a');\n      link.href = url;\n      link.download = `${resume.name.replace(/\\s+/g, '_')}_Resume.txt`;\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n      URL.revokeObjectURL(url);\n\n      toast.success(`Downloaded resume for ${resume.name}`);\n    } catch (error) {\n      console.error('Download error:', error);\n      toast.error('Failed to download resume. Please try again.');\n    }\n  };\n\n  const handleView = (resume) => {\n    setSelectedResume(resume);\n    setIsModalOpen(true);\n    toast.info(`Opening detailed view for ${resume.name}`);\n  };\n\n  const handleCloseModal = () => {\n    setIsModalOpen(false);\n    setSelectedResume(null);\n  };\n\n  const handleEmailClick = (email, name) => {\n    window.open(`mailto:${email}?subject=Regarding your resume&body=Hi ${name},%0D%0A%0D%0AI found your resume and would like to discuss potential opportunities.%0D%0A%0D%0ABest regards`, '_blank');\n    toast.info(`Opening email client to contact ${name}`);\n  };\n\n  const handlePhoneClick = (phone, name) => {\n    if (navigator.userAgent.match(/Mobile|Android|iPhone|iPad/)) {\n      window.open(`tel:${phone}`, '_blank');\n    } else {\n      navigator.clipboard.writeText(phone).then(() => {\n        toast.success(`Phone number copied: ${phone}`);\n      }).catch(() => {\n        toast.info(`Phone: ${phone}`);\n      });\n    }\n  };\n\n\n\n  const handleExportAll = () => {\n    if (filteredResults.length === 0) {\n      toast.warning('No results to export');\n      return;\n    }\n\n    try {\n      const exportData = filteredResults.map(resume => ({\n        name: resume.name,\n        title: resume.title,\n        email: resume.email,\n        phone: resume.phone,\n        location: resume.location,\n        experience: resume.experience,\n        education: resume.education,\n        skills: resume.skills.join(', '),\n        summary: resume.summary,\n        matchScore: resume.score\n      }));\n\n      const csvContent = [\n        'Name,Title,Email,Phone,Location,Experience,Education,Skills,Summary,Match Score',\n        ...exportData.map(row =>\n          Object.values(row).map(value =>\n            `\"${String(value).replace(/\"/g, '\"\"')}\"`\n          ).join(',')\n        )\n      ].join('\\n');\n\n      const blob = new Blob([csvContent], { type: 'text/csv' });\n      const url = URL.createObjectURL(blob);\n      const link = document.createElement('a');\n      link.href = url;\n      link.download = `Resume_Search_Results_${new Date().toISOString().split('T')[0]}.csv`;\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n      URL.revokeObjectURL(url);\n\n      toast.success(`Exported ${filteredResults.length} resumes to CSV`);\n    } catch (error) {\n      console.error('Export error:', error);\n      toast.error('Failed to export results. Please try again.');\n    }\n  };\n\n  return (\n    <div className=\"search-page\">\n      <div className=\"page-header\">\n        <h1>Search Resumes</h1>\n        <p>Use natural language to find the perfect candidates for your needs</p>\n      </div>\n\n      {/* Search Section */}\n      <div className=\"search-section\">\n        <form onSubmit={handleSearch} className=\"search-form\">\n          <div className=\"search-input-group\">\n            <div className=\"search-input-wrapper\">\n              <FiSearch className=\"search-icon\" />\n              <input\n                type=\"text\"\n                placeholder=\"e.g., 'Find Python developers with 5+ years experience' or 'Show frontend engineers in California'\"\n                value={searchQuery}\n                onChange={(e) => setSearchQuery(e.target.value)}\n                className=\"search-input\"\n                disabled={isSearching}\n              />\n            </div>\n            <div className=\"search-buttons\">\n              <button\n                type=\"submit\"\n                className=\"btn btn-primary\"\n                disabled={isSearching}\n              >\n                {isSearching ? (\n                  <>\n                    <div className=\"loading-spinner\" />\n                    Searching...\n                  </>\n                ) : (\n                  <>\n                    <FiSearch size={16} />\n                    Search\n                  </>\n                )}\n              </button>\n              <button\n                type=\"button\"\n                className=\"btn btn-secondary\"\n                onClick={handleShowAll}\n                disabled={isSearching}\n              >\n                Show All\n              </button>\n            </div>\n          </div>\n        </form>\n\n        {/* Filter Section */}\n        {searchResults.length > 0 && (\n          <div className=\"filter-section\">\n            <div className=\"filter-header\">\n              <div className=\"basic-filter-wrapper\">\n                <FiFilter className=\"filter-icon\" />\n                <input\n                  type=\"text\"\n                  placeholder=\"Quick filter by name, title, or location...\"\n                  value={filterQuery}\n                  onChange={(e) => setFilterQuery(e.target.value)}\n                  className=\"filter-input\"\n                />\n              </div>\n              <div className=\"filter-controls\">\n                <button\n                  type=\"button\"\n                  className={`btn btn-secondary filter-toggle ${showFilters ? 'active' : ''}`}\n                  onClick={() => setShowFilters(!showFilters)}\n                >\n                  <FiSettings size={16} />\n                  Advanced Filters\n                  {showFilters ? <FiChevronUp size={16} /> : <FiChevronDown size={16} />}\n                </button>\n                {hasActiveFilters() && (\n                  <button\n                    type=\"button\"\n                    className=\"btn btn-outline clear-filters\"\n                    onClick={clearAllFilters}\n                  >\n                    <FiX size={16} />\n                    Clear All\n                  </button>\n                )}\n              </div>\n            </div>\n\n            {/* Advanced Filters Panel */}\n            {showFilters && (\n              <div className=\"advanced-filters\">\n                <div className=\"filters-grid\">\n                  <div className=\"filter-group\">\n                    <label>Experience (Years)</label>\n                    <div className=\"range-inputs\">\n                      <input\n                        type=\"number\"\n                        placeholder=\"Min\"\n                        value={advancedFilters.minExperience}\n                        onChange={(e) => handleAdvancedFilterChange('minExperience', e.target.value)}\n                        className=\"filter-input small\"\n                        min=\"0\"\n                      />\n                      <span>to</span>\n                      <input\n                        type=\"number\"\n                        placeholder=\"Max\"\n                        value={advancedFilters.maxExperience}\n                        onChange={(e) => handleAdvancedFilterChange('maxExperience', e.target.value)}\n                        className=\"filter-input small\"\n                        min=\"0\"\n                      />\n                    </div>\n                  </div>\n\n                  <div className=\"filter-group\">\n                    <label>Education Level</label>\n                    <input\n                      type=\"text\"\n                      placeholder=\"e.g., Bachelor, Master, PhD\"\n                      value={advancedFilters.education}\n                      onChange={(e) => handleAdvancedFilterChange('education', e.target.value)}\n                      className=\"filter-input\"\n                    />\n                  </div>\n\n                  <div className=\"filter-group\">\n                    <label>Location</label>\n                    <input\n                      type=\"text\"\n                      placeholder=\"e.g., Mumbai, Delhi, Bangalore\"\n                      value={advancedFilters.location}\n                      onChange={(e) => handleAdvancedFilterChange('location', e.target.value)}\n                      className=\"filter-input\"\n                    />\n                  </div>\n\n                  <div className=\"filter-group\">\n                    <label>Skills</label>\n                    <input\n                      type=\"text\"\n                      placeholder=\"e.g., Python, React, Java\"\n                      value={advancedFilters.skills}\n                      onChange={(e) => handleAdvancedFilterChange('skills', e.target.value)}\n                      className=\"filter-input\"\n                    />\n                  </div>\n\n                  <div className=\"filter-group\">\n                    <label>Institution</label>\n                    <input\n                      type=\"text\"\n                      placeholder=\"e.g., IIT, University name\"\n                      value={advancedFilters.institution}\n                      onChange={(e) => handleAdvancedFilterChange('institution', e.target.value)}\n                      className=\"filter-input\"\n                    />\n                  </div>\n\n                  <div className=\"filter-group\">\n                    <label>Graduation Year</label>\n                    <input\n                      type=\"text\"\n                      placeholder=\"e.g., 2020, 2021\"\n                      value={advancedFilters.graduationYear}\n                      onChange={(e) => handleAdvancedFilterChange('graduationYear', e.target.value)}\n                      className=\"filter-input\"\n                    />\n                  </div>\n\n                  <div className=\"filter-group\">\n                    <label>Company</label>\n                    <input\n                      type=\"text\"\n                      placeholder=\"e.g., Google, Microsoft, TCS\"\n                      value={advancedFilters.company}\n                      onChange={(e) => handleAdvancedFilterChange('company', e.target.value)}\n                      className=\"filter-input\"\n                    />\n                  </div>\n\n                  <div className=\"filter-group\">\n                    <label>Minimum Marks (%)</label>\n                    <input\n                      type=\"number\"\n                      placeholder=\"e.g., 75, 80\"\n                      value={advancedFilters.minMarks}\n                      onChange={(e) => handleAdvancedFilterChange('minMarks', e.target.value)}\n                      className=\"filter-input\"\n                      min=\"0\"\n                      max=\"100\"\n                    />\n                  </div>\n                </div>\n              </div>\n            )}\n\n            <div className=\"results-count\">\n              {filteredResults.length} of {searchResults.length} results\n              {hasActiveFilters() && <span className=\"filtered-indicator\"> (filtered)</span>}\n            </div>\n          </div>\n        )}\n      </div>\n\n      {/* Results Section */}\n      {searchResults.length > 0 ? (\n        <div className=\"results-section\">\n          <div className=\"results-header\">\n            <h2>Search Results</h2>\n            <div className=\"results-actions\">\n              <button\n                className=\"btn btn-secondary\"\n                onClick={handleExportAll}\n                title={`Export ${filteredResults.length} results to CSV`}\n              >\n                <FiDownload size={16} />\n                Export All ({filteredResults.length})\n              </button>\n            </div>\n          </div>\n\n          <div className=\"results-grid\">\n            {filteredResults.map((resume) => (\n              <div key={resume.id} className=\"resume-card\">\n                <div className=\"resume-header\">\n                  <div className=\"resume-avatar\">\n                    <FiUser size={24} />\n                  </div>\n                  <div className=\"resume-info\">\n                    <h3 className=\"resume-name\">{resume.name}</h3>\n                    <p className=\"resume-title\">{resume.title}</p>\n                  </div>\n                </div>\n\n                <div className=\"resume-details\">\n                  <div className=\"detail-item location-item\">\n                    <FiMapPin size={14} />\n                    <span>{resume.location}</span>\n                  </div>\n                  <div\n                    className=\"detail-item email-item clickable\"\n                    onClick={() => handleEmailClick(resume.email, resume.name)}\n                    title={`Send email to ${resume.name}`}\n                  >\n                    <FiMail size={14} />\n                    <span>{resume.email}</span>\n                    <FiExternalLink size={12} className=\"external-icon\" />\n                  </div>\n                  <div\n                    className=\"detail-item phone-item clickable\"\n                    onClick={() => handlePhoneClick(resume.phone, resume.name)}\n                    title={`Call ${resume.name}`}\n                  >\n                    <FiPhone size={14} />\n                    <span>{resume.phone}</span>\n                    <FiExternalLink size={12} className=\"external-icon\" />\n                  </div>\n                </div>\n\n                <div className=\"resume-content\">\n                  <div className=\"content-row\">\n                    <div className=\"content-section\">\n                      <h4>Experience</h4>\n                      <p>{resume.experience}</p>\n                    </div>\n                    <div className=\"content-section\">\n                      <h4>Education</h4>\n                      <p>{resume.education}</p>\n                    </div>\n                  </div>\n                  <div className=\"content-section\">\n                    <h4>Skills</h4>\n                    <div className=\"skills-list\">\n                      {resume.skills.map((skill, index) => (\n                        <span key={index} className=\"skill-tag\">{skill}</span>\n                      ))}\n                    </div>\n                  </div>\n                </div>\n\n                <div className=\"resume-actions\">\n                  <button\n                    className=\"btn btn-secondary\"\n                    onClick={() => handleView(resume)}\n                  >\n                    <FiEye size={16} />\n                    View Details\n                  </button>\n                  <button\n                    className=\"btn btn-primary\"\n                    onClick={() => handleDownload(resume)}\n                  >\n                    <FiDownload size={16} />\n                    Download\n                  </button>\n                </div>\n              </div>\n            ))}\n          </div>\n\n          {/* Pagination */}\n          {totalPages > 1 && (\n            <div className=\"pagination-section\">\n              <div className=\"pagination-info\">\n                Showing {((currentPage - 1) * resultsPerPage) + 1} to {Math.min(currentPage * resultsPerPage, totalCount)} of {totalCount} results\n              </div>\n              <div className=\"pagination-controls\">\n                <button\n                  className=\"btn btn-secondary pagination-btn\"\n                  onClick={() => handlePageChange(currentPage - 1)}\n                  disabled={currentPage === 1}\n                >\n                  Previous\n                </button>\n\n                <div className=\"page-numbers\">\n                  {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {\n                    let pageNum;\n                    if (totalPages <= 5) {\n                      pageNum = i + 1;\n                    } else if (currentPage <= 3) {\n                      pageNum = i + 1;\n                    } else if (currentPage >= totalPages - 2) {\n                      pageNum = totalPages - 4 + i;\n                    } else {\n                      pageNum = currentPage - 2 + i;\n                    }\n\n                    return (\n                      <button\n                        key={pageNum}\n                        className={`btn pagination-btn ${currentPage === pageNum ? 'btn-primary' : 'btn-secondary'}`}\n                        onClick={() => handlePageChange(pageNum)}\n                      >\n                        {pageNum}\n                      </button>\n                    );\n                  })}\n                </div>\n\n                <button\n                  className=\"btn btn-secondary pagination-btn\"\n                  onClick={() => handlePageChange(currentPage + 1)}\n                  disabled={currentPage === totalPages}\n                >\n                  Next\n                </button>\n              </div>\n            </div>\n          )}\n        </div>\n      ) : !isSearching ? (\n        <div className=\"empty-state\">\n          <div className=\"empty-icon\">\n            <FiSearch size={48} />\n          </div>\n          <h3>Ready to Search!</h3>\n          <p>Use the search box above to find resumes using natural language, or click \"Show All\" to view all resumes in the database.</p>\n          <div className=\"search-examples\">\n            <h4>Try searching for:</h4>\n            <ul>\n              <li>\"Find Python developers\"</li>\n              <li>\"Show teachers with 5+ years experience\"</li>\n              <li>\"Software engineers in Mumbai\"</li>\n            </ul>\n          </div>\n        </div>\n      ) : null}\n\n      {/* Resume Details Modal */}\n      <ResumeModal\n        resume={selectedResume}\n        isOpen={isModalOpen}\n        onClose={handleCloseModal}\n      />\n    </div>\n  );\n};\n\nexport default SearchResumes;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,QAAQ,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,KAAK,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,OAAO,EAAEC,cAAc,EAAEC,MAAM,EAAEC,GAAG,EAAEC,UAAU,EAAEC,aAAa,EAAEC,WAAW,QAAQ,gBAAgB;AAC9K,SAASC,KAAK,QAAQ,gBAAgB;AACtC,OAAOC,WAAW,MAAM,2BAA2B;AACnD,OAAO,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE7B,MAAMC,aAAa,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC1B,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACyB,WAAW,EAAEC,cAAc,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAAC2B,aAAa,EAAEC,gBAAgB,CAAC,GAAG5B,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAAC6B,WAAW,EAAEC,cAAc,CAAC,GAAG9B,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC+B,WAAW,EAAEC,cAAc,CAAC,GAAGhC,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACiC,eAAe,EAAEC,kBAAkB,CAAC,GAAGlC,QAAQ,CAAC;IACrDmC,aAAa,EAAE,EAAE;IACjBC,aAAa,EAAE,EAAE;IACjBC,SAAS,EAAE,EAAE;IACbC,QAAQ,EAAE,EAAE;IACZC,MAAM,EAAE,EAAE;IACVC,QAAQ,EAAE,EAAE;IACZC,WAAW,EAAE,EAAE;IACfC,cAAc,EAAE,EAAE;IAClBC,OAAO,EAAE;EACX,CAAC,CAAC;EACF,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAG7C,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAAC8C,WAAW,EAAEC,cAAc,CAAC,GAAG/C,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACgD,WAAW,EAAEC,cAAc,CAAC,GAAGjD,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAACkD,UAAU,EAAEC,aAAa,CAAC,GAAGnD,QAAQ,CAAC,CAAC,CAAC;EAC/C,MAAM,CAACoD,UAAU,EAAEC,aAAa,CAAC,GAAGrD,QAAQ,CAAC,CAAC,CAAC;EAC/C,MAAMsD,cAAc,GAAG,EAAE;;EAEzB;EACA,MAAMC,WAAW,GAAG,CAClB;IACEC,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,UAAU;IAChBC,KAAK,EAAE,0BAA0B;IACjCC,KAAK,EAAE,oBAAoB;IAC3BC,KAAK,EAAE,mBAAmB;IAC1BtB,QAAQ,EAAE,mBAAmB;IAC7BuB,UAAU,EAAE,UAAU;IACtBtB,MAAM,EAAE,CAAC,QAAQ,EAAE,OAAO,EAAE,SAAS,EAAE,KAAK,CAAC;IAC7CF,SAAS,EAAE,qBAAqB;IAChCyB,OAAO,EAAE,2EAA2E;IACpFC,KAAK,EAAE,EAAE;IACTC,OAAO,EAAE;MACPC,MAAM,EAAE;QACNC,mBAAmB,EAAE;UACnBC,QAAQ,EAAE,UAAU;UACpBC,KAAK,EAAE,oBAAoB;UAC3BC,aAAa,EAAE,mBAAmB;UAClCC,OAAO,EAAE;QACX,CAAC;QACDC,cAAc,EAAE,CACd;UACEC,IAAI,EAAE,0BAA0B;UAChCC,WAAW,EAAE,WAAW;UACxBC,SAAS,EAAE,MAAM;UACjBC,OAAO,EAAE,SAAS;UAClB,4BAA4B,EAAE;QAChC,CAAC,EACD;UACEH,IAAI,EAAE,mBAAmB;UACzBC,WAAW,EAAE,YAAY;UACzBC,SAAS,EAAE,MAAM;UACjBC,OAAO,EAAE,MAAM;UACf,4BAA4B,EAAE;QAChC,CAAC,CACF;QACDC,SAAS,EAAE,CACT;UACEC,MAAM,EAAE,qBAAqB;UAC7BC,WAAW,EAAE,qBAAqB;UAClCC,cAAc,EAAE,MAAM;UACtB,aAAa,EAAE;QACjB,CAAC,CACF;QACDC,MAAM,EAAE,CAAC,QAAQ,EAAE,OAAO,EAAE,SAAS,EAAE,KAAK,CAAC;QAC7CC,0BAA0B,EAAE;MAC9B;IACF;EACF,CAAC,EACD;IACEzB,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,YAAY;IAClBC,KAAK,EAAE,oBAAoB;IAC3BC,KAAK,EAAE,sBAAsB;IAC7BC,KAAK,EAAE,mBAAmB;IAC1BtB,QAAQ,EAAE,cAAc;IACxBuB,UAAU,EAAE,UAAU;IACtBtB,MAAM,EAAE,CAAC,OAAO,EAAE,YAAY,EAAE,KAAK,EAAE,YAAY,CAAC;IACpDF,SAAS,EAAE,qBAAqB;IAChCyB,OAAO,EAAE,iEAAiE;IAC1EC,KAAK,EAAE,EAAE;IACTC,OAAO,EAAE;MACPC,MAAM,EAAE;QACNC,mBAAmB,EAAE;UACnBC,QAAQ,EAAE,YAAY;UACtBC,KAAK,EAAE,sBAAsB;UAC7BC,aAAa,EAAE,mBAAmB;UAClCC,OAAO,EAAE;QACX,CAAC;QACDC,cAAc,EAAE,CACd;UACEC,IAAI,EAAE,oBAAoB;UAC1BC,WAAW,EAAE,eAAe;UAC5BC,SAAS,EAAE,MAAM;UACjBC,OAAO,EAAE,SAAS;UAClB,4BAA4B,EAAE;QAChC,CAAC,CACF;QACDC,SAAS,EAAE,CACT;UACEC,MAAM,EAAE,qBAAqB;UAC7BC,WAAW,EAAE,KAAK;UAClBC,cAAc,EAAE,MAAM;UACtB,aAAa,EAAE;QACjB,CAAC,CACF;QACDC,MAAM,EAAE,CAAC,OAAO,EAAE,YAAY,EAAE,KAAK,EAAE,YAAY,CAAC;QACpDC,0BAA0B,EAAE;MAC9B;IACF;EACF,CAAC,EACD;IACEzB,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,cAAc;IACpBC,KAAK,EAAE,iBAAiB;IACxBC,KAAK,EAAE,wBAAwB;IAC/BC,KAAK,EAAE,mBAAmB;IAC1BtB,QAAQ,EAAE,YAAY;IACtBuB,UAAU,EAAE,UAAU;IACtBtB,MAAM,EAAE,CAAC,QAAQ,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,CAAC;IACjDF,SAAS,EAAE,2BAA2B;IACtCyB,OAAO,EAAE,wEAAwE;IACjFC,KAAK,EAAE,EAAE;IACTC,OAAO,EAAE;MACPC,MAAM,EAAE;QACNC,mBAAmB,EAAE;UACnBC,QAAQ,EAAE,cAAc;UACxBC,KAAK,EAAE,wBAAwB;UAC/BC,aAAa,EAAE,mBAAmB;UAClCC,OAAO,EAAE;QACX,CAAC;QACDC,cAAc,EAAE,CACd;UACEC,IAAI,EAAE,iBAAiB;UACvBC,WAAW,EAAE,qBAAqB;UAClCC,SAAS,EAAE,MAAM;UACjBC,OAAO,EAAE,SAAS;UAClB,4BAA4B,EAAE;QAChC,CAAC,CACF;QACDC,SAAS,EAAE,CACT;UACEC,MAAM,EAAE,2BAA2B;UACnCC,WAAW,EAAE,WAAW;UACxBC,cAAc,EAAE,MAAM;UACtB,aAAa,EAAE;QACjB,CAAC,CACF;QACDC,MAAM,EAAE,CAAC,QAAQ,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,CAAC;QACjDC,0BAA0B,EAAE;MAC9B;IACF;EACF,CAAC,CACF;EAED,MAAMC,YAAY,GAAG,MAAOC,CAAC,IAAK;IAChCA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,IAAI,CAAC7D,WAAW,CAAC8D,IAAI,CAAC,CAAC,EAAE;MACvBtE,KAAK,CAACuE,OAAO,CAAC,6BAA6B,CAAC;MAC5C;IACF;IAEA5D,cAAc,CAAC,IAAI,CAAC;IAEpB,IAAI;MACF;MACA,MAAM6D,QAAQ,GAAG,MAAMC,KAAK,CAAC,6CAA6CC,kBAAkB,CAAClE,WAAW,CAAC,EAAE,EAAE;QAC3GmE,MAAM,EAAE,KAAK;QACbC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB;MACF,CAAC,CAAC;MAEF,MAAMC,IAAI,GAAG,MAAML,QAAQ,CAACM,IAAI,CAAC,CAAC;MAElC,IAAIN,QAAQ,CAACO,EAAE,EAAE;QACf;QACA,IAAIC,kBAAkB,GAAG,EAAE;QAE3B,IAAIH,IAAI,CAACI,UAAU,IAAIC,KAAK,CAACC,OAAO,CAACN,IAAI,CAACI,UAAU,CAAC,EAAE;UACrD;UACAD,kBAAkB,GAAGH,IAAI,CAACI,UAAU,CAACG,GAAG,CAACC,MAAM,IAAI;YAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,sBAAA;YACjD;YACA,MAAMC,UAAU,GAAGT,MAAM,CAACnC,MAAM,IAAImC,MAAM;YAE1C,OAAO;cACL5C,EAAE,EAAE4C,MAAM,CAACU,GAAG,IAAIC,IAAI,CAACC,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;cACzDzD,IAAI,EAAE,EAAA4C,qBAAA,GAAAQ,UAAU,CAAC3C,mBAAmB,cAAAmC,qBAAA,uBAA9BA,qBAAA,CAAgClC,QAAQ,KAAI0C,UAAU,CAACpD,IAAI,IAAI,SAAS;cAC9EC,KAAK,EAAE,EAAA4C,sBAAA,GAAAO,UAAU,CAAC3C,mBAAmB,cAAAoC,sBAAA,uBAA9BA,sBAAA,CAAgCa,WAAW,KAAIN,UAAU,CAACnD,KAAK,IAAI,UAAU;cACpFC,KAAK,EAAE,EAAA4C,sBAAA,GAAAM,UAAU,CAAC3C,mBAAmB,cAAAqC,sBAAA,uBAA9BA,sBAAA,CAAgCnC,KAAK,KAAIyC,UAAU,CAAClD,KAAK,IAAI,UAAU;cAC9EC,KAAK,EAAE,EAAA4C,sBAAA,GAAAK,UAAU,CAAC3C,mBAAmB,cAAAsC,sBAAA,uBAA9BA,sBAAA,CAAgCnC,aAAa,KAAIwC,UAAU,CAACjD,KAAK,IAAI,UAAU;cACtFtB,QAAQ,EAAE,EAAAmE,sBAAA,GAAAI,UAAU,CAAC3C,mBAAmB,cAAAuC,sBAAA,uBAA9BA,sBAAA,CAAgCnC,OAAO,KAAIuC,UAAU,CAACvE,QAAQ,IAAI,aAAa;cACzFuB,UAAU,EAAEgD,UAAU,CAAC5B,0BAA0B,GAC/C,GAAG4B,UAAU,CAAC5B,0BAA0B,QAAQ,GAChD,CAAAyB,qBAAA,GAAAG,UAAU,CAACtC,cAAc,cAAAmC,qBAAA,eAAzBA,qBAAA,CAA2BU,MAAM,GAC/B,GAAGP,UAAU,CAACtC,cAAc,CAAC6C,MAAM,aAAa,GAChDP,UAAU,CAAChD,UAAU,IAAI,oBAAoB;cACjDtB,MAAM,EAAEsE,UAAU,CAAC7B,MAAM,IAAI6B,UAAU,CAACtE,MAAM,IAAI,EAAE;cACpDF,SAAS,EAAE,CAAAsE,qBAAA,GAAAE,UAAU,CAACjC,SAAS,cAAA+B,qBAAA,eAApBA,qBAAA,CAAsBS,MAAM,GACrC,EAAAR,sBAAA,GAAAC,UAAU,CAACjC,SAAS,CAAC,CAAC,CAAC,cAAAgC,sBAAA,uBAAvBA,sBAAA,CAAyB/B,MAAM,KAAI,qBAAqB,GACxDgC,UAAU,CAACxE,SAAS,IAAI,mBAAmB;cAC7CyB,OAAO,EAAE+C,UAAU,CAACQ,SAAS,IAAIR,UAAU,CAACS,OAAO,IAAIT,UAAU,CAAC/C,OAAO,IAAI,sBAAsB;cACnGC,KAAK,EAAE,EAAE;cAAE;cACXC,OAAO,EAAEoC,MAAM,CAAC;YAClB,CAAC;UACH,CAAC,CAAC;QACJ,CAAC,MAAM;UACL;UACAL,kBAAkB,GAAGxC,WAAW,CAACgE,MAAM,CAACnB,MAAM,IAC5CA,MAAM,CAAC3C,IAAI,CAAC+D,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAClG,WAAW,CAACiG,WAAW,CAAC,CAAC,CAAC,IAC7DpB,MAAM,CAAC1C,KAAK,CAAC8D,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAClG,WAAW,CAACiG,WAAW,CAAC,CAAC,CAAC,IAC9DpB,MAAM,CAAC7D,MAAM,CAACmF,IAAI,CAACC,KAAK,IAAIA,KAAK,CAACH,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAClG,WAAW,CAACiG,WAAW,CAAC,CAAC,CAAC,CACrF,CAAC;QACH;QAEA5F,gBAAgB,CAACmE,kBAAkB,CAAC;;QAEpC;QACA,IAAIH,IAAI,CAACgC,MAAM,EAAE;UACf7G,KAAK,CAAC8G,OAAO,CAAC,iBAAiBjC,IAAI,CAACgC,MAAM,EAAE,CAAC;QAC/C,CAAC,MAAM;UACL7G,KAAK,CAAC8G,OAAO,CAAC,SAAS9B,kBAAkB,CAACqB,MAAM,mBAAmB,CAAC;QACtE;;QAEA;QACA,IAAIxB,IAAI,CAACkC,KAAK,EAAE;UACdC,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEpC,IAAI,CAACkC,KAAK,CAAC;UAC1DC,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEpC,IAAI,CAACgC,MAAM,CAAC;QACzC;MACF,CAAC,MAAM;QACL,MAAM,IAAIK,KAAK,CAACrC,IAAI,CAACsC,KAAK,IAAI,eAAe,CAAC;MAChD;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdH,OAAO,CAACG,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;;MAErC;MACA,MAAMC,eAAe,GAAG5E,WAAW,CAACgE,MAAM,CAACnB,MAAM,IAC/CA,MAAM,CAAC3C,IAAI,CAAC+D,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAClG,WAAW,CAACiG,WAAW,CAAC,CAAC,CAAC,IAC7DpB,MAAM,CAAC1C,KAAK,CAAC8D,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAClG,WAAW,CAACiG,WAAW,CAAC,CAAC,CAAC,IAC9DpB,MAAM,CAAC7D,MAAM,CAACmF,IAAI,CAACC,KAAK,IAAIA,KAAK,CAACH,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAClG,WAAW,CAACiG,WAAW,CAAC,CAAC,CAAC,CACrF,CAAC;MAED5F,gBAAgB,CAACuG,eAAe,CAAC;MACjCpH,KAAK,CAACuE,OAAO,CAAC,2BAA2B6C,eAAe,CAACf,MAAM,mBAAmB,CAAC;IACrF,CAAC,SAAS;MACR1F,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;EAED,MAAM0G,aAAa,GAAG,MAAAA,CAAOC,IAAI,GAAG,CAAC,KAAK;IACxC3G,cAAc,CAAC,IAAI,CAAC;IACpB,IAAI;MACF;MACA,MAAM6D,QAAQ,GAAG,MAAMC,KAAK,CAAC,+BAA+B,CAAC;MAC7D,MAAMI,IAAI,GAAG,MAAML,QAAQ,CAACM,IAAI,CAAC,CAAC;MAElC,IAAIN,QAAQ,CAACO,EAAE,EAAE;QACf,IAAIC,kBAAkB,GAAG,EAAE;QAE3B,IAAIE,KAAK,CAACC,OAAO,CAACN,IAAI,CAAC,EAAE;UACvB;UACAG,kBAAkB,GAAGH,IAAI,CAACO,GAAG,CAACC,MAAM,IAAI;YAAA,IAAAkC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;YACtC;YACA,MAAMhC,UAAU,GAAGT,MAAM,CAACnC,MAAM,IAAImC,MAAM;YAE1C,OAAO;cACL5C,EAAE,EAAE4C,MAAM,CAACU,GAAG,IAAIC,IAAI,CAACC,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;cACzDzD,IAAI,EAAE,EAAA6E,sBAAA,GAAAzB,UAAU,CAAC3C,mBAAmB,cAAAoE,sBAAA,uBAA9BA,sBAAA,CAAgCnE,QAAQ,KAAI0C,UAAU,CAACpD,IAAI,IAAI,SAAS;cAC9EC,KAAK,EAAE,EAAA6E,sBAAA,GAAA1B,UAAU,CAAC3C,mBAAmB,cAAAqE,sBAAA,uBAA9BA,sBAAA,CAAgCpB,WAAW,KAAIN,UAAU,CAACnD,KAAK,IAAI,UAAU;cACpFC,KAAK,EAAE,EAAA6E,sBAAA,GAAA3B,UAAU,CAAC3C,mBAAmB,cAAAsE,sBAAA,uBAA9BA,sBAAA,CAAgCpE,KAAK,KAAIyC,UAAU,CAAClD,KAAK,IAAI,UAAU;cAC9EC,KAAK,EAAE,EAAA6E,sBAAA,GAAA5B,UAAU,CAAC3C,mBAAmB,cAAAuE,sBAAA,uBAA9BA,sBAAA,CAAgCpE,aAAa,KAAIwC,UAAU,CAACjD,KAAK,IAAI,UAAU;cACtFtB,QAAQ,EAAE,EAAAoG,sBAAA,GAAA7B,UAAU,CAAC3C,mBAAmB,cAAAwE,sBAAA,uBAA9BA,sBAAA,CAAgCpE,OAAO,KAAIuC,UAAU,CAACvE,QAAQ,IAAI,aAAa;cACzFuB,UAAU,EAAEgD,UAAU,CAAC5B,0BAA0B,GAC/C,GAAG4B,UAAU,CAAC5B,0BAA0B,QAAQ,GAChD,CAAA0D,sBAAA,GAAA9B,UAAU,CAACtC,cAAc,cAAAoE,sBAAA,eAAzBA,sBAAA,CAA2BvB,MAAM,GAC/B,GAAGP,UAAU,CAACtC,cAAc,CAAC6C,MAAM,aAAa,GAChDP,UAAU,CAAChD,UAAU,IAAI,oBAAoB;cACjDtB,MAAM,EAAEsE,UAAU,CAAC7B,MAAM,IAAI6B,UAAU,CAACtE,MAAM,IAAI,EAAE;cACpDF,SAAS,EAAE,CAAAuG,sBAAA,GAAA/B,UAAU,CAACjC,SAAS,cAAAgE,sBAAA,eAApBA,sBAAA,CAAsBxB,MAAM,GACrC,EAAAyB,sBAAA,GAAAhC,UAAU,CAACjC,SAAS,CAAC,CAAC,CAAC,cAAAiE,sBAAA,uBAAvBA,sBAAA,CAAyBhE,MAAM,KAAI,qBAAqB,GACxDgC,UAAU,CAACxE,SAAS,IAAI,mBAAmB;cAC7CyB,OAAO,EAAE+C,UAAU,CAACQ,SAAS,IAAIR,UAAU,CAACS,OAAO,IAAIT,UAAU,CAAC/C,OAAO,IAAI,sBAAsB;cACnGC,KAAK,EAAE,EAAE;cAAE;cACXC,OAAO,EAAEoC,MAAM,CAAC;YAClB,CAAC;UACH,CAAC,CAAC;QACJ,CAAC,MAAM,IAAIR,IAAI,CAACsC,KAAK,EAAE;UACrB,MAAM,IAAID,KAAK,CAACrC,IAAI,CAACsC,KAAK,CAAC;QAC7B,CAAC,MAAM;UACL;UACAnC,kBAAkB,GAAGxC,WAAW;QAClC;QAEA3B,gBAAgB,CAACmE,kBAAkB,CAAC;QACpC9C,cAAc,CAAC,CAAC,CAAC;QACjBI,aAAa,CAAC0C,kBAAkB,CAACqB,MAAM,CAAC;QACxCjE,aAAa,CAAC4D,IAAI,CAAC+B,IAAI,CAAC/C,kBAAkB,CAACqB,MAAM,GAAG9D,cAAc,CAAC,CAAC;QACpE9B,cAAc,CAAC,EAAE,CAAC;QAElBT,KAAK,CAAC8G,OAAO,CAAC,aAAa9B,kBAAkB,CAACqB,MAAM,wBAAwB,CAAC;MAC/E,CAAC,MAAM;QACL,MAAM,IAAIa,KAAK,CAACrC,IAAI,CAACsC,KAAK,IAAI,wBAAwB,CAAC;MACzD;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdH,OAAO,CAACG,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;;MAE3C;MACAtG,gBAAgB,CAAC2B,WAAW,CAAC;MAC7BN,cAAc,CAAC,CAAC,CAAC;MACjBI,aAAa,CAACE,WAAW,CAAC6D,MAAM,CAAC;MACjCjE,aAAa,CAAC4D,IAAI,CAAC+B,IAAI,CAACvF,WAAW,CAAC6D,MAAM,GAAG9D,cAAc,CAAC,CAAC;MAC7D9B,cAAc,CAAC,EAAE,CAAC;MAElBT,KAAK,CAACuE,OAAO,CAAC,6BAA6B/B,WAAW,CAAC6D,MAAM,iBAAiB,CAAC;IACjF,CAAC,SAAS;MACR1F,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;EAED,MAAMqH,gBAAgB,GAAIV,IAAI,IAAK;IACjC,IAAIA,IAAI,IAAI,CAAC,IAAIA,IAAI,IAAInF,UAAU,EAAE;MACnCkF,aAAa,CAACC,IAAI,CAAC;IACrB;EACF,CAAC;EAED,MAAMW,eAAe,GAAGrH,aAAa,CAAC4F,MAAM,CAACK,MAAM,IAAI;IACrD;IACA,IAAI/F,WAAW,EAAE;MACf,MAAMoH,WAAW,GAAGrB,MAAM,CAACnE,IAAI,CAAC+D,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC5F,WAAW,CAAC2F,WAAW,CAAC,CAAC,CAAC,IAC9DI,MAAM,CAAClE,KAAK,CAAC8D,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC5F,WAAW,CAAC2F,WAAW,CAAC,CAAC,CAAC,IAC9DI,MAAM,CAACtF,QAAQ,CAACkF,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC5F,WAAW,CAAC2F,WAAW,CAAC,CAAC,CAAC;MACpF,IAAI,CAACyB,WAAW,EAAE,OAAO,KAAK;IAChC;;IAEA;IACA,MAAMC,OAAO,GAAGjH,eAAe;;IAE/B;IACA,IAAIiH,OAAO,CAAC/G,aAAa,IAAI+G,OAAO,CAAC9G,aAAa,EAAE;MAAA,IAAA+G,eAAA,EAAAC,qBAAA;MAClD,MAAMC,eAAe,GAAG,EAAAF,eAAA,GAAAvB,MAAM,CAAC5D,OAAO,cAAAmF,eAAA,wBAAAC,qBAAA,GAAdD,eAAA,CAAgBlF,MAAM,cAAAmF,qBAAA,uBAAtBA,qBAAA,CAAwBnE,0BAA0B,KACnDqE,sBAAsB,CAAC1B,MAAM,CAAC/D,UAAU,CAAC,IAAI,CAAC;MAErE,IAAIqF,OAAO,CAAC/G,aAAa,IAAIkH,eAAe,GAAGE,QAAQ,CAACL,OAAO,CAAC/G,aAAa,CAAC,EAAE,OAAO,KAAK;MAC5F,IAAI+G,OAAO,CAAC9G,aAAa,IAAIiH,eAAe,GAAGE,QAAQ,CAACL,OAAO,CAAC9G,aAAa,CAAC,EAAE,OAAO,KAAK;IAC9F;;IAEA;IACA,IAAI8G,OAAO,CAAC7G,SAAS,EAAE;MAAA,IAAAmH,gBAAA,EAAAC,qBAAA,EAAAC,sBAAA;MACrB,MAAMC,YAAY,GAAG,EAAAH,gBAAA,GAAA5B,MAAM,CAAC5D,OAAO,cAAAwF,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBvF,MAAM,cAAAwF,qBAAA,wBAAAC,sBAAA,GAAtBD,qBAAA,CAAwB7E,SAAS,cAAA8E,sBAAA,uBAAjCA,sBAAA,CAAmChC,IAAI,CAACkC,GAAG,IAC9D,CAACA,GAAG,CAAC/E,MAAM,IAAI,EAAE,EAAE2C,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACyB,OAAO,CAAC7G,SAAS,CAACmF,WAAW,CAAC,CAAC,CAC3E,CAAC,KAAI,CAACI,MAAM,CAACvF,SAAS,IAAI,EAAE,EAAEmF,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACyB,OAAO,CAAC7G,SAAS,CAACmF,WAAW,CAAC,CAAC,CAAC;MACrF,IAAI,CAACmC,YAAY,EAAE,OAAO,KAAK;IACjC;;IAEA;IACA,IAAIT,OAAO,CAAC5G,QAAQ,EAAE;MACpB,MAAMuH,eAAe,GAAG,CAACjC,MAAM,CAACtF,QAAQ,IAAI,EAAE,EAAEkF,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACyB,OAAO,CAAC5G,QAAQ,CAACkF,WAAW,CAAC,CAAC,CAAC;MACtG,IAAI,CAACqC,eAAe,EAAE,OAAO,KAAK;IACpC;;IAEA;IACA,IAAIX,OAAO,CAAC3G,MAAM,EAAE;MAAA,IAAAuH,cAAA,EAAAC,gBAAA,EAAAC,qBAAA;MAClB,MAAMC,UAAU,GAAGf,OAAO,CAAC3G,MAAM,CAACiF,WAAW,CAAC,CAAC;MAC/C,MAAM0C,QAAQ,GAAG,EAAAJ,cAAA,GAAAlC,MAAM,CAACrF,MAAM,cAAAuH,cAAA,uBAAbA,cAAA,CAAepC,IAAI,CAACC,KAAK,IAAIA,KAAK,CAACH,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACwC,UAAU,CAAC,CAAC,KACvE,CAAC,EAAAF,gBAAA,GAAAnC,MAAM,CAAC5D,OAAO,cAAA+F,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgB9F,MAAM,cAAA+F,qBAAA,uBAAtBA,qBAAA,CAAwBhF,MAAM,KAAI,EAAE,EAAE0C,IAAI,CAACC,KAAK,IAC/CA,KAAK,CAACH,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACwC,UAAU,CACzC,CAAC;MACjB,IAAI,CAACC,QAAQ,EAAE,OAAO,KAAK;IAC7B;;IAEA;IACA,IAAIhB,OAAO,CAACzG,WAAW,EAAE;MAAA,IAAA0H,gBAAA,EAAAC,qBAAA,EAAAC,sBAAA;MACvB,MAAMC,cAAc,IAAAH,gBAAA,GAAGvC,MAAM,CAAC5D,OAAO,cAAAmG,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBlG,MAAM,cAAAmG,qBAAA,wBAAAC,sBAAA,GAAtBD,qBAAA,CAAwBxF,SAAS,cAAAyF,sBAAA,uBAAjCA,sBAAA,CAAmC3C,IAAI,CAACkC,GAAG,IAChE,CAACA,GAAG,CAAC9E,WAAW,IAAI8E,GAAG,CAACW,MAAM,IAAI,EAAE,EAAE/C,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACyB,OAAO,CAACzG,WAAW,CAAC+E,WAAW,CAAC,CAAC,CAChG,CAAC;MACD,IAAI,CAAC8C,cAAc,EAAE,OAAO,KAAK;IACnC;;IAEA;IACA,IAAIpB,OAAO,CAACxG,cAAc,EAAE;MAAA,IAAA8H,gBAAA,EAAAC,qBAAA,EAAAC,sBAAA;MAC1B,MAAMC,WAAW,IAAAH,gBAAA,GAAG5C,MAAM,CAAC5D,OAAO,cAAAwG,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBvG,MAAM,cAAAwG,qBAAA,wBAAAC,sBAAA,GAAtBD,qBAAA,CAAwB7F,SAAS,cAAA8F,sBAAA,uBAAjCA,sBAAA,CAAmChD,IAAI,CAACkC,GAAG,IAC7D,CAACA,GAAG,CAAC7E,cAAc,IAAI6E,GAAG,CAACgB,IAAI,IAAI,EAAE,EAAE3D,QAAQ,CAAC,CAAC,CAACQ,QAAQ,CAACyB,OAAO,CAACxG,cAAc,CACnF,CAAC;MACD,IAAI,CAACiI,WAAW,EAAE,OAAO,KAAK;IAChC;;IAEA;IACA,IAAIzB,OAAO,CAACvG,OAAO,EAAE;MAAA,IAAAkI,gBAAA,EAAAC,qBAAA,EAAAC,sBAAA;MACnB,MAAMC,UAAU,IAAAH,gBAAA,GAAGjD,MAAM,CAAC5D,OAAO,cAAA6G,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgB5G,MAAM,cAAA6G,qBAAA,wBAAAC,sBAAA,GAAtBD,qBAAA,CAAwBvG,cAAc,cAAAwG,sBAAA,uBAAtCA,sBAAA,CAAwCrD,IAAI,CAACuD,GAAG,IACjE,CAACA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACxG,WAAW,IAAIwG,GAAG,CAACE,YAAY,IAAI,EAAE,EAAE3D,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACyB,OAAO,CAACvG,OAAO,CAAC6E,WAAW,CAAC,CAAC,CACjH,CAAC;MACD,IAAI,CAACwD,UAAU,EAAE,OAAO,KAAK;IAC/B;;IAEA;IACA,IAAI9B,OAAO,CAAC1G,QAAQ,EAAE;MAAA,IAAA4I,gBAAA,EAAAC,qBAAA,EAAAC,sBAAA;MACpB,MAAMC,WAAW,IAAAH,gBAAA,GAAGxD,MAAM,CAAC5D,OAAO,cAAAoH,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBnH,MAAM,cAAAoH,qBAAA,wBAAAC,sBAAA,GAAtBD,qBAAA,CAAwBzG,SAAS,cAAA0G,sBAAA,uBAAjCA,sBAAA,CAAmC5D,IAAI,CAACkC,GAAG,IAAI;QACjE,MAAM4B,KAAK,GAAGC,UAAU,CAAC7B,GAAG,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC;QACjD,OAAO4B,KAAK,IAAIC,UAAU,CAACvC,OAAO,CAAC1G,QAAQ,CAAC;MAC9C,CAAC,CAAC;MACF,IAAI,CAAC+I,WAAW,EAAE,OAAO,KAAK;IAChC;IAEA,OAAO,IAAI;EACb,CAAC,CAAC;;EAEF;EACA,MAAMjC,sBAAsB,GAAIoC,cAAc,IAAK;IACjD,IAAI,CAACA,cAAc,EAAE,OAAO,CAAC;IAC7B,MAAMC,KAAK,GAAGD,cAAc,CAACC,KAAK,CAAC,oBAAoB,CAAC;IACxD,OAAOA,KAAK,GAAGpC,QAAQ,CAACoC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;EACvC,CAAC;EAED,MAAMC,0BAA0B,GAAGA,CAACC,UAAU,EAAEC,KAAK,KAAK;IACxD5J,kBAAkB,CAAC6J,IAAI,KAAK;MAC1B,GAAGA,IAAI;MACP,CAACF,UAAU,GAAGC;IAChB,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAME,eAAe,GAAGA,CAAA,KAAM;IAC5BlK,cAAc,CAAC,EAAE,CAAC;IAClBI,kBAAkB,CAAC;MACjBC,aAAa,EAAE,EAAE;MACjBC,aAAa,EAAE,EAAE;MACjBC,SAAS,EAAE,EAAE;MACbC,QAAQ,EAAE,EAAE;MACZC,MAAM,EAAE,EAAE;MACVC,QAAQ,EAAE,EAAE;MACZC,WAAW,EAAE,EAAE;MACfC,cAAc,EAAE,EAAE;MAClBC,OAAO,EAAE;IACX,CAAC,CAAC;EACJ,CAAC;EAED,MAAMsJ,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,OAAOpK,WAAW,IAAIqK,MAAM,CAACC,MAAM,CAAClK,eAAe,CAAC,CAACyF,IAAI,CAACoE,KAAK,IAAIA,KAAK,KAAK,EAAE,CAAC;EAClF,CAAC;EAED,MAAMM,cAAc,GAAIhG,MAAM,IAAK;IACjC,IAAI;MAAA,IAAAiG,eAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,qBAAA,EAAAC,sBAAA;MACF;MACA,MAAMC,aAAa,GAAG;AAC5B,EAAElH,MAAM,CAAC3C,IAAI;AACb,EAAE2C,MAAM,CAAC1C,KAAK;AACd,EAAE0C,MAAM,CAACzC,KAAK,MAAMyC,MAAM,CAACxC,KAAK,MAAMwC,MAAM,CAAC9D,QAAQ;AACrD;AACA;AACA,EAAE8D,MAAM,CAACtC,OAAO;AAChB;AACA;AACA,EAAE,CAAAuI,eAAA,GAAAjG,MAAM,CAACpC,OAAO,cAAAqI,eAAA,gBAAAC,qBAAA,GAAdD,eAAA,CAAgBpI,MAAM,cAAAqI,qBAAA,eAAtBA,qBAAA,CAAwB/H,cAAc,GACtC6B,MAAM,CAACpC,OAAO,CAACC,MAAM,CAACM,cAAc,CAAC4B,GAAG,CAAC8E,GAAG,IAC1C,GAAGA,GAAG,CAACsC,QAAQ,IAAItC,GAAG,CAACuC,QAAQ,OAAOvC,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,YAAY;AACzE,YAAYF,GAAG,CAACwC,QAAQ,IAAIxC,GAAG,CAACyC,KAAK,IAAI,eAAe;AACxD,EAAEzC,GAAG,CAAC0C,WAAW,GAAG,eAAe,GAAG1C,GAAG,CAAC0C,WAAW,GAAG,EAAE;AAC1D,EAAE1C,GAAG,CAAC2C,gBAAgB,GAAG,oBAAoB,GAAG3C,GAAG,CAAC2C,gBAAgB,GAAG,EAAE;AACzE,CACE,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC,GAAGzH,MAAM,CAACvC,UAAU;AAClC;AACA,EAAE,CAAA0I,gBAAA,GAAAnG,MAAM,CAACpC,OAAO,cAAAuI,gBAAA,gBAAAC,qBAAA,GAAdD,gBAAA,CAAgBtI,MAAM,cAAAuI,qBAAA,eAAtBA,qBAAA,CAAwBvH,0BAA0B,GAClD,qBAAqBmB,MAAM,CAACpC,OAAO,CAACC,MAAM,CAACgB,0BAA0B,UAAU,GAAG,EAAE;AACtF;AACA;AACA,EAAE,CAAAwH,gBAAA,GAAArG,MAAM,CAACpC,OAAO,cAAAyI,gBAAA,gBAAAC,qBAAA,GAAdD,gBAAA,CAAgBxI,MAAM,cAAAyI,qBAAA,eAAtBA,qBAAA,CAAwB9H,SAAS,GACjCwB,MAAM,CAACpC,OAAO,CAACC,MAAM,CAACW,SAAS,CAACuB,GAAG,CAACyD,GAAG,IACrC,GAAGA,GAAG,CAAC/E,MAAM,IAAI,QAAQ,SAAS+E,GAAG,CAAC9E,WAAW,IAAI8E,GAAG,CAACW,MAAM,IAAI,aAAa;AACpF,mBAAmBX,GAAG,CAAC7E,cAAc,IAAI6E,GAAG,CAACgB,IAAI,IAAI,eAAe;AACpE,EAAEhB,GAAG,CAAC,aAAa,CAAC,IAAIA,GAAG,CAAC,aAAa,CAAC,GAAG,CAAC,GAAG,SAAS,GAAGA,GAAG,CAAC,aAAa,CAAC,GAAG,GAAG,GAAG,EAAE;AAC1F,CACE,CAAC,CAACiE,IAAI,CAAC,IAAI,CAAC,GAAGzH,MAAM,CAAC/D,SAAS;AACjC;AACA;AACA,EAAE+D,MAAM,CAAC7D,MAAM,CAACsL,IAAI,CAAC,IAAI,CAAC;AAC1B;AACA,EAAE,CAAAlB,gBAAA,GAAAvG,MAAM,CAACpC,OAAO,cAAA2I,gBAAA,gBAAAC,qBAAA,GAAdD,gBAAA,CAAgB1I,MAAM,cAAA2I,qBAAA,eAAtBA,qBAAA,CAAwBkB,SAAS,IAAI1H,MAAM,CAACpC,OAAO,CAACC,MAAM,CAAC6J,SAAS,CAAC1G,MAAM,GAAG,CAAC,GAC/E,cAAchB,MAAM,CAACpC,OAAO,CAACC,MAAM,CAAC6J,SAAS,CAACD,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,EAAE;AACnE;AACA,EAAE,CAAAhB,gBAAA,GAAAzG,MAAM,CAACpC,OAAO,cAAA6I,gBAAA,gBAAAC,qBAAA,GAAdD,gBAAA,CAAgB5I,MAAM,cAAA6I,qBAAA,eAAtBA,qBAAA,CAAwBiB,QAAQ,IAAI3H,MAAM,CAACpC,OAAO,CAACC,MAAM,CAAC8J,QAAQ,CAAC3G,MAAM,GAAG,CAAC,GAC7E,aAAahB,MAAM,CAACpC,OAAO,CAACC,MAAM,CAAC8J,QAAQ,CAAC5H,GAAG,CAAC6H,OAAO,IACrD,GAAGA,OAAO,CAACC,IAAI,IAAID,OAAO,CAACE,KAAK,IAAI,SAAS;AACjD,EAAEF,OAAO,CAACL,WAAW,GAAG,eAAe,GAAGK,OAAO,CAACL,WAAW,GAAG,EAAE;AAClE,EAAEK,OAAO,CAACG,YAAY,GAAG,gBAAgB,GAAGH,OAAO,CAACG,YAAY,GAAG,EAAE;AACrE,CACE,CAAC,CAACN,IAAI,CAAC,IAAI,CAAC,EAAE,GAAG,EAAE;AACrB;AACA,EAAE,CAAAd,gBAAA,GAAA3G,MAAM,CAACpC,OAAO,cAAA+I,gBAAA,gBAAAC,qBAAA,GAAdD,gBAAA,CAAgB9I,MAAM,cAAA+I,qBAAA,eAAtBA,qBAAA,CAAwBoB,cAAc,IAAIhI,MAAM,CAACpC,OAAO,CAACC,MAAM,CAACmK,cAAc,CAAChH,MAAM,GAAG,CAAC,GACzF,mBAAmBhB,MAAM,CAACpC,OAAO,CAACC,MAAM,CAACmK,cAAc,CAACjI,GAAG,CAACkI,IAAI,IAC9D,GAAGA,IAAI,CAACJ,IAAI,IAAII,IAAI,CAACH,KAAK,IAAI,eAAe;AACjD,EAAEG,IAAI,CAACC,mBAAmB,GAAG,aAAa,GAAGD,IAAI,CAACC,mBAAmB,GAAG,EAAE;AAC1E,EAAED,IAAI,CAACE,SAAS,GAAG,QAAQ,GAAGF,IAAI,CAACE,SAAS,GAAG,EAAE;AACjD,CACE,CAAC,CAACV,IAAI,CAAC,IAAI,CAAC,EAAE,GAAG,EAAE;AACrB;AACA,EAAE,CAAAZ,gBAAA,GAAA7G,MAAM,CAACpC,OAAO,cAAAiJ,gBAAA,gBAAAC,qBAAA,GAAdD,gBAAA,CAAgBhJ,MAAM,cAAAiJ,qBAAA,eAAtBA,qBAAA,CAAwBsB,YAAY,IAAIpI,MAAM,CAACpC,OAAO,CAACC,MAAM,CAACuK,YAAY,CAACpH,MAAM,GAAG,CAAC,GACrF,iBAAiBhB,MAAM,CAACpC,OAAO,CAACC,MAAM,CAACuK,YAAY,CAACrI,GAAG,CAACsI,WAAW,IACjE,OAAOA,WAAW,KAAK,QAAQ,GAAGA,WAAW,GAC7C,GAAGA,WAAW,CAACC,eAAe,IAAID,WAAW,CAACR,IAAI,IAAI,aAAa;AACvE,EAAEQ,WAAW,CAACF,SAAS,GAAG,QAAQ,GAAGE,WAAW,CAACF,SAAS,GAAG,EAAE;AAC/D,EAAEE,WAAW,CAACH,mBAAmB,GAAG,gBAAgB,GAAGG,WAAW,CAACH,mBAAmB,GAAG,EAAE;AAC3F,EAAEG,WAAW,CAACd,WAAW,GAAG,eAAe,GAAGc,WAAW,CAACd,WAAW,GAAG,EAAE;AAC1E,CACE,CAAC,CAACE,IAAI,CAAC,IAAI,CAAC,EAAE,GAAG,EAAE;AACrB;AACA,EAAE,CAAAV,gBAAA,GAAA/G,MAAM,CAACpC,OAAO,cAAAmJ,gBAAA,gBAAAC,qBAAA,GAAdD,gBAAA,CAAgBlJ,MAAM,cAAAmJ,qBAAA,gBAAAC,sBAAA,GAAtBD,qBAAA,CAAwBlJ,mBAAmB,cAAAmJ,sBAAA,eAA3CA,sBAAA,CAA6CsB,QAAQ,GACrD,qCAAqCvI,MAAM,CAACpC,OAAO,CAACC,MAAM,CAACC,mBAAmB,CAACyK,QAAQ,EAAE,GAAG,EAAE;AAChG;AACA,gBAAgB,IAAIC,IAAI,CAAC,CAAC,CAACC,kBAAkB,CAAC,CAAC;AAC/C,OAAO,CAACxJ,IAAI,CAAC,CAAC;;MAER;MACA,MAAMyJ,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACzB,aAAa,CAAC,EAAE;QAAE0B,IAAI,EAAE;MAAa,CAAC,CAAC;MAC9D,MAAMC,GAAG,GAAGC,GAAG,CAACC,eAAe,CAACL,IAAI,CAAC;MACrC,MAAMM,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;MACxCF,IAAI,CAACG,IAAI,GAAGN,GAAG;MACfG,IAAI,CAACI,QAAQ,GAAG,GAAGpJ,MAAM,CAAC3C,IAAI,CAACgM,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,aAAa;MAChEJ,QAAQ,CAACK,IAAI,CAACC,WAAW,CAACP,IAAI,CAAC;MAC/BA,IAAI,CAACQ,KAAK,CAAC,CAAC;MACZP,QAAQ,CAACK,IAAI,CAACG,WAAW,CAACT,IAAI,CAAC;MAC/BF,GAAG,CAACY,eAAe,CAACb,GAAG,CAAC;MAExBlO,KAAK,CAAC8G,OAAO,CAAC,yBAAyBzB,MAAM,CAAC3C,IAAI,EAAE,CAAC;IACvD,CAAC,CAAC,OAAOyE,KAAK,EAAE;MACdH,OAAO,CAACG,KAAK,CAAC,iBAAiB,EAAEA,KAAK,CAAC;MACvCnH,KAAK,CAACmH,KAAK,CAAC,8CAA8C,CAAC;IAC7D;EACF,CAAC;EAED,MAAM6H,UAAU,GAAI3J,MAAM,IAAK;IAC7BvD,iBAAiB,CAACuD,MAAM,CAAC;IACzBrD,cAAc,CAAC,IAAI,CAAC;IACpBhC,KAAK,CAACiP,IAAI,CAAC,6BAA6B5J,MAAM,CAAC3C,IAAI,EAAE,CAAC;EACxD,CAAC;EAED,MAAMwM,gBAAgB,GAAGA,CAAA,KAAM;IAC7BlN,cAAc,CAAC,KAAK,CAAC;IACrBF,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;EAED,MAAMqN,gBAAgB,GAAGA,CAACvM,KAAK,EAAEF,IAAI,KAAK;IACxC0M,MAAM,CAACC,IAAI,CAAC,UAAUzM,KAAK,0CAA0CF,IAAI,6GAA6G,EAAE,QAAQ,CAAC;IACjM1C,KAAK,CAACiP,IAAI,CAAC,mCAAmCvM,IAAI,EAAE,CAAC;EACvD,CAAC;EAED,MAAM4M,gBAAgB,GAAGA,CAACzM,KAAK,EAAEH,IAAI,KAAK;IACxC,IAAI6M,SAAS,CAACC,SAAS,CAAC5E,KAAK,CAAC,4BAA4B,CAAC,EAAE;MAC3DwE,MAAM,CAACC,IAAI,CAAC,OAAOxM,KAAK,EAAE,EAAE,QAAQ,CAAC;IACvC,CAAC,MAAM;MACL0M,SAAS,CAACE,SAAS,CAACC,SAAS,CAAC7M,KAAK,CAAC,CAAC8M,IAAI,CAAC,MAAM;QAC9C3P,KAAK,CAAC8G,OAAO,CAAC,wBAAwBjE,KAAK,EAAE,CAAC;MAChD,CAAC,CAAC,CAAC+M,KAAK,CAAC,MAAM;QACb5P,KAAK,CAACiP,IAAI,CAAC,UAAUpM,KAAK,EAAE,CAAC;MAC/B,CAAC,CAAC;IACJ;EACF,CAAC;EAID,MAAMgN,eAAe,GAAGA,CAAA,KAAM;IAC5B,IAAI5H,eAAe,CAAC5B,MAAM,KAAK,CAAC,EAAE;MAChCrG,KAAK,CAACuE,OAAO,CAAC,sBAAsB,CAAC;MACrC;IACF;IAEA,IAAI;MACF,MAAMuL,UAAU,GAAG7H,eAAe,CAAC7C,GAAG,CAACC,MAAM,KAAK;QAChD3C,IAAI,EAAE2C,MAAM,CAAC3C,IAAI;QACjBC,KAAK,EAAE0C,MAAM,CAAC1C,KAAK;QACnBC,KAAK,EAAEyC,MAAM,CAACzC,KAAK;QACnBC,KAAK,EAAEwC,MAAM,CAACxC,KAAK;QACnBtB,QAAQ,EAAE8D,MAAM,CAAC9D,QAAQ;QACzBuB,UAAU,EAAEuC,MAAM,CAACvC,UAAU;QAC7BxB,SAAS,EAAE+D,MAAM,CAAC/D,SAAS;QAC3BE,MAAM,EAAE6D,MAAM,CAAC7D,MAAM,CAACsL,IAAI,CAAC,IAAI,CAAC;QAChC/J,OAAO,EAAEsC,MAAM,CAACtC,OAAO;QACvBgN,UAAU,EAAE1K,MAAM,CAACrC;MACrB,CAAC,CAAC,CAAC;MAEH,MAAMgN,UAAU,GAAG,CACjB,iFAAiF,EACjF,GAAGF,UAAU,CAAC1K,GAAG,CAAC6K,GAAG,IACnB9E,MAAM,CAACC,MAAM,CAAC6E,GAAG,CAAC,CAAC7K,GAAG,CAAC2F,KAAK,IAC1B,IAAImF,MAAM,CAACnF,KAAK,CAAC,CAAC2D,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,GACvC,CAAC,CAAC5B,IAAI,CAAC,GAAG,CACZ,CAAC,CACF,CAACA,IAAI,CAAC,IAAI,CAAC;MAEZ,MAAMiB,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACgC,UAAU,CAAC,EAAE;QAAE/B,IAAI,EAAE;MAAW,CAAC,CAAC;MACzD,MAAMC,GAAG,GAAGC,GAAG,CAACC,eAAe,CAACL,IAAI,CAAC;MACrC,MAAMM,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;MACxCF,IAAI,CAACG,IAAI,GAAGN,GAAG;MACfG,IAAI,CAACI,QAAQ,GAAG,yBAAyB,IAAIZ,IAAI,CAAC,CAAC,CAACsC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM;MACrF9B,QAAQ,CAACK,IAAI,CAACC,WAAW,CAACP,IAAI,CAAC;MAC/BA,IAAI,CAACQ,KAAK,CAAC,CAAC;MACZP,QAAQ,CAACK,IAAI,CAACG,WAAW,CAACT,IAAI,CAAC;MAC/BF,GAAG,CAACY,eAAe,CAACb,GAAG,CAAC;MAExBlO,KAAK,CAAC8G,OAAO,CAAC,YAAYmB,eAAe,CAAC5B,MAAM,iBAAiB,CAAC;IACpE,CAAC,CAAC,OAAOc,KAAK,EAAE;MACdH,OAAO,CAACG,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;MACrCnH,KAAK,CAACmH,KAAK,CAAC,6CAA6C,CAAC;IAC5D;EACF,CAAC;EAED,oBACEhH,OAAA;IAAKkQ,SAAS,EAAC,aAAa;IAAAC,QAAA,gBAC1BnQ,OAAA;MAAKkQ,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1BnQ,OAAA;QAAAmQ,QAAA,EAAI;MAAc;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACvBvQ,OAAA;QAAAmQ,QAAA,EAAG;MAAkE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtE,CAAC,eAGNvQ,OAAA;MAAKkQ,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAC7BnQ,OAAA;QAAMwQ,QAAQ,EAAExM,YAAa;QAACkM,SAAS,EAAC,aAAa;QAAAC,QAAA,eACnDnQ,OAAA;UAAKkQ,SAAS,EAAC,oBAAoB;UAAAC,QAAA,gBACjCnQ,OAAA;YAAKkQ,SAAS,EAAC,sBAAsB;YAAAC,QAAA,gBACnCnQ,OAAA,CAACjB,QAAQ;cAACmR,SAAS,EAAC;YAAa;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACpCvQ,OAAA;cACE8N,IAAI,EAAC,MAAM;cACX2C,WAAW,EAAC,oGAAoG;cAChH7F,KAAK,EAAEvK,WAAY;cACnBqQ,QAAQ,EAAGzM,CAAC,IAAK3D,cAAc,CAAC2D,CAAC,CAAC0M,MAAM,CAAC/F,KAAK,CAAE;cAChDsF,SAAS,EAAC,cAAc;cACxBU,QAAQ,EAAErQ;YAAY;cAAA6P,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNvQ,OAAA;YAAKkQ,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7BnQ,OAAA;cACE8N,IAAI,EAAC,QAAQ;cACboC,SAAS,EAAC,iBAAiB;cAC3BU,QAAQ,EAAErQ,WAAY;cAAA4P,QAAA,EAErB5P,WAAW,gBACVP,OAAA,CAAAE,SAAA;gBAAAiQ,QAAA,gBACEnQ,OAAA;kBAAKkQ,SAAS,EAAC;gBAAiB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gBAErC;cAAA,eAAE,CAAC,gBAEHvQ,OAAA,CAAAE,SAAA;gBAAAiQ,QAAA,gBACEnQ,OAAA,CAACjB,QAAQ;kBAAC8R,IAAI,EAAE;gBAAG;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,UAExB;cAAA,eAAE;YACH;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK,CAAC,eACTvQ,OAAA;cACE8N,IAAI,EAAC,QAAQ;cACboC,SAAS,EAAC,mBAAmB;cAC7BY,OAAO,EAAE5J,aAAc;cACvB0J,QAAQ,EAAErQ,WAAY;cAAA4P,QAAA,EACvB;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,EAGN9P,aAAa,CAACyF,MAAM,GAAG,CAAC,iBACvBlG,OAAA;QAAKkQ,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7BnQ,OAAA;UAAKkQ,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5BnQ,OAAA;YAAKkQ,SAAS,EAAC,sBAAsB;YAAAC,QAAA,gBACnCnQ,OAAA,CAAChB,QAAQ;cAACkR,SAAS,EAAC;YAAa;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACpCvQ,OAAA;cACE8N,IAAI,EAAC,MAAM;cACX2C,WAAW,EAAC,6CAA6C;cACzD7F,KAAK,EAAEjK,WAAY;cACnB+P,QAAQ,EAAGzM,CAAC,IAAKrD,cAAc,CAACqD,CAAC,CAAC0M,MAAM,CAAC/F,KAAK,CAAE;cAChDsF,SAAS,EAAC;YAAc;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNvQ,OAAA;YAAKkQ,SAAS,EAAC,iBAAiB;YAAAC,QAAA,gBAC9BnQ,OAAA;cACE8N,IAAI,EAAC,QAAQ;cACboC,SAAS,EAAE,mCAAmCrP,WAAW,GAAG,QAAQ,GAAG,EAAE,EAAG;cAC5EiQ,OAAO,EAAEA,CAAA,KAAMhQ,cAAc,CAAC,CAACD,WAAW,CAAE;cAAAsP,QAAA,gBAE5CnQ,OAAA,CAACN,UAAU;gBAACmR,IAAI,EAAE;cAAG;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,oBAExB,EAAC1P,WAAW,gBAAGb,OAAA,CAACJ,WAAW;gBAACiR,IAAI,EAAE;cAAG;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAAGvQ,OAAA,CAACL,aAAa;gBAACkR,IAAI,EAAE;cAAG;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChE,CAAC,EACRxF,gBAAgB,CAAC,CAAC,iBACjB/K,OAAA;cACE8N,IAAI,EAAC,QAAQ;cACboC,SAAS,EAAC,+BAA+B;cACzCY,OAAO,EAAEhG,eAAgB;cAAAqF,QAAA,gBAEzBnQ,OAAA,CAACP,GAAG;gBAACoR,IAAI,EAAE;cAAG;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,aAEnB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CACT;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAGL1P,WAAW,iBACVb,OAAA;UAAKkQ,SAAS,EAAC,kBAAkB;UAAAC,QAAA,eAC/BnQ,OAAA;YAAKkQ,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BnQ,OAAA;cAAKkQ,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3BnQ,OAAA;gBAAAmQ,QAAA,EAAO;cAAkB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACjCvQ,OAAA;gBAAKkQ,SAAS,EAAC,cAAc;gBAAAC,QAAA,gBAC3BnQ,OAAA;kBACE8N,IAAI,EAAC,QAAQ;kBACb2C,WAAW,EAAC,KAAK;kBACjB7F,KAAK,EAAE7J,eAAe,CAACE,aAAc;kBACrCyP,QAAQ,EAAGzM,CAAC,IAAKyG,0BAA0B,CAAC,eAAe,EAAEzG,CAAC,CAAC0M,MAAM,CAAC/F,KAAK,CAAE;kBAC7EsF,SAAS,EAAC,oBAAoB;kBAC9Ba,GAAG,EAAC;gBAAG;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR,CAAC,eACFvQ,OAAA;kBAAAmQ,QAAA,EAAM;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACfvQ,OAAA;kBACE8N,IAAI,EAAC,QAAQ;kBACb2C,WAAW,EAAC,KAAK;kBACjB7F,KAAK,EAAE7J,eAAe,CAACG,aAAc;kBACrCwP,QAAQ,EAAGzM,CAAC,IAAKyG,0BAA0B,CAAC,eAAe,EAAEzG,CAAC,CAAC0M,MAAM,CAAC/F,KAAK,CAAE;kBAC7EsF,SAAS,EAAC,oBAAoB;kBAC9Ba,GAAG,EAAC;gBAAG;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENvQ,OAAA;cAAKkQ,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3BnQ,OAAA;gBAAAmQ,QAAA,EAAO;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC9BvQ,OAAA;gBACE8N,IAAI,EAAC,MAAM;gBACX2C,WAAW,EAAC,6BAA6B;gBACzC7F,KAAK,EAAE7J,eAAe,CAACI,SAAU;gBACjCuP,QAAQ,EAAGzM,CAAC,IAAKyG,0BAA0B,CAAC,WAAW,EAAEzG,CAAC,CAAC0M,MAAM,CAAC/F,KAAK,CAAE;gBACzEsF,SAAS,EAAC;cAAc;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAENvQ,OAAA;cAAKkQ,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3BnQ,OAAA;gBAAAmQ,QAAA,EAAO;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACvBvQ,OAAA;gBACE8N,IAAI,EAAC,MAAM;gBACX2C,WAAW,EAAC,gCAAgC;gBAC5C7F,KAAK,EAAE7J,eAAe,CAACK,QAAS;gBAChCsP,QAAQ,EAAGzM,CAAC,IAAKyG,0BAA0B,CAAC,UAAU,EAAEzG,CAAC,CAAC0M,MAAM,CAAC/F,KAAK,CAAE;gBACxEsF,SAAS,EAAC;cAAc;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAENvQ,OAAA;cAAKkQ,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3BnQ,OAAA;gBAAAmQ,QAAA,EAAO;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACrBvQ,OAAA;gBACE8N,IAAI,EAAC,MAAM;gBACX2C,WAAW,EAAC,2BAA2B;gBACvC7F,KAAK,EAAE7J,eAAe,CAACM,MAAO;gBAC9BqP,QAAQ,EAAGzM,CAAC,IAAKyG,0BAA0B,CAAC,QAAQ,EAAEzG,CAAC,CAAC0M,MAAM,CAAC/F,KAAK,CAAE;gBACtEsF,SAAS,EAAC;cAAc;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAENvQ,OAAA;cAAKkQ,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3BnQ,OAAA;gBAAAmQ,QAAA,EAAO;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC1BvQ,OAAA;gBACE8N,IAAI,EAAC,MAAM;gBACX2C,WAAW,EAAC,4BAA4B;gBACxC7F,KAAK,EAAE7J,eAAe,CAACQ,WAAY;gBACnCmP,QAAQ,EAAGzM,CAAC,IAAKyG,0BAA0B,CAAC,aAAa,EAAEzG,CAAC,CAAC0M,MAAM,CAAC/F,KAAK,CAAE;gBAC3EsF,SAAS,EAAC;cAAc;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAENvQ,OAAA;cAAKkQ,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3BnQ,OAAA;gBAAAmQ,QAAA,EAAO;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC9BvQ,OAAA;gBACE8N,IAAI,EAAC,MAAM;gBACX2C,WAAW,EAAC,kBAAkB;gBAC9B7F,KAAK,EAAE7J,eAAe,CAACS,cAAe;gBACtCkP,QAAQ,EAAGzM,CAAC,IAAKyG,0BAA0B,CAAC,gBAAgB,EAAEzG,CAAC,CAAC0M,MAAM,CAAC/F,KAAK,CAAE;gBAC9EsF,SAAS,EAAC;cAAc;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAENvQ,OAAA;cAAKkQ,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3BnQ,OAAA;gBAAAmQ,QAAA,EAAO;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACtBvQ,OAAA;gBACE8N,IAAI,EAAC,MAAM;gBACX2C,WAAW,EAAC,8BAA8B;gBAC1C7F,KAAK,EAAE7J,eAAe,CAACU,OAAQ;gBAC/BiP,QAAQ,EAAGzM,CAAC,IAAKyG,0BAA0B,CAAC,SAAS,EAAEzG,CAAC,CAAC0M,MAAM,CAAC/F,KAAK,CAAE;gBACvEsF,SAAS,EAAC;cAAc;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAENvQ,OAAA;cAAKkQ,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3BnQ,OAAA;gBAAAmQ,QAAA,EAAO;cAAiB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAChCvQ,OAAA;gBACE8N,IAAI,EAAC,QAAQ;gBACb2C,WAAW,EAAC,cAAc;gBAC1B7F,KAAK,EAAE7J,eAAe,CAACO,QAAS;gBAChCoP,QAAQ,EAAGzM,CAAC,IAAKyG,0BAA0B,CAAC,UAAU,EAAEzG,CAAC,CAAC0M,MAAM,CAAC/F,KAAK,CAAE;gBACxEsF,SAAS,EAAC,cAAc;gBACxBa,GAAG,EAAC,GAAG;gBACPC,GAAG,EAAC;cAAK;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,eAEDvQ,OAAA;UAAKkQ,SAAS,EAAC,eAAe;UAAAC,QAAA,GAC3BrI,eAAe,CAAC5B,MAAM,EAAC,MAAI,EAACzF,aAAa,CAACyF,MAAM,EAAC,UAClD,EAAC6E,gBAAgB,CAAC,CAAC,iBAAI/K,OAAA;YAAMkQ,SAAS,EAAC,oBAAoB;YAAAC,QAAA,EAAC;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3E,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAGL9P,aAAa,CAACyF,MAAM,GAAG,CAAC,gBACvBlG,OAAA;MAAKkQ,SAAS,EAAC,iBAAiB;MAAAC,QAAA,gBAC9BnQ,OAAA;QAAKkQ,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7BnQ,OAAA;UAAAmQ,QAAA,EAAI;QAAc;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACvBvQ,OAAA;UAAKkQ,SAAS,EAAC,iBAAiB;UAAAC,QAAA,eAC9BnQ,OAAA;YACEkQ,SAAS,EAAC,mBAAmB;YAC7BY,OAAO,EAAEpB,eAAgB;YACzBlN,KAAK,EAAE,UAAUsF,eAAe,CAAC5B,MAAM,iBAAkB;YAAAiK,QAAA,gBAEzDnQ,OAAA,CAACf,UAAU;cAAC4R,IAAI,EAAE;YAAG;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBACZ,EAACzI,eAAe,CAAC5B,MAAM,EAAC,GACtC;UAAA;YAAAkK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENvQ,OAAA;QAAKkQ,SAAS,EAAC,cAAc;QAAAC,QAAA,EAC1BrI,eAAe,CAAC7C,GAAG,CAAEC,MAAM,iBAC1BlF,OAAA;UAAqBkQ,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1CnQ,OAAA;YAAKkQ,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5BnQ,OAAA;cAAKkQ,SAAS,EAAC,eAAe;cAAAC,QAAA,eAC5BnQ,OAAA,CAACb,MAAM;gBAAC0R,IAAI,EAAE;cAAG;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjB,CAAC,eACNvQ,OAAA;cAAKkQ,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BnQ,OAAA;gBAAIkQ,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAEjL,MAAM,CAAC3C;cAAI;gBAAA6N,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC9CvQ,OAAA;gBAAGkQ,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAEjL,MAAM,CAAC1C;cAAK;gBAAA4N,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENvQ,OAAA;YAAKkQ,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7BnQ,OAAA;cAAKkQ,SAAS,EAAC,2BAA2B;cAAAC,QAAA,gBACxCnQ,OAAA,CAACZ,QAAQ;gBAACyR,IAAI,EAAE;cAAG;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACtBvQ,OAAA;gBAAAmQ,QAAA,EAAOjL,MAAM,CAAC9D;cAAQ;gBAAAgP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3B,CAAC,eACNvQ,OAAA;cACEkQ,SAAS,EAAC,kCAAkC;cAC5CY,OAAO,EAAEA,CAAA,KAAM9B,gBAAgB,CAAC9J,MAAM,CAACzC,KAAK,EAAEyC,MAAM,CAAC3C,IAAI,CAAE;cAC3DC,KAAK,EAAE,iBAAiB0C,MAAM,CAAC3C,IAAI,EAAG;cAAA4N,QAAA,gBAEtCnQ,OAAA,CAACX,MAAM;gBAACwR,IAAI,EAAE;cAAG;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACpBvQ,OAAA;gBAAAmQ,QAAA,EAAOjL,MAAM,CAACzC;cAAK;gBAAA2N,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC3BvQ,OAAA,CAACT,cAAc;gBAACsR,IAAI,EAAE,EAAG;gBAACX,SAAS,EAAC;cAAe;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnD,CAAC,eACNvQ,OAAA;cACEkQ,SAAS,EAAC,kCAAkC;cAC5CY,OAAO,EAAEA,CAAA,KAAM3B,gBAAgB,CAACjK,MAAM,CAACxC,KAAK,EAAEwC,MAAM,CAAC3C,IAAI,CAAE;cAC3DC,KAAK,EAAE,QAAQ0C,MAAM,CAAC3C,IAAI,EAAG;cAAA4N,QAAA,gBAE7BnQ,OAAA,CAACV,OAAO;gBAACuR,IAAI,EAAE;cAAG;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACrBvQ,OAAA;gBAAAmQ,QAAA,EAAOjL,MAAM,CAACxC;cAAK;gBAAA0N,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC3BvQ,OAAA,CAACT,cAAc;gBAACsR,IAAI,EAAE,EAAG;gBAACX,SAAS,EAAC;cAAe;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENvQ,OAAA;YAAKkQ,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7BnQ,OAAA;cAAKkQ,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BnQ,OAAA;gBAAKkQ,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,gBAC9BnQ,OAAA;kBAAAmQ,QAAA,EAAI;gBAAU;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACnBvQ,OAAA;kBAAAmQ,QAAA,EAAIjL,MAAM,CAACvC;gBAAU;kBAAAyN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvB,CAAC,eACNvQ,OAAA;gBAAKkQ,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,gBAC9BnQ,OAAA;kBAAAmQ,QAAA,EAAI;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAClBvQ,OAAA;kBAAAmQ,QAAA,EAAIjL,MAAM,CAAC/D;gBAAS;kBAAAiP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNvQ,OAAA;cAAKkQ,SAAS,EAAC,iBAAiB;cAAAC,QAAA,gBAC9BnQ,OAAA;gBAAAmQ,QAAA,EAAI;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACfvQ,OAAA;gBAAKkQ,SAAS,EAAC,aAAa;gBAAAC,QAAA,EACzBjL,MAAM,CAAC7D,MAAM,CAAC4D,GAAG,CAAC,CAACwB,KAAK,EAAEwK,KAAK,kBAC9BjR,OAAA;kBAAkBkQ,SAAS,EAAC,WAAW;kBAAAC,QAAA,EAAE1J;gBAAK,GAAnCwK,KAAK;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAqC,CACtD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENvQ,OAAA;YAAKkQ,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7BnQ,OAAA;cACEkQ,SAAS,EAAC,mBAAmB;cAC7BY,OAAO,EAAEA,CAAA,KAAMjC,UAAU,CAAC3J,MAAM,CAAE;cAAAiL,QAAA,gBAElCnQ,OAAA,CAACd,KAAK;gBAAC2R,IAAI,EAAE;cAAG;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAErB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTvQ,OAAA;cACEkQ,SAAS,EAAC,iBAAiB;cAC3BY,OAAO,EAAEA,CAAA,KAAM5F,cAAc,CAAChG,MAAM,CAAE;cAAAiL,QAAA,gBAEtCnQ,OAAA,CAACf,UAAU;gBAAC4R,IAAI,EAAE;cAAG;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,YAE1B;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA,GAxEErL,MAAM,CAAC5C,EAAE;UAAA8N,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAyEd,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,EAGLvO,UAAU,GAAG,CAAC,iBACbhC,OAAA;QAAKkQ,SAAS,EAAC,oBAAoB;QAAAC,QAAA,gBACjCnQ,OAAA;UAAKkQ,SAAS,EAAC,iBAAiB;UAAAC,QAAA,GAAC,UACvB,EAAE,CAACrO,WAAW,GAAG,CAAC,IAAIM,cAAc,GAAI,CAAC,EAAC,MAAI,EAACyD,IAAI,CAACkL,GAAG,CAACjP,WAAW,GAAGM,cAAc,EAAEF,UAAU,CAAC,EAAC,MAAI,EAACA,UAAU,EAAC,UAC5H;QAAA;UAAAkO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACNvQ,OAAA;UAAKkQ,SAAS,EAAC,qBAAqB;UAAAC,QAAA,gBAClCnQ,OAAA;YACEkQ,SAAS,EAAC,kCAAkC;YAC5CY,OAAO,EAAEA,CAAA,KAAMjJ,gBAAgB,CAAC/F,WAAW,GAAG,CAAC,CAAE;YACjD8O,QAAQ,EAAE9O,WAAW,KAAK,CAAE;YAAAqO,QAAA,EAC7B;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAETvQ,OAAA;YAAKkQ,SAAS,EAAC,cAAc;YAAAC,QAAA,EAC1BpL,KAAK,CAACmM,IAAI,CAAC;cAAEhL,MAAM,EAAEL,IAAI,CAACkL,GAAG,CAAC,CAAC,EAAE/O,UAAU;YAAE,CAAC,EAAE,CAACmP,CAAC,EAAEC,CAAC,KAAK;cACzD,IAAIC,OAAO;cACX,IAAIrP,UAAU,IAAI,CAAC,EAAE;gBACnBqP,OAAO,GAAGD,CAAC,GAAG,CAAC;cACjB,CAAC,MAAM,IAAItP,WAAW,IAAI,CAAC,EAAE;gBAC3BuP,OAAO,GAAGD,CAAC,GAAG,CAAC;cACjB,CAAC,MAAM,IAAItP,WAAW,IAAIE,UAAU,GAAG,CAAC,EAAE;gBACxCqP,OAAO,GAAGrP,UAAU,GAAG,CAAC,GAAGoP,CAAC;cAC9B,CAAC,MAAM;gBACLC,OAAO,GAAGvP,WAAW,GAAG,CAAC,GAAGsP,CAAC;cAC/B;cAEA,oBACEpR,OAAA;gBAEEkQ,SAAS,EAAE,sBAAsBpO,WAAW,KAAKuP,OAAO,GAAG,aAAa,GAAG,eAAe,EAAG;gBAC7FP,OAAO,EAAEA,CAAA,KAAMjJ,gBAAgB,CAACwJ,OAAO,CAAE;gBAAAlB,QAAA,EAExCkB;cAAO,GAJHA,OAAO;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAKN,CAAC;YAEb,CAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAENvQ,OAAA;YACEkQ,SAAS,EAAC,kCAAkC;YAC5CY,OAAO,EAAEA,CAAA,KAAMjJ,gBAAgB,CAAC/F,WAAW,GAAG,CAAC,CAAE;YACjD8O,QAAQ,EAAE9O,WAAW,KAAKE,UAAW;YAAAmO,QAAA,EACtC;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,GACJ,CAAChQ,WAAW,gBACdP,OAAA;MAAKkQ,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1BnQ,OAAA;QAAKkQ,SAAS,EAAC,YAAY;QAAAC,QAAA,eACzBnQ,OAAA,CAACjB,QAAQ;UAAC8R,IAAI,EAAE;QAAG;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnB,CAAC,eACNvQ,OAAA;QAAAmQ,QAAA,EAAI;MAAgB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACzBvQ,OAAA;QAAAmQ,QAAA,EAAG;MAAyH;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eAChIvQ,OAAA;QAAKkQ,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9BnQ,OAAA;UAAAmQ,QAAA,EAAI;QAAkB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC3BvQ,OAAA;UAAAmQ,QAAA,gBACEnQ,OAAA;YAAAmQ,QAAA,EAAI;UAAwB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACjCvQ,OAAA;YAAAmQ,QAAA,EAAI;UAAwC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACjDvQ,OAAA;YAAAmQ,QAAA,EAAI;UAA8B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,GACJ,IAAI,eAGRvQ,OAAA,CAACF,WAAW;MACVoF,MAAM,EAAExD,cAAe;MACvB4P,MAAM,EAAE1P,WAAY;MACpB2P,OAAO,EAAExC;IAAiB;MAAAqB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3B,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAACnQ,EAAA,CA79BID,aAAa;AAAAqR,EAAA,GAAbrR,aAAa;AA+9BnB,eAAeA,aAAa;AAAC,IAAAqR,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}