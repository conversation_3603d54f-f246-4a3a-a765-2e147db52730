import datetime
import json
from pymongo import MongoClient
from dotenv import load_dotenv
import os
from bson import ObjectId 
from bson.json_util import dumps
from bson import ObjectId 

# Replace with your actual MongoDB connection string and database name.
# CONNECTION_URI = "mongodb://localhost:27017/"
# DATABASE_NAME = "resume-ai-agent"
# COLLECTION_NAME = "resumes"



load_dotenv()

# Access the variables using os.getenv()
CONNECTION_URI = os.getenv('CONNECTION_URI')
DATABASE_NAME = os.getenv('DATABASE_NAME')
COLLECTION_NAME = os.getenv('COLLECTION_NAME')

class MongoDBClient:
    def __init__(self, connection_uri: str= CONNECTION_URI, db_name: str =DATABASE_NAME):
        """
        Initialize the MongoDB client with the provided connection URI and database name.
        """
        self.client = MongoClient(connection_uri)
        self.db = self.client[db_name]
        print(f"Connected to database: {db_name}")

    def insert_json_file(self, json_file_path: str, collection_name: str =COLLECTION_NAME):
        """
        Read a JSON file and insert its content into the specified collection.
        The JSON file can contain a single JSON object or a list of objects.
        """
        collection = self.db[collection_name]
        try:
            with open(json_file_path, 'r', encoding='utf-8') as file:
                data = json.load(file)
            if isinstance(data, list):
                result = collection.insert_many(data)
                print(f"Inserted {len(result.inserted_ids)} documents into '{collection_name}'.")
            else:
                result = collection.insert_one(data)
                print(f"Inserted document with id {result.inserted_id} into '{collection_name}'.")
        except Exception as e:
            print(f"Error inserting JSON data: {e}")

    
    def is_duplicate(self, checksum, collection_name: str =COLLECTION_NAME):
        collection = self.db[collection_name]
        self.checksum = checksum

        # Check if the checksum already exists in the database
        existing_pdf = collection.find_one({"checksum": checksum})
        return existing_pdf is not None

    def insert_record_withTime_checksum(self, dictOrLsRecord, collection_name: str =COLLECTION_NAME):
        """
        Read a JSON file and insert its content into the specified collection.
        The JSON file can contain a single JSON object or a list of objects.
        """
        collection = self.db[collection_name]
        try:
            if isinstance(dictOrLsRecord, list):
                result = collection.insert_many(dictOrLsRecord)
                print(f"Inserted {len(result.inserted_ids)} documents into '{collection_name}'.")
            elif isinstance(dictOrLsRecord, dict):
                result = collection.insert_one(dictOrLsRecord)
                print(f"Inserted document with id {result.inserted_id} into '{collection_name}'.")
        except Exception as e:
            print(f"Error inserting JSON data: {e}")
        
    def get_all_resume_data(self, collection_name: str =COLLECTION_NAME):
        collection = self.db[collection_name]
        
        # resumes = list(collection.find({}))  # Fetch all resumes, exclude _id if not needed
        cursorShowAll = collection.find({})  # Fetch all resumes, exclude _id if not needed
        return cursorShowAll 


    
    def is_duplicate(self, checksum, collection_name: str =COLLECTION_NAME):
        collection = self.db[collection_name]
        self.checksum = checksum

        # Check if the checksum already exists in the database
        existing_pdf = collection.find_one({"checksum": checksum})
        return existing_pdf is not None

    def insert_record_withTime_checksum(self, dictOrLsRecord, collection_name: str =COLLECTION_NAME):
        """
        Read a JSON file and insert its content into the specified collection.
        The JSON file can contain a single JSON object or a list of objects.
        """
        collection = self.db[collection_name]
        try:
            if isinstance(dictOrLsRecord, list):
                result = collection.insert_many(dictOrLsRecord)
                print(f"Inserted {len(result.inserted_ids)} documents into '{collection_name}'.")
            elif isinstance(dictOrLsRecord, dict):
                result = collection.insert_one(dictOrLsRecord)
                print(f"Inserted document with id {result.inserted_id} into '{collection_name}'.")
        except Exception as e:
            print(f"Error inserting JSON data: {e}")

    def insert_record(self, dictOrLsRecord, collection_name: str =COLLECTION_NAME):
        """
        Read a JSON file and insert its content into the specified collection.
        The JSON file can contain a single JSON object or a list of objects.
        """
        collection = self.db[collection_name]
        try:
            if isinstance(dictOrLsRecord, list):
                result = collection.insert_many(dictOrLsRecord)
                print(f"Inserted {len(result.inserted_ids)} documents into '{collection_name}'.")
            elif isinstance(dictOrLsRecord, dict):
                result = collection.insert_one(dictOrLsRecord)
                print(f"Inserted document with id {result.inserted_id} into '{collection_name}'.")
        except Exception as e:
            print(f"Error inserting JSON data: {e}")

    def execute_pipeline(self, listPipeline =[{'$limit': 1}], collection_name: str =COLLECTION_NAME):
        
        collection = self.db[collection_name]
        cursorMongoDb = collection.aggregate(listPipeline)
        return cursorMongoDb

    def insert_data(self, data, collection_name: str =COLLECTION_NAME):
        """
        Insert data directly into a specified collection.
        Data can be a single dictionary or a list of dictionaries.
        """
        collection = self.db[collection_name]
        try:
            if isinstance(data, list):
                result = collection.insert_many(data)
                print(f"Inserted {len(result.inserted_ids)} documents into '{collection_name}'.")
            else:
                result = collection.insert_one(data)
                print(f"Inserted document with id {result.inserted_id} into '{collection_name}'.")
        except Exception as e:
            print(f"Error inserting data: {e}")

    def retrieve_data(self, query: dict = None, collection_name: str =COLLECTION_NAME):
        """
        Retrieve documents from a specified collection.
        If no query is provided, all documents will be returned.
        """
        collection = self.db[collection_name]
        try:
            if query is None:
                query = {}
            documents = collection.find(query)
            return list(documents)
        except Exception as e:
            print(f"Error retrieving data: {e}")
            return []

    def update_data(self, query: dict, update_values: dict, collection_name: str =COLLECTION_NAME):
        """
        Update documents matching the query in the specified collection.
        Use proper MongoDB update operators in update_values (e.g., {"$set": {...}}).
        """
        collection = self.db[collection_name]
        try:
            result = collection.update_many(query, update_values)
            print(f"Matched {result.matched_count} documents, modified {result.modified_count} documents in '{collection_name}'.")
        except Exception as e:
            print(f"Error updating data: {e}")

    def delete_data(self, query: dict, collection_name: str =COLLECTION_NAME):
        """
        Delete documents matching the query from the specified collection.
        """
        collection = self.db[collection_name]
        try:
            result = collection.delete_many(query)
            print(f"Deleted {result.deleted_count} documents from '{collection_name}'.")
        except Exception as e:
            print(f"Error deleting data: {e}")

    @staticmethod
    def delete_data_with_id(resume_id: str):
        """
        Delete documents matching the query from the specified collection.
        """
        client = MongoClient(CONNECTION_URI)
        db = client[DATABASE_NAME]
        collection = db[COLLECTION_NAME]
        
        try:
            # Convert string ID to ObjectId if necessary
            if not ObjectId.is_valid(resume_id):
                print(f"Invalid ObjectId format: {resume_id}")
                return False  # Prevent MongoDB errors
            
            object_id = ObjectId(resume_id)  # Convert to ObjectId

            """Delete a resume by its ID."""
            result = collection.delete_one({"_id": object_id})

            if result.deleted_count > 0:
                return True  # Return True if deletion was successful
            else:
                return False  # Return False if no document was deleted

        except Exception as e:
            print(f"Error deleting data: {e}")
            return False  # Return False if an error occurred


    def close_connection(self):
        """
        Close the connection to the MongoDB server.
        """
        self.client.close()
        print("Connection closed.")

# Example usage:
if __name__ == "__main__":

    import os

    # Create an instance of MongoDBClient.
    mongo_client = MongoDBClient()
    cursorMongoDb = mongo_client.execute_pipeline([{'$match': {}}])
    # results = mongo_client.execute_query({"Resume.PersonalInformation.BirthDate": {"$exists": True}})

    # results = mongo_client.retrieve_data(COLLECTION_NAME)
    for result in cursorMongoDb:
        print(result)

    # listResumes = os.listdir(".\\sampleResumes")
    # print(listResumes)

    # for resume in listResumes:
    #     # Insert JSON data from a file.
    #     json_file_path = f".\\sampleResumes\\{resume}"  # Ensure this file exists and is properly formatted.
    #     mongo_client.insert_json_file(json_file_path)

    '''# Insert a single document directly.
    sample_document = {"name": "John Doe", "age": 30, "email": "<EMAIL>"}
    mongo_client.insert_data(COLLECTION_NAME, sample_document)

    # Retrieve all documents.
    documents = mongo_client.retrieve_data(COLLECTION_NAME)
    print("Retrieved documents:", documents)

    # Update documents matching a condition.
    update_query = {"name": "John Doe"}
    update_operation = {"$set": {"age": 31}}
    mongo_client.update_data(COLLECTION_NAME, update_query, update_operation)

    # Delete documents matching a condition.
    delete_query = {"name": "John Doe"}
    mongo_client.delete_data(COLLECTION_NAME, delete_query)

    # Close the connection when done.
    mongo_client.close_connection()'''
