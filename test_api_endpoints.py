#!/usr/bin/env python3
"""
Test script to verify API endpoints are working correctly
"""

import requests
import json
import time

def test_backend_health():
    """Test if backend is running"""
    try:
        response = requests.get('http://localhost:8002/api/health', timeout=5)
        if response.status_code == 200:
            print("✅ Backend health check passed")
            return True
        else:
            print(f"❌ Backend health check failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Backend health check failed: {e}")
        return False

def test_search_endpoint():
    """Test search endpoint"""
    try:
        payload = {"query": "Python developer"}
        response = requests.post(
            'http://localhost:8002/api/search', 
            json=payload,
            headers={'Content-Type': 'application/json'},
            timeout=10
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Search endpoint working - Found {len(data.get('results', []))} results")
            print(f"   API Status: {data.get('api_status', 'unknown')}")
            return True
        else:
            print(f"❌ Search endpoint failed: {response.status_code}")
            print(f"   Response: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Search endpoint failed: {e}")
        return False

def test_show_all_endpoint():
    """Test show all resumes endpoint"""
    try:
        response = requests.get('http://localhost:8002/api/resumes?page=1&limit=20', timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            resumes = data.get('resumes', [])
            print(f"✅ Show All endpoint working - Found {len(resumes)} resumes")
            print(f"   Source: {data.get('source', 'unknown')}")
            return True
        else:
            print(f"❌ Show All endpoint failed: {response.status_code}")
            print(f"   Response: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Show All endpoint failed: {e}")
        return False

def main():
    """Run all tests"""
    print("🧪 Testing Resume AI Agent API Endpoints")
    print("=" * 50)
    
    # Test backend health
    if not test_backend_health():
        print("\n❌ Backend is not running. Please start the backend first:")
        print("   python backend_api.py")
        return
    
    print()
    
    # Test search endpoint
    test_search_endpoint()
    
    print()
    
    # Test show all endpoint
    test_show_all_endpoint()
    
    print("\n" + "=" * 50)
    print("🎯 Test Summary:")
    print("   - Backend Health: ✅")
    print("   - Search Functionality: Check above")
    print("   - Show All Functionality: Check above")
    print("\n💡 If tests pass, the React frontend should work correctly!")
    print("   Frontend URL: http://localhost:3000")

if __name__ == "__main__":
    main()
