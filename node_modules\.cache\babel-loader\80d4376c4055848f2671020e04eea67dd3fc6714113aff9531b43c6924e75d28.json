{"ast": null, "code": "import { __awaiter } from \"tslib\";\nimport { toFileWithPath } from './file';\nconst FILES_TO_IGNORE = [\n// Thumbnail cache files for macOS and Windows\n'.DS_Store',\n// macOs\n'Thumbs.db' // Windows\n];\n/**\n * Convert a DragEvent's DataTrasfer object to a list of File objects\n * NOTE: If some of the items are folders,\n * everything will be flattened and placed in the same list but the paths will be kept as a {path} property.\n *\n * EXPERIMENTAL: A list of https://developer.mozilla.org/en-US/docs/Web/API/FileSystemHandle objects can also be passed as an arg\n * and a list of File objects will be returned.\n *\n * @param evt\n */\nexport function fromEvent(evt) {\n  return __awaiter(this, void 0, void 0, function* () {\n    if (isObject(evt) && isDataTransfer(evt.dataTransfer)) {\n      return getDataTransferFiles(evt.dataTransfer, evt.type);\n    } else if (isChangeEvt(evt)) {\n      return getInputFiles(evt);\n    } else if (Array.isArray(evt) && evt.every(item => 'getFile' in item && typeof item.getFile === 'function')) {\n      return getFsHandleFiles(evt);\n    }\n    return [];\n  });\n}\nfunction isDataTransfer(value) {\n  return isObject(value);\n}\nfunction isChangeEvt(value) {\n  return isObject(value) && isObject(value.target);\n}\nfunction isObject(v) {\n  return typeof v === 'object' && v !== null;\n}\nfunction getInputFiles(evt) {\n  return fromList(evt.target.files).map(file => toFileWithPath(file));\n}\n// Ee expect each handle to be https://developer.mozilla.org/en-US/docs/Web/API/FileSystemFileHandle\nfunction getFsHandleFiles(handles) {\n  return __awaiter(this, void 0, void 0, function* () {\n    const files = yield Promise.all(handles.map(h => h.getFile()));\n    return files.map(file => toFileWithPath(file));\n  });\n}\nfunction getDataTransferFiles(dt, type) {\n  return __awaiter(this, void 0, void 0, function* () {\n    // IE11 does not support dataTransfer.items\n    // See https://developer.mozilla.org/en-US/docs/Web/API/DataTransfer/items#Browser_compatibility\n    if (dt.items) {\n      const items = fromList(dt.items).filter(item => item.kind === 'file');\n      // According to https://html.spec.whatwg.org/multipage/dnd.html#dndevents,\n      // only 'dragstart' and 'drop' has access to the data (source node)\n      if (type !== 'drop') {\n        return items;\n      }\n      const files = yield Promise.all(items.map(toFilePromises));\n      return noIgnoredFiles(flatten(files));\n    }\n    return noIgnoredFiles(fromList(dt.files).map(file => toFileWithPath(file)));\n  });\n}\nfunction noIgnoredFiles(files) {\n  return files.filter(file => FILES_TO_IGNORE.indexOf(file.name) === -1);\n}\n// IE11 does not support Array.from()\n// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/from#Browser_compatibility\n// https://developer.mozilla.org/en-US/docs/Web/API/FileList\n// https://developer.mozilla.org/en-US/docs/Web/API/DataTransferItemList\nfunction fromList(items) {\n  if (items === null) {\n    return [];\n  }\n  const files = [];\n  // tslint:disable: prefer-for-of\n  for (let i = 0; i < items.length; i++) {\n    const file = items[i];\n    files.push(file);\n  }\n  return files;\n}\n// https://developer.mozilla.org/en-US/docs/Web/API/DataTransferItem\nfunction toFilePromises(item) {\n  if (typeof item.webkitGetAsEntry !== 'function') {\n    return fromDataTransferItem(item);\n  }\n  const entry = item.webkitGetAsEntry();\n  // Safari supports dropping an image node from a different window and can be retrieved using\n  // the DataTransferItem.getAsFile() API\n  // NOTE: FileSystemEntry.file() throws if trying to get the file\n  if (entry && entry.isDirectory) {\n    return fromDirEntry(entry);\n  }\n  return fromDataTransferItem(item, entry);\n}\nfunction flatten(items) {\n  return items.reduce((acc, files) => [...acc, ...(Array.isArray(files) ? flatten(files) : [files])], []);\n}\nfunction fromDataTransferItem(item, entry) {\n  return __awaiter(this, void 0, void 0, function* () {\n    var _a;\n    // Check if we're in a secure context; due to a bug in Chrome (as far as we know)\n    // the browser crashes when calling this API (yet to be confirmed as a consistent behaviour).\n    //\n    // See:\n    // - https://issues.chromium.org/issues/40186242\n    // - https://github.com/react-dropzone/react-dropzone/issues/1397\n    if (globalThis.isSecureContext && typeof item.getAsFileSystemHandle === 'function') {\n      const h = yield item.getAsFileSystemHandle();\n      if (h === null) {\n        throw new Error(`${item} is not a File`);\n      }\n      // It seems that the handle can be `undefined` (see https://github.com/react-dropzone/file-selector/issues/120),\n      // so we check if it isn't; if it is, the code path continues to the next API (`getAsFile`).\n      if (h !== undefined) {\n        const file = yield h.getFile();\n        file.handle = h;\n        return toFileWithPath(file);\n      }\n    }\n    const file = item.getAsFile();\n    if (!file) {\n      throw new Error(`${item} is not a File`);\n    }\n    const fwp = toFileWithPath(file, (_a = entry === null || entry === void 0 ? void 0 : entry.fullPath) !== null && _a !== void 0 ? _a : undefined);\n    return fwp;\n  });\n}\n// https://developer.mozilla.org/en-US/docs/Web/API/FileSystemEntry\nfunction fromEntry(entry) {\n  return __awaiter(this, void 0, void 0, function* () {\n    return entry.isDirectory ? fromDirEntry(entry) : fromFileEntry(entry);\n  });\n}\n// https://developer.mozilla.org/en-US/docs/Web/API/FileSystemDirectoryEntry\nfunction fromDirEntry(entry) {\n  const reader = entry.createReader();\n  return new Promise((resolve, reject) => {\n    const entries = [];\n    function readEntries() {\n      // https://developer.mozilla.org/en-US/docs/Web/API/FileSystemDirectoryEntry/createReader\n      // https://developer.mozilla.org/en-US/docs/Web/API/FileSystemDirectoryReader/readEntries\n      reader.readEntries(batch => __awaiter(this, void 0, void 0, function* () {\n        if (!batch.length) {\n          // Done reading directory\n          try {\n            const files = yield Promise.all(entries);\n            resolve(files);\n          } catch (err) {\n            reject(err);\n          }\n        } else {\n          const items = Promise.all(batch.map(fromEntry));\n          entries.push(items);\n          // Continue reading\n          readEntries();\n        }\n      }), err => {\n        reject(err);\n      });\n    }\n    readEntries();\n  });\n}\n// https://developer.mozilla.org/en-US/docs/Web/API/FileSystemFileEntry\nfunction fromFileEntry(entry) {\n  return __awaiter(this, void 0, void 0, function* () {\n    return new Promise((resolve, reject) => {\n      entry.file(file => {\n        const fwp = toFileWithPath(file, entry.fullPath);\n        resolve(fwp);\n      }, err => {\n        reject(err);\n      });\n    });\n  });\n}", "map": {"version": 3, "names": ["toFileWithPath", "FILES_TO_IGNORE", "fromEvent", "evt", "isObject", "isDataTransfer", "dataTransfer", "getDataTransferFiles", "type", "isChangeEvt", "getInputFiles", "Array", "isArray", "every", "item", "getFile", "getFsHandleFiles", "value", "target", "v", "fromList", "files", "map", "file", "handles", "Promise", "all", "h", "dt", "items", "filter", "kind", "toFilePromises", "noIgnoredFiles", "flatten", "indexOf", "name", "i", "length", "push", "webkitGetAsEntry", "fromDataTransferItem", "entry", "isDirectory", "fromDirEntry", "reduce", "acc", "globalThis", "isSecureContext", "getAsFileSystemHandle", "Error", "undefined", "handle", "getAsFile", "fwp", "_a", "fullPath", "fromEntry", "fromFileEntry", "reader", "createReader", "resolve", "reject", "entries", "readEntries", "batch", "__awaiter", "err"], "sources": ["D:\\Projects\\SQL-Agent-Task-Submission-main\\SQL-Agent-Task-Submission-main\\node_modules\\file-selector\\src\\file-selector.ts"], "sourcesContent": ["import {FileWithPath, toFileWithPath} from './file';\n\n\nconst FILES_TO_IGNORE = [\n    // Thumbnail cache files for macOS and Windows\n    '.DS_Store', // macOs\n    'Thumbs.db'  // Windows\n];\n\n\n/**\n * Convert a DragEvent's DataTrasfer object to a list of File objects\n * NOTE: If some of the items are folders,\n * everything will be flattened and placed in the same list but the paths will be kept as a {path} property.\n *\n * EXPERIMENTAL: A list of https://developer.mozilla.org/en-US/docs/Web/API/FileSystemHandle objects can also be passed as an arg\n * and a list of File objects will be returned.\n *\n * @param evt\n */\nexport async function fromEvent(evt: Event | any): Promise<(FileWithPath | DataTransferItem)[]> {\n    if (isObject<DragEvent>(evt) && isDataTransfer(evt.dataTransfer)) {\n        return getDataTransferFiles(evt.dataTransfer, evt.type);\n    } else if (isChangeEvt(evt)) {\n        return getInputFiles(evt);\n    } else if (Array.isArray(evt) && evt.every(item => 'getFile' in item && typeof item.getFile === 'function')) {\n        return getFsHandleFiles(evt)\n    }\n    return [];\n}\n\nfunction isDataTransfer(value: any): value is DataTransfer {\n    return isObject(value);\n}\n\nfunction isChangeEvt(value: any): value is Event {\n    return isObject<Event>(value) && isObject(value.target);\n}\n\nfunction isObject<T>(v: any): v is T {\n    return typeof v === 'object' && v !== null\n}\n\nfunction getInputFiles(evt: Event) {\n    return fromList<FileWithPath>((evt.target as HTMLInputElement).files).map(file => toFileWithPath(file));\n}\n\n// Ee expect each handle to be https://developer.mozilla.org/en-US/docs/Web/API/FileSystemFileHandle\nasync function getFsHandleFiles(handles: any[]) {\n    const files = await Promise.all(handles.map(h => h.getFile()));\n    return files.map(file => toFileWithPath(file));\n}\n\n\nasync function getDataTransferFiles(dt: DataTransfer, type: string) {\n    // IE11 does not support dataTransfer.items\n    // See https://developer.mozilla.org/en-US/docs/Web/API/DataTransfer/items#Browser_compatibility\n    if (dt.items) {\n        const items = fromList<DataTransferItem>(dt.items)\n            .filter(item => item.kind === 'file');\n        // According to https://html.spec.whatwg.org/multipage/dnd.html#dndevents,\n        // only 'dragstart' and 'drop' has access to the data (source node)\n        if (type !== 'drop') {\n            return items;\n        }\n        const files = await Promise.all(items.map(toFilePromises));\n        return noIgnoredFiles(flatten<FileWithPath>(files));\n    }\n\n    return noIgnoredFiles(fromList<FileWithPath>(dt.files)\n        .map(file => toFileWithPath(file)));\n}\n\nfunction noIgnoredFiles(files: FileWithPath[]) {\n    return files.filter(file => FILES_TO_IGNORE.indexOf(file.name) === -1);\n}\n\n// IE11 does not support Array.from()\n// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/from#Browser_compatibility\n// https://developer.mozilla.org/en-US/docs/Web/API/FileList\n// https://developer.mozilla.org/en-US/docs/Web/API/DataTransferItemList\nfunction fromList<T>(items: DataTransferItemList | FileList | null): T[] {\n    if (items === null) {\n        return [];\n    }\n\n    const files = [];\n\n    // tslint:disable: prefer-for-of\n    for (let i = 0; i < items.length; i++) {\n        const file = items[i];\n        files.push(file);\n    }\n\n    return files as any;\n}\n\n// https://developer.mozilla.org/en-US/docs/Web/API/DataTransferItem\nfunction toFilePromises(item: DataTransferItem) {\n    if (typeof item.webkitGetAsEntry !== 'function') {\n        return fromDataTransferItem(item);\n    }\n\n    const entry = item.webkitGetAsEntry();\n\n    // Safari supports dropping an image node from a different window and can be retrieved using\n    // the DataTransferItem.getAsFile() API\n    // NOTE: FileSystemEntry.file() throws if trying to get the file\n    if (entry && entry.isDirectory) {\n        return fromDirEntry(entry) as any;\n    }\n\n    return fromDataTransferItem(item, entry);\n}\n\nfunction flatten<T>(items: any[]): T[] {\n    return items.reduce((acc, files) => [\n        ...acc,\n        ...(Array.isArray(files) ? flatten(files) : [files])\n    ], []);\n}\n\nasync function fromDataTransferItem(item: DataTransferItem, entry?: FileSystemEntry | null) {\n    // Check if we're in a secure context; due to a bug in Chrome (as far as we know)\n    // the browser crashes when calling this API (yet to be confirmed as a consistent behaviour).\n    //\n    // See:\n    // - https://issues.chromium.org/issues/40186242\n    // - https://github.com/react-dropzone/react-dropzone/issues/1397\n    if (globalThis.isSecureContext && typeof (item as any).getAsFileSystemHandle === 'function') {\n        const h = await (item as any).getAsFileSystemHandle();\n        if (h === null) {\n            throw new Error(`${item} is not a File`);\n        }\n        // It seems that the handle can be `undefined` (see https://github.com/react-dropzone/file-selector/issues/120),\n        // so we check if it isn't; if it is, the code path continues to the next API (`getAsFile`).\n        if (h !== undefined) {\n            const file = await h.getFile();\n            file.handle = h;\n            return toFileWithPath(file);\n        }\n    }\n    const file = item.getAsFile();\n    if (!file) {\n        throw new Error(`${item} is not a File`);\n    }\n    const fwp = toFileWithPath(file, entry?.fullPath ?? undefined);\n    return fwp;\n}\n\n// https://developer.mozilla.org/en-US/docs/Web/API/FileSystemEntry\nasync function fromEntry(entry: any) {\n    return entry.isDirectory ? fromDirEntry(entry) : fromFileEntry(entry);\n}\n\n// https://developer.mozilla.org/en-US/docs/Web/API/FileSystemDirectoryEntry\nfunction fromDirEntry(entry: any) {\n    const reader = entry.createReader();\n\n    return new Promise<FileArray[]>((resolve, reject) => {\n        const entries: Promise<FileValue[]>[] = [];\n\n        function readEntries() {\n            // https://developer.mozilla.org/en-US/docs/Web/API/FileSystemDirectoryEntry/createReader\n            // https://developer.mozilla.org/en-US/docs/Web/API/FileSystemDirectoryReader/readEntries\n            reader.readEntries(async (batch: any[]) => {\n                if (!batch.length) {\n                    // Done reading directory\n                    try {\n                        const files = await Promise.all(entries);\n                        resolve(files);\n                    } catch (err) {\n                        reject(err);\n                    }\n                } else {\n                    const items = Promise.all(batch.map(fromEntry));\n                    entries.push(items);\n\n                    // Continue reading\n                    readEntries();\n                }\n            }, (err: any) => {\n                reject(err);\n            });\n        }\n\n        readEntries();\n    });\n}\n\n// https://developer.mozilla.org/en-US/docs/Web/API/FileSystemFileEntry\nasync function fromFileEntry(entry: any) {\n    return new Promise<FileWithPath>((resolve, reject) => {\n        entry.file((file: FileWithPath) => {\n            const fwp = toFileWithPath(file, entry.fullPath);\n            resolve(fwp);\n        }, (err: any) => {\n            reject(err);\n        });\n    });\n}\n\n// Infinite type recursion\n// https://github.com/Microsoft/TypeScript/issues/3496#issuecomment-*********\ninterface FileArray extends Array<FileValue> {}\ntype FileValue = FileWithPath\n    | FileArray[];\n"], "mappings": ";AAAA,SAAsBA,cAAc,QAAO,QAAQ;AAGnD,MAAMC,eAAe,GAAG;AACpB;AACA,WAAW;AAAE;AACb,WAAW,CAAE;AAAA,CAChB;AAGD;;;;;;;;;;AAUA,OAAM,SAAgBC,SAASA,CAACC,GAAgB;;IAC5C,IAAIC,QAAQ,CAAYD,GAAG,CAAC,IAAIE,cAAc,CAACF,GAAG,CAACG,YAAY,CAAC,EAAE;MAC9D,OAAOC,oBAAoB,CAACJ,GAAG,CAACG,YAAY,EAAEH,GAAG,CAACK,IAAI,CAAC;IAC3D,CAAC,MAAM,IAAIC,WAAW,CAACN,GAAG,CAAC,EAAE;MACzB,OAAOO,aAAa,CAACP,GAAG,CAAC;IAC7B,CAAC,MAAM,IAAIQ,KAAK,CAACC,OAAO,CAACT,GAAG,CAAC,IAAIA,GAAG,CAACU,KAAK,CAACC,IAAI,IAAI,SAAS,IAAIA,IAAI,IAAI,OAAOA,IAAI,CAACC,OAAO,KAAK,UAAU,CAAC,EAAE;MACzG,OAAOC,gBAAgB,CAACb,GAAG,CAAC;IAChC;IACA,OAAO,EAAE;EACb,CAAC;;AAED,SAASE,cAAcA,CAACY,KAAU;EAC9B,OAAOb,QAAQ,CAACa,KAAK,CAAC;AAC1B;AAEA,SAASR,WAAWA,CAACQ,KAAU;EAC3B,OAAOb,QAAQ,CAAQa,KAAK,CAAC,IAAIb,QAAQ,CAACa,KAAK,CAACC,MAAM,CAAC;AAC3D;AAEA,SAASd,QAAQA,CAAIe,CAAM;EACvB,OAAO,OAAOA,CAAC,KAAK,QAAQ,IAAIA,CAAC,KAAK,IAAI;AAC9C;AAEA,SAAST,aAAaA,CAACP,GAAU;EAC7B,OAAOiB,QAAQ,CAAgBjB,GAAG,CAACe,MAA2B,CAACG,KAAK,CAAC,CAACC,GAAG,CAACC,IAAI,IAAIvB,cAAc,CAACuB,IAAI,CAAC,CAAC;AAC3G;AAEA;AACA,SAAeP,gBAAgBA,CAACQ,OAAc;;IAC1C,MAAMH,KAAK,GAAG,MAAMI,OAAO,CAACC,GAAG,CAACF,OAAO,CAACF,GAAG,CAACK,CAAC,IAAIA,CAAC,CAACZ,OAAO,EAAE,CAAC,CAAC;IAC9D,OAAOM,KAAK,CAACC,GAAG,CAACC,IAAI,IAAIvB,cAAc,CAACuB,IAAI,CAAC,CAAC;EAClD,CAAC;;AAGD,SAAehB,oBAAoBA,CAACqB,EAAgB,EAAEpB,IAAY;;IAC9D;IACA;IACA,IAAIoB,EAAE,CAACC,KAAK,EAAE;MACV,MAAMA,KAAK,GAAGT,QAAQ,CAAmBQ,EAAE,CAACC,KAAK,CAAC,CAC7CC,MAAM,CAAChB,IAAI,IAAIA,IAAI,CAACiB,IAAI,KAAK,MAAM,CAAC;MACzC;MACA;MACA,IAAIvB,IAAI,KAAK,MAAM,EAAE;QACjB,OAAOqB,KAAK;MAChB;MACA,MAAMR,KAAK,GAAG,MAAMI,OAAO,CAACC,GAAG,CAACG,KAAK,CAACP,GAAG,CAACU,cAAc,CAAC,CAAC;MAC1D,OAAOC,cAAc,CAACC,OAAO,CAAeb,KAAK,CAAC,CAAC;IACvD;IAEA,OAAOY,cAAc,CAACb,QAAQ,CAAeQ,EAAE,CAACP,KAAK,CAAC,CACjDC,GAAG,CAACC,IAAI,IAAIvB,cAAc,CAACuB,IAAI,CAAC,CAAC,CAAC;EAC3C,CAAC;;AAED,SAASU,cAAcA,CAACZ,KAAqB;EACzC,OAAOA,KAAK,CAACS,MAAM,CAACP,IAAI,IAAItB,eAAe,CAACkC,OAAO,CAACZ,IAAI,CAACa,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;AAC1E;AAEA;AACA;AACA;AACA;AACA,SAAShB,QAAQA,CAAIS,KAA6C;EAC9D,IAAIA,KAAK,KAAK,IAAI,EAAE;IAChB,OAAO,EAAE;EACb;EAEA,MAAMR,KAAK,GAAG,EAAE;EAEhB;EACA,KAAK,IAAIgB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGR,KAAK,CAACS,MAAM,EAAED,CAAC,EAAE,EAAE;IACnC,MAAMd,IAAI,GAAGM,KAAK,CAACQ,CAAC,CAAC;IACrBhB,KAAK,CAACkB,IAAI,CAAChB,IAAI,CAAC;EACpB;EAEA,OAAOF,KAAY;AACvB;AAEA;AACA,SAASW,cAAcA,CAAClB,IAAsB;EAC1C,IAAI,OAAOA,IAAI,CAAC0B,gBAAgB,KAAK,UAAU,EAAE;IAC7C,OAAOC,oBAAoB,CAAC3B,IAAI,CAAC;EACrC;EAEA,MAAM4B,KAAK,GAAG5B,IAAI,CAAC0B,gBAAgB,EAAE;EAErC;EACA;EACA;EACA,IAAIE,KAAK,IAAIA,KAAK,CAACC,WAAW,EAAE;IAC5B,OAAOC,YAAY,CAACF,KAAK,CAAQ;EACrC;EAEA,OAAOD,oBAAoB,CAAC3B,IAAI,EAAE4B,KAAK,CAAC;AAC5C;AAEA,SAASR,OAAOA,CAAIL,KAAY;EAC5B,OAAOA,KAAK,CAACgB,MAAM,CAAC,CAACC,GAAG,EAAEzB,KAAK,KAAK,CAChC,GAAGyB,GAAG,EACN,IAAInC,KAAK,CAACC,OAAO,CAACS,KAAK,CAAC,GAAGa,OAAO,CAACb,KAAK,CAAC,GAAG,CAACA,KAAK,CAAC,CAAC,CACvD,EAAE,EAAE,CAAC;AACV;AAEA,SAAeoB,oBAAoBA,CAAC3B,IAAsB,EAAE4B,KAA8B;;;IACtF;IACA;IACA;IACA;IACA;IACA;IACA,IAAIK,UAAU,CAACC,eAAe,IAAI,OAAQlC,IAAY,CAACmC,qBAAqB,KAAK,UAAU,EAAE;MACzF,MAAMtB,CAAC,GAAG,MAAOb,IAAY,CAACmC,qBAAqB,EAAE;MACrD,IAAItB,CAAC,KAAK,IAAI,EAAE;QACZ,MAAM,IAAIuB,KAAK,CAAC,GAAGpC,IAAI,gBAAgB,CAAC;MAC5C;MACA;MACA;MACA,IAAIa,CAAC,KAAKwB,SAAS,EAAE;QACjB,MAAM5B,IAAI,GAAG,MAAMI,CAAC,CAACZ,OAAO,EAAE;QAC9BQ,IAAI,CAAC6B,MAAM,GAAGzB,CAAC;QACf,OAAO3B,cAAc,CAACuB,IAAI,CAAC;MAC/B;IACJ;IACA,MAAMA,IAAI,GAAGT,IAAI,CAACuC,SAAS,EAAE;IAC7B,IAAI,CAAC9B,IAAI,EAAE;MACP,MAAM,IAAI2B,KAAK,CAAC,GAAGpC,IAAI,gBAAgB,CAAC;IAC5C;IACA,MAAMwC,GAAG,GAAGtD,cAAc,CAACuB,IAAI,EAAE,CAAAgC,EAAA,GAAAb,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEc,QAAQ,cAAAD,EAAA,cAAAA,EAAA,GAAIJ,SAAS,CAAC;IAC9D,OAAOG,GAAG;EACd,CAAC;;AAED;AACA,SAAeG,SAASA,CAACf,KAAU;;IAC/B,OAAOA,KAAK,CAACC,WAAW,GAAGC,YAAY,CAACF,KAAK,CAAC,GAAGgB,aAAa,CAAChB,KAAK,CAAC;EACzE,CAAC;;AAED;AACA,SAASE,YAAYA,CAACF,KAAU;EAC5B,MAAMiB,MAAM,GAAGjB,KAAK,CAACkB,YAAY,EAAE;EAEnC,OAAO,IAAInC,OAAO,CAAc,CAACoC,OAAO,EAAEC,MAAM,KAAI;IAChD,MAAMC,OAAO,GAA2B,EAAE;IAE1C,SAASC,WAAWA,CAAA;MAChB;MACA;MACAL,MAAM,CAACK,WAAW,CAAQC,KAAY,IAAIC,SAAA;QACtC,IAAI,CAACD,KAAK,CAAC3B,MAAM,EAAE;UACf;UACA,IAAI;YACA,MAAMjB,KAAK,GAAG,MAAMI,OAAO,CAACC,GAAG,CAACqC,OAAO,CAAC;YACxCF,OAAO,CAACxC,KAAK,CAAC;UAClB,CAAC,CAAC,OAAO8C,GAAG,EAAE;YACVL,MAAM,CAACK,GAAG,CAAC;UACf;QACJ,CAAC,MAAM;UACH,MAAMtC,KAAK,GAAGJ,OAAO,CAACC,GAAG,CAACuC,KAAK,CAAC3C,GAAG,CAACmC,SAAS,CAAC,CAAC;UAC/CM,OAAO,CAACxB,IAAI,CAACV,KAAK,CAAC;UAEnB;UACAmC,WAAW,EAAE;QACjB;MACJ,CAAC,GAAGG,GAAQ,IAAI;QACZL,MAAM,CAACK,GAAG,CAAC;MACf,CAAC,CAAC;IACN;IAEAH,WAAW,EAAE;EACjB,CAAC,CAAC;AACN;AAEA;AACA,SAAeN,aAAaA,CAAChB,KAAU;;IACnC,OAAO,IAAIjB,OAAO,CAAe,CAACoC,OAAO,EAAEC,MAAM,KAAI;MACjDpB,KAAK,CAACnB,IAAI,CAAEA,IAAkB,IAAI;QAC9B,MAAM+B,GAAG,GAAGtD,cAAc,CAACuB,IAAI,EAAEmB,KAAK,CAACc,QAAQ,CAAC;QAChDK,OAAO,CAACP,GAAG,CAAC;MAChB,CAAC,EAAGa,GAAQ,IAAI;QACZL,MAAM,CAACK,GAAG,CAAC;MACf,CAAC,CAAC;IACN,CAAC,CAAC;EACN,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}