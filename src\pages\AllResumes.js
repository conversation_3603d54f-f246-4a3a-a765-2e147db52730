import React, { useState, useEffect } from 'react';
import { FiUsers, FiSearch, FiDownload, <PERSON>Eye, FiUser, FiMapPin } from 'react-icons/fi';
import './AllResumes.css';

const AllResumes = () => {
  const [resumes, setResumes] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    // Simulate loading data
    setTimeout(() => {
      setResumes([
        { id: 1, name: '<PERSON>', title: 'Software Engineer', location: 'San Francisco, CA', skills: ['Python', 'React'] },
        { id: 2, name: '<PERSON>', title: 'Frontend Developer', location: 'New York, NY', skills: ['React', 'TypeScript'] },
        { id: 3, name: '<PERSON>', title: 'DevOps Engineer', location: 'Austin, TX', skills: ['Docker', 'AWS'] }
      ]);
      setLoading(false);
    }, 1000);
  }, []);

  const filteredResumes = resumes.filter(resume =>
    resume.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    resume.title.toLowerCase().includes(searchTerm.toLowerCase())
  );

  if (loading) {
    return (
      <div className="all-resumes-page">
        <div className="loading-container">
          <div className="loading-spinner large" />
          <p>Loading resumes...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="all-resumes-page">
      <div className="page-header">
        <h1>All Resumes</h1>
        <p>Browse and manage your complete resume database</p>
      </div>

      <div className="controls-section">
        <div className="search-control">
          <FiSearch className="search-icon" />
          <input
            type="text"
            placeholder="Search resumes..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="search-input"
          />
        </div>
        <div className="stats">
          <FiUsers size={16} />
          <span>{filteredResumes.length} resumes</span>
        </div>
      </div>

      <div className="resumes-grid">
        {filteredResumes.map(resume => (
          <div key={resume.id} className="resume-card">
            <div className="resume-avatar">
              <FiUser size={24} />
            </div>
            <h3>{resume.name}</h3>
            <p className="title">{resume.title}</p>
            <div className="location">
              <FiMapPin size={14} />
              <span>{resume.location}</span>
            </div>
            <div className="skills">
              {resume.skills.map((skill, index) => (
                <span key={index} className="skill-tag">{skill}</span>
              ))}
            </div>
            <div className="actions">
              <button className="btn btn-secondary">
                <FiEye size={16} />
                View
              </button>
              <button className="btn btn-primary">
                <FiDownload size={16} />
                Download
              </button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default AllResumes;
