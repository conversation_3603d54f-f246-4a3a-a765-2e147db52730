# SQL Agent - Natural Language to SQL Query Engine

## Introduction
The SQL Agent is an AI-powered solution that enables users to interact with SQL databases using natural language queries. It dynamically generates SQL queries, executes them, and returns user-friendly results. This system bridges the gap between technical SQL queries and human-friendly interactions, making database querying more accessible for non-technical users.

## Project Requirements
### Technologies Used:
- **LLM Model:** OpenAI GPT-4o
- **Backend:** FastAPI
- **Frontend:** Streamlit
- **Database:** MySQL
- **Libraries:**
  - `mysql.connector` (for database connection)
  - `openai` (for LLM integration)
  - `pydantic` (for request validation)
  - `streamlit` (for UI)
  - `fastapi` (for API development)

## Solution Overview
This solution allows users to query an SQL database in plain English. The SQL Agent follows these steps:
- **User Input:** Users enter their query in natural language.

**Part-1. SQL Query Generation:**
   - The input is processed by OpenAI's GPT-4o model.
   - The model converts the natural language query into a structured SQL query.

**Part-2. Database Query Execution:**
   - The generated SQL query is executed using the `mysql.connector` module.
   - The query retrieves data from one or more of the following tables:
     - `employees` (employee information)
     - `employee_in_out_time` (check-ins and check-outs)
     - `employee_leave` (leave records)

**Part-3. Response Processing:**
   - The retrieved database results, along with the original query and SQL query, are passed to GPT-4o.
   - The model generates a natural language response based on the query results.

**UI Display:** The response is displayed in a user-friendly format via a Streamlit-based web UI.

## Why GPT-4o?
Several open-source models like Mistral and Llama were tested but did not perform well on highly complex production grade queries. GPT-4o was chosen for its superior performance in query generation and natural language response quality. Structured outputs and OpenAI's prompt guidelines were followed to ensure accurate query execution and response generation.

## How to Run the Project
### Prerequisites
- Python 3.9+
- MySQL database
- OpenAI API key
- Required Python libraries:
  ```sh
  pip install -r requirements.txt
  ```
  If you encounter any errors, install the missing modules manually:
  ```sh
  pip install python-dotenv
  pip install mysql-connector-python
  pip install streamlit
  pip install fastapi
  pip install openai
  pip install uvicorn
  ```
- Update the `.env` file with your MySQL `username` and `password`.

### Steps to Run
1. **Create a Virtual Environment:**
   ```sh
   python -m venv envSqlAgent
   source envSqlAgent/bin/activate  # On macOS/Linux
   envSqlAgent\Scripts\activate     # On Windows
   ```
2. **Install Dependencies:**
   ```sh
   pip install -r requirements.txt
   ```
3. **Start the FastAPI Backend:**
   ```sh
   uvicorn app:app --reload
   ```
4. **Run the Streamlit UI:**
   ```sh
   streamlit run frontend.py
   ```

## Conclusion
This SQL Agent provides an intuitive way for users to query databases without SQL knowledge. By leveraging OpenAI's GPT-4o model and integrating it with FastAPI and Streamlit, we have created a robust and easily deployable solution. The system efficiently converts natural language into SQL queries, retrieves accurate results, and presents them in a user-friendly manner.

