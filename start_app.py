#!/usr/bin/env python3
"""
Startup script for the Resume AI Agent application.
This script helps users start both the backend API and frontend easily.
"""

import os
import sys
import time
import subprocess
import threading
from pathlib import Path

def print_banner():
    """Print application banner."""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                    🤖 Resume AI Agent                        ║
    ║                                                              ║
    ║              Intelligent Resume Search & Management         ║
    ║                     Version 2.0 Enhanced                    ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def check_dependencies():
    """Check if required dependencies are installed."""
    print("🔍 Checking dependencies...")
    
    required_packages = [
        'streamlit',
        'fastapi',
        'uvicorn',
        'pymongo',
        'requests',
        'python-dotenv',
        'openai'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package} - MISSING")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n⚠️ Missing packages: {', '.join(missing_packages)}")
        print("Install them with: pip install " + " ".join(missing_packages))
        return False
    
    print("✅ All dependencies are installed!")
    return True

def check_environment():
    """Check environment setup."""
    print("\n🔧 Checking environment...")
    
    # Check .env file
    if not os.path.exists('.env'):
        print("❌ .env file not found")
        return False
    
    # Load environment variables
    from dotenv import load_dotenv
    load_dotenv()
    
    required_vars = ['CONNECTION_URI', 'DATABASE_NAME', 'COLLECTION_NAME', 'STR_API_URL']
    missing_vars = []
    
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        print(f"❌ Missing environment variables: {', '.join(missing_vars)}")
        return False
    
    print("✅ Environment configuration is correct!")
    return True

def start_backend():
    """Start the FastAPI backend server."""
    print("\n🚀 Starting backend API server...")
    
    try:
        # Start uvicorn server
        cmd = [sys.executable, "-m", "uvicorn", "app:app", "--port", "8001", "--reload"]
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        # Wait a bit for the server to start
        time.sleep(3)
        
        # Check if process is still running
        if process.poll() is None:
            print("✅ Backend API server started successfully!")
            print("📡 API running at: http://localhost:8001")
            return process
        else:
            stdout, stderr = process.communicate()
            print(f"❌ Backend server failed to start")
            print(f"Error: {stderr}")
            return None
            
    except Exception as e:
        print(f"❌ Failed to start backend server: {e}")
        return None

def start_frontend():
    """Start the Streamlit frontend."""
    print("\n🎨 Starting frontend application...")
    
    try:
        # Start streamlit
        cmd = [sys.executable, "-m", "streamlit", "run", "frontendV3.py", "--server.port", "9024"]
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        # Wait a bit for the server to start
        time.sleep(5)
        
        # Check if process is still running
        if process.poll() is None:
            print("✅ Frontend application started successfully!")
            print("🌐 Frontend running at: http://localhost:9024")
            return process
        else:
            stdout, stderr = process.communicate()
            print(f"❌ Frontend failed to start")
            print(f"Error: {stderr}")
            return None
            
    except Exception as e:
        print(f"❌ Failed to start frontend: {e}")
        return None

def test_mongodb():
    """Test MongoDB connection."""
    print("\n🗄️ Testing MongoDB connection...")
    
    try:
        from helperMongoDb import MongoDBClient
        mongo_client = MongoDBClient()
        cursor = mongo_client.get_all_resume_data()
        resumes = list(cursor)
        print(f"✅ MongoDB connected - {len(resumes)} resumes found")
        return True
    except Exception as e:
        print(f"⚠️ MongoDB connection issue: {e}")
        print("💡 Make sure MongoDB is running on localhost:27017")
        return False

def main():
    """Main function to start the application."""
    print_banner()
    
    # Check dependencies
    if not check_dependencies():
        print("\n❌ Please install missing dependencies before continuing.")
        return False
    
    # Check environment
    if not check_environment():
        print("\n❌ Please fix environment configuration before continuing.")
        return False
    
    # Test MongoDB (optional - app can still work without it for testing)
    mongodb_ok = test_mongodb()
    if not mongodb_ok:
        print("⚠️ MongoDB not available - some features may not work")
        response = input("Continue anyway? (y/n): ").lower()
        if response != 'y':
            return False
    
    print("\n" + "="*60)
    print("🚀 STARTING APPLICATION")
    print("="*60)
    
    # Start backend
    backend_process = start_backend()
    if not backend_process:
        print("❌ Cannot start application without backend API")
        return False
    
    # Start frontend
    frontend_process = start_frontend()
    if not frontend_process:
        print("❌ Cannot start frontend application")
        backend_process.terminate()
        return False
    
    print("\n" + "="*60)
    print("🎉 APPLICATION STARTED SUCCESSFULLY!")
    print("="*60)
    print("📡 Backend API: http://localhost:8001")
    print("🌐 Frontend App: http://localhost:9024")
    print("\n💡 Open your browser and go to http://localhost:9024")
    print("💡 Press Ctrl+C to stop both servers")
    
    try:
        # Keep the script running and monitor processes
        while True:
            time.sleep(1)
            
            # Check if processes are still running
            if backend_process.poll() is not None:
                print("\n❌ Backend process stopped unexpectedly")
                break
                
            if frontend_process.poll() is not None:
                print("\n❌ Frontend process stopped unexpectedly")
                break
                
    except KeyboardInterrupt:
        print("\n\n🛑 Shutting down application...")
        
        # Terminate processes
        if backend_process and backend_process.poll() is None:
            backend_process.terminate()
            print("✅ Backend server stopped")
            
        if frontend_process and frontend_process.poll() is None:
            frontend_process.terminate()
            print("✅ Frontend application stopped")
        
        print("👋 Application stopped successfully!")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
