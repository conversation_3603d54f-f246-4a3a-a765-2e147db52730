{"ast": null, "code": "var _jsxFileName = \"D:\\\\Projects\\\\SQL-Agent-Task-Submission-main\\\\SQL-Agent-Task-Submission-main\\\\src\\\\pages\\\\SearchResumes.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { FiSearch, FiFilter, FiDownload, FiEye, FiUser, FiMapPin, FiMail, FiPhone, FiExternalLink, FiStar, FiX, FiSettings, FiChevronDown, FiChevronUp } from 'react-icons/fi';\nimport { toast } from 'react-toastify';\nimport ResumeModal from '../components/ResumeModal';\nimport './SearchResumes.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst SearchResumes = () => {\n  _s();\n  const [searchQuery, setSearchQuery] = useState('');\n  const [isSearching, setIsSearching] = useState(false);\n  const [searchResults, setSearchResults] = useState([]);\n  const [filterQuery, setFilterQuery] = useState('');\n  const [showFilters, setShowFilters] = useState(false);\n  const [advancedFilters, setAdvancedFilters] = useState({\n    minExperience: '',\n    maxExperience: '',\n    education: '',\n    location: '',\n    skills: '',\n    minMarks: '',\n    institution: '',\n    graduationYear: '',\n    company: ''\n  });\n  const [selectedResume, setSelectedResume] = useState(null);\n  const [isModalOpen, setIsModalOpen] = useState(false);\n  const [currentPage, setCurrentPage] = useState(1);\n  const [totalPages, setTotalPages] = useState(1);\n  const [totalCount, setTotalCount] = useState(0);\n  const resultsPerPage = 20;\n\n  // Mock data for demonstration\n  const mockResults = [{\n    id: 1,\n    name: 'John Doe',\n    title: 'Senior Software Engineer',\n    email: '<EMAIL>',\n    phone: '+****************',\n    location: 'San Francisco, CA',\n    experience: '5+ years',\n    skills: ['Python', 'React', 'Node.js', 'AWS'],\n    education: 'MS Computer Science',\n    summary: 'Experienced software engineer with expertise in full-stack development...',\n    score: 95,\n    rawData: {\n      Resume: {\n        PersonalInformation: {\n          FullName: 'John Doe',\n          Email: '<EMAIL>',\n          ContactNumber: '+****************',\n          Address: 'San Francisco, CA'\n        },\n        WorkExperience: [{\n          Role: 'Senior Software Engineer',\n          CompanyName: 'Tech Corp',\n          StartYear: '2019',\n          EndYear: 'Present',\n          'Description/Responsibility': 'Led development of scalable web applications using React and Node.js'\n        }, {\n          Role: 'Software Engineer',\n          CompanyName: 'StartupXYZ',\n          StartYear: '2017',\n          EndYear: '2019',\n          'Description/Responsibility': 'Developed full-stack applications and improved system performance'\n        }],\n        Education: [{\n          Degree: 'MS Computer Science',\n          Institution: 'Stanford University',\n          GraduationYear: '2017',\n          'GPA/Marks/%': 85\n        }],\n        Skills: ['Python', 'React', 'Node.js', 'AWS'],\n        TotalWorkExperienceInYears: 5\n      }\n    }\n  }, {\n    id: 2,\n    name: 'Jane Smith',\n    title: 'Frontend Developer',\n    email: '<EMAIL>',\n    phone: '+****************',\n    location: 'New York, NY',\n    experience: '3+ years',\n    skills: ['React', 'TypeScript', 'CSS', 'JavaScript'],\n    education: 'BS Computer Science',\n    summary: 'Creative frontend developer passionate about user experience...',\n    score: 88,\n    rawData: {\n      Resume: {\n        PersonalInformation: {\n          FullName: 'Jane Smith',\n          Email: '<EMAIL>',\n          ContactNumber: '+****************',\n          Address: 'New York, NY'\n        },\n        WorkExperience: [{\n          Role: 'Frontend Developer',\n          CompanyName: 'Design Studio',\n          StartYear: '2021',\n          EndYear: 'Present',\n          'Description/Responsibility': 'Created responsive web interfaces using React and modern CSS'\n        }],\n        Education: [{\n          Degree: 'BS Computer Science',\n          Institution: 'NYU',\n          GraduationYear: '2021',\n          'GPA/Marks/%': 78\n        }],\n        Skills: ['React', 'TypeScript', 'CSS', 'JavaScript'],\n        TotalWorkExperienceInYears: 3\n      }\n    }\n  }, {\n    id: 3,\n    name: 'Mike Johnson',\n    title: 'DevOps Engineer',\n    email: '<EMAIL>',\n    phone: '+****************',\n    location: 'Austin, TX',\n    experience: '4+ years',\n    skills: ['Docker', 'Kubernetes', 'AWS', 'Python'],\n    education: 'BS Information Technology',\n    summary: 'DevOps engineer specializing in cloud infrastructure and automation...',\n    score: 92,\n    rawData: {\n      Resume: {\n        PersonalInformation: {\n          FullName: 'Mike Johnson',\n          Email: '<EMAIL>',\n          ContactNumber: '+****************',\n          Address: 'Austin, TX'\n        },\n        WorkExperience: [{\n          Role: 'DevOps Engineer',\n          CompanyName: 'Cloud Solutions Inc',\n          StartYear: '2020',\n          EndYear: 'Present',\n          'Description/Responsibility': 'Managed cloud infrastructure and automated deployment pipelines'\n        }],\n        Education: [{\n          Degree: 'BS Information Technology',\n          Institution: 'UT Austin',\n          GraduationYear: '2020',\n          'GPA/Marks/%': 82\n        }],\n        Skills: ['Docker', 'Kubernetes', 'AWS', 'Python'],\n        TotalWorkExperienceInYears: 4\n      }\n    }\n  }];\n  const handleSearch = async e => {\n    e.preventDefault();\n    if (!searchQuery.trim()) {\n      toast.warning('Please enter a search query');\n      return;\n    }\n    setIsSearching(true);\n    try {\n      // Call the FastAPI backend exactly like frontendV3.py does\n      const url = new URL('http://localhost:8001/query/');\n      url.searchParams.append('naturalQuery', searchQuery);\n      const response = await fetch(url, {\n        method: 'GET',\n        headers: {\n          'Content-Type': 'application/json'\n        }\n      });\n      const data = await response.json();\n      if (response.ok) {\n        // Handle FastAPI response format exactly like frontendV3.py: {query: [], listOfDict: [], result: \"\"}\n        let transformedResults = [];\n\n        // Check if API returns a direct list (like frontendV3.py handles)\n        if (Array.isArray(data)) {\n          transformedResults = data.map(resume => transformResumeData(resume));\n        } else if (data.listOfDict && Array.isArray(data.listOfDict)) {\n          // Transform the resume data from MongoDB to display format\n          transformedResults = data.listOfDict.map(resume => transformResumeData(resume));\n        } else {\n          // Fallback to mock data if API doesn't return results\n          transformedResults = mockResults.filter(resume => resume.name.toLowerCase().includes(searchQuery.toLowerCase()) || resume.title.toLowerCase().includes(searchQuery.toLowerCase()) || resume.skills.some(skill => skill.toLowerCase().includes(searchQuery.toLowerCase())));\n        }\n        setSearchResults(transformedResults);\n\n        // Show success message with GPT result (exactly like frontendV3.py)\n        if (data.result && data.result !== 'Data received successfully.') {\n          toast.success(`🤖 GPT Analysis: ${data.result}`);\n        } else {\n          toast.success(`🔍 Found ${transformedResults.length} matching resumes`);\n        }\n\n        // Log GPT response and MongoDB query for debugging (like frontendV3.py developer mode)\n        if (data.query) {\n          console.log('🤖 MongoDB Query Generated by GPT:', JSON.stringify(data.query, null, 2));\n          console.log('📊 GPT Result:', data.result);\n          console.log('📋 Full API Response:', data);\n        }\n      } else {\n        throw new Error(data.error || 'Search failed');\n      }\n    } catch (error) {\n      console.error('Search error:', error);\n\n      // Fallback to mock data on network error\n      const fallbackResults = mockResults.filter(resume => resume.name.toLowerCase().includes(searchQuery.toLowerCase()) || resume.title.toLowerCase().includes(searchQuery.toLowerCase()) || resume.skills.some(skill => skill.toLowerCase().includes(searchQuery.toLowerCase())));\n      setSearchResults(fallbackResults);\n      toast.warning(`Using demo data - Found ${fallbackResults.length} matching resumes`);\n    } finally {\n      setIsSearching(false);\n    }\n  };\n  const handleShowAll = async (page = 1) => {\n    setIsSearching(true);\n    try {\n      // Call the FastAPI backend directly to get all resumes\n      const response = await fetch('http://localhost:8001/showall');\n      const data = await response.json();\n      if (response.ok) {\n        let transformedResults = [];\n        if (Array.isArray(data)) {\n          // FastAPI /showall returns array directly\n          transformedResults = data.map(resume => {\n            var _resumeData$PersonalI, _resumeData$PersonalI2, _resumeData$PersonalI3, _resumeData$PersonalI4, _resumeData$PersonalI5, _resumeData$WorkExper, _resumeData$Education, _resumeData$Education2;\n            // Handle the nested Resume structure from MongoDB\n            const resumeData = resume.Resume || resume;\n            return {\n              id: resume._id || Math.random().toString(36).substr(2, 9),\n              name: ((_resumeData$PersonalI = resumeData.PersonalInformation) === null || _resumeData$PersonalI === void 0 ? void 0 : _resumeData$PersonalI.FullName) || resumeData.name || 'Unknown',\n              title: ((_resumeData$PersonalI2 = resumeData.PersonalInformation) === null || _resumeData$PersonalI2 === void 0 ? void 0 : _resumeData$PersonalI2.Designation) || resumeData.title || 'No title',\n              email: ((_resumeData$PersonalI3 = resumeData.PersonalInformation) === null || _resumeData$PersonalI3 === void 0 ? void 0 : _resumeData$PersonalI3.Email) || resumeData.email || 'No email',\n              phone: ((_resumeData$PersonalI4 = resumeData.PersonalInformation) === null || _resumeData$PersonalI4 === void 0 ? void 0 : _resumeData$PersonalI4.ContactNumber) || resumeData.phone || 'No phone',\n              location: ((_resumeData$PersonalI5 = resumeData.PersonalInformation) === null || _resumeData$PersonalI5 === void 0 ? void 0 : _resumeData$PersonalI5.Address) || resumeData.location || 'No location',\n              experience: resumeData.TotalWorkExperienceInYears ? `${resumeData.TotalWorkExperienceInYears} years` : (_resumeData$WorkExper = resumeData.WorkExperience) !== null && _resumeData$WorkExper !== void 0 && _resumeData$WorkExper.length ? `${resumeData.WorkExperience.length}+ positions` : resumeData.experience || 'No experience data',\n              skills: resumeData.Skills || resumeData.skills || [],\n              education: (_resumeData$Education = resumeData.Education) !== null && _resumeData$Education !== void 0 && _resumeData$Education.length ? ((_resumeData$Education2 = resumeData.Education[0]) === null || _resumeData$Education2 === void 0 ? void 0 : _resumeData$Education2.Degree) || 'Education available' : resumeData.education || 'No education data',\n              summary: resumeData.Objective || resumeData.Summary || resumeData.summary || 'No summary available',\n              score: 90,\n              // Default score for show all\n              rawData: resume // Keep original data for reference\n            };\n          });\n        } else if (data.error) {\n          throw new Error(data.error);\n        } else {\n          // Fallback to mock data if API doesn't return proper results\n          transformedResults = mockResults;\n        }\n        setSearchResults(transformedResults);\n        setCurrentPage(1);\n        setTotalCount(transformedResults.length);\n        setTotalPages(Math.ceil(transformedResults.length / resultsPerPage));\n        setSearchQuery('');\n        toast.success(`📊 Loaded ${transformedResults.length} resumes from database`);\n      } else {\n        throw new Error(data.error || 'Failed to load resumes');\n      }\n    } catch (error) {\n      console.error('Load resumes error:', error);\n\n      // Fallback to mock data on network error\n      setSearchResults(mockResults);\n      setCurrentPage(1);\n      setTotalCount(mockResults.length);\n      setTotalPages(Math.ceil(mockResults.length / resultsPerPage));\n      setSearchQuery('');\n      toast.warning(`Using demo data - Showing ${mockResults.length} sample resumes`);\n    } finally {\n      setIsSearching(false);\n    }\n  };\n  const handlePageChange = page => {\n    if (page >= 1 && page <= totalPages) {\n      handleShowAll(page);\n    }\n  };\n  const filteredResults = searchResults.filter(result => {\n    // Basic text filter\n    if (filterQuery) {\n      const matchesText = result.name.toLowerCase().includes(filterQuery.toLowerCase()) || result.title.toLowerCase().includes(filterQuery.toLowerCase()) || result.location.toLowerCase().includes(filterQuery.toLowerCase());\n      if (!matchesText) return false;\n    }\n\n    // Advanced filters\n    const filters = advancedFilters;\n\n    // Experience filter\n    if (filters.minExperience || filters.maxExperience) {\n      var _result$rawData, _result$rawData$Resum;\n      const experienceYears = ((_result$rawData = result.rawData) === null || _result$rawData === void 0 ? void 0 : (_result$rawData$Resum = _result$rawData.Resume) === null || _result$rawData$Resum === void 0 ? void 0 : _result$rawData$Resum.TotalWorkExperienceInYears) || extractExperienceYears(result.experience) || 0;\n      if (filters.minExperience && experienceYears < parseInt(filters.minExperience)) return false;\n      if (filters.maxExperience && experienceYears > parseInt(filters.maxExperience)) return false;\n    }\n\n    // Education filter\n    if (filters.education) {\n      var _result$rawData2, _result$rawData2$Resu, _result$rawData2$Resu2;\n      const hasEducation = ((_result$rawData2 = result.rawData) === null || _result$rawData2 === void 0 ? void 0 : (_result$rawData2$Resu = _result$rawData2.Resume) === null || _result$rawData2$Resu === void 0 ? void 0 : (_result$rawData2$Resu2 = _result$rawData2$Resu.Education) === null || _result$rawData2$Resu2 === void 0 ? void 0 : _result$rawData2$Resu2.some(edu => (edu.Degree || '').toLowerCase().includes(filters.education.toLowerCase()))) || (result.education || '').toLowerCase().includes(filters.education.toLowerCase());\n      if (!hasEducation) return false;\n    }\n\n    // Location filter\n    if (filters.location) {\n      const matchesLocation = (result.location || '').toLowerCase().includes(filters.location.toLowerCase());\n      if (!matchesLocation) return false;\n    }\n\n    // Skills filter\n    if (filters.skills) {\n      var _result$skills, _result$rawData3, _result$rawData3$Resu;\n      const skillsText = filters.skills.toLowerCase();\n      const hasSkill = ((_result$skills = result.skills) === null || _result$skills === void 0 ? void 0 : _result$skills.some(skill => skill.toLowerCase().includes(skillsText))) || (((_result$rawData3 = result.rawData) === null || _result$rawData3 === void 0 ? void 0 : (_result$rawData3$Resu = _result$rawData3.Resume) === null || _result$rawData3$Resu === void 0 ? void 0 : _result$rawData3$Resu.Skills) || []).some(skill => skill.toLowerCase().includes(skillsText));\n      if (!hasSkill) return false;\n    }\n\n    // Institution filter\n    if (filters.institution) {\n      var _result$rawData4, _result$rawData4$Resu, _result$rawData4$Resu2;\n      const hasInstitution = (_result$rawData4 = result.rawData) === null || _result$rawData4 === void 0 ? void 0 : (_result$rawData4$Resu = _result$rawData4.Resume) === null || _result$rawData4$Resu === void 0 ? void 0 : (_result$rawData4$Resu2 = _result$rawData4$Resu.Education) === null || _result$rawData4$Resu2 === void 0 ? void 0 : _result$rawData4$Resu2.some(edu => (edu.Institution || edu.School || '').toLowerCase().includes(filters.institution.toLowerCase()));\n      if (!hasInstitution) return false;\n    }\n\n    // Graduation year filter\n    if (filters.graduationYear) {\n      var _result$rawData5, _result$rawData5$Resu, _result$rawData5$Resu2;\n      const hasGradYear = (_result$rawData5 = result.rawData) === null || _result$rawData5 === void 0 ? void 0 : (_result$rawData5$Resu = _result$rawData5.Resume) === null || _result$rawData5$Resu === void 0 ? void 0 : (_result$rawData5$Resu2 = _result$rawData5$Resu.Education) === null || _result$rawData5$Resu2 === void 0 ? void 0 : _result$rawData5$Resu2.some(edu => (edu.GraduationYear || edu.Year || '').toString().includes(filters.graduationYear));\n      if (!hasGradYear) return false;\n    }\n\n    // Company filter\n    if (filters.company) {\n      var _result$rawData6, _result$rawData6$Resu, _result$rawData6$Resu2;\n      const hasCompany = (_result$rawData6 = result.rawData) === null || _result$rawData6 === void 0 ? void 0 : (_result$rawData6$Resu = _result$rawData6.Resume) === null || _result$rawData6$Resu === void 0 ? void 0 : (_result$rawData6$Resu2 = _result$rawData6$Resu.WorkExperience) === null || _result$rawData6$Resu2 === void 0 ? void 0 : _result$rawData6$Resu2.some(exp => (exp.Company || exp.CompanyName || exp.Organization || '').toLowerCase().includes(filters.company.toLowerCase()));\n      if (!hasCompany) return false;\n    }\n\n    // Minimum marks filter\n    if (filters.minMarks) {\n      var _result$rawData7, _result$rawData7$Resu, _result$rawData7$Resu2;\n      const hasMinMarks = (_result$rawData7 = result.rawData) === null || _result$rawData7 === void 0 ? void 0 : (_result$rawData7$Resu = _result$rawData7.Resume) === null || _result$rawData7$Resu === void 0 ? void 0 : (_result$rawData7$Resu2 = _result$rawData7$Resu.Education) === null || _result$rawData7$Resu2 === void 0 ? void 0 : _result$rawData7$Resu2.some(edu => {\n        const marks = parseFloat(edu['GPA/Marks/%']) || 0;\n        return marks >= parseFloat(filters.minMarks);\n      });\n      if (!hasMinMarks) return false;\n    }\n    return true;\n  });\n\n  // Helper function to extract experience years from text\n  const extractExperienceYears = experienceText => {\n    if (!experienceText) return 0;\n    const match = experienceText.match(/(\\d+)\\+?\\s*years?/i);\n    return match ? parseInt(match[1]) : 0;\n  };\n\n  // Helper function to transform resume data (like frontendV3.py)\n  const transformResumeData = resume => {\n    var _resumeData$PersonalI6, _resumeData$PersonalI7, _resumeData$PersonalI8, _resumeData$PersonalI9, _resumeData$PersonalI0, _resumeData$WorkExper2, _resumeData$Education3, _resumeData$Education4;\n    // Handle the nested Resume structure from MongoDB\n    const resumeData = resume.Resume || resume;\n    return {\n      id: resume._id || Math.random().toString(36).substr(2, 9),\n      name: ((_resumeData$PersonalI6 = resumeData.PersonalInformation) === null || _resumeData$PersonalI6 === void 0 ? void 0 : _resumeData$PersonalI6.FullName) || resumeData.name || 'Unknown',\n      title: ((_resumeData$PersonalI7 = resumeData.PersonalInformation) === null || _resumeData$PersonalI7 === void 0 ? void 0 : _resumeData$PersonalI7.Designation) || resumeData.title || 'No title',\n      email: ((_resumeData$PersonalI8 = resumeData.PersonalInformation) === null || _resumeData$PersonalI8 === void 0 ? void 0 : _resumeData$PersonalI8.Email) || resumeData.email || 'No email',\n      phone: ((_resumeData$PersonalI9 = resumeData.PersonalInformation) === null || _resumeData$PersonalI9 === void 0 ? void 0 : _resumeData$PersonalI9.ContactNumber) || resumeData.phone || 'No phone',\n      location: ((_resumeData$PersonalI0 = resumeData.PersonalInformation) === null || _resumeData$PersonalI0 === void 0 ? void 0 : _resumeData$PersonalI0.Address) || resumeData.location || 'No location',\n      experience: resumeData.TotalWorkExperienceInYears ? `${resumeData.TotalWorkExperienceInYears} years` : (_resumeData$WorkExper2 = resumeData.WorkExperience) !== null && _resumeData$WorkExper2 !== void 0 && _resumeData$WorkExper2.length ? `${resumeData.WorkExperience.length}+ positions` : resumeData.experience || 'No experience data',\n      skills: resumeData.Skills || resumeData.skills || [],\n      education: (_resumeData$Education3 = resumeData.Education) !== null && _resumeData$Education3 !== void 0 && _resumeData$Education3.length ? ((_resumeData$Education4 = resumeData.Education[0]) === null || _resumeData$Education4 === void 0 ? void 0 : _resumeData$Education4.Degree) || 'Education available' : resumeData.education || 'No education data',\n      summary: resumeData.Objective || resumeData.Summary || resumeData.summary || 'No summary available',\n      score: 95,\n      // Default score\n      rawData: resume // Keep original data for reference\n    };\n  };\n  const handleAdvancedFilterChange = (filterName, value) => {\n    setAdvancedFilters(prev => ({\n      ...prev,\n      [filterName]: value\n    }));\n  };\n  const clearAllFilters = () => {\n    setFilterQuery('');\n    setAdvancedFilters({\n      minExperience: '',\n      maxExperience: '',\n      education: '',\n      location: '',\n      skills: '',\n      minMarks: '',\n      institution: '',\n      graduationYear: '',\n      company: ''\n    });\n  };\n  const hasActiveFilters = () => {\n    return filterQuery || Object.values(advancedFilters).some(value => value !== '');\n  };\n  const handleDownload = resume => {\n    try {\n      var _resume$rawData, _resume$rawData$Resum, _resume$rawData2, _resume$rawData2$Resu, _resume$rawData3, _resume$rawData3$Resu, _resume$rawData4, _resume$rawData4$Resu, _resume$rawData5, _resume$rawData5$Resu, _resume$rawData6, _resume$rawData6$Resu, _resume$rawData7, _resume$rawData7$Resu, _resume$rawData8, _resume$rawData8$Resu, _resume$rawData8$Resu2;\n      // Create a comprehensive text-based resume content\n      const resumeContent = `\n${resume.name}\n${resume.title}\n${resume.email} | ${resume.phone} | ${resume.location}\n\nPROFESSIONAL SUMMARY\n${resume.summary}\n\nWORK EXPERIENCE\n${(_resume$rawData = resume.rawData) !== null && _resume$rawData !== void 0 && (_resume$rawData$Resum = _resume$rawData.Resume) !== null && _resume$rawData$Resum !== void 0 && _resume$rawData$Resum.WorkExperience ? resume.rawData.Resume.WorkExperience.map(exp => `${exp.JobTitle || exp.Position} at ${exp.Company || exp.Organization}\nDuration: ${exp.Duration || exp.Years || 'Not specified'}\n${exp.Description ? 'Description: ' + exp.Description : ''}\n${exp.Responsibilities ? 'Responsibilities: ' + exp.Responsibilities : ''}\n`).join('\\n') : resume.experience}\n\n${(_resume$rawData2 = resume.rawData) !== null && _resume$rawData2 !== void 0 && (_resume$rawData2$Resu = _resume$rawData2.Resume) !== null && _resume$rawData2$Resu !== void 0 && _resume$rawData2$Resu.TotalWorkExperienceInYears ? `Total Experience: ${resume.rawData.Resume.TotalWorkExperienceInYears} years\\n` : ''}\n\nEDUCATION\n${(_resume$rawData3 = resume.rawData) !== null && _resume$rawData3 !== void 0 && (_resume$rawData3$Resu = _resume$rawData3.Resume) !== null && _resume$rawData3$Resu !== void 0 && _resume$rawData3$Resu.Education ? resume.rawData.Resume.Education.map(edu => `${edu.Degree || 'Degree'} from ${edu.Institution || edu.School || 'Institution'}\nGraduation Year: ${edu.GraduationYear || edu.Year || 'Not specified'}\n${edu['GPA/Marks/%'] && edu['GPA/Marks/%'] > 0 ? 'Marks: ' + edu['GPA/Marks/%'] + '%' : ''}\n`).join('\\n') : resume.education}\n\nSKILLS & TECHNOLOGIES\n${resume.skills.join(', ')}\n\n${(_resume$rawData4 = resume.rawData) !== null && _resume$rawData4 !== void 0 && (_resume$rawData4$Resu = _resume$rawData4.Resume) !== null && _resume$rawData4$Resu !== void 0 && _resume$rawData4$Resu.Languages && resume.rawData.Resume.Languages.length > 0 ? `LANGUAGES\\n${resume.rawData.Resume.Languages.join(', ')}\\n` : ''}\n\n${(_resume$rawData5 = resume.rawData) !== null && _resume$rawData5 !== void 0 && (_resume$rawData5$Resu = _resume$rawData5.Resume) !== null && _resume$rawData5$Resu !== void 0 && _resume$rawData5$Resu.Projects && resume.rawData.Resume.Projects.length > 0 ? `PROJECTS\\n${resume.rawData.Resume.Projects.map(project => `${project.Name || project.Title || 'Project'}\n${project.Description ? 'Description: ' + project.Description : ''}\n${project.Technologies ? 'Technologies: ' + project.Technologies : ''}\n`).join('\\n')}` : ''}\n\n${(_resume$rawData6 = resume.rawData) !== null && _resume$rawData6 !== void 0 && (_resume$rawData6$Resu = _resume$rawData6.Resume) !== null && _resume$rawData6$Resu !== void 0 && _resume$rawData6$Resu.Certifications && resume.rawData.Resume.Certifications.length > 0 ? `CERTIFICATIONS\\n${resume.rawData.Resume.Certifications.map(cert => `${cert.Name || cert.Title || 'Certification'}\n${cert.IssuingOrganization ? 'Issued by: ' + cert.IssuingOrganization : ''}\n${cert.IssueDate ? 'Date: ' + cert.IssueDate : ''}\n`).join('\\n')}` : ''}\n\n${(_resume$rawData7 = resume.rawData) !== null && _resume$rawData7 !== void 0 && (_resume$rawData7$Resu = _resume$rawData7.Resume) !== null && _resume$rawData7$Resu !== void 0 && _resume$rawData7$Resu.Achievements && resume.rawData.Resume.Achievements.length > 0 ? `ACHIEVEMENTS\\n${resume.rawData.Resume.Achievements.map(achievement => typeof achievement === 'string' ? achievement : `${achievement.AchievementName || achievement.Name || 'Achievement'}\n${achievement.IssueDate ? 'Date: ' + achievement.IssueDate : ''}\n${achievement.IssuingOrganization ? 'Organization: ' + achievement.IssuingOrganization : ''}\n${achievement.Description ? 'Description: ' + achievement.Description : ''}\n`).join('\\n')}` : ''}\n\n${(_resume$rawData8 = resume.rawData) !== null && _resume$rawData8 !== void 0 && (_resume$rawData8$Resu = _resume$rawData8.Resume) !== null && _resume$rawData8$Resu !== void 0 && (_resume$rawData8$Resu2 = _resume$rawData8$Resu.PersonalInformation) !== null && _resume$rawData8$Resu2 !== void 0 && _resume$rawData8$Resu2.LinkedIn ? `ADDITIONAL INFORMATION\\nLinkedIn: ${resume.rawData.Resume.PersonalInformation.LinkedIn}` : ''}\n\nGenerated on: ${new Date().toLocaleDateString()}\n      `.trim();\n\n      // Create and download the file\n      const blob = new Blob([resumeContent], {\n        type: 'text/plain'\n      });\n      const url = URL.createObjectURL(blob);\n      const link = document.createElement('a');\n      link.href = url;\n      link.download = `${resume.name.replace(/\\s+/g, '_')}_Resume.txt`;\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n      URL.revokeObjectURL(url);\n      toast.success(`Downloaded resume for ${resume.name}`);\n    } catch (error) {\n      console.error('Download error:', error);\n      toast.error('Failed to download resume. Please try again.');\n    }\n  };\n  const handleView = resume => {\n    setSelectedResume(resume);\n    setIsModalOpen(true);\n    toast.info(`Opening detailed view for ${resume.name}`);\n  };\n  const handleCloseModal = () => {\n    setIsModalOpen(false);\n    setSelectedResume(null);\n  };\n  const handleEmailClick = (email, name) => {\n    window.open(`mailto:${email}?subject=Regarding your resume&body=Hi ${name},%0D%0A%0D%0AI found your resume and would like to discuss potential opportunities.%0D%0A%0D%0ABest regards`, '_blank');\n    toast.info(`Opening email client to contact ${name}`);\n  };\n  const handlePhoneClick = (phone, name) => {\n    if (navigator.userAgent.match(/Mobile|Android|iPhone|iPad/)) {\n      window.open(`tel:${phone}`, '_blank');\n    } else {\n      navigator.clipboard.writeText(phone).then(() => {\n        toast.success(`Phone number copied: ${phone}`);\n      }).catch(() => {\n        toast.info(`Phone: ${phone}`);\n      });\n    }\n  };\n  const handleExportAll = () => {\n    if (filteredResults.length === 0) {\n      toast.warning('No results to export');\n      return;\n    }\n    try {\n      const exportData = filteredResults.map(resume => ({\n        name: resume.name,\n        title: resume.title,\n        email: resume.email,\n        phone: resume.phone,\n        location: resume.location,\n        experience: resume.experience,\n        education: resume.education,\n        skills: resume.skills.join(', '),\n        summary: resume.summary,\n        matchScore: resume.score\n      }));\n      const csvContent = ['Name,Title,Email,Phone,Location,Experience,Education,Skills,Summary,Match Score', ...exportData.map(row => Object.values(row).map(value => `\"${String(value).replace(/\"/g, '\"\"')}\"`).join(','))].join('\\n');\n      const blob = new Blob([csvContent], {\n        type: 'text/csv'\n      });\n      const url = URL.createObjectURL(blob);\n      const link = document.createElement('a');\n      link.href = url;\n      link.download = `Resume_Search_Results_${new Date().toISOString().split('T')[0]}.csv`;\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n      URL.revokeObjectURL(url);\n      toast.success(`Exported ${filteredResults.length} resumes to CSV`);\n    } catch (error) {\n      console.error('Export error:', error);\n      toast.error('Failed to export results. Please try again.');\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"search-page\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"page-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        children: \"Search Resumes\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 630,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Use natural language to find the perfect candidates for your needs\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 631,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 629,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"search-section\",\n      children: [/*#__PURE__*/_jsxDEV(\"form\", {\n        onSubmit: handleSearch,\n        className: \"search-form\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"search-input-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"search-input-wrapper\",\n            children: [/*#__PURE__*/_jsxDEV(FiSearch, {\n              className: \"search-icon\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 639,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              placeholder: \"e.g., 'Find Python developers with 5+ years experience' or 'Show frontend engineers in California'\",\n              value: searchQuery,\n              onChange: e => setSearchQuery(e.target.value),\n              className: \"search-input\",\n              disabled: isSearching\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 640,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 638,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"search-buttons\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"submit\",\n              className: \"btn btn-primary\",\n              disabled: isSearching,\n              children: isSearching ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"loading-spinner\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 657,\n                  columnNumber: 21\n                }, this), \"Searching...\"]\n              }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(FiSearch, {\n                  size: 16\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 662,\n                  columnNumber: 21\n                }, this), \"Search\"]\n              }, void 0, true)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 650,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              className: \"btn btn-secondary\",\n              onClick: handleShowAll,\n              disabled: isSearching,\n              children: \"Show All\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 667,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 649,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 637,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 636,\n        columnNumber: 9\n      }, this), searchResults.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"filter-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"filter-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"basic-filter-wrapper\",\n            children: [/*#__PURE__*/_jsxDEV(FiFilter, {\n              className: \"filter-icon\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 684,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              placeholder: \"Quick filter by name, title, or location...\",\n              value: filterQuery,\n              onChange: e => setFilterQuery(e.target.value),\n              className: \"filter-input\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 685,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 683,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"filter-controls\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              className: `btn btn-secondary filter-toggle ${showFilters ? 'active' : ''}`,\n              onClick: () => setShowFilters(!showFilters),\n              children: [/*#__PURE__*/_jsxDEV(FiSettings, {\n                size: 16\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 699,\n                columnNumber: 19\n              }, this), \"Advanced Filters\", showFilters ? /*#__PURE__*/_jsxDEV(FiChevronUp, {\n                size: 16\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 701,\n                columnNumber: 34\n              }, this) : /*#__PURE__*/_jsxDEV(FiChevronDown, {\n                size: 16\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 701,\n                columnNumber: 62\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 694,\n              columnNumber: 17\n            }, this), hasActiveFilters() && /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              className: \"btn btn-outline clear-filters\",\n              onClick: clearAllFilters,\n              children: [/*#__PURE__*/_jsxDEV(FiX, {\n                size: 16\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 709,\n                columnNumber: 21\n              }, this), \"Clear All\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 704,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 693,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 682,\n          columnNumber: 13\n        }, this), showFilters && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"advanced-filters\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"filters-grid\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"filter-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Experience (Years)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 721,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"range-inputs\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"number\",\n                  placeholder: \"Min\",\n                  value: advancedFilters.minExperience,\n                  onChange: e => handleAdvancedFilterChange('minExperience', e.target.value),\n                  className: \"filter-input small\",\n                  min: \"0\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 723,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"to\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 731,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"number\",\n                  placeholder: \"Max\",\n                  value: advancedFilters.maxExperience,\n                  onChange: e => handleAdvancedFilterChange('maxExperience', e.target.value),\n                  className: \"filter-input small\",\n                  min: \"0\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 732,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 722,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 720,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"filter-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Education Level\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 744,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                placeholder: \"e.g., Bachelor, Master, PhD\",\n                value: advancedFilters.education,\n                onChange: e => handleAdvancedFilterChange('education', e.target.value),\n                className: \"filter-input\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 745,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 743,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"filter-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Location\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 755,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                placeholder: \"e.g., Mumbai, Delhi, Bangalore\",\n                value: advancedFilters.location,\n                onChange: e => handleAdvancedFilterChange('location', e.target.value),\n                className: \"filter-input\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 756,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 754,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"filter-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Skills\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 766,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                placeholder: \"e.g., Python, React, Java\",\n                value: advancedFilters.skills,\n                onChange: e => handleAdvancedFilterChange('skills', e.target.value),\n                className: \"filter-input\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 767,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 765,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"filter-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Institution\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 777,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                placeholder: \"e.g., IIT, University name\",\n                value: advancedFilters.institution,\n                onChange: e => handleAdvancedFilterChange('institution', e.target.value),\n                className: \"filter-input\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 778,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 776,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"filter-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Graduation Year\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 788,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                placeholder: \"e.g., 2020, 2021\",\n                value: advancedFilters.graduationYear,\n                onChange: e => handleAdvancedFilterChange('graduationYear', e.target.value),\n                className: \"filter-input\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 789,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 787,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"filter-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Company\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 799,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                placeholder: \"e.g., Google, Microsoft, TCS\",\n                value: advancedFilters.company,\n                onChange: e => handleAdvancedFilterChange('company', e.target.value),\n                className: \"filter-input\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 800,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 798,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"filter-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Minimum Marks (%)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 810,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"number\",\n                placeholder: \"e.g., 75, 80\",\n                value: advancedFilters.minMarks,\n                onChange: e => handleAdvancedFilterChange('minMarks', e.target.value),\n                className: \"filter-input\",\n                min: \"0\",\n                max: \"100\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 811,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 809,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 719,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 718,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"results-count\",\n          children: [filteredResults.length, \" of \", searchResults.length, \" results\", hasActiveFilters() && /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"filtered-indicator\",\n            children: \" (filtered)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 827,\n            columnNumber: 38\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 825,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 681,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 635,\n      columnNumber: 7\n    }, this), searchResults.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"results-section\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"results-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Search Results\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 837,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"results-actions\",\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"btn btn-secondary\",\n            onClick: handleExportAll,\n            title: `Export ${filteredResults.length} results to CSV`,\n            children: [/*#__PURE__*/_jsxDEV(FiDownload, {\n              size: 16\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 844,\n              columnNumber: 17\n            }, this), \"Export All (\", filteredResults.length, \")\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 839,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 838,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 836,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"results-grid\",\n        children: filteredResults.map(resume => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"resume-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"resume-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"resume-avatar\",\n              children: /*#__PURE__*/_jsxDEV(FiUser, {\n                size: 24\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 855,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 854,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"resume-info\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"resume-name\",\n                children: resume.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 858,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"resume-title\",\n                children: resume.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 859,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 857,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 853,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"resume-details\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"detail-item location-item\",\n              children: [/*#__PURE__*/_jsxDEV(FiMapPin, {\n                size: 14\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 865,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: resume.location\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 866,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 864,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"detail-item email-item clickable\",\n              onClick: () => handleEmailClick(resume.email, resume.name),\n              title: `Send email to ${resume.name}`,\n              children: [/*#__PURE__*/_jsxDEV(FiMail, {\n                size: 14\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 873,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: resume.email\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 874,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(FiExternalLink, {\n                size: 12,\n                className: \"external-icon\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 875,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 868,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"detail-item phone-item clickable\",\n              onClick: () => handlePhoneClick(resume.phone, resume.name),\n              title: `Call ${resume.name}`,\n              children: [/*#__PURE__*/_jsxDEV(FiPhone, {\n                size: 14\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 882,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: resume.phone\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 883,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(FiExternalLink, {\n                size: 12,\n                className: \"external-icon\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 884,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 877,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 863,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"resume-content\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"content-row\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"content-section\",\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  children: \"Experience\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 891,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: resume.experience\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 892,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 890,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"content-section\",\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  children: \"Education\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 895,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: resume.education\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 896,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 894,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 889,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"content-section\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                children: \"Skills\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 900,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"skills-list\",\n                children: resume.skills.map((skill, index) => /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"skill-tag\",\n                  children: skill\n                }, index, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 903,\n                  columnNumber: 25\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 901,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 899,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 888,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"resume-actions\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"btn btn-secondary\",\n              onClick: () => handleView(resume),\n              children: [/*#__PURE__*/_jsxDEV(FiEye, {\n                size: 16\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 914,\n                columnNumber: 21\n              }, this), \"View Details\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 910,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"btn btn-primary\",\n              onClick: () => handleDownload(resume),\n              children: [/*#__PURE__*/_jsxDEV(FiDownload, {\n                size: 16\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 921,\n                columnNumber: 21\n              }, this), \"Download\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 917,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 909,\n            columnNumber: 17\n          }, this)]\n        }, resume.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 852,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 850,\n        columnNumber: 11\n      }, this), totalPages > 1 && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"pagination-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"pagination-info\",\n          children: [\"Showing \", (currentPage - 1) * resultsPerPage + 1, \" to \", Math.min(currentPage * resultsPerPage, totalCount), \" of \", totalCount, \" results\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 932,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"pagination-controls\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"btn btn-secondary pagination-btn\",\n            onClick: () => handlePageChange(currentPage - 1),\n            disabled: currentPage === 1,\n            children: \"Previous\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 936,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"page-numbers\",\n            children: Array.from({\n              length: Math.min(5, totalPages)\n            }, (_, i) => {\n              let pageNum;\n              if (totalPages <= 5) {\n                pageNum = i + 1;\n              } else if (currentPage <= 3) {\n                pageNum = i + 1;\n              } else if (currentPage >= totalPages - 2) {\n                pageNum = totalPages - 4 + i;\n              } else {\n                pageNum = currentPage - 2 + i;\n              }\n              return /*#__PURE__*/_jsxDEV(\"button\", {\n                className: `btn pagination-btn ${currentPage === pageNum ? 'btn-primary' : 'btn-secondary'}`,\n                onClick: () => handlePageChange(pageNum),\n                children: pageNum\n              }, pageNum, false, {\n                fileName: _jsxFileName,\n                lineNumber: 958,\n                columnNumber: 23\n              }, this);\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 944,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"btn btn-secondary pagination-btn\",\n            onClick: () => handlePageChange(currentPage + 1),\n            disabled: currentPage === totalPages,\n            children: \"Next\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 969,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 935,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 931,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 835,\n      columnNumber: 9\n    }, this) : !isSearching ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"empty-state\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"empty-icon\",\n        children: /*#__PURE__*/_jsxDEV(FiSearch, {\n          size: 48\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 983,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 982,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n        children: \"Ready to Search!\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 985,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Use the search box above to find resumes using natural language, or click \\\"Show All\\\" to view all resumes in the database.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 986,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"search-examples\",\n        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n          children: \"Try searching for:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 988,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n          children: [/*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"\\\"Find Python developers\\\"\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 990,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"\\\"Show teachers with 5+ years experience\\\"\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 991,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"\\\"Software engineers in Mumbai\\\"\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 992,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 989,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 987,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 981,\n      columnNumber: 9\n    }, this) : null, /*#__PURE__*/_jsxDEV(ResumeModal, {\n      resume: selectedResume,\n      isOpen: isModalOpen,\n      onClose: handleCloseModal\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 999,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 628,\n    columnNumber: 5\n  }, this);\n};\n_s(SearchResumes, \"uvVHvTgqjBYZkn8TcWc0iS1zcVo=\");\n_c = SearchResumes;\nexport default SearchResumes;\nvar _c;\n$RefreshReg$(_c, \"SearchResumes\");", "map": {"version": 3, "names": ["React", "useState", "FiSearch", "<PERSON><PERSON><PERSON><PERSON>", "FiDownload", "FiEye", "FiUser", "FiMapPin", "FiMail", "FiPhone", "FiExternalLink", "FiStar", "FiX", "FiSettings", "FiChevronDown", "FiChevronUp", "toast", "ResumeModal", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "SearchResumes", "_s", "searchQuery", "setSearch<PERSON>uery", "isSearching", "setIsSearching", "searchResults", "setSearchResults", "filterQuery", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "showFilters", "setShowFilters", "advancedFilters", "setAdvancedFilters", "minExperience", "maxExperience", "education", "location", "skills", "minMarks", "institution", "graduationYear", "company", "selectedResume", "setSelectedResume", "isModalOpen", "setIsModalOpen", "currentPage", "setCurrentPage", "totalPages", "setTotalPages", "totalCount", "setTotalCount", "resultsPerPage", "mockResults", "id", "name", "title", "email", "phone", "experience", "summary", "score", "rawData", "Resume", "PersonalInformation", "FullName", "Email", "ContactNumber", "Address", "WorkExperience", "Role", "CompanyName", "StartYear", "EndYear", "Education", "Degree", "Institution", "GraduationYear", "Skills", "TotalWorkExperienceInYears", "handleSearch", "e", "preventDefault", "trim", "warning", "url", "URL", "searchParams", "append", "response", "fetch", "method", "headers", "data", "json", "ok", "transformedResults", "Array", "isArray", "map", "resume", "transformResumeData", "listOfDict", "filter", "toLowerCase", "includes", "some", "skill", "result", "success", "length", "query", "console", "log", "JSON", "stringify", "Error", "error", "fallback<PERSON><PERSON><PERSON>s", "handleShowAll", "page", "_resumeData$PersonalI", "_resumeData$PersonalI2", "_resumeData$PersonalI3", "_resumeData$PersonalI4", "_resumeData$PersonalI5", "_resumeData$WorkExper", "_resumeData$Education", "_resumeData$Education2", "resumeData", "_id", "Math", "random", "toString", "substr", "Designation", "Objective", "Summary", "ceil", "handlePageChange", "filteredResults", "matchesText", "filters", "_result$rawData", "_result$rawData$Resum", "experienceYears", "extractExperienceYears", "parseInt", "_result$rawData2", "_result$rawData2$Resu", "_result$rawData2$Resu2", "hasEducation", "edu", "matchesLocation", "_result$skills", "_result$rawData3", "_result$rawData3$Resu", "skillsText", "hasSkill", "_result$rawData4", "_result$rawData4$Resu", "_result$rawData4$Resu2", "hasInstitution", "School", "_result$rawData5", "_result$rawData5$Resu", "_result$rawData5$Resu2", "hasGradYear", "Year", "_result$rawData6", "_result$rawData6$Resu", "_result$rawData6$Resu2", "hasCompany", "exp", "Company", "Organization", "_result$rawData7", "_result$rawData7$Resu", "_result$rawData7$Resu2", "hasMinMarks", "marks", "parseFloat", "experienceText", "match", "_resumeData$PersonalI6", "_resumeData$PersonalI7", "_resumeData$PersonalI8", "_resumeData$PersonalI9", "_resumeData$PersonalI0", "_resumeData$WorkExper2", "_resumeData$Education3", "_resumeData$Education4", "handleAdvancedFilterChange", "filterName", "value", "prev", "clearAllFilters", "hasActiveFilters", "Object", "values", "handleDownload", "_resume$rawData", "_resume$rawData$Resum", "_resume$rawData2", "_resume$rawData2$Resu", "_resume$rawData3", "_resume$rawData3$Resu", "_resume$rawData4", "_resume$rawData4$Resu", "_resume$rawData5", "_resume$rawData5$Resu", "_resume$rawData6", "_resume$rawData6$Resu", "_resume$rawData7", "_resume$rawData7$Resu", "_resume$rawData8", "_resume$rawData8$Resu", "_resume$rawData8$Resu2", "<PERSON><PERSON><PERSON>nt", "JobTitle", "Position", "Duration", "Years", "Description", "Responsibilities", "join", "Languages", "Projects", "project", "Name", "Title", "Technologies", "Certifications", "cert", "IssuingOrganization", "IssueDate", "Achievements", "achievement", "AchievementName", "LinkedIn", "Date", "toLocaleDateString", "blob", "Blob", "type", "createObjectURL", "link", "document", "createElement", "href", "download", "replace", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL", "handleView", "info", "handleCloseModal", "handleEmailClick", "window", "open", "handlePhoneClick", "navigator", "userAgent", "clipboard", "writeText", "then", "catch", "handleExportAll", "exportData", "matchScore", "csv<PERSON><PERSON>nt", "row", "String", "toISOString", "split", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSubmit", "placeholder", "onChange", "target", "disabled", "size", "onClick", "min", "max", "index", "from", "_", "i", "pageNum", "isOpen", "onClose", "_c", "$RefreshReg$"], "sources": ["D:/Projects/SQL-Agent-Task-Submission-main/SQL-Agent-Task-Submission-main/src/pages/SearchResumes.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { <PERSON><PERSON>earch, Fi<PERSON>ilter, <PERSON>Down<PERSON>, <PERSON>Eye, Fi<PERSON>ser, FiMapPin, FiMail, FiPhone, FiExternalLink, FiStar, FiX, FiSettings, FiChevronDown, FiChevronUp } from 'react-icons/fi';\nimport { toast } from 'react-toastify';\nimport ResumeModal from '../components/ResumeModal';\nimport './SearchResumes.css';\n\nconst SearchResumes = () => {\n  const [searchQuery, setSearchQuery] = useState('');\n  const [isSearching, setIsSearching] = useState(false);\n  const [searchResults, setSearchResults] = useState([]);\n  const [filterQuery, setFilterQuery] = useState('');\n  const [showFilters, setShowFilters] = useState(false);\n  const [advancedFilters, setAdvancedFilters] = useState({\n    minExperience: '',\n    maxExperience: '',\n    education: '',\n    location: '',\n    skills: '',\n    minMarks: '',\n    institution: '',\n    graduationYear: '',\n    company: ''\n  });\n  const [selectedResume, setSelectedResume] = useState(null);\n  const [isModalOpen, setIsModalOpen] = useState(false);\n  const [currentPage, setCurrentPage] = useState(1);\n  const [totalPages, setTotalPages] = useState(1);\n  const [totalCount, setTotalCount] = useState(0);\n  const resultsPerPage = 20;\n\n  // Mock data for demonstration\n  const mockResults = [\n    {\n      id: 1,\n      name: 'John Doe',\n      title: 'Senior Software Engineer',\n      email: '<EMAIL>',\n      phone: '+****************',\n      location: 'San Francisco, CA',\n      experience: '5+ years',\n      skills: ['Python', 'React', 'Node.js', 'AWS'],\n      education: 'MS Computer Science',\n      summary: 'Experienced software engineer with expertise in full-stack development...',\n      score: 95,\n      rawData: {\n        Resume: {\n          PersonalInformation: {\n            FullName: 'John Doe',\n            Email: '<EMAIL>',\n            ContactNumber: '+****************',\n            Address: 'San Francisco, CA'\n          },\n          WorkExperience: [\n            {\n              Role: 'Senior Software Engineer',\n              CompanyName: 'Tech Corp',\n              StartYear: '2019',\n              EndYear: 'Present',\n              'Description/Responsibility': 'Led development of scalable web applications using React and Node.js'\n            },\n            {\n              Role: 'Software Engineer',\n              CompanyName: 'StartupXYZ',\n              StartYear: '2017',\n              EndYear: '2019',\n              'Description/Responsibility': 'Developed full-stack applications and improved system performance'\n            }\n          ],\n          Education: [\n            {\n              Degree: 'MS Computer Science',\n              Institution: 'Stanford University',\n              GraduationYear: '2017',\n              'GPA/Marks/%': 85\n            }\n          ],\n          Skills: ['Python', 'React', 'Node.js', 'AWS'],\n          TotalWorkExperienceInYears: 5\n        }\n      }\n    },\n    {\n      id: 2,\n      name: 'Jane Smith',\n      title: 'Frontend Developer',\n      email: '<EMAIL>',\n      phone: '+****************',\n      location: 'New York, NY',\n      experience: '3+ years',\n      skills: ['React', 'TypeScript', 'CSS', 'JavaScript'],\n      education: 'BS Computer Science',\n      summary: 'Creative frontend developer passionate about user experience...',\n      score: 88,\n      rawData: {\n        Resume: {\n          PersonalInformation: {\n            FullName: 'Jane Smith',\n            Email: '<EMAIL>',\n            ContactNumber: '+****************',\n            Address: 'New York, NY'\n          },\n          WorkExperience: [\n            {\n              Role: 'Frontend Developer',\n              CompanyName: 'Design Studio',\n              StartYear: '2021',\n              EndYear: 'Present',\n              'Description/Responsibility': 'Created responsive web interfaces using React and modern CSS'\n            }\n          ],\n          Education: [\n            {\n              Degree: 'BS Computer Science',\n              Institution: 'NYU',\n              GraduationYear: '2021',\n              'GPA/Marks/%': 78\n            }\n          ],\n          Skills: ['React', 'TypeScript', 'CSS', 'JavaScript'],\n          TotalWorkExperienceInYears: 3\n        }\n      }\n    },\n    {\n      id: 3,\n      name: 'Mike Johnson',\n      title: 'DevOps Engineer',\n      email: '<EMAIL>',\n      phone: '+****************',\n      location: 'Austin, TX',\n      experience: '4+ years',\n      skills: ['Docker', 'Kubernetes', 'AWS', 'Python'],\n      education: 'BS Information Technology',\n      summary: 'DevOps engineer specializing in cloud infrastructure and automation...',\n      score: 92,\n      rawData: {\n        Resume: {\n          PersonalInformation: {\n            FullName: 'Mike Johnson',\n            Email: '<EMAIL>',\n            ContactNumber: '+****************',\n            Address: 'Austin, TX'\n          },\n          WorkExperience: [\n            {\n              Role: 'DevOps Engineer',\n              CompanyName: 'Cloud Solutions Inc',\n              StartYear: '2020',\n              EndYear: 'Present',\n              'Description/Responsibility': 'Managed cloud infrastructure and automated deployment pipelines'\n            }\n          ],\n          Education: [\n            {\n              Degree: 'BS Information Technology',\n              Institution: 'UT Austin',\n              GraduationYear: '2020',\n              'GPA/Marks/%': 82\n            }\n          ],\n          Skills: ['Docker', 'Kubernetes', 'AWS', 'Python'],\n          TotalWorkExperienceInYears: 4\n        }\n      }\n    }\n  ];\n\n  const handleSearch = async (e) => {\n    e.preventDefault();\n    if (!searchQuery.trim()) {\n      toast.warning('Please enter a search query');\n      return;\n    }\n\n    setIsSearching(true);\n\n    try {\n      // Call the FastAPI backend exactly like frontendV3.py does\n      const url = new URL('http://localhost:8001/query/');\n      url.searchParams.append('naturalQuery', searchQuery);\n\n      const response = await fetch(url, {\n        method: 'GET',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n      });\n\n      const data = await response.json();\n\n      if (response.ok) {\n        // Handle FastAPI response format exactly like frontendV3.py: {query: [], listOfDict: [], result: \"\"}\n        let transformedResults = [];\n\n        // Check if API returns a direct list (like frontendV3.py handles)\n        if (Array.isArray(data)) {\n          transformedResults = data.map(resume => transformResumeData(resume));\n        } else if (data.listOfDict && Array.isArray(data.listOfDict)) {\n          // Transform the resume data from MongoDB to display format\n          transformedResults = data.listOfDict.map(resume => transformResumeData(resume));\n        } else {\n          // Fallback to mock data if API doesn't return results\n          transformedResults = mockResults.filter(resume =>\n            resume.name.toLowerCase().includes(searchQuery.toLowerCase()) ||\n            resume.title.toLowerCase().includes(searchQuery.toLowerCase()) ||\n            resume.skills.some(skill => skill.toLowerCase().includes(searchQuery.toLowerCase()))\n          );\n        }\n\n        setSearchResults(transformedResults);\n\n        // Show success message with GPT result (exactly like frontendV3.py)\n        if (data.result && data.result !== 'Data received successfully.') {\n          toast.success(`🤖 GPT Analysis: ${data.result}`);\n        } else {\n          toast.success(`🔍 Found ${transformedResults.length} matching resumes`);\n        }\n\n        // Log GPT response and MongoDB query for debugging (like frontendV3.py developer mode)\n        if (data.query) {\n          console.log('🤖 MongoDB Query Generated by GPT:', JSON.stringify(data.query, null, 2));\n          console.log('📊 GPT Result:', data.result);\n          console.log('📋 Full API Response:', data);\n        }\n      } else {\n        throw new Error(data.error || 'Search failed');\n      }\n    } catch (error) {\n      console.error('Search error:', error);\n\n      // Fallback to mock data on network error\n      const fallbackResults = mockResults.filter(resume =>\n        resume.name.toLowerCase().includes(searchQuery.toLowerCase()) ||\n        resume.title.toLowerCase().includes(searchQuery.toLowerCase()) ||\n        resume.skills.some(skill => skill.toLowerCase().includes(searchQuery.toLowerCase()))\n      );\n\n      setSearchResults(fallbackResults);\n      toast.warning(`Using demo data - Found ${fallbackResults.length} matching resumes`);\n    } finally {\n      setIsSearching(false);\n    }\n  };\n\n  const handleShowAll = async (page = 1) => {\n    setIsSearching(true);\n    try {\n      // Call the FastAPI backend directly to get all resumes\n      const response = await fetch('http://localhost:8001/showall');\n      const data = await response.json();\n\n      if (response.ok) {\n        let transformedResults = [];\n\n        if (Array.isArray(data)) {\n          // FastAPI /showall returns array directly\n          transformedResults = data.map(resume => {\n            // Handle the nested Resume structure from MongoDB\n            const resumeData = resume.Resume || resume;\n\n            return {\n              id: resume._id || Math.random().toString(36).substr(2, 9),\n              name: resumeData.PersonalInformation?.FullName || resumeData.name || 'Unknown',\n              title: resumeData.PersonalInformation?.Designation || resumeData.title || 'No title',\n              email: resumeData.PersonalInformation?.Email || resumeData.email || 'No email',\n              phone: resumeData.PersonalInformation?.ContactNumber || resumeData.phone || 'No phone',\n              location: resumeData.PersonalInformation?.Address || resumeData.location || 'No location',\n              experience: resumeData.TotalWorkExperienceInYears ?\n                `${resumeData.TotalWorkExperienceInYears} years` :\n                resumeData.WorkExperience?.length ?\n                  `${resumeData.WorkExperience.length}+ positions` :\n                  resumeData.experience || 'No experience data',\n              skills: resumeData.Skills || resumeData.skills || [],\n              education: resumeData.Education?.length ?\n                resumeData.Education[0]?.Degree || 'Education available' :\n                resumeData.education || 'No education data',\n              summary: resumeData.Objective || resumeData.Summary || resumeData.summary || 'No summary available',\n              score: 90, // Default score for show all\n              rawData: resume // Keep original data for reference\n            };\n          });\n        } else if (data.error) {\n          throw new Error(data.error);\n        } else {\n          // Fallback to mock data if API doesn't return proper results\n          transformedResults = mockResults;\n        }\n\n        setSearchResults(transformedResults);\n        setCurrentPage(1);\n        setTotalCount(transformedResults.length);\n        setTotalPages(Math.ceil(transformedResults.length / resultsPerPage));\n        setSearchQuery('');\n\n        toast.success(`📊 Loaded ${transformedResults.length} resumes from database`);\n      } else {\n        throw new Error(data.error || 'Failed to load resumes');\n      }\n    } catch (error) {\n      console.error('Load resumes error:', error);\n\n      // Fallback to mock data on network error\n      setSearchResults(mockResults);\n      setCurrentPage(1);\n      setTotalCount(mockResults.length);\n      setTotalPages(Math.ceil(mockResults.length / resultsPerPage));\n      setSearchQuery('');\n\n      toast.warning(`Using demo data - Showing ${mockResults.length} sample resumes`);\n    } finally {\n      setIsSearching(false);\n    }\n  };\n\n  const handlePageChange = (page) => {\n    if (page >= 1 && page <= totalPages) {\n      handleShowAll(page);\n    }\n  };\n\n  const filteredResults = searchResults.filter(result => {\n    // Basic text filter\n    if (filterQuery) {\n      const matchesText = result.name.toLowerCase().includes(filterQuery.toLowerCase()) ||\n                         result.title.toLowerCase().includes(filterQuery.toLowerCase()) ||\n                         result.location.toLowerCase().includes(filterQuery.toLowerCase());\n      if (!matchesText) return false;\n    }\n\n    // Advanced filters\n    const filters = advancedFilters;\n\n    // Experience filter\n    if (filters.minExperience || filters.maxExperience) {\n      const experienceYears = result.rawData?.Resume?.TotalWorkExperienceInYears ||\n                             extractExperienceYears(result.experience) || 0;\n\n      if (filters.minExperience && experienceYears < parseInt(filters.minExperience)) return false;\n      if (filters.maxExperience && experienceYears > parseInt(filters.maxExperience)) return false;\n    }\n\n    // Education filter\n    if (filters.education) {\n      const hasEducation = result.rawData?.Resume?.Education?.some(edu =>\n        (edu.Degree || '').toLowerCase().includes(filters.education.toLowerCase())\n      ) || (result.education || '').toLowerCase().includes(filters.education.toLowerCase());\n      if (!hasEducation) return false;\n    }\n\n    // Location filter\n    if (filters.location) {\n      const matchesLocation = (result.location || '').toLowerCase().includes(filters.location.toLowerCase());\n      if (!matchesLocation) return false;\n    }\n\n    // Skills filter\n    if (filters.skills) {\n      const skillsText = filters.skills.toLowerCase();\n      const hasSkill = result.skills?.some(skill => skill.toLowerCase().includes(skillsText)) ||\n                      (result.rawData?.Resume?.Skills || []).some(skill =>\n                        skill.toLowerCase().includes(skillsText)\n                      );\n      if (!hasSkill) return false;\n    }\n\n    // Institution filter\n    if (filters.institution) {\n      const hasInstitution = result.rawData?.Resume?.Education?.some(edu =>\n        (edu.Institution || edu.School || '').toLowerCase().includes(filters.institution.toLowerCase())\n      );\n      if (!hasInstitution) return false;\n    }\n\n    // Graduation year filter\n    if (filters.graduationYear) {\n      const hasGradYear = result.rawData?.Resume?.Education?.some(edu =>\n        (edu.GraduationYear || edu.Year || '').toString().includes(filters.graduationYear)\n      );\n      if (!hasGradYear) return false;\n    }\n\n    // Company filter\n    if (filters.company) {\n      const hasCompany = result.rawData?.Resume?.WorkExperience?.some(exp =>\n        (exp.Company || exp.CompanyName || exp.Organization || '').toLowerCase().includes(filters.company.toLowerCase())\n      );\n      if (!hasCompany) return false;\n    }\n\n    // Minimum marks filter\n    if (filters.minMarks) {\n      const hasMinMarks = result.rawData?.Resume?.Education?.some(edu => {\n        const marks = parseFloat(edu['GPA/Marks/%']) || 0;\n        return marks >= parseFloat(filters.minMarks);\n      });\n      if (!hasMinMarks) return false;\n    }\n\n    return true;\n  });\n\n  // Helper function to extract experience years from text\n  const extractExperienceYears = (experienceText) => {\n    if (!experienceText) return 0;\n    const match = experienceText.match(/(\\d+)\\+?\\s*years?/i);\n    return match ? parseInt(match[1]) : 0;\n  };\n\n  // Helper function to transform resume data (like frontendV3.py)\n  const transformResumeData = (resume) => {\n    // Handle the nested Resume structure from MongoDB\n    const resumeData = resume.Resume || resume;\n\n    return {\n      id: resume._id || Math.random().toString(36).substr(2, 9),\n      name: resumeData.PersonalInformation?.FullName || resumeData.name || 'Unknown',\n      title: resumeData.PersonalInformation?.Designation || resumeData.title || 'No title',\n      email: resumeData.PersonalInformation?.Email || resumeData.email || 'No email',\n      phone: resumeData.PersonalInformation?.ContactNumber || resumeData.phone || 'No phone',\n      location: resumeData.PersonalInformation?.Address || resumeData.location || 'No location',\n      experience: resumeData.TotalWorkExperienceInYears ?\n        `${resumeData.TotalWorkExperienceInYears} years` :\n        resumeData.WorkExperience?.length ?\n          `${resumeData.WorkExperience.length}+ positions` :\n          resumeData.experience || 'No experience data',\n      skills: resumeData.Skills || resumeData.skills || [],\n      education: resumeData.Education?.length ?\n        resumeData.Education[0]?.Degree || 'Education available' :\n        resumeData.education || 'No education data',\n      summary: resumeData.Objective || resumeData.Summary || resumeData.summary || 'No summary available',\n      score: 95, // Default score\n      rawData: resume // Keep original data for reference\n    };\n  };\n\n  const handleAdvancedFilterChange = (filterName, value) => {\n    setAdvancedFilters(prev => ({\n      ...prev,\n      [filterName]: value\n    }));\n  };\n\n  const clearAllFilters = () => {\n    setFilterQuery('');\n    setAdvancedFilters({\n      minExperience: '',\n      maxExperience: '',\n      education: '',\n      location: '',\n      skills: '',\n      minMarks: '',\n      institution: '',\n      graduationYear: '',\n      company: ''\n    });\n  };\n\n  const hasActiveFilters = () => {\n    return filterQuery || Object.values(advancedFilters).some(value => value !== '');\n  };\n\n  const handleDownload = (resume) => {\n    try {\n      // Create a comprehensive text-based resume content\n      const resumeContent = `\n${resume.name}\n${resume.title}\n${resume.email} | ${resume.phone} | ${resume.location}\n\nPROFESSIONAL SUMMARY\n${resume.summary}\n\nWORK EXPERIENCE\n${resume.rawData?.Resume?.WorkExperience ?\n  resume.rawData.Resume.WorkExperience.map(exp =>\n    `${exp.JobTitle || exp.Position} at ${exp.Company || exp.Organization}\nDuration: ${exp.Duration || exp.Years || 'Not specified'}\n${exp.Description ? 'Description: ' + exp.Description : ''}\n${exp.Responsibilities ? 'Responsibilities: ' + exp.Responsibilities : ''}\n`\n  ).join('\\n') : resume.experience}\n\n${resume.rawData?.Resume?.TotalWorkExperienceInYears ?\n  `Total Experience: ${resume.rawData.Resume.TotalWorkExperienceInYears} years\\n` : ''}\n\nEDUCATION\n${resume.rawData?.Resume?.Education ?\n  resume.rawData.Resume.Education.map(edu =>\n    `${edu.Degree || 'Degree'} from ${edu.Institution || edu.School || 'Institution'}\nGraduation Year: ${edu.GraduationYear || edu.Year || 'Not specified'}\n${edu['GPA/Marks/%'] && edu['GPA/Marks/%'] > 0 ? 'Marks: ' + edu['GPA/Marks/%'] + '%' : ''}\n`\n  ).join('\\n') : resume.education}\n\nSKILLS & TECHNOLOGIES\n${resume.skills.join(', ')}\n\n${resume.rawData?.Resume?.Languages && resume.rawData.Resume.Languages.length > 0 ?\n  `LANGUAGES\\n${resume.rawData.Resume.Languages.join(', ')}\\n` : ''}\n\n${resume.rawData?.Resume?.Projects && resume.rawData.Resume.Projects.length > 0 ?\n  `PROJECTS\\n${resume.rawData.Resume.Projects.map(project =>\n    `${project.Name || project.Title || 'Project'}\n${project.Description ? 'Description: ' + project.Description : ''}\n${project.Technologies ? 'Technologies: ' + project.Technologies : ''}\n`\n  ).join('\\n')}` : ''}\n\n${resume.rawData?.Resume?.Certifications && resume.rawData.Resume.Certifications.length > 0 ?\n  `CERTIFICATIONS\\n${resume.rawData.Resume.Certifications.map(cert =>\n    `${cert.Name || cert.Title || 'Certification'}\n${cert.IssuingOrganization ? 'Issued by: ' + cert.IssuingOrganization : ''}\n${cert.IssueDate ? 'Date: ' + cert.IssueDate : ''}\n`\n  ).join('\\n')}` : ''}\n\n${resume.rawData?.Resume?.Achievements && resume.rawData.Resume.Achievements.length > 0 ?\n  `ACHIEVEMENTS\\n${resume.rawData.Resume.Achievements.map(achievement =>\n    typeof achievement === 'string' ? achievement :\n    `${achievement.AchievementName || achievement.Name || 'Achievement'}\n${achievement.IssueDate ? 'Date: ' + achievement.IssueDate : ''}\n${achievement.IssuingOrganization ? 'Organization: ' + achievement.IssuingOrganization : ''}\n${achievement.Description ? 'Description: ' + achievement.Description : ''}\n`\n  ).join('\\n')}` : ''}\n\n${resume.rawData?.Resume?.PersonalInformation?.LinkedIn ?\n  `ADDITIONAL INFORMATION\\nLinkedIn: ${resume.rawData.Resume.PersonalInformation.LinkedIn}` : ''}\n\nGenerated on: ${new Date().toLocaleDateString()}\n      `.trim();\n\n      // Create and download the file\n      const blob = new Blob([resumeContent], { type: 'text/plain' });\n      const url = URL.createObjectURL(blob);\n      const link = document.createElement('a');\n      link.href = url;\n      link.download = `${resume.name.replace(/\\s+/g, '_')}_Resume.txt`;\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n      URL.revokeObjectURL(url);\n\n      toast.success(`Downloaded resume for ${resume.name}`);\n    } catch (error) {\n      console.error('Download error:', error);\n      toast.error('Failed to download resume. Please try again.');\n    }\n  };\n\n  const handleView = (resume) => {\n    setSelectedResume(resume);\n    setIsModalOpen(true);\n    toast.info(`Opening detailed view for ${resume.name}`);\n  };\n\n  const handleCloseModal = () => {\n    setIsModalOpen(false);\n    setSelectedResume(null);\n  };\n\n  const handleEmailClick = (email, name) => {\n    window.open(`mailto:${email}?subject=Regarding your resume&body=Hi ${name},%0D%0A%0D%0AI found your resume and would like to discuss potential opportunities.%0D%0A%0D%0ABest regards`, '_blank');\n    toast.info(`Opening email client to contact ${name}`);\n  };\n\n  const handlePhoneClick = (phone, name) => {\n    if (navigator.userAgent.match(/Mobile|Android|iPhone|iPad/)) {\n      window.open(`tel:${phone}`, '_blank');\n    } else {\n      navigator.clipboard.writeText(phone).then(() => {\n        toast.success(`Phone number copied: ${phone}`);\n      }).catch(() => {\n        toast.info(`Phone: ${phone}`);\n      });\n    }\n  };\n\n\n\n  const handleExportAll = () => {\n    if (filteredResults.length === 0) {\n      toast.warning('No results to export');\n      return;\n    }\n\n    try {\n      const exportData = filteredResults.map(resume => ({\n        name: resume.name,\n        title: resume.title,\n        email: resume.email,\n        phone: resume.phone,\n        location: resume.location,\n        experience: resume.experience,\n        education: resume.education,\n        skills: resume.skills.join(', '),\n        summary: resume.summary,\n        matchScore: resume.score\n      }));\n\n      const csvContent = [\n        'Name,Title,Email,Phone,Location,Experience,Education,Skills,Summary,Match Score',\n        ...exportData.map(row =>\n          Object.values(row).map(value =>\n            `\"${String(value).replace(/\"/g, '\"\"')}\"`\n          ).join(',')\n        )\n      ].join('\\n');\n\n      const blob = new Blob([csvContent], { type: 'text/csv' });\n      const url = URL.createObjectURL(blob);\n      const link = document.createElement('a');\n      link.href = url;\n      link.download = `Resume_Search_Results_${new Date().toISOString().split('T')[0]}.csv`;\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n      URL.revokeObjectURL(url);\n\n      toast.success(`Exported ${filteredResults.length} resumes to CSV`);\n    } catch (error) {\n      console.error('Export error:', error);\n      toast.error('Failed to export results. Please try again.');\n    }\n  };\n\n  return (\n    <div className=\"search-page\">\n      <div className=\"page-header\">\n        <h1>Search Resumes</h1>\n        <p>Use natural language to find the perfect candidates for your needs</p>\n      </div>\n\n      {/* Search Section */}\n      <div className=\"search-section\">\n        <form onSubmit={handleSearch} className=\"search-form\">\n          <div className=\"search-input-group\">\n            <div className=\"search-input-wrapper\">\n              <FiSearch className=\"search-icon\" />\n              <input\n                type=\"text\"\n                placeholder=\"e.g., 'Find Python developers with 5+ years experience' or 'Show frontend engineers in California'\"\n                value={searchQuery}\n                onChange={(e) => setSearchQuery(e.target.value)}\n                className=\"search-input\"\n                disabled={isSearching}\n              />\n            </div>\n            <div className=\"search-buttons\">\n              <button\n                type=\"submit\"\n                className=\"btn btn-primary\"\n                disabled={isSearching}\n              >\n                {isSearching ? (\n                  <>\n                    <div className=\"loading-spinner\" />\n                    Searching...\n                  </>\n                ) : (\n                  <>\n                    <FiSearch size={16} />\n                    Search\n                  </>\n                )}\n              </button>\n              <button\n                type=\"button\"\n                className=\"btn btn-secondary\"\n                onClick={handleShowAll}\n                disabled={isSearching}\n              >\n                Show All\n              </button>\n            </div>\n          </div>\n        </form>\n\n        {/* Filter Section */}\n        {searchResults.length > 0 && (\n          <div className=\"filter-section\">\n            <div className=\"filter-header\">\n              <div className=\"basic-filter-wrapper\">\n                <FiFilter className=\"filter-icon\" />\n                <input\n                  type=\"text\"\n                  placeholder=\"Quick filter by name, title, or location...\"\n                  value={filterQuery}\n                  onChange={(e) => setFilterQuery(e.target.value)}\n                  className=\"filter-input\"\n                />\n              </div>\n              <div className=\"filter-controls\">\n                <button\n                  type=\"button\"\n                  className={`btn btn-secondary filter-toggle ${showFilters ? 'active' : ''}`}\n                  onClick={() => setShowFilters(!showFilters)}\n                >\n                  <FiSettings size={16} />\n                  Advanced Filters\n                  {showFilters ? <FiChevronUp size={16} /> : <FiChevronDown size={16} />}\n                </button>\n                {hasActiveFilters() && (\n                  <button\n                    type=\"button\"\n                    className=\"btn btn-outline clear-filters\"\n                    onClick={clearAllFilters}\n                  >\n                    <FiX size={16} />\n                    Clear All\n                  </button>\n                )}\n              </div>\n            </div>\n\n            {/* Advanced Filters Panel */}\n            {showFilters && (\n              <div className=\"advanced-filters\">\n                <div className=\"filters-grid\">\n                  <div className=\"filter-group\">\n                    <label>Experience (Years)</label>\n                    <div className=\"range-inputs\">\n                      <input\n                        type=\"number\"\n                        placeholder=\"Min\"\n                        value={advancedFilters.minExperience}\n                        onChange={(e) => handleAdvancedFilterChange('minExperience', e.target.value)}\n                        className=\"filter-input small\"\n                        min=\"0\"\n                      />\n                      <span>to</span>\n                      <input\n                        type=\"number\"\n                        placeholder=\"Max\"\n                        value={advancedFilters.maxExperience}\n                        onChange={(e) => handleAdvancedFilterChange('maxExperience', e.target.value)}\n                        className=\"filter-input small\"\n                        min=\"0\"\n                      />\n                    </div>\n                  </div>\n\n                  <div className=\"filter-group\">\n                    <label>Education Level</label>\n                    <input\n                      type=\"text\"\n                      placeholder=\"e.g., Bachelor, Master, PhD\"\n                      value={advancedFilters.education}\n                      onChange={(e) => handleAdvancedFilterChange('education', e.target.value)}\n                      className=\"filter-input\"\n                    />\n                  </div>\n\n                  <div className=\"filter-group\">\n                    <label>Location</label>\n                    <input\n                      type=\"text\"\n                      placeholder=\"e.g., Mumbai, Delhi, Bangalore\"\n                      value={advancedFilters.location}\n                      onChange={(e) => handleAdvancedFilterChange('location', e.target.value)}\n                      className=\"filter-input\"\n                    />\n                  </div>\n\n                  <div className=\"filter-group\">\n                    <label>Skills</label>\n                    <input\n                      type=\"text\"\n                      placeholder=\"e.g., Python, React, Java\"\n                      value={advancedFilters.skills}\n                      onChange={(e) => handleAdvancedFilterChange('skills', e.target.value)}\n                      className=\"filter-input\"\n                    />\n                  </div>\n\n                  <div className=\"filter-group\">\n                    <label>Institution</label>\n                    <input\n                      type=\"text\"\n                      placeholder=\"e.g., IIT, University name\"\n                      value={advancedFilters.institution}\n                      onChange={(e) => handleAdvancedFilterChange('institution', e.target.value)}\n                      className=\"filter-input\"\n                    />\n                  </div>\n\n                  <div className=\"filter-group\">\n                    <label>Graduation Year</label>\n                    <input\n                      type=\"text\"\n                      placeholder=\"e.g., 2020, 2021\"\n                      value={advancedFilters.graduationYear}\n                      onChange={(e) => handleAdvancedFilterChange('graduationYear', e.target.value)}\n                      className=\"filter-input\"\n                    />\n                  </div>\n\n                  <div className=\"filter-group\">\n                    <label>Company</label>\n                    <input\n                      type=\"text\"\n                      placeholder=\"e.g., Google, Microsoft, TCS\"\n                      value={advancedFilters.company}\n                      onChange={(e) => handleAdvancedFilterChange('company', e.target.value)}\n                      className=\"filter-input\"\n                    />\n                  </div>\n\n                  <div className=\"filter-group\">\n                    <label>Minimum Marks (%)</label>\n                    <input\n                      type=\"number\"\n                      placeholder=\"e.g., 75, 80\"\n                      value={advancedFilters.minMarks}\n                      onChange={(e) => handleAdvancedFilterChange('minMarks', e.target.value)}\n                      className=\"filter-input\"\n                      min=\"0\"\n                      max=\"100\"\n                    />\n                  </div>\n                </div>\n              </div>\n            )}\n\n            <div className=\"results-count\">\n              {filteredResults.length} of {searchResults.length} results\n              {hasActiveFilters() && <span className=\"filtered-indicator\"> (filtered)</span>}\n            </div>\n          </div>\n        )}\n      </div>\n\n      {/* Results Section */}\n      {searchResults.length > 0 ? (\n        <div className=\"results-section\">\n          <div className=\"results-header\">\n            <h2>Search Results</h2>\n            <div className=\"results-actions\">\n              <button\n                className=\"btn btn-secondary\"\n                onClick={handleExportAll}\n                title={`Export ${filteredResults.length} results to CSV`}\n              >\n                <FiDownload size={16} />\n                Export All ({filteredResults.length})\n              </button>\n            </div>\n          </div>\n\n          <div className=\"results-grid\">\n            {filteredResults.map((resume) => (\n              <div key={resume.id} className=\"resume-card\">\n                <div className=\"resume-header\">\n                  <div className=\"resume-avatar\">\n                    <FiUser size={24} />\n                  </div>\n                  <div className=\"resume-info\">\n                    <h3 className=\"resume-name\">{resume.name}</h3>\n                    <p className=\"resume-title\">{resume.title}</p>\n                  </div>\n                </div>\n\n                <div className=\"resume-details\">\n                  <div className=\"detail-item location-item\">\n                    <FiMapPin size={14} />\n                    <span>{resume.location}</span>\n                  </div>\n                  <div\n                    className=\"detail-item email-item clickable\"\n                    onClick={() => handleEmailClick(resume.email, resume.name)}\n                    title={`Send email to ${resume.name}`}\n                  >\n                    <FiMail size={14} />\n                    <span>{resume.email}</span>\n                    <FiExternalLink size={12} className=\"external-icon\" />\n                  </div>\n                  <div\n                    className=\"detail-item phone-item clickable\"\n                    onClick={() => handlePhoneClick(resume.phone, resume.name)}\n                    title={`Call ${resume.name}`}\n                  >\n                    <FiPhone size={14} />\n                    <span>{resume.phone}</span>\n                    <FiExternalLink size={12} className=\"external-icon\" />\n                  </div>\n                </div>\n\n                <div className=\"resume-content\">\n                  <div className=\"content-row\">\n                    <div className=\"content-section\">\n                      <h4>Experience</h4>\n                      <p>{resume.experience}</p>\n                    </div>\n                    <div className=\"content-section\">\n                      <h4>Education</h4>\n                      <p>{resume.education}</p>\n                    </div>\n                  </div>\n                  <div className=\"content-section\">\n                    <h4>Skills</h4>\n                    <div className=\"skills-list\">\n                      {resume.skills.map((skill, index) => (\n                        <span key={index} className=\"skill-tag\">{skill}</span>\n                      ))}\n                    </div>\n                  </div>\n                </div>\n\n                <div className=\"resume-actions\">\n                  <button\n                    className=\"btn btn-secondary\"\n                    onClick={() => handleView(resume)}\n                  >\n                    <FiEye size={16} />\n                    View Details\n                  </button>\n                  <button\n                    className=\"btn btn-primary\"\n                    onClick={() => handleDownload(resume)}\n                  >\n                    <FiDownload size={16} />\n                    Download\n                  </button>\n                </div>\n              </div>\n            ))}\n          </div>\n\n          {/* Pagination */}\n          {totalPages > 1 && (\n            <div className=\"pagination-section\">\n              <div className=\"pagination-info\">\n                Showing {((currentPage - 1) * resultsPerPage) + 1} to {Math.min(currentPage * resultsPerPage, totalCount)} of {totalCount} results\n              </div>\n              <div className=\"pagination-controls\">\n                <button\n                  className=\"btn btn-secondary pagination-btn\"\n                  onClick={() => handlePageChange(currentPage - 1)}\n                  disabled={currentPage === 1}\n                >\n                  Previous\n                </button>\n\n                <div className=\"page-numbers\">\n                  {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {\n                    let pageNum;\n                    if (totalPages <= 5) {\n                      pageNum = i + 1;\n                    } else if (currentPage <= 3) {\n                      pageNum = i + 1;\n                    } else if (currentPage >= totalPages - 2) {\n                      pageNum = totalPages - 4 + i;\n                    } else {\n                      pageNum = currentPage - 2 + i;\n                    }\n\n                    return (\n                      <button\n                        key={pageNum}\n                        className={`btn pagination-btn ${currentPage === pageNum ? 'btn-primary' : 'btn-secondary'}`}\n                        onClick={() => handlePageChange(pageNum)}\n                      >\n                        {pageNum}\n                      </button>\n                    );\n                  })}\n                </div>\n\n                <button\n                  className=\"btn btn-secondary pagination-btn\"\n                  onClick={() => handlePageChange(currentPage + 1)}\n                  disabled={currentPage === totalPages}\n                >\n                  Next\n                </button>\n              </div>\n            </div>\n          )}\n        </div>\n      ) : !isSearching ? (\n        <div className=\"empty-state\">\n          <div className=\"empty-icon\">\n            <FiSearch size={48} />\n          </div>\n          <h3>Ready to Search!</h3>\n          <p>Use the search box above to find resumes using natural language, or click \"Show All\" to view all resumes in the database.</p>\n          <div className=\"search-examples\">\n            <h4>Try searching for:</h4>\n            <ul>\n              <li>\"Find Python developers\"</li>\n              <li>\"Show teachers with 5+ years experience\"</li>\n              <li>\"Software engineers in Mumbai\"</li>\n            </ul>\n          </div>\n        </div>\n      ) : null}\n\n      {/* Resume Details Modal */}\n      <ResumeModal\n        resume={selectedResume}\n        isOpen={isModalOpen}\n        onClose={handleCloseModal}\n      />\n    </div>\n  );\n};\n\nexport default SearchResumes;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,QAAQ,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,KAAK,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,OAAO,EAAEC,cAAc,EAAEC,MAAM,EAAEC,GAAG,EAAEC,UAAU,EAAEC,aAAa,EAAEC,WAAW,QAAQ,gBAAgB;AAC9K,SAASC,KAAK,QAAQ,gBAAgB;AACtC,OAAOC,WAAW,MAAM,2BAA2B;AACnD,OAAO,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE7B,MAAMC,aAAa,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC1B,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACyB,WAAW,EAAEC,cAAc,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAAC2B,aAAa,EAAEC,gBAAgB,CAAC,GAAG5B,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAAC6B,WAAW,EAAEC,cAAc,CAAC,GAAG9B,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC+B,WAAW,EAAEC,cAAc,CAAC,GAAGhC,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACiC,eAAe,EAAEC,kBAAkB,CAAC,GAAGlC,QAAQ,CAAC;IACrDmC,aAAa,EAAE,EAAE;IACjBC,aAAa,EAAE,EAAE;IACjBC,SAAS,EAAE,EAAE;IACbC,QAAQ,EAAE,EAAE;IACZC,MAAM,EAAE,EAAE;IACVC,QAAQ,EAAE,EAAE;IACZC,WAAW,EAAE,EAAE;IACfC,cAAc,EAAE,EAAE;IAClBC,OAAO,EAAE;EACX,CAAC,CAAC;EACF,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAG7C,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAAC8C,WAAW,EAAEC,cAAc,CAAC,GAAG/C,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACgD,WAAW,EAAEC,cAAc,CAAC,GAAGjD,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAACkD,UAAU,EAAEC,aAAa,CAAC,GAAGnD,QAAQ,CAAC,CAAC,CAAC;EAC/C,MAAM,CAACoD,UAAU,EAAEC,aAAa,CAAC,GAAGrD,QAAQ,CAAC,CAAC,CAAC;EAC/C,MAAMsD,cAAc,GAAG,EAAE;;EAEzB;EACA,MAAMC,WAAW,GAAG,CAClB;IACEC,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,UAAU;IAChBC,KAAK,EAAE,0BAA0B;IACjCC,KAAK,EAAE,oBAAoB;IAC3BC,KAAK,EAAE,mBAAmB;IAC1BtB,QAAQ,EAAE,mBAAmB;IAC7BuB,UAAU,EAAE,UAAU;IACtBtB,MAAM,EAAE,CAAC,QAAQ,EAAE,OAAO,EAAE,SAAS,EAAE,KAAK,CAAC;IAC7CF,SAAS,EAAE,qBAAqB;IAChCyB,OAAO,EAAE,2EAA2E;IACpFC,KAAK,EAAE,EAAE;IACTC,OAAO,EAAE;MACPC,MAAM,EAAE;QACNC,mBAAmB,EAAE;UACnBC,QAAQ,EAAE,UAAU;UACpBC,KAAK,EAAE,oBAAoB;UAC3BC,aAAa,EAAE,mBAAmB;UAClCC,OAAO,EAAE;QACX,CAAC;QACDC,cAAc,EAAE,CACd;UACEC,IAAI,EAAE,0BAA0B;UAChCC,WAAW,EAAE,WAAW;UACxBC,SAAS,EAAE,MAAM;UACjBC,OAAO,EAAE,SAAS;UAClB,4BAA4B,EAAE;QAChC,CAAC,EACD;UACEH,IAAI,EAAE,mBAAmB;UACzBC,WAAW,EAAE,YAAY;UACzBC,SAAS,EAAE,MAAM;UACjBC,OAAO,EAAE,MAAM;UACf,4BAA4B,EAAE;QAChC,CAAC,CACF;QACDC,SAAS,EAAE,CACT;UACEC,MAAM,EAAE,qBAAqB;UAC7BC,WAAW,EAAE,qBAAqB;UAClCC,cAAc,EAAE,MAAM;UACtB,aAAa,EAAE;QACjB,CAAC,CACF;QACDC,MAAM,EAAE,CAAC,QAAQ,EAAE,OAAO,EAAE,SAAS,EAAE,KAAK,CAAC;QAC7CC,0BAA0B,EAAE;MAC9B;IACF;EACF,CAAC,EACD;IACEzB,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,YAAY;IAClBC,KAAK,EAAE,oBAAoB;IAC3BC,KAAK,EAAE,sBAAsB;IAC7BC,KAAK,EAAE,mBAAmB;IAC1BtB,QAAQ,EAAE,cAAc;IACxBuB,UAAU,EAAE,UAAU;IACtBtB,MAAM,EAAE,CAAC,OAAO,EAAE,YAAY,EAAE,KAAK,EAAE,YAAY,CAAC;IACpDF,SAAS,EAAE,qBAAqB;IAChCyB,OAAO,EAAE,iEAAiE;IAC1EC,KAAK,EAAE,EAAE;IACTC,OAAO,EAAE;MACPC,MAAM,EAAE;QACNC,mBAAmB,EAAE;UACnBC,QAAQ,EAAE,YAAY;UACtBC,KAAK,EAAE,sBAAsB;UAC7BC,aAAa,EAAE,mBAAmB;UAClCC,OAAO,EAAE;QACX,CAAC;QACDC,cAAc,EAAE,CACd;UACEC,IAAI,EAAE,oBAAoB;UAC1BC,WAAW,EAAE,eAAe;UAC5BC,SAAS,EAAE,MAAM;UACjBC,OAAO,EAAE,SAAS;UAClB,4BAA4B,EAAE;QAChC,CAAC,CACF;QACDC,SAAS,EAAE,CACT;UACEC,MAAM,EAAE,qBAAqB;UAC7BC,WAAW,EAAE,KAAK;UAClBC,cAAc,EAAE,MAAM;UACtB,aAAa,EAAE;QACjB,CAAC,CACF;QACDC,MAAM,EAAE,CAAC,OAAO,EAAE,YAAY,EAAE,KAAK,EAAE,YAAY,CAAC;QACpDC,0BAA0B,EAAE;MAC9B;IACF;EACF,CAAC,EACD;IACEzB,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,cAAc;IACpBC,KAAK,EAAE,iBAAiB;IACxBC,KAAK,EAAE,wBAAwB;IAC/BC,KAAK,EAAE,mBAAmB;IAC1BtB,QAAQ,EAAE,YAAY;IACtBuB,UAAU,EAAE,UAAU;IACtBtB,MAAM,EAAE,CAAC,QAAQ,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,CAAC;IACjDF,SAAS,EAAE,2BAA2B;IACtCyB,OAAO,EAAE,wEAAwE;IACjFC,KAAK,EAAE,EAAE;IACTC,OAAO,EAAE;MACPC,MAAM,EAAE;QACNC,mBAAmB,EAAE;UACnBC,QAAQ,EAAE,cAAc;UACxBC,KAAK,EAAE,wBAAwB;UAC/BC,aAAa,EAAE,mBAAmB;UAClCC,OAAO,EAAE;QACX,CAAC;QACDC,cAAc,EAAE,CACd;UACEC,IAAI,EAAE,iBAAiB;UACvBC,WAAW,EAAE,qBAAqB;UAClCC,SAAS,EAAE,MAAM;UACjBC,OAAO,EAAE,SAAS;UAClB,4BAA4B,EAAE;QAChC,CAAC,CACF;QACDC,SAAS,EAAE,CACT;UACEC,MAAM,EAAE,2BAA2B;UACnCC,WAAW,EAAE,WAAW;UACxBC,cAAc,EAAE,MAAM;UACtB,aAAa,EAAE;QACjB,CAAC,CACF;QACDC,MAAM,EAAE,CAAC,QAAQ,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,CAAC;QACjDC,0BAA0B,EAAE;MAC9B;IACF;EACF,CAAC,CACF;EAED,MAAMC,YAAY,GAAG,MAAOC,CAAC,IAAK;IAChCA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,IAAI,CAAC7D,WAAW,CAAC8D,IAAI,CAAC,CAAC,EAAE;MACvBtE,KAAK,CAACuE,OAAO,CAAC,6BAA6B,CAAC;MAC5C;IACF;IAEA5D,cAAc,CAAC,IAAI,CAAC;IAEpB,IAAI;MACF;MACA,MAAM6D,GAAG,GAAG,IAAIC,GAAG,CAAC,8BAA8B,CAAC;MACnDD,GAAG,CAACE,YAAY,CAACC,MAAM,CAAC,cAAc,EAAEnE,WAAW,CAAC;MAEpD,MAAMoE,QAAQ,GAAG,MAAMC,KAAK,CAACL,GAAG,EAAE;QAChCM,MAAM,EAAE,KAAK;QACbC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB;MACF,CAAC,CAAC;MAEF,MAAMC,IAAI,GAAG,MAAMJ,QAAQ,CAACK,IAAI,CAAC,CAAC;MAElC,IAAIL,QAAQ,CAACM,EAAE,EAAE;QACf;QACA,IAAIC,kBAAkB,GAAG,EAAE;;QAE3B;QACA,IAAIC,KAAK,CAACC,OAAO,CAACL,IAAI,CAAC,EAAE;UACvBG,kBAAkB,GAAGH,IAAI,CAACM,GAAG,CAACC,MAAM,IAAIC,mBAAmB,CAACD,MAAM,CAAC,CAAC;QACtE,CAAC,MAAM,IAAIP,IAAI,CAACS,UAAU,IAAIL,KAAK,CAACC,OAAO,CAACL,IAAI,CAACS,UAAU,CAAC,EAAE;UAC5D;UACAN,kBAAkB,GAAGH,IAAI,CAACS,UAAU,CAACH,GAAG,CAACC,MAAM,IAAIC,mBAAmB,CAACD,MAAM,CAAC,CAAC;QACjF,CAAC,MAAM;UACL;UACAJ,kBAAkB,GAAG3C,WAAW,CAACkD,MAAM,CAACH,MAAM,IAC5CA,MAAM,CAAC7C,IAAI,CAACiD,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACpF,WAAW,CAACmF,WAAW,CAAC,CAAC,CAAC,IAC7DJ,MAAM,CAAC5C,KAAK,CAACgD,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACpF,WAAW,CAACmF,WAAW,CAAC,CAAC,CAAC,IAC9DJ,MAAM,CAAC/D,MAAM,CAACqE,IAAI,CAACC,KAAK,IAAIA,KAAK,CAACH,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACpF,WAAW,CAACmF,WAAW,CAAC,CAAC,CAAC,CACrF,CAAC;QACH;QAEA9E,gBAAgB,CAACsE,kBAAkB,CAAC;;QAEpC;QACA,IAAIH,IAAI,CAACe,MAAM,IAAIf,IAAI,CAACe,MAAM,KAAK,6BAA6B,EAAE;UAChE/F,KAAK,CAACgG,OAAO,CAAC,oBAAoBhB,IAAI,CAACe,MAAM,EAAE,CAAC;QAClD,CAAC,MAAM;UACL/F,KAAK,CAACgG,OAAO,CAAC,YAAYb,kBAAkB,CAACc,MAAM,mBAAmB,CAAC;QACzE;;QAEA;QACA,IAAIjB,IAAI,CAACkB,KAAK,EAAE;UACdC,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAEC,IAAI,CAACC,SAAS,CAACtB,IAAI,CAACkB,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;UACtFC,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEpB,IAAI,CAACe,MAAM,CAAC;UAC1CI,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEpB,IAAI,CAAC;QAC5C;MACF,CAAC,MAAM;QACL,MAAM,IAAIuB,KAAK,CAACvB,IAAI,CAACwB,KAAK,IAAI,eAAe,CAAC;MAChD;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdL,OAAO,CAACK,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;;MAErC;MACA,MAAMC,eAAe,GAAGjE,WAAW,CAACkD,MAAM,CAACH,MAAM,IAC/CA,MAAM,CAAC7C,IAAI,CAACiD,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACpF,WAAW,CAACmF,WAAW,CAAC,CAAC,CAAC,IAC7DJ,MAAM,CAAC5C,KAAK,CAACgD,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACpF,WAAW,CAACmF,WAAW,CAAC,CAAC,CAAC,IAC9DJ,MAAM,CAAC/D,MAAM,CAACqE,IAAI,CAACC,KAAK,IAAIA,KAAK,CAACH,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACpF,WAAW,CAACmF,WAAW,CAAC,CAAC,CAAC,CACrF,CAAC;MAED9E,gBAAgB,CAAC4F,eAAe,CAAC;MACjCzG,KAAK,CAACuE,OAAO,CAAC,2BAA2BkC,eAAe,CAACR,MAAM,mBAAmB,CAAC;IACrF,CAAC,SAAS;MACRtF,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;EAED,MAAM+F,aAAa,GAAG,MAAAA,CAAOC,IAAI,GAAG,CAAC,KAAK;IACxChG,cAAc,CAAC,IAAI,CAAC;IACpB,IAAI;MACF;MACA,MAAMiE,QAAQ,GAAG,MAAMC,KAAK,CAAC,+BAA+B,CAAC;MAC7D,MAAMG,IAAI,GAAG,MAAMJ,QAAQ,CAACK,IAAI,CAAC,CAAC;MAElC,IAAIL,QAAQ,CAACM,EAAE,EAAE;QACf,IAAIC,kBAAkB,GAAG,EAAE;QAE3B,IAAIC,KAAK,CAACC,OAAO,CAACL,IAAI,CAAC,EAAE;UACvB;UACAG,kBAAkB,GAAGH,IAAI,CAACM,GAAG,CAACC,MAAM,IAAI;YAAA,IAAAqB,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,sBAAA;YACtC;YACA,MAAMC,UAAU,GAAG7B,MAAM,CAACrC,MAAM,IAAIqC,MAAM;YAE1C,OAAO;cACL9C,EAAE,EAAE8C,MAAM,CAAC8B,GAAG,IAAIC,IAAI,CAACC,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;cACzD/E,IAAI,EAAE,EAAAkE,qBAAA,GAAAQ,UAAU,CAACjE,mBAAmB,cAAAyD,qBAAA,uBAA9BA,qBAAA,CAAgCxD,QAAQ,KAAIgE,UAAU,CAAC1E,IAAI,IAAI,SAAS;cAC9EC,KAAK,EAAE,EAAAkE,sBAAA,GAAAO,UAAU,CAACjE,mBAAmB,cAAA0D,sBAAA,uBAA9BA,sBAAA,CAAgCa,WAAW,KAAIN,UAAU,CAACzE,KAAK,IAAI,UAAU;cACpFC,KAAK,EAAE,EAAAkE,sBAAA,GAAAM,UAAU,CAACjE,mBAAmB,cAAA2D,sBAAA,uBAA9BA,sBAAA,CAAgCzD,KAAK,KAAI+D,UAAU,CAACxE,KAAK,IAAI,UAAU;cAC9EC,KAAK,EAAE,EAAAkE,sBAAA,GAAAK,UAAU,CAACjE,mBAAmB,cAAA4D,sBAAA,uBAA9BA,sBAAA,CAAgCzD,aAAa,KAAI8D,UAAU,CAACvE,KAAK,IAAI,UAAU;cACtFtB,QAAQ,EAAE,EAAAyF,sBAAA,GAAAI,UAAU,CAACjE,mBAAmB,cAAA6D,sBAAA,uBAA9BA,sBAAA,CAAgCzD,OAAO,KAAI6D,UAAU,CAAC7F,QAAQ,IAAI,aAAa;cACzFuB,UAAU,EAAEsE,UAAU,CAAClD,0BAA0B,GAC/C,GAAGkD,UAAU,CAAClD,0BAA0B,QAAQ,GAChD,CAAA+C,qBAAA,GAAAG,UAAU,CAAC5D,cAAc,cAAAyD,qBAAA,eAAzBA,qBAAA,CAA2BhB,MAAM,GAC/B,GAAGmB,UAAU,CAAC5D,cAAc,CAACyC,MAAM,aAAa,GAChDmB,UAAU,CAACtE,UAAU,IAAI,oBAAoB;cACjDtB,MAAM,EAAE4F,UAAU,CAACnD,MAAM,IAAImD,UAAU,CAAC5F,MAAM,IAAI,EAAE;cACpDF,SAAS,EAAE,CAAA4F,qBAAA,GAAAE,UAAU,CAACvD,SAAS,cAAAqD,qBAAA,eAApBA,qBAAA,CAAsBjB,MAAM,GACrC,EAAAkB,sBAAA,GAAAC,UAAU,CAACvD,SAAS,CAAC,CAAC,CAAC,cAAAsD,sBAAA,uBAAvBA,sBAAA,CAAyBrD,MAAM,KAAI,qBAAqB,GACxDsD,UAAU,CAAC9F,SAAS,IAAI,mBAAmB;cAC7CyB,OAAO,EAAEqE,UAAU,CAACO,SAAS,IAAIP,UAAU,CAACQ,OAAO,IAAIR,UAAU,CAACrE,OAAO,IAAI,sBAAsB;cACnGC,KAAK,EAAE,EAAE;cAAE;cACXC,OAAO,EAAEsC,MAAM,CAAC;YAClB,CAAC;UACH,CAAC,CAAC;QACJ,CAAC,MAAM,IAAIP,IAAI,CAACwB,KAAK,EAAE;UACrB,MAAM,IAAID,KAAK,CAACvB,IAAI,CAACwB,KAAK,CAAC;QAC7B,CAAC,MAAM;UACL;UACArB,kBAAkB,GAAG3C,WAAW;QAClC;QAEA3B,gBAAgB,CAACsE,kBAAkB,CAAC;QACpCjD,cAAc,CAAC,CAAC,CAAC;QACjBI,aAAa,CAAC6C,kBAAkB,CAACc,MAAM,CAAC;QACxC7D,aAAa,CAACkF,IAAI,CAACO,IAAI,CAAC1C,kBAAkB,CAACc,MAAM,GAAG1D,cAAc,CAAC,CAAC;QACpE9B,cAAc,CAAC,EAAE,CAAC;QAElBT,KAAK,CAACgG,OAAO,CAAC,aAAab,kBAAkB,CAACc,MAAM,wBAAwB,CAAC;MAC/E,CAAC,MAAM;QACL,MAAM,IAAIM,KAAK,CAACvB,IAAI,CAACwB,KAAK,IAAI,wBAAwB,CAAC;MACzD;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdL,OAAO,CAACK,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;;MAE3C;MACA3F,gBAAgB,CAAC2B,WAAW,CAAC;MAC7BN,cAAc,CAAC,CAAC,CAAC;MACjBI,aAAa,CAACE,WAAW,CAACyD,MAAM,CAAC;MACjC7D,aAAa,CAACkF,IAAI,CAACO,IAAI,CAACrF,WAAW,CAACyD,MAAM,GAAG1D,cAAc,CAAC,CAAC;MAC7D9B,cAAc,CAAC,EAAE,CAAC;MAElBT,KAAK,CAACuE,OAAO,CAAC,6BAA6B/B,WAAW,CAACyD,MAAM,iBAAiB,CAAC;IACjF,CAAC,SAAS;MACRtF,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;EAED,MAAMmH,gBAAgB,GAAInB,IAAI,IAAK;IACjC,IAAIA,IAAI,IAAI,CAAC,IAAIA,IAAI,IAAIxE,UAAU,EAAE;MACnCuE,aAAa,CAACC,IAAI,CAAC;IACrB;EACF,CAAC;EAED,MAAMoB,eAAe,GAAGnH,aAAa,CAAC8E,MAAM,CAACK,MAAM,IAAI;IACrD;IACA,IAAIjF,WAAW,EAAE;MACf,MAAMkH,WAAW,GAAGjC,MAAM,CAACrD,IAAI,CAACiD,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC9E,WAAW,CAAC6E,WAAW,CAAC,CAAC,CAAC,IAC9DI,MAAM,CAACpD,KAAK,CAACgD,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC9E,WAAW,CAAC6E,WAAW,CAAC,CAAC,CAAC,IAC9DI,MAAM,CAACxE,QAAQ,CAACoE,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC9E,WAAW,CAAC6E,WAAW,CAAC,CAAC,CAAC;MACpF,IAAI,CAACqC,WAAW,EAAE,OAAO,KAAK;IAChC;;IAEA;IACA,MAAMC,OAAO,GAAG/G,eAAe;;IAE/B;IACA,IAAI+G,OAAO,CAAC7G,aAAa,IAAI6G,OAAO,CAAC5G,aAAa,EAAE;MAAA,IAAA6G,eAAA,EAAAC,qBAAA;MAClD,MAAMC,eAAe,GAAG,EAAAF,eAAA,GAAAnC,MAAM,CAAC9C,OAAO,cAAAiF,eAAA,wBAAAC,qBAAA,GAAdD,eAAA,CAAgBhF,MAAM,cAAAiF,qBAAA,uBAAtBA,qBAAA,CAAwBjE,0BAA0B,KACnDmE,sBAAsB,CAACtC,MAAM,CAACjD,UAAU,CAAC,IAAI,CAAC;MAErE,IAAImF,OAAO,CAAC7G,aAAa,IAAIgH,eAAe,GAAGE,QAAQ,CAACL,OAAO,CAAC7G,aAAa,CAAC,EAAE,OAAO,KAAK;MAC5F,IAAI6G,OAAO,CAAC5G,aAAa,IAAI+G,eAAe,GAAGE,QAAQ,CAACL,OAAO,CAAC5G,aAAa,CAAC,EAAE,OAAO,KAAK;IAC9F;;IAEA;IACA,IAAI4G,OAAO,CAAC3G,SAAS,EAAE;MAAA,IAAAiH,gBAAA,EAAAC,qBAAA,EAAAC,sBAAA;MACrB,MAAMC,YAAY,GAAG,EAAAH,gBAAA,GAAAxC,MAAM,CAAC9C,OAAO,cAAAsF,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBrF,MAAM,cAAAsF,qBAAA,wBAAAC,sBAAA,GAAtBD,qBAAA,CAAwB3E,SAAS,cAAA4E,sBAAA,uBAAjCA,sBAAA,CAAmC5C,IAAI,CAAC8C,GAAG,IAC9D,CAACA,GAAG,CAAC7E,MAAM,IAAI,EAAE,EAAE6B,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACqC,OAAO,CAAC3G,SAAS,CAACqE,WAAW,CAAC,CAAC,CAC3E,CAAC,KAAI,CAACI,MAAM,CAACzE,SAAS,IAAI,EAAE,EAAEqE,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACqC,OAAO,CAAC3G,SAAS,CAACqE,WAAW,CAAC,CAAC,CAAC;MACrF,IAAI,CAAC+C,YAAY,EAAE,OAAO,KAAK;IACjC;;IAEA;IACA,IAAIT,OAAO,CAAC1G,QAAQ,EAAE;MACpB,MAAMqH,eAAe,GAAG,CAAC7C,MAAM,CAACxE,QAAQ,IAAI,EAAE,EAAEoE,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACqC,OAAO,CAAC1G,QAAQ,CAACoE,WAAW,CAAC,CAAC,CAAC;MACtG,IAAI,CAACiD,eAAe,EAAE,OAAO,KAAK;IACpC;;IAEA;IACA,IAAIX,OAAO,CAACzG,MAAM,EAAE;MAAA,IAAAqH,cAAA,EAAAC,gBAAA,EAAAC,qBAAA;MAClB,MAAMC,UAAU,GAAGf,OAAO,CAACzG,MAAM,CAACmE,WAAW,CAAC,CAAC;MAC/C,MAAMsD,QAAQ,GAAG,EAAAJ,cAAA,GAAA9C,MAAM,CAACvE,MAAM,cAAAqH,cAAA,uBAAbA,cAAA,CAAehD,IAAI,CAACC,KAAK,IAAIA,KAAK,CAACH,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACoD,UAAU,CAAC,CAAC,KACvE,CAAC,EAAAF,gBAAA,GAAA/C,MAAM,CAAC9C,OAAO,cAAA6F,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgB5F,MAAM,cAAA6F,qBAAA,uBAAtBA,qBAAA,CAAwB9E,MAAM,KAAI,EAAE,EAAE4B,IAAI,CAACC,KAAK,IAC/CA,KAAK,CAACH,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACoD,UAAU,CACzC,CAAC;MACjB,IAAI,CAACC,QAAQ,EAAE,OAAO,KAAK;IAC7B;;IAEA;IACA,IAAIhB,OAAO,CAACvG,WAAW,EAAE;MAAA,IAAAwH,gBAAA,EAAAC,qBAAA,EAAAC,sBAAA;MACvB,MAAMC,cAAc,IAAAH,gBAAA,GAAGnD,MAAM,CAAC9C,OAAO,cAAAiG,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBhG,MAAM,cAAAiG,qBAAA,wBAAAC,sBAAA,GAAtBD,qBAAA,CAAwBtF,SAAS,cAAAuF,sBAAA,uBAAjCA,sBAAA,CAAmCvD,IAAI,CAAC8C,GAAG,IAChE,CAACA,GAAG,CAAC5E,WAAW,IAAI4E,GAAG,CAACW,MAAM,IAAI,EAAE,EAAE3D,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACqC,OAAO,CAACvG,WAAW,CAACiE,WAAW,CAAC,CAAC,CAChG,CAAC;MACD,IAAI,CAAC0D,cAAc,EAAE,OAAO,KAAK;IACnC;;IAEA;IACA,IAAIpB,OAAO,CAACtG,cAAc,EAAE;MAAA,IAAA4H,gBAAA,EAAAC,qBAAA,EAAAC,sBAAA;MAC1B,MAAMC,WAAW,IAAAH,gBAAA,GAAGxD,MAAM,CAAC9C,OAAO,cAAAsG,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBrG,MAAM,cAAAsG,qBAAA,wBAAAC,sBAAA,GAAtBD,qBAAA,CAAwB3F,SAAS,cAAA4F,sBAAA,uBAAjCA,sBAAA,CAAmC5D,IAAI,CAAC8C,GAAG,IAC7D,CAACA,GAAG,CAAC3E,cAAc,IAAI2E,GAAG,CAACgB,IAAI,IAAI,EAAE,EAAEnC,QAAQ,CAAC,CAAC,CAAC5B,QAAQ,CAACqC,OAAO,CAACtG,cAAc,CACnF,CAAC;MACD,IAAI,CAAC+H,WAAW,EAAE,OAAO,KAAK;IAChC;;IAEA;IACA,IAAIzB,OAAO,CAACrG,OAAO,EAAE;MAAA,IAAAgI,gBAAA,EAAAC,qBAAA,EAAAC,sBAAA;MACnB,MAAMC,UAAU,IAAAH,gBAAA,GAAG7D,MAAM,CAAC9C,OAAO,cAAA2G,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgB1G,MAAM,cAAA2G,qBAAA,wBAAAC,sBAAA,GAAtBD,qBAAA,CAAwBrG,cAAc,cAAAsG,sBAAA,uBAAtCA,sBAAA,CAAwCjE,IAAI,CAACmE,GAAG,IACjE,CAACA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACtG,WAAW,IAAIsG,GAAG,CAACE,YAAY,IAAI,EAAE,EAAEvE,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACqC,OAAO,CAACrG,OAAO,CAAC+D,WAAW,CAAC,CAAC,CACjH,CAAC;MACD,IAAI,CAACoE,UAAU,EAAE,OAAO,KAAK;IAC/B;;IAEA;IACA,IAAI9B,OAAO,CAACxG,QAAQ,EAAE;MAAA,IAAA0I,gBAAA,EAAAC,qBAAA,EAAAC,sBAAA;MACpB,MAAMC,WAAW,IAAAH,gBAAA,GAAGpE,MAAM,CAAC9C,OAAO,cAAAkH,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBjH,MAAM,cAAAkH,qBAAA,wBAAAC,sBAAA,GAAtBD,qBAAA,CAAwBvG,SAAS,cAAAwG,sBAAA,uBAAjCA,sBAAA,CAAmCxE,IAAI,CAAC8C,GAAG,IAAI;QACjE,MAAM4B,KAAK,GAAGC,UAAU,CAAC7B,GAAG,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC;QACjD,OAAO4B,KAAK,IAAIC,UAAU,CAACvC,OAAO,CAACxG,QAAQ,CAAC;MAC9C,CAAC,CAAC;MACF,IAAI,CAAC6I,WAAW,EAAE,OAAO,KAAK;IAChC;IAEA,OAAO,IAAI;EACb,CAAC,CAAC;;EAEF;EACA,MAAMjC,sBAAsB,GAAIoC,cAAc,IAAK;IACjD,IAAI,CAACA,cAAc,EAAE,OAAO,CAAC;IAC7B,MAAMC,KAAK,GAAGD,cAAc,CAACC,KAAK,CAAC,oBAAoB,CAAC;IACxD,OAAOA,KAAK,GAAGpC,QAAQ,CAACoC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;EACvC,CAAC;;EAED;EACA,MAAMlF,mBAAmB,GAAID,MAAM,IAAK;IAAA,IAAAoF,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;IACtC;IACA,MAAM9D,UAAU,GAAG7B,MAAM,CAACrC,MAAM,IAAIqC,MAAM;IAE1C,OAAO;MACL9C,EAAE,EAAE8C,MAAM,CAAC8B,GAAG,IAAIC,IAAI,CAACC,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;MACzD/E,IAAI,EAAE,EAAAiI,sBAAA,GAAAvD,UAAU,CAACjE,mBAAmB,cAAAwH,sBAAA,uBAA9BA,sBAAA,CAAgCvH,QAAQ,KAAIgE,UAAU,CAAC1E,IAAI,IAAI,SAAS;MAC9EC,KAAK,EAAE,EAAAiI,sBAAA,GAAAxD,UAAU,CAACjE,mBAAmB,cAAAyH,sBAAA,uBAA9BA,sBAAA,CAAgClD,WAAW,KAAIN,UAAU,CAACzE,KAAK,IAAI,UAAU;MACpFC,KAAK,EAAE,EAAAiI,sBAAA,GAAAzD,UAAU,CAACjE,mBAAmB,cAAA0H,sBAAA,uBAA9BA,sBAAA,CAAgCxH,KAAK,KAAI+D,UAAU,CAACxE,KAAK,IAAI,UAAU;MAC9EC,KAAK,EAAE,EAAAiI,sBAAA,GAAA1D,UAAU,CAACjE,mBAAmB,cAAA2H,sBAAA,uBAA9BA,sBAAA,CAAgCxH,aAAa,KAAI8D,UAAU,CAACvE,KAAK,IAAI,UAAU;MACtFtB,QAAQ,EAAE,EAAAwJ,sBAAA,GAAA3D,UAAU,CAACjE,mBAAmB,cAAA4H,sBAAA,uBAA9BA,sBAAA,CAAgCxH,OAAO,KAAI6D,UAAU,CAAC7F,QAAQ,IAAI,aAAa;MACzFuB,UAAU,EAAEsE,UAAU,CAAClD,0BAA0B,GAC/C,GAAGkD,UAAU,CAAClD,0BAA0B,QAAQ,GAChD,CAAA8G,sBAAA,GAAA5D,UAAU,CAAC5D,cAAc,cAAAwH,sBAAA,eAAzBA,sBAAA,CAA2B/E,MAAM,GAC/B,GAAGmB,UAAU,CAAC5D,cAAc,CAACyC,MAAM,aAAa,GAChDmB,UAAU,CAACtE,UAAU,IAAI,oBAAoB;MACjDtB,MAAM,EAAE4F,UAAU,CAACnD,MAAM,IAAImD,UAAU,CAAC5F,MAAM,IAAI,EAAE;MACpDF,SAAS,EAAE,CAAA2J,sBAAA,GAAA7D,UAAU,CAACvD,SAAS,cAAAoH,sBAAA,eAApBA,sBAAA,CAAsBhF,MAAM,GACrC,EAAAiF,sBAAA,GAAA9D,UAAU,CAACvD,SAAS,CAAC,CAAC,CAAC,cAAAqH,sBAAA,uBAAvBA,sBAAA,CAAyBpH,MAAM,KAAI,qBAAqB,GACxDsD,UAAU,CAAC9F,SAAS,IAAI,mBAAmB;MAC7CyB,OAAO,EAAEqE,UAAU,CAACO,SAAS,IAAIP,UAAU,CAACQ,OAAO,IAAIR,UAAU,CAACrE,OAAO,IAAI,sBAAsB;MACnGC,KAAK,EAAE,EAAE;MAAE;MACXC,OAAO,EAAEsC,MAAM,CAAC;IAClB,CAAC;EACH,CAAC;EAED,MAAM4F,0BAA0B,GAAGA,CAACC,UAAU,EAAEC,KAAK,KAAK;IACxDlK,kBAAkB,CAACmK,IAAI,KAAK;MAC1B,GAAGA,IAAI;MACP,CAACF,UAAU,GAAGC;IAChB,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAME,eAAe,GAAGA,CAAA,KAAM;IAC5BxK,cAAc,CAAC,EAAE,CAAC;IAClBI,kBAAkB,CAAC;MACjBC,aAAa,EAAE,EAAE;MACjBC,aAAa,EAAE,EAAE;MACjBC,SAAS,EAAE,EAAE;MACbC,QAAQ,EAAE,EAAE;MACZC,MAAM,EAAE,EAAE;MACVC,QAAQ,EAAE,EAAE;MACZC,WAAW,EAAE,EAAE;MACfC,cAAc,EAAE,EAAE;MAClBC,OAAO,EAAE;IACX,CAAC,CAAC;EACJ,CAAC;EAED,MAAM4J,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,OAAO1K,WAAW,IAAI2K,MAAM,CAACC,MAAM,CAACxK,eAAe,CAAC,CAAC2E,IAAI,CAACwF,KAAK,IAAIA,KAAK,KAAK,EAAE,CAAC;EAClF,CAAC;EAED,MAAMM,cAAc,GAAIpG,MAAM,IAAK;IACjC,IAAI;MAAA,IAAAqG,eAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,qBAAA,EAAAC,sBAAA;MACF;MACA,MAAMC,aAAa,GAAG;AAC5B,EAAEtH,MAAM,CAAC7C,IAAI;AACb,EAAE6C,MAAM,CAAC5C,KAAK;AACd,EAAE4C,MAAM,CAAC3C,KAAK,MAAM2C,MAAM,CAAC1C,KAAK,MAAM0C,MAAM,CAAChE,QAAQ;AACrD;AACA;AACA,EAAEgE,MAAM,CAACxC,OAAO;AAChB;AACA;AACA,EAAE,CAAA6I,eAAA,GAAArG,MAAM,CAACtC,OAAO,cAAA2I,eAAA,gBAAAC,qBAAA,GAAdD,eAAA,CAAgB1I,MAAM,cAAA2I,qBAAA,eAAtBA,qBAAA,CAAwBrI,cAAc,GACtC+B,MAAM,CAACtC,OAAO,CAACC,MAAM,CAACM,cAAc,CAAC8B,GAAG,CAAC0E,GAAG,IAC1C,GAAGA,GAAG,CAAC8C,QAAQ,IAAI9C,GAAG,CAAC+C,QAAQ,OAAO/C,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,YAAY;AACzE,YAAYF,GAAG,CAACgD,QAAQ,IAAIhD,GAAG,CAACiD,KAAK,IAAI,eAAe;AACxD,EAAEjD,GAAG,CAACkD,WAAW,GAAG,eAAe,GAAGlD,GAAG,CAACkD,WAAW,GAAG,EAAE;AAC1D,EAAElD,GAAG,CAACmD,gBAAgB,GAAG,oBAAoB,GAAGnD,GAAG,CAACmD,gBAAgB,GAAG,EAAE;AACzE,CACE,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC,GAAG7H,MAAM,CAACzC,UAAU;AAClC;AACA,EAAE,CAAAgJ,gBAAA,GAAAvG,MAAM,CAACtC,OAAO,cAAA6I,gBAAA,gBAAAC,qBAAA,GAAdD,gBAAA,CAAgB5I,MAAM,cAAA6I,qBAAA,eAAtBA,qBAAA,CAAwB7H,0BAA0B,GAClD,qBAAqBqB,MAAM,CAACtC,OAAO,CAACC,MAAM,CAACgB,0BAA0B,UAAU,GAAG,EAAE;AACtF;AACA;AACA,EAAE,CAAA8H,gBAAA,GAAAzG,MAAM,CAACtC,OAAO,cAAA+I,gBAAA,gBAAAC,qBAAA,GAAdD,gBAAA,CAAgB9I,MAAM,cAAA+I,qBAAA,eAAtBA,qBAAA,CAAwBpI,SAAS,GACjC0B,MAAM,CAACtC,OAAO,CAACC,MAAM,CAACW,SAAS,CAACyB,GAAG,CAACqD,GAAG,IACrC,GAAGA,GAAG,CAAC7E,MAAM,IAAI,QAAQ,SAAS6E,GAAG,CAAC5E,WAAW,IAAI4E,GAAG,CAACW,MAAM,IAAI,aAAa;AACpF,mBAAmBX,GAAG,CAAC3E,cAAc,IAAI2E,GAAG,CAACgB,IAAI,IAAI,eAAe;AACpE,EAAEhB,GAAG,CAAC,aAAa,CAAC,IAAIA,GAAG,CAAC,aAAa,CAAC,GAAG,CAAC,GAAG,SAAS,GAAGA,GAAG,CAAC,aAAa,CAAC,GAAG,GAAG,GAAG,EAAE;AAC1F,CACE,CAAC,CAACyE,IAAI,CAAC,IAAI,CAAC,GAAG7H,MAAM,CAACjE,SAAS;AACjC;AACA;AACA,EAAEiE,MAAM,CAAC/D,MAAM,CAAC4L,IAAI,CAAC,IAAI,CAAC;AAC1B;AACA,EAAE,CAAAlB,gBAAA,GAAA3G,MAAM,CAACtC,OAAO,cAAAiJ,gBAAA,gBAAAC,qBAAA,GAAdD,gBAAA,CAAgBhJ,MAAM,cAAAiJ,qBAAA,eAAtBA,qBAAA,CAAwBkB,SAAS,IAAI9H,MAAM,CAACtC,OAAO,CAACC,MAAM,CAACmK,SAAS,CAACpH,MAAM,GAAG,CAAC,GAC/E,cAAcV,MAAM,CAACtC,OAAO,CAACC,MAAM,CAACmK,SAAS,CAACD,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,EAAE;AACnE;AACA,EAAE,CAAAhB,gBAAA,GAAA7G,MAAM,CAACtC,OAAO,cAAAmJ,gBAAA,gBAAAC,qBAAA,GAAdD,gBAAA,CAAgBlJ,MAAM,cAAAmJ,qBAAA,eAAtBA,qBAAA,CAAwBiB,QAAQ,IAAI/H,MAAM,CAACtC,OAAO,CAACC,MAAM,CAACoK,QAAQ,CAACrH,MAAM,GAAG,CAAC,GAC7E,aAAaV,MAAM,CAACtC,OAAO,CAACC,MAAM,CAACoK,QAAQ,CAAChI,GAAG,CAACiI,OAAO,IACrD,GAAGA,OAAO,CAACC,IAAI,IAAID,OAAO,CAACE,KAAK,IAAI,SAAS;AACjD,EAAEF,OAAO,CAACL,WAAW,GAAG,eAAe,GAAGK,OAAO,CAACL,WAAW,GAAG,EAAE;AAClE,EAAEK,OAAO,CAACG,YAAY,GAAG,gBAAgB,GAAGH,OAAO,CAACG,YAAY,GAAG,EAAE;AACrE,CACE,CAAC,CAACN,IAAI,CAAC,IAAI,CAAC,EAAE,GAAG,EAAE;AACrB;AACA,EAAE,CAAAd,gBAAA,GAAA/G,MAAM,CAACtC,OAAO,cAAAqJ,gBAAA,gBAAAC,qBAAA,GAAdD,gBAAA,CAAgBpJ,MAAM,cAAAqJ,qBAAA,eAAtBA,qBAAA,CAAwBoB,cAAc,IAAIpI,MAAM,CAACtC,OAAO,CAACC,MAAM,CAACyK,cAAc,CAAC1H,MAAM,GAAG,CAAC,GACzF,mBAAmBV,MAAM,CAACtC,OAAO,CAACC,MAAM,CAACyK,cAAc,CAACrI,GAAG,CAACsI,IAAI,IAC9D,GAAGA,IAAI,CAACJ,IAAI,IAAII,IAAI,CAACH,KAAK,IAAI,eAAe;AACjD,EAAEG,IAAI,CAACC,mBAAmB,GAAG,aAAa,GAAGD,IAAI,CAACC,mBAAmB,GAAG,EAAE;AAC1E,EAAED,IAAI,CAACE,SAAS,GAAG,QAAQ,GAAGF,IAAI,CAACE,SAAS,GAAG,EAAE;AACjD,CACE,CAAC,CAACV,IAAI,CAAC,IAAI,CAAC,EAAE,GAAG,EAAE;AACrB;AACA,EAAE,CAAAZ,gBAAA,GAAAjH,MAAM,CAACtC,OAAO,cAAAuJ,gBAAA,gBAAAC,qBAAA,GAAdD,gBAAA,CAAgBtJ,MAAM,cAAAuJ,qBAAA,eAAtBA,qBAAA,CAAwBsB,YAAY,IAAIxI,MAAM,CAACtC,OAAO,CAACC,MAAM,CAAC6K,YAAY,CAAC9H,MAAM,GAAG,CAAC,GACrF,iBAAiBV,MAAM,CAACtC,OAAO,CAACC,MAAM,CAAC6K,YAAY,CAACzI,GAAG,CAAC0I,WAAW,IACjE,OAAOA,WAAW,KAAK,QAAQ,GAAGA,WAAW,GAC7C,GAAGA,WAAW,CAACC,eAAe,IAAID,WAAW,CAACR,IAAI,IAAI,aAAa;AACvE,EAAEQ,WAAW,CAACF,SAAS,GAAG,QAAQ,GAAGE,WAAW,CAACF,SAAS,GAAG,EAAE;AAC/D,EAAEE,WAAW,CAACH,mBAAmB,GAAG,gBAAgB,GAAGG,WAAW,CAACH,mBAAmB,GAAG,EAAE;AAC3F,EAAEG,WAAW,CAACd,WAAW,GAAG,eAAe,GAAGc,WAAW,CAACd,WAAW,GAAG,EAAE;AAC1E,CACE,CAAC,CAACE,IAAI,CAAC,IAAI,CAAC,EAAE,GAAG,EAAE;AACrB;AACA,EAAE,CAAAV,gBAAA,GAAAnH,MAAM,CAACtC,OAAO,cAAAyJ,gBAAA,gBAAAC,qBAAA,GAAdD,gBAAA,CAAgBxJ,MAAM,cAAAyJ,qBAAA,gBAAAC,sBAAA,GAAtBD,qBAAA,CAAwBxJ,mBAAmB,cAAAyJ,sBAAA,eAA3CA,sBAAA,CAA6CsB,QAAQ,GACrD,qCAAqC3I,MAAM,CAACtC,OAAO,CAACC,MAAM,CAACC,mBAAmB,CAAC+K,QAAQ,EAAE,GAAG,EAAE;AAChG;AACA,gBAAgB,IAAIC,IAAI,CAAC,CAAC,CAACC,kBAAkB,CAAC,CAAC;AAC/C,OAAO,CAAC9J,IAAI,CAAC,CAAC;;MAER;MACA,MAAM+J,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACzB,aAAa,CAAC,EAAE;QAAE0B,IAAI,EAAE;MAAa,CAAC,CAAC;MAC9D,MAAM/J,GAAG,GAAGC,GAAG,CAAC+J,eAAe,CAACH,IAAI,CAAC;MACrC,MAAMI,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;MACxCF,IAAI,CAACG,IAAI,GAAGpK,GAAG;MACfiK,IAAI,CAACI,QAAQ,GAAG,GAAGtJ,MAAM,CAAC7C,IAAI,CAACoM,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,aAAa;MAChEJ,QAAQ,CAACK,IAAI,CAACC,WAAW,CAACP,IAAI,CAAC;MAC/BA,IAAI,CAACQ,KAAK,CAAC,CAAC;MACZP,QAAQ,CAACK,IAAI,CAACG,WAAW,CAACT,IAAI,CAAC;MAC/BhK,GAAG,CAAC0K,eAAe,CAAC3K,GAAG,CAAC;MAExBxE,KAAK,CAACgG,OAAO,CAAC,yBAAyBT,MAAM,CAAC7C,IAAI,EAAE,CAAC;IACvD,CAAC,CAAC,OAAO8D,KAAK,EAAE;MACdL,OAAO,CAACK,KAAK,CAAC,iBAAiB,EAAEA,KAAK,CAAC;MACvCxG,KAAK,CAACwG,KAAK,CAAC,8CAA8C,CAAC;IAC7D;EACF,CAAC;EAED,MAAM4I,UAAU,GAAI7J,MAAM,IAAK;IAC7BzD,iBAAiB,CAACyD,MAAM,CAAC;IACzBvD,cAAc,CAAC,IAAI,CAAC;IACpBhC,KAAK,CAACqP,IAAI,CAAC,6BAA6B9J,MAAM,CAAC7C,IAAI,EAAE,CAAC;EACxD,CAAC;EAED,MAAM4M,gBAAgB,GAAGA,CAAA,KAAM;IAC7BtN,cAAc,CAAC,KAAK,CAAC;IACrBF,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;EAED,MAAMyN,gBAAgB,GAAGA,CAAC3M,KAAK,EAAEF,IAAI,KAAK;IACxC8M,MAAM,CAACC,IAAI,CAAC,UAAU7M,KAAK,0CAA0CF,IAAI,6GAA6G,EAAE,QAAQ,CAAC;IACjM1C,KAAK,CAACqP,IAAI,CAAC,mCAAmC3M,IAAI,EAAE,CAAC;EACvD,CAAC;EAED,MAAMgN,gBAAgB,GAAGA,CAAC7M,KAAK,EAAEH,IAAI,KAAK;IACxC,IAAIiN,SAAS,CAACC,SAAS,CAAClF,KAAK,CAAC,4BAA4B,CAAC,EAAE;MAC3D8E,MAAM,CAACC,IAAI,CAAC,OAAO5M,KAAK,EAAE,EAAE,QAAQ,CAAC;IACvC,CAAC,MAAM;MACL8M,SAAS,CAACE,SAAS,CAACC,SAAS,CAACjN,KAAK,CAAC,CAACkN,IAAI,CAAC,MAAM;QAC9C/P,KAAK,CAACgG,OAAO,CAAC,wBAAwBnD,KAAK,EAAE,CAAC;MAChD,CAAC,CAAC,CAACmN,KAAK,CAAC,MAAM;QACbhQ,KAAK,CAACqP,IAAI,CAAC,UAAUxM,KAAK,EAAE,CAAC;MAC/B,CAAC,CAAC;IACJ;EACF,CAAC;EAID,MAAMoN,eAAe,GAAGA,CAAA,KAAM;IAC5B,IAAIlI,eAAe,CAAC9B,MAAM,KAAK,CAAC,EAAE;MAChCjG,KAAK,CAACuE,OAAO,CAAC,sBAAsB,CAAC;MACrC;IACF;IAEA,IAAI;MACF,MAAM2L,UAAU,GAAGnI,eAAe,CAACzC,GAAG,CAACC,MAAM,KAAK;QAChD7C,IAAI,EAAE6C,MAAM,CAAC7C,IAAI;QACjBC,KAAK,EAAE4C,MAAM,CAAC5C,KAAK;QACnBC,KAAK,EAAE2C,MAAM,CAAC3C,KAAK;QACnBC,KAAK,EAAE0C,MAAM,CAAC1C,KAAK;QACnBtB,QAAQ,EAAEgE,MAAM,CAAChE,QAAQ;QACzBuB,UAAU,EAAEyC,MAAM,CAACzC,UAAU;QAC7BxB,SAAS,EAAEiE,MAAM,CAACjE,SAAS;QAC3BE,MAAM,EAAE+D,MAAM,CAAC/D,MAAM,CAAC4L,IAAI,CAAC,IAAI,CAAC;QAChCrK,OAAO,EAAEwC,MAAM,CAACxC,OAAO;QACvBoN,UAAU,EAAE5K,MAAM,CAACvC;MACrB,CAAC,CAAC,CAAC;MAEH,MAAMoN,UAAU,GAAG,CACjB,iFAAiF,EACjF,GAAGF,UAAU,CAAC5K,GAAG,CAAC+K,GAAG,IACnB5E,MAAM,CAACC,MAAM,CAAC2E,GAAG,CAAC,CAAC/K,GAAG,CAAC+F,KAAK,IAC1B,IAAIiF,MAAM,CAACjF,KAAK,CAAC,CAACyD,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,GACvC,CAAC,CAAC1B,IAAI,CAAC,GAAG,CACZ,CAAC,CACF,CAACA,IAAI,CAAC,IAAI,CAAC;MAEZ,MAAMiB,IAAI,GAAG,IAAIC,IAAI,CAAC,CAAC8B,UAAU,CAAC,EAAE;QAAE7B,IAAI,EAAE;MAAW,CAAC,CAAC;MACzD,MAAM/J,GAAG,GAAGC,GAAG,CAAC+J,eAAe,CAACH,IAAI,CAAC;MACrC,MAAMI,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;MACxCF,IAAI,CAACG,IAAI,GAAGpK,GAAG;MACfiK,IAAI,CAACI,QAAQ,GAAG,yBAAyB,IAAIV,IAAI,CAAC,CAAC,CAACoC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM;MACrF9B,QAAQ,CAACK,IAAI,CAACC,WAAW,CAACP,IAAI,CAAC;MAC/BA,IAAI,CAACQ,KAAK,CAAC,CAAC;MACZP,QAAQ,CAACK,IAAI,CAACG,WAAW,CAACT,IAAI,CAAC;MAC/BhK,GAAG,CAAC0K,eAAe,CAAC3K,GAAG,CAAC;MAExBxE,KAAK,CAACgG,OAAO,CAAC,YAAY+B,eAAe,CAAC9B,MAAM,iBAAiB,CAAC;IACpE,CAAC,CAAC,OAAOO,KAAK,EAAE;MACdL,OAAO,CAACK,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;MACrCxG,KAAK,CAACwG,KAAK,CAAC,6CAA6C,CAAC;IAC5D;EACF,CAAC;EAED,oBACErG,OAAA;IAAKsQ,SAAS,EAAC,aAAa;IAAAC,QAAA,gBAC1BvQ,OAAA;MAAKsQ,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1BvQ,OAAA;QAAAuQ,QAAA,EAAI;MAAc;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACvB3Q,OAAA;QAAAuQ,QAAA,EAAG;MAAkE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtE,CAAC,eAGN3Q,OAAA;MAAKsQ,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAC7BvQ,OAAA;QAAM4Q,QAAQ,EAAE5M,YAAa;QAACsM,SAAS,EAAC,aAAa;QAAAC,QAAA,eACnDvQ,OAAA;UAAKsQ,SAAS,EAAC,oBAAoB;UAAAC,QAAA,gBACjCvQ,OAAA;YAAKsQ,SAAS,EAAC,sBAAsB;YAAAC,QAAA,gBACnCvQ,OAAA,CAACjB,QAAQ;cAACuR,SAAS,EAAC;YAAa;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACpC3Q,OAAA;cACEoO,IAAI,EAAC,MAAM;cACXyC,WAAW,EAAC,oGAAoG;cAChH3F,KAAK,EAAE7K,WAAY;cACnByQ,QAAQ,EAAG7M,CAAC,IAAK3D,cAAc,CAAC2D,CAAC,CAAC8M,MAAM,CAAC7F,KAAK,CAAE;cAChDoF,SAAS,EAAC,cAAc;cACxBU,QAAQ,EAAEzQ;YAAY;cAAAiQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACN3Q,OAAA;YAAKsQ,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7BvQ,OAAA;cACEoO,IAAI,EAAC,QAAQ;cACbkC,SAAS,EAAC,iBAAiB;cAC3BU,QAAQ,EAAEzQ,WAAY;cAAAgQ,QAAA,EAErBhQ,WAAW,gBACVP,OAAA,CAAAE,SAAA;gBAAAqQ,QAAA,gBACEvQ,OAAA;kBAAKsQ,SAAS,EAAC;gBAAiB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gBAErC;cAAA,eAAE,CAAC,gBAEH3Q,OAAA,CAAAE,SAAA;gBAAAqQ,QAAA,gBACEvQ,OAAA,CAACjB,QAAQ;kBAACkS,IAAI,EAAE;gBAAG;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,UAExB;cAAA,eAAE;YACH;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK,CAAC,eACT3Q,OAAA;cACEoO,IAAI,EAAC,QAAQ;cACbkC,SAAS,EAAC,mBAAmB;cAC7BY,OAAO,EAAE3K,aAAc;cACvByK,QAAQ,EAAEzQ,WAAY;cAAAgQ,QAAA,EACvB;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,EAGNlQ,aAAa,CAACqF,MAAM,GAAG,CAAC,iBACvB9F,OAAA;QAAKsQ,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7BvQ,OAAA;UAAKsQ,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5BvQ,OAAA;YAAKsQ,SAAS,EAAC,sBAAsB;YAAAC,QAAA,gBACnCvQ,OAAA,CAAChB,QAAQ;cAACsR,SAAS,EAAC;YAAa;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACpC3Q,OAAA;cACEoO,IAAI,EAAC,MAAM;cACXyC,WAAW,EAAC,6CAA6C;cACzD3F,KAAK,EAAEvK,WAAY;cACnBmQ,QAAQ,EAAG7M,CAAC,IAAKrD,cAAc,CAACqD,CAAC,CAAC8M,MAAM,CAAC7F,KAAK,CAAE;cAChDoF,SAAS,EAAC;YAAc;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACN3Q,OAAA;YAAKsQ,SAAS,EAAC,iBAAiB;YAAAC,QAAA,gBAC9BvQ,OAAA;cACEoO,IAAI,EAAC,QAAQ;cACbkC,SAAS,EAAE,mCAAmCzP,WAAW,GAAG,QAAQ,GAAG,EAAE,EAAG;cAC5EqQ,OAAO,EAAEA,CAAA,KAAMpQ,cAAc,CAAC,CAACD,WAAW,CAAE;cAAA0P,QAAA,gBAE5CvQ,OAAA,CAACN,UAAU;gBAACuR,IAAI,EAAE;cAAG;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,oBAExB,EAAC9P,WAAW,gBAAGb,OAAA,CAACJ,WAAW;gBAACqR,IAAI,EAAE;cAAG;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAAG3Q,OAAA,CAACL,aAAa;gBAACsR,IAAI,EAAE;cAAG;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChE,CAAC,EACRtF,gBAAgB,CAAC,CAAC,iBACjBrL,OAAA;cACEoO,IAAI,EAAC,QAAQ;cACbkC,SAAS,EAAC,+BAA+B;cACzCY,OAAO,EAAE9F,eAAgB;cAAAmF,QAAA,gBAEzBvQ,OAAA,CAACP,GAAG;gBAACwR,IAAI,EAAE;cAAG;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,aAEnB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CACT;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAGL9P,WAAW,iBACVb,OAAA;UAAKsQ,SAAS,EAAC,kBAAkB;UAAAC,QAAA,eAC/BvQ,OAAA;YAAKsQ,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BvQ,OAAA;cAAKsQ,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3BvQ,OAAA;gBAAAuQ,QAAA,EAAO;cAAkB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACjC3Q,OAAA;gBAAKsQ,SAAS,EAAC,cAAc;gBAAAC,QAAA,gBAC3BvQ,OAAA;kBACEoO,IAAI,EAAC,QAAQ;kBACbyC,WAAW,EAAC,KAAK;kBACjB3F,KAAK,EAAEnK,eAAe,CAACE,aAAc;kBACrC6P,QAAQ,EAAG7M,CAAC,IAAK+G,0BAA0B,CAAC,eAAe,EAAE/G,CAAC,CAAC8M,MAAM,CAAC7F,KAAK,CAAE;kBAC7EoF,SAAS,EAAC,oBAAoB;kBAC9Ba,GAAG,EAAC;gBAAG;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR,CAAC,eACF3Q,OAAA;kBAAAuQ,QAAA,EAAM;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACf3Q,OAAA;kBACEoO,IAAI,EAAC,QAAQ;kBACbyC,WAAW,EAAC,KAAK;kBACjB3F,KAAK,EAAEnK,eAAe,CAACG,aAAc;kBACrC4P,QAAQ,EAAG7M,CAAC,IAAK+G,0BAA0B,CAAC,eAAe,EAAE/G,CAAC,CAAC8M,MAAM,CAAC7F,KAAK,CAAE;kBAC7EoF,SAAS,EAAC,oBAAoB;kBAC9Ba,GAAG,EAAC;gBAAG;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN3Q,OAAA;cAAKsQ,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3BvQ,OAAA;gBAAAuQ,QAAA,EAAO;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC9B3Q,OAAA;gBACEoO,IAAI,EAAC,MAAM;gBACXyC,WAAW,EAAC,6BAA6B;gBACzC3F,KAAK,EAAEnK,eAAe,CAACI,SAAU;gBACjC2P,QAAQ,EAAG7M,CAAC,IAAK+G,0BAA0B,CAAC,WAAW,EAAE/G,CAAC,CAAC8M,MAAM,CAAC7F,KAAK,CAAE;gBACzEoF,SAAS,EAAC;cAAc;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAEN3Q,OAAA;cAAKsQ,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3BvQ,OAAA;gBAAAuQ,QAAA,EAAO;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACvB3Q,OAAA;gBACEoO,IAAI,EAAC,MAAM;gBACXyC,WAAW,EAAC,gCAAgC;gBAC5C3F,KAAK,EAAEnK,eAAe,CAACK,QAAS;gBAChC0P,QAAQ,EAAG7M,CAAC,IAAK+G,0BAA0B,CAAC,UAAU,EAAE/G,CAAC,CAAC8M,MAAM,CAAC7F,KAAK,CAAE;gBACxEoF,SAAS,EAAC;cAAc;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAEN3Q,OAAA;cAAKsQ,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3BvQ,OAAA;gBAAAuQ,QAAA,EAAO;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACrB3Q,OAAA;gBACEoO,IAAI,EAAC,MAAM;gBACXyC,WAAW,EAAC,2BAA2B;gBACvC3F,KAAK,EAAEnK,eAAe,CAACM,MAAO;gBAC9ByP,QAAQ,EAAG7M,CAAC,IAAK+G,0BAA0B,CAAC,QAAQ,EAAE/G,CAAC,CAAC8M,MAAM,CAAC7F,KAAK,CAAE;gBACtEoF,SAAS,EAAC;cAAc;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAEN3Q,OAAA;cAAKsQ,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3BvQ,OAAA;gBAAAuQ,QAAA,EAAO;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC1B3Q,OAAA;gBACEoO,IAAI,EAAC,MAAM;gBACXyC,WAAW,EAAC,4BAA4B;gBACxC3F,KAAK,EAAEnK,eAAe,CAACQ,WAAY;gBACnCuP,QAAQ,EAAG7M,CAAC,IAAK+G,0BAA0B,CAAC,aAAa,EAAE/G,CAAC,CAAC8M,MAAM,CAAC7F,KAAK,CAAE;gBAC3EoF,SAAS,EAAC;cAAc;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAEN3Q,OAAA;cAAKsQ,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3BvQ,OAAA;gBAAAuQ,QAAA,EAAO;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC9B3Q,OAAA;gBACEoO,IAAI,EAAC,MAAM;gBACXyC,WAAW,EAAC,kBAAkB;gBAC9B3F,KAAK,EAAEnK,eAAe,CAACS,cAAe;gBACtCsP,QAAQ,EAAG7M,CAAC,IAAK+G,0BAA0B,CAAC,gBAAgB,EAAE/G,CAAC,CAAC8M,MAAM,CAAC7F,KAAK,CAAE;gBAC9EoF,SAAS,EAAC;cAAc;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAEN3Q,OAAA;cAAKsQ,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3BvQ,OAAA;gBAAAuQ,QAAA,EAAO;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACtB3Q,OAAA;gBACEoO,IAAI,EAAC,MAAM;gBACXyC,WAAW,EAAC,8BAA8B;gBAC1C3F,KAAK,EAAEnK,eAAe,CAACU,OAAQ;gBAC/BqP,QAAQ,EAAG7M,CAAC,IAAK+G,0BAA0B,CAAC,SAAS,EAAE/G,CAAC,CAAC8M,MAAM,CAAC7F,KAAK,CAAE;gBACvEoF,SAAS,EAAC;cAAc;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAEN3Q,OAAA;cAAKsQ,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3BvQ,OAAA;gBAAAuQ,QAAA,EAAO;cAAiB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAChC3Q,OAAA;gBACEoO,IAAI,EAAC,QAAQ;gBACbyC,WAAW,EAAC,cAAc;gBAC1B3F,KAAK,EAAEnK,eAAe,CAACO,QAAS;gBAChCwP,QAAQ,EAAG7M,CAAC,IAAK+G,0BAA0B,CAAC,UAAU,EAAE/G,CAAC,CAAC8M,MAAM,CAAC7F,KAAK,CAAE;gBACxEoF,SAAS,EAAC,cAAc;gBACxBa,GAAG,EAAC,GAAG;gBACPC,GAAG,EAAC;cAAK;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,eAED3Q,OAAA;UAAKsQ,SAAS,EAAC,eAAe;UAAAC,QAAA,GAC3B3I,eAAe,CAAC9B,MAAM,EAAC,MAAI,EAACrF,aAAa,CAACqF,MAAM,EAAC,UAClD,EAACuF,gBAAgB,CAAC,CAAC,iBAAIrL,OAAA;YAAMsQ,SAAS,EAAC,oBAAoB;YAAAC,QAAA,EAAC;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3E,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAGLlQ,aAAa,CAACqF,MAAM,GAAG,CAAC,gBACvB9F,OAAA;MAAKsQ,SAAS,EAAC,iBAAiB;MAAAC,QAAA,gBAC9BvQ,OAAA;QAAKsQ,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7BvQ,OAAA;UAAAuQ,QAAA,EAAI;QAAc;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACvB3Q,OAAA;UAAKsQ,SAAS,EAAC,iBAAiB;UAAAC,QAAA,eAC9BvQ,OAAA;YACEsQ,SAAS,EAAC,mBAAmB;YAC7BY,OAAO,EAAEpB,eAAgB;YACzBtN,KAAK,EAAE,UAAUoF,eAAe,CAAC9B,MAAM,iBAAkB;YAAAyK,QAAA,gBAEzDvQ,OAAA,CAACf,UAAU;cAACgS,IAAI,EAAE;YAAG;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBACZ,EAAC/I,eAAe,CAAC9B,MAAM,EAAC,GACtC;UAAA;YAAA0K,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN3Q,OAAA;QAAKsQ,SAAS,EAAC,cAAc;QAAAC,QAAA,EAC1B3I,eAAe,CAACzC,GAAG,CAAEC,MAAM,iBAC1BpF,OAAA;UAAqBsQ,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1CvQ,OAAA;YAAKsQ,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5BvQ,OAAA;cAAKsQ,SAAS,EAAC,eAAe;cAAAC,QAAA,eAC5BvQ,OAAA,CAACb,MAAM;gBAAC8R,IAAI,EAAE;cAAG;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjB,CAAC,eACN3Q,OAAA;cAAKsQ,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BvQ,OAAA;gBAAIsQ,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAEnL,MAAM,CAAC7C;cAAI;gBAAAiO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC9C3Q,OAAA;gBAAGsQ,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAEnL,MAAM,CAAC5C;cAAK;gBAAAgO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN3Q,OAAA;YAAKsQ,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7BvQ,OAAA;cAAKsQ,SAAS,EAAC,2BAA2B;cAAAC,QAAA,gBACxCvQ,OAAA,CAACZ,QAAQ;gBAAC6R,IAAI,EAAE;cAAG;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACtB3Q,OAAA;gBAAAuQ,QAAA,EAAOnL,MAAM,CAAChE;cAAQ;gBAAAoP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3B,CAAC,eACN3Q,OAAA;cACEsQ,SAAS,EAAC,kCAAkC;cAC5CY,OAAO,EAAEA,CAAA,KAAM9B,gBAAgB,CAAChK,MAAM,CAAC3C,KAAK,EAAE2C,MAAM,CAAC7C,IAAI,CAAE;cAC3DC,KAAK,EAAE,iBAAiB4C,MAAM,CAAC7C,IAAI,EAAG;cAAAgO,QAAA,gBAEtCvQ,OAAA,CAACX,MAAM;gBAAC4R,IAAI,EAAE;cAAG;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACpB3Q,OAAA;gBAAAuQ,QAAA,EAAOnL,MAAM,CAAC3C;cAAK;gBAAA+N,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC3B3Q,OAAA,CAACT,cAAc;gBAAC0R,IAAI,EAAE,EAAG;gBAACX,SAAS,EAAC;cAAe;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnD,CAAC,eACN3Q,OAAA;cACEsQ,SAAS,EAAC,kCAAkC;cAC5CY,OAAO,EAAEA,CAAA,KAAM3B,gBAAgB,CAACnK,MAAM,CAAC1C,KAAK,EAAE0C,MAAM,CAAC7C,IAAI,CAAE;cAC3DC,KAAK,EAAE,QAAQ4C,MAAM,CAAC7C,IAAI,EAAG;cAAAgO,QAAA,gBAE7BvQ,OAAA,CAACV,OAAO;gBAAC2R,IAAI,EAAE;cAAG;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACrB3Q,OAAA;gBAAAuQ,QAAA,EAAOnL,MAAM,CAAC1C;cAAK;gBAAA8N,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC3B3Q,OAAA,CAACT,cAAc;gBAAC0R,IAAI,EAAE,EAAG;gBAACX,SAAS,EAAC;cAAe;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN3Q,OAAA;YAAKsQ,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7BvQ,OAAA;cAAKsQ,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BvQ,OAAA;gBAAKsQ,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,gBAC9BvQ,OAAA;kBAAAuQ,QAAA,EAAI;gBAAU;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACnB3Q,OAAA;kBAAAuQ,QAAA,EAAInL,MAAM,CAACzC;gBAAU;kBAAA6N,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvB,CAAC,eACN3Q,OAAA;gBAAKsQ,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,gBAC9BvQ,OAAA;kBAAAuQ,QAAA,EAAI;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAClB3Q,OAAA;kBAAAuQ,QAAA,EAAInL,MAAM,CAACjE;gBAAS;kBAAAqP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACN3Q,OAAA;cAAKsQ,SAAS,EAAC,iBAAiB;cAAAC,QAAA,gBAC9BvQ,OAAA;gBAAAuQ,QAAA,EAAI;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACf3Q,OAAA;gBAAKsQ,SAAS,EAAC,aAAa;gBAAAC,QAAA,EACzBnL,MAAM,CAAC/D,MAAM,CAAC8D,GAAG,CAAC,CAACQ,KAAK,EAAE0L,KAAK,kBAC9BrR,OAAA;kBAAkBsQ,SAAS,EAAC,WAAW;kBAAAC,QAAA,EAAE5K;gBAAK,GAAnC0L,KAAK;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAqC,CACtD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN3Q,OAAA;YAAKsQ,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7BvQ,OAAA;cACEsQ,SAAS,EAAC,mBAAmB;cAC7BY,OAAO,EAAEA,CAAA,KAAMjC,UAAU,CAAC7J,MAAM,CAAE;cAAAmL,QAAA,gBAElCvQ,OAAA,CAACd,KAAK;gBAAC+R,IAAI,EAAE;cAAG;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAErB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACT3Q,OAAA;cACEsQ,SAAS,EAAC,iBAAiB;cAC3BY,OAAO,EAAEA,CAAA,KAAM1F,cAAc,CAACpG,MAAM,CAAE;cAAAmL,QAAA,gBAEtCvQ,OAAA,CAACf,UAAU;gBAACgS,IAAI,EAAE;cAAG;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,YAE1B;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA,GAxEEvL,MAAM,CAAC9C,EAAE;UAAAkO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAyEd,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,EAGL3O,UAAU,GAAG,CAAC,iBACbhC,OAAA;QAAKsQ,SAAS,EAAC,oBAAoB;QAAAC,QAAA,gBACjCvQ,OAAA;UAAKsQ,SAAS,EAAC,iBAAiB;UAAAC,QAAA,GAAC,UACvB,EAAE,CAACzO,WAAW,GAAG,CAAC,IAAIM,cAAc,GAAI,CAAC,EAAC,MAAI,EAAC+E,IAAI,CAACgK,GAAG,CAACrP,WAAW,GAAGM,cAAc,EAAEF,UAAU,CAAC,EAAC,MAAI,EAACA,UAAU,EAAC,UAC5H;QAAA;UAAAsO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACN3Q,OAAA;UAAKsQ,SAAS,EAAC,qBAAqB;UAAAC,QAAA,gBAClCvQ,OAAA;YACEsQ,SAAS,EAAC,kCAAkC;YAC5CY,OAAO,EAAEA,CAAA,KAAMvJ,gBAAgB,CAAC7F,WAAW,GAAG,CAAC,CAAE;YACjDkP,QAAQ,EAAElP,WAAW,KAAK,CAAE;YAAAyO,QAAA,EAC7B;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAET3Q,OAAA;YAAKsQ,SAAS,EAAC,cAAc;YAAAC,QAAA,EAC1BtL,KAAK,CAACqM,IAAI,CAAC;cAAExL,MAAM,EAAEqB,IAAI,CAACgK,GAAG,CAAC,CAAC,EAAEnP,UAAU;YAAE,CAAC,EAAE,CAACuP,CAAC,EAAEC,CAAC,KAAK;cACzD,IAAIC,OAAO;cACX,IAAIzP,UAAU,IAAI,CAAC,EAAE;gBACnByP,OAAO,GAAGD,CAAC,GAAG,CAAC;cACjB,CAAC,MAAM,IAAI1P,WAAW,IAAI,CAAC,EAAE;gBAC3B2P,OAAO,GAAGD,CAAC,GAAG,CAAC;cACjB,CAAC,MAAM,IAAI1P,WAAW,IAAIE,UAAU,GAAG,CAAC,EAAE;gBACxCyP,OAAO,GAAGzP,UAAU,GAAG,CAAC,GAAGwP,CAAC;cAC9B,CAAC,MAAM;gBACLC,OAAO,GAAG3P,WAAW,GAAG,CAAC,GAAG0P,CAAC;cAC/B;cAEA,oBACExR,OAAA;gBAEEsQ,SAAS,EAAE,sBAAsBxO,WAAW,KAAK2P,OAAO,GAAG,aAAa,GAAG,eAAe,EAAG;gBAC7FP,OAAO,EAAEA,CAAA,KAAMvJ,gBAAgB,CAAC8J,OAAO,CAAE;gBAAAlB,QAAA,EAExCkB;cAAO,GAJHA,OAAO;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAKN,CAAC;YAEb,CAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAEN3Q,OAAA;YACEsQ,SAAS,EAAC,kCAAkC;YAC5CY,OAAO,EAAEA,CAAA,KAAMvJ,gBAAgB,CAAC7F,WAAW,GAAG,CAAC,CAAE;YACjDkP,QAAQ,EAAElP,WAAW,KAAKE,UAAW;YAAAuO,QAAA,EACtC;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,GACJ,CAACpQ,WAAW,gBACdP,OAAA;MAAKsQ,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1BvQ,OAAA;QAAKsQ,SAAS,EAAC,YAAY;QAAAC,QAAA,eACzBvQ,OAAA,CAACjB,QAAQ;UAACkS,IAAI,EAAE;QAAG;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnB,CAAC,eACN3Q,OAAA;QAAAuQ,QAAA,EAAI;MAAgB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACzB3Q,OAAA;QAAAuQ,QAAA,EAAG;MAAyH;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eAChI3Q,OAAA;QAAKsQ,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9BvQ,OAAA;UAAAuQ,QAAA,EAAI;QAAkB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC3B3Q,OAAA;UAAAuQ,QAAA,gBACEvQ,OAAA;YAAAuQ,QAAA,EAAI;UAAwB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACjC3Q,OAAA;YAAAuQ,QAAA,EAAI;UAAwC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACjD3Q,OAAA;YAAAuQ,QAAA,EAAI;UAA8B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,GACJ,IAAI,eAGR3Q,OAAA,CAACF,WAAW;MACVsF,MAAM,EAAE1D,cAAe;MACvBgQ,MAAM,EAAE9P,WAAY;MACpB+P,OAAO,EAAExC;IAAiB;MAAAqB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3B,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAACvQ,EAAA,CAv+BID,aAAa;AAAAyR,EAAA,GAAbzR,aAAa;AAy+BnB,eAAeA,aAAa;AAAC,IAAAyR,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}