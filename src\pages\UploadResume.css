.upload-page {
  max-width: 800px;
  margin: 0 auto;
  animation: fadeIn 0.5s ease-in;
}

.page-header {
  margin-bottom: 32px;
  text-align: center;
}

.page-header h1 {
  font-size: 32px;
  font-weight: 700;
  color: #1f2937;
  margin: 0 0 12px 0;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.page-header p {
  font-size: 16px;
  color: #6b7280;
  margin: 0;
  line-height: 1.6;
  max-width: 600px;
  margin: 0 auto;
}

.upload-container {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

/* Upload Section */
.upload-section {
  background: white;
  border-radius: 16px;
  border: 1px solid #e2e8f0;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.dropzone {
  padding: 48px 32px;
  border: 2px dashed #d1d5db;
  border-radius: 16px;
  background: #f9fafb;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
}

.dropzone:hover,
.dropzone.active {
  border-color: #3b82f6;
  background: #eff6ff;
}

.dropzone-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.upload-icon {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  margin-bottom: 8px;
}

.dropzone-content h3 {
  font-size: 20px;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.dropzone-content p {
  font-size: 16px;
  color: #6b7280;
  margin: 0;
}

.upload-specs {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 14px;
  color: #9ca3af;
  margin-top: 8px;
}

.upload-actions {
  padding: 24px 32px;
  border-top: 1px solid #e2e8f0;
  background: #f8fafc;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* File List Section */
.file-list-section {
  background: white;
  border-radius: 16px;
  border: 1px solid #e2e8f0;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.section-header {
  padding: 24px 32px;
  border-bottom: 1px solid #e2e8f0;
  background: #f8fafc;
}

.section-header h3 {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.file-list {
  padding: 0;
}

.file-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 32px;
  border-bottom: 1px solid #f1f5f9;
  transition: all 0.2s ease;
}

.file-item:last-child {
  border-bottom: none;
}

.file-item:hover {
  background: #f8fafc;
}

.file-item.success {
  background: #f0fdf4;
  border-color: #bbf7d0;
}

.file-item.error {
  background: #fef2f2;
  border-color: #fecaca;
}

.file-info {
  display: flex;
  align-items: center;
  gap: 16px;
  flex: 1;
}

.file-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background: #f1f5f9;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.status-icon {
  width: 20px;
  height: 20px;
}

.status-icon.pending {
  color: #6b7280;
}

.status-icon.success {
  color: #10b981;
}

.status-icon.error {
  color: #ef4444;
}

.file-details {
  flex: 1;
  min-width: 0;
}

.file-name {
  font-size: 14px;
  font-weight: 500;
  color: #1f2937;
  margin-bottom: 4px;
  word-break: break-word;
}

.file-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: #6b7280;
}

.error-text {
  color: #ef4444;
}

.file-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.progress-container {
  display: flex;
  align-items: center;
  gap: 12px;
  min-width: 120px;
}

.progress-bar {
  flex: 1;
  height: 6px;
  background: #e5e7eb;
  border-radius: 3px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #3b82f6 0%, #1d4ed8 100%);
  border-radius: 3px;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 12px;
  font-weight: 500;
  color: #6b7280;
  min-width: 35px;
  text-align: right;
}

.remove-btn {
  background: none;
  border: none;
  color: #6b7280;
  cursor: pointer;
  padding: 6px;
  border-radius: 4px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.remove-btn:hover {
  background: #f3f4f6;
  color: #ef4444;
}

/* Loading Spinner */
.loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid #e5e7eb;
  border-top: 2px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
  .upload-page {
    max-width: 100%;
  }
  
  .page-header h1 {
    font-size: 28px;
  }
  
  .page-header p {
    font-size: 14px;
  }
  
  .dropzone {
    padding: 32px 24px;
  }
  
  .upload-icon {
    width: 60px;
    height: 60px;
  }
  
  .dropzone-content h3 {
    font-size: 18px;
  }
  
  .dropzone-content p {
    font-size: 14px;
  }
  
  .upload-actions {
    padding: 20px 24px;
    flex-direction: column;
  }
  
  .section-header {
    padding: 20px 24px;
  }
  
  .file-item {
    padding: 16px 24px;
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .file-info {
    width: 100%;
  }
  
  .file-actions {
    width: 100%;
    justify-content: flex-end;
  }
  
  .progress-container {
    min-width: 100px;
  }
}

@media (max-width: 480px) {
  .dropzone {
    padding: 24px 16px;
  }
  
  .upload-specs {
    flex-direction: column;
    gap: 4px;
  }
  
  .upload-actions {
    padding: 16px 20px;
  }
  
  .section-header {
    padding: 16px 20px;
  }
  
  .file-item {
    padding: 12px 20px;
  }
}
