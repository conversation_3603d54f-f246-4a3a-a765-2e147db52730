{"ast": null, "code": "var _jsxFileName = \"D:\\\\Projects\\\\SQL-Agent-Task-Submission-main\\\\SQL-Agent-Task-Submission-main\\\\src\\\\pages\\\\SearchResumes.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { FiSearch, FiFilter, FiDownload, FiEye, FiUser, FiMapPin, FiMail, FiPhone, FiExternalLink, FiStar, FiX, FiSettings, FiChevronDown, FiChevronUp } from 'react-icons/fi';\nimport { toast } from 'react-toastify';\nimport ResumeModal from '../components/ResumeModal';\nimport './SearchResumes.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst SearchResumes = () => {\n  _s();\n  const [searchQuery, setSearchQuery] = useState('');\n  const [isSearching, setIsSearching] = useState(false);\n  const [searchResults, setSearchResults] = useState([]);\n  const [filterQuery, setFilterQuery] = useState('');\n  const [showFilters, setShowFilters] = useState(false);\n  const [advancedFilters, setAdvancedFilters] = useState({\n    minExperience: '',\n    maxExperience: '',\n    education: '',\n    location: '',\n    skills: '',\n    minMarks: '',\n    institution: '',\n    graduationYear: '',\n    company: ''\n  });\n  const [selectedResume, setSelectedResume] = useState(null);\n  const [isModalOpen, setIsModalOpen] = useState(false);\n  const [currentPage, setCurrentPage] = useState(1);\n  const [totalPages, setTotalPages] = useState(1);\n  const [totalCount, setTotalCount] = useState(0);\n  const resultsPerPage = 20;\n\n  // Mock data for demonstration\n  const mockResults = [{\n    id: 1,\n    name: 'John Doe',\n    title: 'Senior Software Engineer',\n    email: '<EMAIL>',\n    phone: '+****************',\n    location: 'San Francisco, CA',\n    experience: '5+ years',\n    skills: ['Python', 'React', 'Node.js', 'AWS'],\n    education: 'MS Computer Science',\n    summary: 'Experienced software engineer with expertise in full-stack development...',\n    score: 95,\n    rawData: {\n      Resume: {\n        PersonalInformation: {\n          FullName: 'John Doe',\n          Email: '<EMAIL>',\n          ContactNumber: '+****************',\n          Address: 'San Francisco, CA'\n        },\n        WorkExperience: [{\n          Role: 'Senior Software Engineer',\n          CompanyName: 'Tech Corp',\n          StartYear: '2019',\n          EndYear: 'Present',\n          'Description/Responsibility': 'Led development of scalable web applications using React and Node.js'\n        }, {\n          Role: 'Software Engineer',\n          CompanyName: 'StartupXYZ',\n          StartYear: '2017',\n          EndYear: '2019',\n          'Description/Responsibility': 'Developed full-stack applications and improved system performance'\n        }],\n        Education: [{\n          Degree: 'MS Computer Science',\n          Institution: 'Stanford University',\n          GraduationYear: '2017',\n          'GPA/Marks/%': 85\n        }],\n        Skills: ['Python', 'React', 'Node.js', 'AWS'],\n        TotalWorkExperienceInYears: 5\n      }\n    }\n  }, {\n    id: 2,\n    name: 'Jane Smith',\n    title: 'Frontend Developer',\n    email: '<EMAIL>',\n    phone: '+****************',\n    location: 'New York, NY',\n    experience: '3+ years',\n    skills: ['React', 'TypeScript', 'CSS', 'JavaScript'],\n    education: 'BS Computer Science',\n    summary: 'Creative frontend developer passionate about user experience...',\n    score: 88,\n    rawData: {\n      Resume: {\n        PersonalInformation: {\n          FullName: 'Jane Smith',\n          Email: '<EMAIL>',\n          ContactNumber: '+****************',\n          Address: 'New York, NY'\n        },\n        WorkExperience: [{\n          Role: 'Frontend Developer',\n          CompanyName: 'Design Studio',\n          StartYear: '2021',\n          EndYear: 'Present',\n          'Description/Responsibility': 'Created responsive web interfaces using React and modern CSS'\n        }],\n        Education: [{\n          Degree: 'BS Computer Science',\n          Institution: 'NYU',\n          GraduationYear: '2021',\n          'GPA/Marks/%': 78\n        }],\n        Skills: ['React', 'TypeScript', 'CSS', 'JavaScript'],\n        TotalWorkExperienceInYears: 3\n      }\n    }\n  }, {\n    id: 3,\n    name: 'Mike Johnson',\n    title: 'DevOps Engineer',\n    email: '<EMAIL>',\n    phone: '+****************',\n    location: 'Austin, TX',\n    experience: '4+ years',\n    skills: ['Docker', 'Kubernetes', 'AWS', 'Python'],\n    education: 'BS Information Technology',\n    summary: 'DevOps engineer specializing in cloud infrastructure and automation...',\n    score: 92,\n    rawData: {\n      Resume: {\n        PersonalInformation: {\n          FullName: 'Mike Johnson',\n          Email: '<EMAIL>',\n          ContactNumber: '+****************',\n          Address: 'Austin, TX'\n        },\n        WorkExperience: [{\n          Role: 'DevOps Engineer',\n          CompanyName: 'Cloud Solutions Inc',\n          StartYear: '2020',\n          EndYear: 'Present',\n          'Description/Responsibility': 'Managed cloud infrastructure and automated deployment pipelines'\n        }],\n        Education: [{\n          Degree: 'BS Information Technology',\n          Institution: 'UT Austin',\n          GraduationYear: '2020',\n          'GPA/Marks/%': 82\n        }],\n        Skills: ['Docker', 'Kubernetes', 'AWS', 'Python'],\n        TotalWorkExperienceInYears: 4\n      }\n    }\n  }];\n  const handleSearch = async e => {\n    e.preventDefault();\n    if (!searchQuery.trim()) {\n      toast.warning('Please enter a search query');\n      return;\n    }\n    setIsSearching(true);\n    try {\n      // Call the FastAPI backend directly for GPT-powered search\n      const response = await fetch(`http://localhost:8001/query/?naturalQuery=${encodeURIComponent(searchQuery)}`, {\n        method: 'GET',\n        headers: {\n          'Content-Type': 'application/json'\n        }\n      });\n      const data = await response.json();\n      if (response.ok) {\n        // Handle FastAPI response format: {query: [], listOfDict: [], result: \"\"}\n        let transformedResults = [];\n        if (data.listOfDict && Array.isArray(data.listOfDict)) {\n          // Transform the resume data from MongoDB to display format\n          transformedResults = data.listOfDict.map(resume => {\n            var _resumeData$PersonalI, _resumeData$PersonalI2, _resumeData$PersonalI3, _resumeData$PersonalI4, _resumeData$PersonalI5, _resumeData$WorkExper, _resumeData$Education, _resumeData$Education2;\n            // Handle the nested Resume structure from MongoDB\n            const resumeData = resume.Resume || resume;\n            return {\n              id: resume._id || Math.random().toString(36).substr(2, 9),\n              name: ((_resumeData$PersonalI = resumeData.PersonalInformation) === null || _resumeData$PersonalI === void 0 ? void 0 : _resumeData$PersonalI.FullName) || resumeData.name || 'Unknown',\n              title: ((_resumeData$PersonalI2 = resumeData.PersonalInformation) === null || _resumeData$PersonalI2 === void 0 ? void 0 : _resumeData$PersonalI2.Designation) || resumeData.title || 'No title',\n              email: ((_resumeData$PersonalI3 = resumeData.PersonalInformation) === null || _resumeData$PersonalI3 === void 0 ? void 0 : _resumeData$PersonalI3.Email) || resumeData.email || 'No email',\n              phone: ((_resumeData$PersonalI4 = resumeData.PersonalInformation) === null || _resumeData$PersonalI4 === void 0 ? void 0 : _resumeData$PersonalI4.ContactNumber) || resumeData.phone || 'No phone',\n              location: ((_resumeData$PersonalI5 = resumeData.PersonalInformation) === null || _resumeData$PersonalI5 === void 0 ? void 0 : _resumeData$PersonalI5.Address) || resumeData.location || 'No location',\n              experience: resumeData.TotalWorkExperienceInYears ? `${resumeData.TotalWorkExperienceInYears} years` : (_resumeData$WorkExper = resumeData.WorkExperience) !== null && _resumeData$WorkExper !== void 0 && _resumeData$WorkExper.length ? `${resumeData.WorkExperience.length}+ positions` : resumeData.experience || 'No experience data',\n              skills: resumeData.Skills || resumeData.skills || [],\n              education: (_resumeData$Education = resumeData.Education) !== null && _resumeData$Education !== void 0 && _resumeData$Education.length ? ((_resumeData$Education2 = resumeData.Education[0]) === null || _resumeData$Education2 === void 0 ? void 0 : _resumeData$Education2.Degree) || 'Education available' : resumeData.education || 'No education data',\n              summary: resumeData.Objective || resumeData.Summary || resumeData.summary || 'No summary available',\n              score: 95,\n              // Default score\n              rawData: resume // Keep original data for reference\n            };\n          });\n        } else {\n          // Fallback to mock data if API doesn't return results\n          transformedResults = mockResults.filter(resume => resume.name.toLowerCase().includes(searchQuery.toLowerCase()) || resume.title.toLowerCase().includes(searchQuery.toLowerCase()) || resume.skills.some(skill => skill.toLowerCase().includes(searchQuery.toLowerCase())));\n        }\n        setSearchResults(transformedResults);\n\n        // Show success message with GPT result\n        if (data.result) {\n          toast.success(`🤖 AI Search: ${data.result}`);\n        } else {\n          toast.success(`Found ${transformedResults.length} matching resumes`);\n        }\n\n        // Log GPT response and MongoDB query for debugging\n        if (data.query) {\n          console.log('MongoDB Query Generated by GPT:', data.query);\n          console.log('GPT Result:', data.result);\n        }\n      } else {\n        throw new Error(data.error || 'Search failed');\n      }\n    } catch (error) {\n      console.error('Search error:', error);\n\n      // Fallback to mock data on network error\n      const fallbackResults = mockResults.filter(resume => resume.name.toLowerCase().includes(searchQuery.toLowerCase()) || resume.title.toLowerCase().includes(searchQuery.toLowerCase()) || resume.skills.some(skill => skill.toLowerCase().includes(searchQuery.toLowerCase())));\n      setSearchResults(fallbackResults);\n      toast.warning(`Using demo data - Found ${fallbackResults.length} matching resumes`);\n    } finally {\n      setIsSearching(false);\n    }\n  };\n  const handleShowAll = async (page = 1) => {\n    setIsSearching(true);\n    try {\n      // Call the actual API to get paginated resumes\n      const response = await fetch(`/api/resumes?page=${page}&limit=${resultsPerPage}`);\n      const data = await response.json();\n      if (response.ok) {\n        let transformedResults = [];\n        if (data.resumes && Array.isArray(data.resumes)) {\n          // Transform the resume data to match our display format\n          transformedResults = data.resumes.map(resume => {\n            var _resumeData$PersonalI6, _resumeData$PersonalI7, _resumeData$PersonalI8, _resumeData$PersonalI9, _resumeData$PersonalI0, _resumeData$WorkExper2, _resumeData$Education3, _resumeData$Education4;\n            // Handle the nested Resume structure from MongoDB\n            const resumeData = resume.Resume || resume;\n            return {\n              id: resume._id || Math.random().toString(36).substr(2, 9),\n              name: ((_resumeData$PersonalI6 = resumeData.PersonalInformation) === null || _resumeData$PersonalI6 === void 0 ? void 0 : _resumeData$PersonalI6.FullName) || resumeData.name || 'Unknown',\n              title: ((_resumeData$PersonalI7 = resumeData.PersonalInformation) === null || _resumeData$PersonalI7 === void 0 ? void 0 : _resumeData$PersonalI7.Designation) || resumeData.title || 'No title',\n              email: ((_resumeData$PersonalI8 = resumeData.PersonalInformation) === null || _resumeData$PersonalI8 === void 0 ? void 0 : _resumeData$PersonalI8.Email) || resumeData.email || 'No email',\n              phone: ((_resumeData$PersonalI9 = resumeData.PersonalInformation) === null || _resumeData$PersonalI9 === void 0 ? void 0 : _resumeData$PersonalI9.ContactNumber) || resumeData.phone || 'No phone',\n              location: ((_resumeData$PersonalI0 = resumeData.PersonalInformation) === null || _resumeData$PersonalI0 === void 0 ? void 0 : _resumeData$PersonalI0.Address) || resumeData.location || 'No location',\n              experience: resumeData.TotalWorkExperienceInYears ? `${resumeData.TotalWorkExperienceInYears} years` : (_resumeData$WorkExper2 = resumeData.WorkExperience) !== null && _resumeData$WorkExper2 !== void 0 && _resumeData$WorkExper2.length ? `${resumeData.WorkExperience.length}+ positions` : resumeData.experience || 'No experience data',\n              skills: resumeData.Skills || resumeData.skills || [],\n              education: (_resumeData$Education3 = resumeData.Education) !== null && _resumeData$Education3 !== void 0 && _resumeData$Education3.length ? ((_resumeData$Education4 = resumeData.Education[0]) === null || _resumeData$Education4 === void 0 ? void 0 : _resumeData$Education4.Degree) || 'Education available' : resumeData.education || 'No education data',\n              summary: resumeData.Objective || resumeData.Summary || resumeData.summary || 'No summary available',\n              score: 90,\n              // Default score for show all\n              rawData: resume // Keep original data for reference\n            };\n          });\n        } else {\n          // Fallback to mock data if API doesn't return proper results\n          transformedResults = mockResults;\n        }\n        setSearchResults(transformedResults);\n        setCurrentPage(page);\n        setTotalCount(data.count || data.total || transformedResults.length);\n        setTotalPages(Math.ceil((data.count || data.total || transformedResults.length) / resultsPerPage));\n        setSearchQuery('');\n        if (data.source === 'database') {\n          toast.success(`Showing page ${page} of ${Math.ceil((data.count || data.total || transformedResults.length) / resultsPerPage)} (${data.count || data.total} total resumes)`);\n        } else {\n          toast.info(`Showing ${transformedResults.length} resumes`);\n        }\n      } else {\n        throw new Error(data.error || 'Failed to load resumes');\n      }\n    } catch (error) {\n      console.error('Load resumes error:', error);\n\n      // Fallback to mock data on network error\n      setSearchResults(mockResults);\n      setCurrentPage(1);\n      setTotalCount(mockResults.length);\n      setTotalPages(Math.ceil(mockResults.length / resultsPerPage));\n      setSearchQuery('');\n      toast.warning(`Using demo data - Showing ${mockResults.length} sample resumes`);\n    } finally {\n      setIsSearching(false);\n    }\n  };\n  const handlePageChange = page => {\n    if (page >= 1 && page <= totalPages) {\n      handleShowAll(page);\n    }\n  };\n  const filteredResults = searchResults.filter(result => {\n    // Basic text filter\n    if (filterQuery) {\n      const matchesText = result.name.toLowerCase().includes(filterQuery.toLowerCase()) || result.title.toLowerCase().includes(filterQuery.toLowerCase()) || result.location.toLowerCase().includes(filterQuery.toLowerCase());\n      if (!matchesText) return false;\n    }\n\n    // Advanced filters\n    const filters = advancedFilters;\n\n    // Experience filter\n    if (filters.minExperience || filters.maxExperience) {\n      var _result$rawData, _result$rawData$Resum;\n      const experienceYears = ((_result$rawData = result.rawData) === null || _result$rawData === void 0 ? void 0 : (_result$rawData$Resum = _result$rawData.Resume) === null || _result$rawData$Resum === void 0 ? void 0 : _result$rawData$Resum.TotalWorkExperienceInYears) || extractExperienceYears(result.experience) || 0;\n      if (filters.minExperience && experienceYears < parseInt(filters.minExperience)) return false;\n      if (filters.maxExperience && experienceYears > parseInt(filters.maxExperience)) return false;\n    }\n\n    // Education filter\n    if (filters.education) {\n      var _result$rawData2, _result$rawData2$Resu, _result$rawData2$Resu2;\n      const hasEducation = ((_result$rawData2 = result.rawData) === null || _result$rawData2 === void 0 ? void 0 : (_result$rawData2$Resu = _result$rawData2.Resume) === null || _result$rawData2$Resu === void 0 ? void 0 : (_result$rawData2$Resu2 = _result$rawData2$Resu.Education) === null || _result$rawData2$Resu2 === void 0 ? void 0 : _result$rawData2$Resu2.some(edu => (edu.Degree || '').toLowerCase().includes(filters.education.toLowerCase()))) || (result.education || '').toLowerCase().includes(filters.education.toLowerCase());\n      if (!hasEducation) return false;\n    }\n\n    // Location filter\n    if (filters.location) {\n      const matchesLocation = (result.location || '').toLowerCase().includes(filters.location.toLowerCase());\n      if (!matchesLocation) return false;\n    }\n\n    // Skills filter\n    if (filters.skills) {\n      var _result$skills, _result$rawData3, _result$rawData3$Resu;\n      const skillsText = filters.skills.toLowerCase();\n      const hasSkill = ((_result$skills = result.skills) === null || _result$skills === void 0 ? void 0 : _result$skills.some(skill => skill.toLowerCase().includes(skillsText))) || (((_result$rawData3 = result.rawData) === null || _result$rawData3 === void 0 ? void 0 : (_result$rawData3$Resu = _result$rawData3.Resume) === null || _result$rawData3$Resu === void 0 ? void 0 : _result$rawData3$Resu.Skills) || []).some(skill => skill.toLowerCase().includes(skillsText));\n      if (!hasSkill) return false;\n    }\n\n    // Institution filter\n    if (filters.institution) {\n      var _result$rawData4, _result$rawData4$Resu, _result$rawData4$Resu2;\n      const hasInstitution = (_result$rawData4 = result.rawData) === null || _result$rawData4 === void 0 ? void 0 : (_result$rawData4$Resu = _result$rawData4.Resume) === null || _result$rawData4$Resu === void 0 ? void 0 : (_result$rawData4$Resu2 = _result$rawData4$Resu.Education) === null || _result$rawData4$Resu2 === void 0 ? void 0 : _result$rawData4$Resu2.some(edu => (edu.Institution || edu.School || '').toLowerCase().includes(filters.institution.toLowerCase()));\n      if (!hasInstitution) return false;\n    }\n\n    // Graduation year filter\n    if (filters.graduationYear) {\n      var _result$rawData5, _result$rawData5$Resu, _result$rawData5$Resu2;\n      const hasGradYear = (_result$rawData5 = result.rawData) === null || _result$rawData5 === void 0 ? void 0 : (_result$rawData5$Resu = _result$rawData5.Resume) === null || _result$rawData5$Resu === void 0 ? void 0 : (_result$rawData5$Resu2 = _result$rawData5$Resu.Education) === null || _result$rawData5$Resu2 === void 0 ? void 0 : _result$rawData5$Resu2.some(edu => (edu.GraduationYear || edu.Year || '').toString().includes(filters.graduationYear));\n      if (!hasGradYear) return false;\n    }\n\n    // Company filter\n    if (filters.company) {\n      var _result$rawData6, _result$rawData6$Resu, _result$rawData6$Resu2;\n      const hasCompany = (_result$rawData6 = result.rawData) === null || _result$rawData6 === void 0 ? void 0 : (_result$rawData6$Resu = _result$rawData6.Resume) === null || _result$rawData6$Resu === void 0 ? void 0 : (_result$rawData6$Resu2 = _result$rawData6$Resu.WorkExperience) === null || _result$rawData6$Resu2 === void 0 ? void 0 : _result$rawData6$Resu2.some(exp => (exp.Company || exp.CompanyName || exp.Organization || '').toLowerCase().includes(filters.company.toLowerCase()));\n      if (!hasCompany) return false;\n    }\n\n    // Minimum marks filter\n    if (filters.minMarks) {\n      var _result$rawData7, _result$rawData7$Resu, _result$rawData7$Resu2;\n      const hasMinMarks = (_result$rawData7 = result.rawData) === null || _result$rawData7 === void 0 ? void 0 : (_result$rawData7$Resu = _result$rawData7.Resume) === null || _result$rawData7$Resu === void 0 ? void 0 : (_result$rawData7$Resu2 = _result$rawData7$Resu.Education) === null || _result$rawData7$Resu2 === void 0 ? void 0 : _result$rawData7$Resu2.some(edu => {\n        const marks = parseFloat(edu['GPA/Marks/%']) || 0;\n        return marks >= parseFloat(filters.minMarks);\n      });\n      if (!hasMinMarks) return false;\n    }\n    return true;\n  });\n\n  // Helper function to extract experience years from text\n  const extractExperienceYears = experienceText => {\n    if (!experienceText) return 0;\n    const match = experienceText.match(/(\\d+)\\+?\\s*years?/i);\n    return match ? parseInt(match[1]) : 0;\n  };\n  const handleAdvancedFilterChange = (filterName, value) => {\n    setAdvancedFilters(prev => ({\n      ...prev,\n      [filterName]: value\n    }));\n  };\n  const clearAllFilters = () => {\n    setFilterQuery('');\n    setAdvancedFilters({\n      minExperience: '',\n      maxExperience: '',\n      education: '',\n      location: '',\n      skills: '',\n      minMarks: '',\n      institution: '',\n      graduationYear: '',\n      company: ''\n    });\n  };\n  const hasActiveFilters = () => {\n    return filterQuery || Object.values(advancedFilters).some(value => value !== '');\n  };\n  const handleDownload = resume => {\n    try {\n      var _resume$rawData, _resume$rawData$Resum, _resume$rawData2, _resume$rawData2$Resu, _resume$rawData3, _resume$rawData3$Resu, _resume$rawData4, _resume$rawData4$Resu, _resume$rawData5, _resume$rawData5$Resu, _resume$rawData6, _resume$rawData6$Resu, _resume$rawData7, _resume$rawData7$Resu, _resume$rawData8, _resume$rawData8$Resu, _resume$rawData8$Resu2;\n      // Create a comprehensive text-based resume content\n      const resumeContent = `\n${resume.name}\n${resume.title}\n${resume.email} | ${resume.phone} | ${resume.location}\n\nPROFESSIONAL SUMMARY\n${resume.summary}\n\nWORK EXPERIENCE\n${(_resume$rawData = resume.rawData) !== null && _resume$rawData !== void 0 && (_resume$rawData$Resum = _resume$rawData.Resume) !== null && _resume$rawData$Resum !== void 0 && _resume$rawData$Resum.WorkExperience ? resume.rawData.Resume.WorkExperience.map(exp => `${exp.JobTitle || exp.Position} at ${exp.Company || exp.Organization}\nDuration: ${exp.Duration || exp.Years || 'Not specified'}\n${exp.Description ? 'Description: ' + exp.Description : ''}\n${exp.Responsibilities ? 'Responsibilities: ' + exp.Responsibilities : ''}\n`).join('\\n') : resume.experience}\n\n${(_resume$rawData2 = resume.rawData) !== null && _resume$rawData2 !== void 0 && (_resume$rawData2$Resu = _resume$rawData2.Resume) !== null && _resume$rawData2$Resu !== void 0 && _resume$rawData2$Resu.TotalWorkExperienceInYears ? `Total Experience: ${resume.rawData.Resume.TotalWorkExperienceInYears} years\\n` : ''}\n\nEDUCATION\n${(_resume$rawData3 = resume.rawData) !== null && _resume$rawData3 !== void 0 && (_resume$rawData3$Resu = _resume$rawData3.Resume) !== null && _resume$rawData3$Resu !== void 0 && _resume$rawData3$Resu.Education ? resume.rawData.Resume.Education.map(edu => `${edu.Degree || 'Degree'} from ${edu.Institution || edu.School || 'Institution'}\nGraduation Year: ${edu.GraduationYear || edu.Year || 'Not specified'}\n${edu['GPA/Marks/%'] && edu['GPA/Marks/%'] > 0 ? 'Marks: ' + edu['GPA/Marks/%'] + '%' : ''}\n`).join('\\n') : resume.education}\n\nSKILLS & TECHNOLOGIES\n${resume.skills.join(', ')}\n\n${(_resume$rawData4 = resume.rawData) !== null && _resume$rawData4 !== void 0 && (_resume$rawData4$Resu = _resume$rawData4.Resume) !== null && _resume$rawData4$Resu !== void 0 && _resume$rawData4$Resu.Languages && resume.rawData.Resume.Languages.length > 0 ? `LANGUAGES\\n${resume.rawData.Resume.Languages.join(', ')}\\n` : ''}\n\n${(_resume$rawData5 = resume.rawData) !== null && _resume$rawData5 !== void 0 && (_resume$rawData5$Resu = _resume$rawData5.Resume) !== null && _resume$rawData5$Resu !== void 0 && _resume$rawData5$Resu.Projects && resume.rawData.Resume.Projects.length > 0 ? `PROJECTS\\n${resume.rawData.Resume.Projects.map(project => `${project.Name || project.Title || 'Project'}\n${project.Description ? 'Description: ' + project.Description : ''}\n${project.Technologies ? 'Technologies: ' + project.Technologies : ''}\n`).join('\\n')}` : ''}\n\n${(_resume$rawData6 = resume.rawData) !== null && _resume$rawData6 !== void 0 && (_resume$rawData6$Resu = _resume$rawData6.Resume) !== null && _resume$rawData6$Resu !== void 0 && _resume$rawData6$Resu.Certifications && resume.rawData.Resume.Certifications.length > 0 ? `CERTIFICATIONS\\n${resume.rawData.Resume.Certifications.map(cert => `${cert.Name || cert.Title || 'Certification'}\n${cert.IssuingOrganization ? 'Issued by: ' + cert.IssuingOrganization : ''}\n${cert.IssueDate ? 'Date: ' + cert.IssueDate : ''}\n`).join('\\n')}` : ''}\n\n${(_resume$rawData7 = resume.rawData) !== null && _resume$rawData7 !== void 0 && (_resume$rawData7$Resu = _resume$rawData7.Resume) !== null && _resume$rawData7$Resu !== void 0 && _resume$rawData7$Resu.Achievements && resume.rawData.Resume.Achievements.length > 0 ? `ACHIEVEMENTS\\n${resume.rawData.Resume.Achievements.map(achievement => typeof achievement === 'string' ? achievement : `${achievement.AchievementName || achievement.Name || 'Achievement'}\n${achievement.IssueDate ? 'Date: ' + achievement.IssueDate : ''}\n${achievement.IssuingOrganization ? 'Organization: ' + achievement.IssuingOrganization : ''}\n${achievement.Description ? 'Description: ' + achievement.Description : ''}\n`).join('\\n')}` : ''}\n\n${(_resume$rawData8 = resume.rawData) !== null && _resume$rawData8 !== void 0 && (_resume$rawData8$Resu = _resume$rawData8.Resume) !== null && _resume$rawData8$Resu !== void 0 && (_resume$rawData8$Resu2 = _resume$rawData8$Resu.PersonalInformation) !== null && _resume$rawData8$Resu2 !== void 0 && _resume$rawData8$Resu2.LinkedIn ? `ADDITIONAL INFORMATION\\nLinkedIn: ${resume.rawData.Resume.PersonalInformation.LinkedIn}` : ''}\n\nGenerated on: ${new Date().toLocaleDateString()}\n      `.trim();\n\n      // Create and download the file\n      const blob = new Blob([resumeContent], {\n        type: 'text/plain'\n      });\n      const url = URL.createObjectURL(blob);\n      const link = document.createElement('a');\n      link.href = url;\n      link.download = `${resume.name.replace(/\\s+/g, '_')}_Resume.txt`;\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n      URL.revokeObjectURL(url);\n      toast.success(`Downloaded resume for ${resume.name}`);\n    } catch (error) {\n      console.error('Download error:', error);\n      toast.error('Failed to download resume. Please try again.');\n    }\n  };\n  const handleView = resume => {\n    setSelectedResume(resume);\n    setIsModalOpen(true);\n    toast.info(`Opening detailed view for ${resume.name}`);\n  };\n  const handleCloseModal = () => {\n    setIsModalOpen(false);\n    setSelectedResume(null);\n  };\n  const handleEmailClick = (email, name) => {\n    window.open(`mailto:${email}?subject=Regarding your resume&body=Hi ${name},%0D%0A%0D%0AI found your resume and would like to discuss potential opportunities.%0D%0A%0D%0ABest regards`, '_blank');\n    toast.info(`Opening email client to contact ${name}`);\n  };\n  const handlePhoneClick = (phone, name) => {\n    if (navigator.userAgent.match(/Mobile|Android|iPhone|iPad/)) {\n      window.open(`tel:${phone}`, '_blank');\n    } else {\n      navigator.clipboard.writeText(phone).then(() => {\n        toast.success(`Phone number copied: ${phone}`);\n      }).catch(() => {\n        toast.info(`Phone: ${phone}`);\n      });\n    }\n  };\n  const handleExportAll = () => {\n    if (filteredResults.length === 0) {\n      toast.warning('No results to export');\n      return;\n    }\n    try {\n      const exportData = filteredResults.map(resume => ({\n        name: resume.name,\n        title: resume.title,\n        email: resume.email,\n        phone: resume.phone,\n        location: resume.location,\n        experience: resume.experience,\n        education: resume.education,\n        skills: resume.skills.join(', '),\n        summary: resume.summary,\n        matchScore: resume.score\n      }));\n      const csvContent = ['Name,Title,Email,Phone,Location,Experience,Education,Skills,Summary,Match Score', ...exportData.map(row => Object.values(row).map(value => `\"${String(value).replace(/\"/g, '\"\"')}\"`).join(','))].join('\\n');\n      const blob = new Blob([csvContent], {\n        type: 'text/csv'\n      });\n      const url = URL.createObjectURL(blob);\n      const link = document.createElement('a');\n      link.href = url;\n      link.download = `Resume_Search_Results_${new Date().toISOString().split('T')[0]}.csv`;\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n      URL.revokeObjectURL(url);\n      toast.success(`Exported ${filteredResults.length} resumes to CSV`);\n    } catch (error) {\n      console.error('Export error:', error);\n      toast.error('Failed to export results. Please try again.');\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"search-page\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"page-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        children: \"Search Resumes\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 622,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Use natural language to find the perfect candidates for your needs\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 623,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 621,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"search-section\",\n      children: [/*#__PURE__*/_jsxDEV(\"form\", {\n        onSubmit: handleSearch,\n        className: \"search-form\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"search-input-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"search-input-wrapper\",\n            children: [/*#__PURE__*/_jsxDEV(FiSearch, {\n              className: \"search-icon\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 631,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              placeholder: \"e.g., 'Find Python developers with 5+ years experience' or 'Show frontend engineers in California'\",\n              value: searchQuery,\n              onChange: e => setSearchQuery(e.target.value),\n              className: \"search-input\",\n              disabled: isSearching\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 632,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 630,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"search-buttons\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"submit\",\n              className: \"btn btn-primary\",\n              disabled: isSearching,\n              children: isSearching ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"loading-spinner\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 649,\n                  columnNumber: 21\n                }, this), \"Searching...\"]\n              }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(FiSearch, {\n                  size: 16\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 654,\n                  columnNumber: 21\n                }, this), \"Search\"]\n              }, void 0, true)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 642,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              className: \"btn btn-secondary\",\n              onClick: handleShowAll,\n              disabled: isSearching,\n              children: \"Show All\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 659,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 641,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 629,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 628,\n        columnNumber: 9\n      }, this), searchResults.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"filter-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"filter-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"basic-filter-wrapper\",\n            children: [/*#__PURE__*/_jsxDEV(FiFilter, {\n              className: \"filter-icon\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 676,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              placeholder: \"Quick filter by name, title, or location...\",\n              value: filterQuery,\n              onChange: e => setFilterQuery(e.target.value),\n              className: \"filter-input\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 677,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 675,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"filter-controls\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              className: `btn btn-secondary filter-toggle ${showFilters ? 'active' : ''}`,\n              onClick: () => setShowFilters(!showFilters),\n              children: [/*#__PURE__*/_jsxDEV(FiSettings, {\n                size: 16\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 691,\n                columnNumber: 19\n              }, this), \"Advanced Filters\", showFilters ? /*#__PURE__*/_jsxDEV(FiChevronUp, {\n                size: 16\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 693,\n                columnNumber: 34\n              }, this) : /*#__PURE__*/_jsxDEV(FiChevronDown, {\n                size: 16\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 693,\n                columnNumber: 62\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 686,\n              columnNumber: 17\n            }, this), hasActiveFilters() && /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              className: \"btn btn-outline clear-filters\",\n              onClick: clearAllFilters,\n              children: [/*#__PURE__*/_jsxDEV(FiX, {\n                size: 16\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 701,\n                columnNumber: 21\n              }, this), \"Clear All\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 696,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 685,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 674,\n          columnNumber: 13\n        }, this), showFilters && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"advanced-filters\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"filters-grid\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"filter-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Experience (Years)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 713,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"range-inputs\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"number\",\n                  placeholder: \"Min\",\n                  value: advancedFilters.minExperience,\n                  onChange: e => handleAdvancedFilterChange('minExperience', e.target.value),\n                  className: \"filter-input small\",\n                  min: \"0\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 715,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"to\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 723,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"number\",\n                  placeholder: \"Max\",\n                  value: advancedFilters.maxExperience,\n                  onChange: e => handleAdvancedFilterChange('maxExperience', e.target.value),\n                  className: \"filter-input small\",\n                  min: \"0\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 724,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 714,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 712,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"filter-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Education Level\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 736,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                placeholder: \"e.g., Bachelor, Master, PhD\",\n                value: advancedFilters.education,\n                onChange: e => handleAdvancedFilterChange('education', e.target.value),\n                className: \"filter-input\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 737,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 735,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"filter-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Location\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 747,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                placeholder: \"e.g., Mumbai, Delhi, Bangalore\",\n                value: advancedFilters.location,\n                onChange: e => handleAdvancedFilterChange('location', e.target.value),\n                className: \"filter-input\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 748,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 746,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"filter-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Skills\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 758,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                placeholder: \"e.g., Python, React, Java\",\n                value: advancedFilters.skills,\n                onChange: e => handleAdvancedFilterChange('skills', e.target.value),\n                className: \"filter-input\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 759,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 757,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"filter-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Institution\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 769,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                placeholder: \"e.g., IIT, University name\",\n                value: advancedFilters.institution,\n                onChange: e => handleAdvancedFilterChange('institution', e.target.value),\n                className: \"filter-input\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 770,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 768,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"filter-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Graduation Year\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 780,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                placeholder: \"e.g., 2020, 2021\",\n                value: advancedFilters.graduationYear,\n                onChange: e => handleAdvancedFilterChange('graduationYear', e.target.value),\n                className: \"filter-input\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 781,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 779,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"filter-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Company\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 791,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                placeholder: \"e.g., Google, Microsoft, TCS\",\n                value: advancedFilters.company,\n                onChange: e => handleAdvancedFilterChange('company', e.target.value),\n                className: \"filter-input\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 792,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 790,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"filter-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Minimum Marks (%)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 802,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"number\",\n                placeholder: \"e.g., 75, 80\",\n                value: advancedFilters.minMarks,\n                onChange: e => handleAdvancedFilterChange('minMarks', e.target.value),\n                className: \"filter-input\",\n                min: \"0\",\n                max: \"100\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 803,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 801,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 711,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 710,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"results-count\",\n          children: [filteredResults.length, \" of \", searchResults.length, \" results\", hasActiveFilters() && /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"filtered-indicator\",\n            children: \" (filtered)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 819,\n            columnNumber: 38\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 817,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 673,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 627,\n      columnNumber: 7\n    }, this), searchResults.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"results-section\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"results-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Search Results\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 829,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"results-actions\",\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"btn btn-secondary\",\n            onClick: handleExportAll,\n            title: `Export ${filteredResults.length} results to CSV`,\n            children: [/*#__PURE__*/_jsxDEV(FiDownload, {\n              size: 16\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 836,\n              columnNumber: 17\n            }, this), \"Export All (\", filteredResults.length, \")\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 831,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 830,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 828,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"results-grid\",\n        children: filteredResults.map(resume => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"resume-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"resume-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"resume-avatar\",\n              children: /*#__PURE__*/_jsxDEV(FiUser, {\n                size: 24\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 847,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 846,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"resume-info\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"resume-name\",\n                children: resume.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 850,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"resume-title\",\n                children: resume.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 851,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 849,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 845,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"resume-details\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"detail-item location-item\",\n              children: [/*#__PURE__*/_jsxDEV(FiMapPin, {\n                size: 14\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 857,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: resume.location\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 858,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 856,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"detail-item email-item clickable\",\n              onClick: () => handleEmailClick(resume.email, resume.name),\n              title: `Send email to ${resume.name}`,\n              children: [/*#__PURE__*/_jsxDEV(FiMail, {\n                size: 14\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 865,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: resume.email\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 866,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(FiExternalLink, {\n                size: 12,\n                className: \"external-icon\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 867,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 860,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"detail-item phone-item clickable\",\n              onClick: () => handlePhoneClick(resume.phone, resume.name),\n              title: `Call ${resume.name}`,\n              children: [/*#__PURE__*/_jsxDEV(FiPhone, {\n                size: 14\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 874,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: resume.phone\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 875,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(FiExternalLink, {\n                size: 12,\n                className: \"external-icon\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 876,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 869,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 855,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"resume-content\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"content-row\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"content-section\",\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  children: \"Experience\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 883,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: resume.experience\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 884,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 882,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"content-section\",\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  children: \"Education\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 887,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: resume.education\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 888,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 886,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 881,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"content-section\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                children: \"Skills\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 892,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"skills-list\",\n                children: resume.skills.map((skill, index) => /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"skill-tag\",\n                  children: skill\n                }, index, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 895,\n                  columnNumber: 25\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 893,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 891,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 880,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"resume-actions\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"btn btn-secondary\",\n              onClick: () => handleView(resume),\n              children: [/*#__PURE__*/_jsxDEV(FiEye, {\n                size: 16\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 906,\n                columnNumber: 21\n              }, this), \"View Details\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 902,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"btn btn-primary\",\n              onClick: () => handleDownload(resume),\n              children: [/*#__PURE__*/_jsxDEV(FiDownload, {\n                size: 16\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 913,\n                columnNumber: 21\n              }, this), \"Download\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 909,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 901,\n            columnNumber: 17\n          }, this)]\n        }, resume.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 844,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 842,\n        columnNumber: 11\n      }, this), totalPages > 1 && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"pagination-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"pagination-info\",\n          children: [\"Showing \", (currentPage - 1) * resultsPerPage + 1, \" to \", Math.min(currentPage * resultsPerPage, totalCount), \" of \", totalCount, \" results\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 924,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"pagination-controls\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"btn btn-secondary pagination-btn\",\n            onClick: () => handlePageChange(currentPage - 1),\n            disabled: currentPage === 1,\n            children: \"Previous\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 928,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"page-numbers\",\n            children: Array.from({\n              length: Math.min(5, totalPages)\n            }, (_, i) => {\n              let pageNum;\n              if (totalPages <= 5) {\n                pageNum = i + 1;\n              } else if (currentPage <= 3) {\n                pageNum = i + 1;\n              } else if (currentPage >= totalPages - 2) {\n                pageNum = totalPages - 4 + i;\n              } else {\n                pageNum = currentPage - 2 + i;\n              }\n              return /*#__PURE__*/_jsxDEV(\"button\", {\n                className: `btn pagination-btn ${currentPage === pageNum ? 'btn-primary' : 'btn-secondary'}`,\n                onClick: () => handlePageChange(pageNum),\n                children: pageNum\n              }, pageNum, false, {\n                fileName: _jsxFileName,\n                lineNumber: 950,\n                columnNumber: 23\n              }, this);\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 936,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"btn btn-secondary pagination-btn\",\n            onClick: () => handlePageChange(currentPage + 1),\n            disabled: currentPage === totalPages,\n            children: \"Next\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 961,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 927,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 923,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 827,\n      columnNumber: 9\n    }, this) : !isSearching ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"empty-state\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"empty-icon\",\n        children: /*#__PURE__*/_jsxDEV(FiSearch, {\n          size: 48\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 975,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 974,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n        children: \"Ready to Search!\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 977,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Use the search box above to find resumes using natural language, or click \\\"Show All\\\" to view all resumes in the database.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 978,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"search-examples\",\n        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n          children: \"Try searching for:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 980,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n          children: [/*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"\\\"Find Python developers\\\"\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 982,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"\\\"Show teachers with 5+ years experience\\\"\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 983,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"\\\"Software engineers in Mumbai\\\"\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 984,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 981,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 979,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 973,\n      columnNumber: 9\n    }, this) : null, /*#__PURE__*/_jsxDEV(ResumeModal, {\n      resume: selectedResume,\n      isOpen: isModalOpen,\n      onClose: handleCloseModal\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 991,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 620,\n    columnNumber: 5\n  }, this);\n};\n_s(SearchResumes, \"uvVHvTgqjBYZkn8TcWc0iS1zcVo=\");\n_c = SearchResumes;\nexport default SearchResumes;\nvar _c;\n$RefreshReg$(_c, \"SearchResumes\");", "map": {"version": 3, "names": ["React", "useState", "FiSearch", "<PERSON><PERSON><PERSON><PERSON>", "FiDownload", "FiEye", "FiUser", "FiMapPin", "FiMail", "FiPhone", "FiExternalLink", "FiStar", "FiX", "FiSettings", "FiChevronDown", "FiChevronUp", "toast", "ResumeModal", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "SearchResumes", "_s", "searchQuery", "setSearch<PERSON>uery", "isSearching", "setIsSearching", "searchResults", "setSearchResults", "filterQuery", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "showFilters", "setShowFilters", "advancedFilters", "setAdvancedFilters", "minExperience", "maxExperience", "education", "location", "skills", "minMarks", "institution", "graduationYear", "company", "selectedResume", "setSelectedResume", "isModalOpen", "setIsModalOpen", "currentPage", "setCurrentPage", "totalPages", "setTotalPages", "totalCount", "setTotalCount", "resultsPerPage", "mockResults", "id", "name", "title", "email", "phone", "experience", "summary", "score", "rawData", "Resume", "PersonalInformation", "FullName", "Email", "ContactNumber", "Address", "WorkExperience", "Role", "CompanyName", "StartYear", "EndYear", "Education", "Degree", "Institution", "GraduationYear", "Skills", "TotalWorkExperienceInYears", "handleSearch", "e", "preventDefault", "trim", "warning", "response", "fetch", "encodeURIComponent", "method", "headers", "data", "json", "ok", "transformedResults", "listOfDict", "Array", "isArray", "map", "resume", "_resumeData$PersonalI", "_resumeData$PersonalI2", "_resumeData$PersonalI3", "_resumeData$PersonalI4", "_resumeData$PersonalI5", "_resumeData$WorkExper", "_resumeData$Education", "_resumeData$Education2", "resumeData", "_id", "Math", "random", "toString", "substr", "Designation", "length", "Objective", "Summary", "filter", "toLowerCase", "includes", "some", "skill", "result", "success", "query", "console", "log", "Error", "error", "fallback<PERSON><PERSON><PERSON>s", "handleShowAll", "page", "resumes", "_resumeData$PersonalI6", "_resumeData$PersonalI7", "_resumeData$PersonalI8", "_resumeData$PersonalI9", "_resumeData$PersonalI0", "_resumeData$WorkExper2", "_resumeData$Education3", "_resumeData$Education4", "count", "total", "ceil", "source", "info", "handlePageChange", "filteredResults", "matchesText", "filters", "_result$rawData", "_result$rawData$Resum", "experienceYears", "extractExperienceYears", "parseInt", "_result$rawData2", "_result$rawData2$Resu", "_result$rawData2$Resu2", "hasEducation", "edu", "matchesLocation", "_result$skills", "_result$rawData3", "_result$rawData3$Resu", "skillsText", "hasSkill", "_result$rawData4", "_result$rawData4$Resu", "_result$rawData4$Resu2", "hasInstitution", "School", "_result$rawData5", "_result$rawData5$Resu", "_result$rawData5$Resu2", "hasGradYear", "Year", "_result$rawData6", "_result$rawData6$Resu", "_result$rawData6$Resu2", "hasCompany", "exp", "Company", "Organization", "_result$rawData7", "_result$rawData7$Resu", "_result$rawData7$Resu2", "hasMinMarks", "marks", "parseFloat", "experienceText", "match", "handleAdvancedFilterChange", "filterName", "value", "prev", "clearAllFilters", "hasActiveFilters", "Object", "values", "handleDownload", "_resume$rawData", "_resume$rawData$Resum", "_resume$rawData2", "_resume$rawData2$Resu", "_resume$rawData3", "_resume$rawData3$Resu", "_resume$rawData4", "_resume$rawData4$Resu", "_resume$rawData5", "_resume$rawData5$Resu", "_resume$rawData6", "_resume$rawData6$Resu", "_resume$rawData7", "_resume$rawData7$Resu", "_resume$rawData8", "_resume$rawData8$Resu", "_resume$rawData8$Resu2", "<PERSON><PERSON><PERSON>nt", "JobTitle", "Position", "Duration", "Years", "Description", "Responsibilities", "join", "Languages", "Projects", "project", "Name", "Title", "Technologies", "Certifications", "cert", "IssuingOrganization", "IssueDate", "Achievements", "achievement", "AchievementName", "LinkedIn", "Date", "toLocaleDateString", "blob", "Blob", "type", "url", "URL", "createObjectURL", "link", "document", "createElement", "href", "download", "replace", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL", "handleView", "handleCloseModal", "handleEmailClick", "window", "open", "handlePhoneClick", "navigator", "userAgent", "clipboard", "writeText", "then", "catch", "handleExportAll", "exportData", "matchScore", "csv<PERSON><PERSON>nt", "row", "String", "toISOString", "split", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSubmit", "placeholder", "onChange", "target", "disabled", "size", "onClick", "min", "max", "index", "from", "_", "i", "pageNum", "isOpen", "onClose", "_c", "$RefreshReg$"], "sources": ["D:/Projects/SQL-Agent-Task-Submission-main/SQL-Agent-Task-Submission-main/src/pages/SearchResumes.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { <PERSON><PERSON>earch, Fi<PERSON>ilter, <PERSON>Down<PERSON>, <PERSON>Eye, Fi<PERSON>ser, FiMapPin, FiMail, FiPhone, FiExternalLink, FiStar, FiX, FiSettings, FiChevronDown, FiChevronUp } from 'react-icons/fi';\nimport { toast } from 'react-toastify';\nimport ResumeModal from '../components/ResumeModal';\nimport './SearchResumes.css';\n\nconst SearchResumes = () => {\n  const [searchQuery, setSearchQuery] = useState('');\n  const [isSearching, setIsSearching] = useState(false);\n  const [searchResults, setSearchResults] = useState([]);\n  const [filterQuery, setFilterQuery] = useState('');\n  const [showFilters, setShowFilters] = useState(false);\n  const [advancedFilters, setAdvancedFilters] = useState({\n    minExperience: '',\n    maxExperience: '',\n    education: '',\n    location: '',\n    skills: '',\n    minMarks: '',\n    institution: '',\n    graduationYear: '',\n    company: ''\n  });\n  const [selectedResume, setSelectedResume] = useState(null);\n  const [isModalOpen, setIsModalOpen] = useState(false);\n  const [currentPage, setCurrentPage] = useState(1);\n  const [totalPages, setTotalPages] = useState(1);\n  const [totalCount, setTotalCount] = useState(0);\n  const resultsPerPage = 20;\n\n  // Mock data for demonstration\n  const mockResults = [\n    {\n      id: 1,\n      name: 'John Doe',\n      title: 'Senior Software Engineer',\n      email: '<EMAIL>',\n      phone: '+****************',\n      location: 'San Francisco, CA',\n      experience: '5+ years',\n      skills: ['Python', 'React', 'Node.js', 'AWS'],\n      education: 'MS Computer Science',\n      summary: 'Experienced software engineer with expertise in full-stack development...',\n      score: 95,\n      rawData: {\n        Resume: {\n          PersonalInformation: {\n            FullName: 'John Doe',\n            Email: '<EMAIL>',\n            ContactNumber: '+****************',\n            Address: 'San Francisco, CA'\n          },\n          WorkExperience: [\n            {\n              Role: 'Senior Software Engineer',\n              CompanyName: 'Tech Corp',\n              StartYear: '2019',\n              EndYear: 'Present',\n              'Description/Responsibility': 'Led development of scalable web applications using React and Node.js'\n            },\n            {\n              Role: 'Software Engineer',\n              CompanyName: 'StartupXYZ',\n              StartYear: '2017',\n              EndYear: '2019',\n              'Description/Responsibility': 'Developed full-stack applications and improved system performance'\n            }\n          ],\n          Education: [\n            {\n              Degree: 'MS Computer Science',\n              Institution: 'Stanford University',\n              GraduationYear: '2017',\n              'GPA/Marks/%': 85\n            }\n          ],\n          Skills: ['Python', 'React', 'Node.js', 'AWS'],\n          TotalWorkExperienceInYears: 5\n        }\n      }\n    },\n    {\n      id: 2,\n      name: 'Jane Smith',\n      title: 'Frontend Developer',\n      email: '<EMAIL>',\n      phone: '+****************',\n      location: 'New York, NY',\n      experience: '3+ years',\n      skills: ['React', 'TypeScript', 'CSS', 'JavaScript'],\n      education: 'BS Computer Science',\n      summary: 'Creative frontend developer passionate about user experience...',\n      score: 88,\n      rawData: {\n        Resume: {\n          PersonalInformation: {\n            FullName: 'Jane Smith',\n            Email: '<EMAIL>',\n            ContactNumber: '+****************',\n            Address: 'New York, NY'\n          },\n          WorkExperience: [\n            {\n              Role: 'Frontend Developer',\n              CompanyName: 'Design Studio',\n              StartYear: '2021',\n              EndYear: 'Present',\n              'Description/Responsibility': 'Created responsive web interfaces using React and modern CSS'\n            }\n          ],\n          Education: [\n            {\n              Degree: 'BS Computer Science',\n              Institution: 'NYU',\n              GraduationYear: '2021',\n              'GPA/Marks/%': 78\n            }\n          ],\n          Skills: ['React', 'TypeScript', 'CSS', 'JavaScript'],\n          TotalWorkExperienceInYears: 3\n        }\n      }\n    },\n    {\n      id: 3,\n      name: 'Mike Johnson',\n      title: 'DevOps Engineer',\n      email: '<EMAIL>',\n      phone: '+****************',\n      location: 'Austin, TX',\n      experience: '4+ years',\n      skills: ['Docker', 'Kubernetes', 'AWS', 'Python'],\n      education: 'BS Information Technology',\n      summary: 'DevOps engineer specializing in cloud infrastructure and automation...',\n      score: 92,\n      rawData: {\n        Resume: {\n          PersonalInformation: {\n            FullName: 'Mike Johnson',\n            Email: '<EMAIL>',\n            ContactNumber: '+****************',\n            Address: 'Austin, TX'\n          },\n          WorkExperience: [\n            {\n              Role: 'DevOps Engineer',\n              CompanyName: 'Cloud Solutions Inc',\n              StartYear: '2020',\n              EndYear: 'Present',\n              'Description/Responsibility': 'Managed cloud infrastructure and automated deployment pipelines'\n            }\n          ],\n          Education: [\n            {\n              Degree: 'BS Information Technology',\n              Institution: 'UT Austin',\n              GraduationYear: '2020',\n              'GPA/Marks/%': 82\n            }\n          ],\n          Skills: ['Docker', 'Kubernetes', 'AWS', 'Python'],\n          TotalWorkExperienceInYears: 4\n        }\n      }\n    }\n  ];\n\n  const handleSearch = async (e) => {\n    e.preventDefault();\n    if (!searchQuery.trim()) {\n      toast.warning('Please enter a search query');\n      return;\n    }\n\n    setIsSearching(true);\n\n    try {\n      // Call the FastAPI backend directly for GPT-powered search\n      const response = await fetch(`http://localhost:8001/query/?naturalQuery=${encodeURIComponent(searchQuery)}`, {\n        method: 'GET',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n      });\n\n      const data = await response.json();\n\n      if (response.ok) {\n        // Handle FastAPI response format: {query: [], listOfDict: [], result: \"\"}\n        let transformedResults = [];\n\n        if (data.listOfDict && Array.isArray(data.listOfDict)) {\n          // Transform the resume data from MongoDB to display format\n          transformedResults = data.listOfDict.map(resume => {\n            // Handle the nested Resume structure from MongoDB\n            const resumeData = resume.Resume || resume;\n\n            return {\n              id: resume._id || Math.random().toString(36).substr(2, 9),\n              name: resumeData.PersonalInformation?.FullName || resumeData.name || 'Unknown',\n              title: resumeData.PersonalInformation?.Designation || resumeData.title || 'No title',\n              email: resumeData.PersonalInformation?.Email || resumeData.email || 'No email',\n              phone: resumeData.PersonalInformation?.ContactNumber || resumeData.phone || 'No phone',\n              location: resumeData.PersonalInformation?.Address || resumeData.location || 'No location',\n              experience: resumeData.TotalWorkExperienceInYears ?\n                `${resumeData.TotalWorkExperienceInYears} years` :\n                resumeData.WorkExperience?.length ?\n                  `${resumeData.WorkExperience.length}+ positions` :\n                  resumeData.experience || 'No experience data',\n              skills: resumeData.Skills || resumeData.skills || [],\n              education: resumeData.Education?.length ?\n                resumeData.Education[0]?.Degree || 'Education available' :\n                resumeData.education || 'No education data',\n              summary: resumeData.Objective || resumeData.Summary || resumeData.summary || 'No summary available',\n              score: 95, // Default score\n              rawData: resume // Keep original data for reference\n            };\n          });\n        } else {\n          // Fallback to mock data if API doesn't return results\n          transformedResults = mockResults.filter(resume =>\n            resume.name.toLowerCase().includes(searchQuery.toLowerCase()) ||\n            resume.title.toLowerCase().includes(searchQuery.toLowerCase()) ||\n            resume.skills.some(skill => skill.toLowerCase().includes(searchQuery.toLowerCase()))\n          );\n        }\n\n        setSearchResults(transformedResults);\n\n        // Show success message with GPT result\n        if (data.result) {\n          toast.success(`🤖 AI Search: ${data.result}`);\n        } else {\n          toast.success(`Found ${transformedResults.length} matching resumes`);\n        }\n\n        // Log GPT response and MongoDB query for debugging\n        if (data.query) {\n          console.log('MongoDB Query Generated by GPT:', data.query);\n          console.log('GPT Result:', data.result);\n        }\n      } else {\n        throw new Error(data.error || 'Search failed');\n      }\n    } catch (error) {\n      console.error('Search error:', error);\n\n      // Fallback to mock data on network error\n      const fallbackResults = mockResults.filter(resume =>\n        resume.name.toLowerCase().includes(searchQuery.toLowerCase()) ||\n        resume.title.toLowerCase().includes(searchQuery.toLowerCase()) ||\n        resume.skills.some(skill => skill.toLowerCase().includes(searchQuery.toLowerCase()))\n      );\n\n      setSearchResults(fallbackResults);\n      toast.warning(`Using demo data - Found ${fallbackResults.length} matching resumes`);\n    } finally {\n      setIsSearching(false);\n    }\n  };\n\n  const handleShowAll = async (page = 1) => {\n    setIsSearching(true);\n    try {\n      // Call the actual API to get paginated resumes\n      const response = await fetch(`/api/resumes?page=${page}&limit=${resultsPerPage}`);\n      const data = await response.json();\n\n      if (response.ok) {\n        let transformedResults = [];\n\n        if (data.resumes && Array.isArray(data.resumes)) {\n          // Transform the resume data to match our display format\n          transformedResults = data.resumes.map(resume => {\n            // Handle the nested Resume structure from MongoDB\n            const resumeData = resume.Resume || resume;\n\n            return {\n              id: resume._id || Math.random().toString(36).substr(2, 9),\n              name: resumeData.PersonalInformation?.FullName || resumeData.name || 'Unknown',\n              title: resumeData.PersonalInformation?.Designation || resumeData.title || 'No title',\n              email: resumeData.PersonalInformation?.Email || resumeData.email || 'No email',\n              phone: resumeData.PersonalInformation?.ContactNumber || resumeData.phone || 'No phone',\n              location: resumeData.PersonalInformation?.Address || resumeData.location || 'No location',\n              experience: resumeData.TotalWorkExperienceInYears ?\n                `${resumeData.TotalWorkExperienceInYears} years` :\n                resumeData.WorkExperience?.length ?\n                  `${resumeData.WorkExperience.length}+ positions` :\n                  resumeData.experience || 'No experience data',\n              skills: resumeData.Skills || resumeData.skills || [],\n              education: resumeData.Education?.length ?\n                resumeData.Education[0]?.Degree || 'Education available' :\n                resumeData.education || 'No education data',\n              summary: resumeData.Objective || resumeData.Summary || resumeData.summary || 'No summary available',\n              score: 90, // Default score for show all\n              rawData: resume // Keep original data for reference\n            };\n          });\n        } else {\n          // Fallback to mock data if API doesn't return proper results\n          transformedResults = mockResults;\n        }\n\n        setSearchResults(transformedResults);\n        setCurrentPage(page);\n        setTotalCount(data.count || data.total || transformedResults.length);\n        setTotalPages(Math.ceil((data.count || data.total || transformedResults.length) / resultsPerPage));\n        setSearchQuery('');\n\n        if (data.source === 'database') {\n          toast.success(`Showing page ${page} of ${Math.ceil((data.count || data.total || transformedResults.length) / resultsPerPage)} (${data.count || data.total} total resumes)`);\n        } else {\n          toast.info(`Showing ${transformedResults.length} resumes`);\n        }\n      } else {\n        throw new Error(data.error || 'Failed to load resumes');\n      }\n    } catch (error) {\n      console.error('Load resumes error:', error);\n\n      // Fallback to mock data on network error\n      setSearchResults(mockResults);\n      setCurrentPage(1);\n      setTotalCount(mockResults.length);\n      setTotalPages(Math.ceil(mockResults.length / resultsPerPage));\n      setSearchQuery('');\n\n      toast.warning(`Using demo data - Showing ${mockResults.length} sample resumes`);\n    } finally {\n      setIsSearching(false);\n    }\n  };\n\n  const handlePageChange = (page) => {\n    if (page >= 1 && page <= totalPages) {\n      handleShowAll(page);\n    }\n  };\n\n  const filteredResults = searchResults.filter(result => {\n    // Basic text filter\n    if (filterQuery) {\n      const matchesText = result.name.toLowerCase().includes(filterQuery.toLowerCase()) ||\n                         result.title.toLowerCase().includes(filterQuery.toLowerCase()) ||\n                         result.location.toLowerCase().includes(filterQuery.toLowerCase());\n      if (!matchesText) return false;\n    }\n\n    // Advanced filters\n    const filters = advancedFilters;\n\n    // Experience filter\n    if (filters.minExperience || filters.maxExperience) {\n      const experienceYears = result.rawData?.Resume?.TotalWorkExperienceInYears ||\n                             extractExperienceYears(result.experience) || 0;\n\n      if (filters.minExperience && experienceYears < parseInt(filters.minExperience)) return false;\n      if (filters.maxExperience && experienceYears > parseInt(filters.maxExperience)) return false;\n    }\n\n    // Education filter\n    if (filters.education) {\n      const hasEducation = result.rawData?.Resume?.Education?.some(edu =>\n        (edu.Degree || '').toLowerCase().includes(filters.education.toLowerCase())\n      ) || (result.education || '').toLowerCase().includes(filters.education.toLowerCase());\n      if (!hasEducation) return false;\n    }\n\n    // Location filter\n    if (filters.location) {\n      const matchesLocation = (result.location || '').toLowerCase().includes(filters.location.toLowerCase());\n      if (!matchesLocation) return false;\n    }\n\n    // Skills filter\n    if (filters.skills) {\n      const skillsText = filters.skills.toLowerCase();\n      const hasSkill = result.skills?.some(skill => skill.toLowerCase().includes(skillsText)) ||\n                      (result.rawData?.Resume?.Skills || []).some(skill =>\n                        skill.toLowerCase().includes(skillsText)\n                      );\n      if (!hasSkill) return false;\n    }\n\n    // Institution filter\n    if (filters.institution) {\n      const hasInstitution = result.rawData?.Resume?.Education?.some(edu =>\n        (edu.Institution || edu.School || '').toLowerCase().includes(filters.institution.toLowerCase())\n      );\n      if (!hasInstitution) return false;\n    }\n\n    // Graduation year filter\n    if (filters.graduationYear) {\n      const hasGradYear = result.rawData?.Resume?.Education?.some(edu =>\n        (edu.GraduationYear || edu.Year || '').toString().includes(filters.graduationYear)\n      );\n      if (!hasGradYear) return false;\n    }\n\n    // Company filter\n    if (filters.company) {\n      const hasCompany = result.rawData?.Resume?.WorkExperience?.some(exp =>\n        (exp.Company || exp.CompanyName || exp.Organization || '').toLowerCase().includes(filters.company.toLowerCase())\n      );\n      if (!hasCompany) return false;\n    }\n\n    // Minimum marks filter\n    if (filters.minMarks) {\n      const hasMinMarks = result.rawData?.Resume?.Education?.some(edu => {\n        const marks = parseFloat(edu['GPA/Marks/%']) || 0;\n        return marks >= parseFloat(filters.minMarks);\n      });\n      if (!hasMinMarks) return false;\n    }\n\n    return true;\n  });\n\n  // Helper function to extract experience years from text\n  const extractExperienceYears = (experienceText) => {\n    if (!experienceText) return 0;\n    const match = experienceText.match(/(\\d+)\\+?\\s*years?/i);\n    return match ? parseInt(match[1]) : 0;\n  };\n\n  const handleAdvancedFilterChange = (filterName, value) => {\n    setAdvancedFilters(prev => ({\n      ...prev,\n      [filterName]: value\n    }));\n  };\n\n  const clearAllFilters = () => {\n    setFilterQuery('');\n    setAdvancedFilters({\n      minExperience: '',\n      maxExperience: '',\n      education: '',\n      location: '',\n      skills: '',\n      minMarks: '',\n      institution: '',\n      graduationYear: '',\n      company: ''\n    });\n  };\n\n  const hasActiveFilters = () => {\n    return filterQuery || Object.values(advancedFilters).some(value => value !== '');\n  };\n\n  const handleDownload = (resume) => {\n    try {\n      // Create a comprehensive text-based resume content\n      const resumeContent = `\n${resume.name}\n${resume.title}\n${resume.email} | ${resume.phone} | ${resume.location}\n\nPROFESSIONAL SUMMARY\n${resume.summary}\n\nWORK EXPERIENCE\n${resume.rawData?.Resume?.WorkExperience ?\n  resume.rawData.Resume.WorkExperience.map(exp =>\n    `${exp.JobTitle || exp.Position} at ${exp.Company || exp.Organization}\nDuration: ${exp.Duration || exp.Years || 'Not specified'}\n${exp.Description ? 'Description: ' + exp.Description : ''}\n${exp.Responsibilities ? 'Responsibilities: ' + exp.Responsibilities : ''}\n`\n  ).join('\\n') : resume.experience}\n\n${resume.rawData?.Resume?.TotalWorkExperienceInYears ?\n  `Total Experience: ${resume.rawData.Resume.TotalWorkExperienceInYears} years\\n` : ''}\n\nEDUCATION\n${resume.rawData?.Resume?.Education ?\n  resume.rawData.Resume.Education.map(edu =>\n    `${edu.Degree || 'Degree'} from ${edu.Institution || edu.School || 'Institution'}\nGraduation Year: ${edu.GraduationYear || edu.Year || 'Not specified'}\n${edu['GPA/Marks/%'] && edu['GPA/Marks/%'] > 0 ? 'Marks: ' + edu['GPA/Marks/%'] + '%' : ''}\n`\n  ).join('\\n') : resume.education}\n\nSKILLS & TECHNOLOGIES\n${resume.skills.join(', ')}\n\n${resume.rawData?.Resume?.Languages && resume.rawData.Resume.Languages.length > 0 ?\n  `LANGUAGES\\n${resume.rawData.Resume.Languages.join(', ')}\\n` : ''}\n\n${resume.rawData?.Resume?.Projects && resume.rawData.Resume.Projects.length > 0 ?\n  `PROJECTS\\n${resume.rawData.Resume.Projects.map(project =>\n    `${project.Name || project.Title || 'Project'}\n${project.Description ? 'Description: ' + project.Description : ''}\n${project.Technologies ? 'Technologies: ' + project.Technologies : ''}\n`\n  ).join('\\n')}` : ''}\n\n${resume.rawData?.Resume?.Certifications && resume.rawData.Resume.Certifications.length > 0 ?\n  `CERTIFICATIONS\\n${resume.rawData.Resume.Certifications.map(cert =>\n    `${cert.Name || cert.Title || 'Certification'}\n${cert.IssuingOrganization ? 'Issued by: ' + cert.IssuingOrganization : ''}\n${cert.IssueDate ? 'Date: ' + cert.IssueDate : ''}\n`\n  ).join('\\n')}` : ''}\n\n${resume.rawData?.Resume?.Achievements && resume.rawData.Resume.Achievements.length > 0 ?\n  `ACHIEVEMENTS\\n${resume.rawData.Resume.Achievements.map(achievement =>\n    typeof achievement === 'string' ? achievement :\n    `${achievement.AchievementName || achievement.Name || 'Achievement'}\n${achievement.IssueDate ? 'Date: ' + achievement.IssueDate : ''}\n${achievement.IssuingOrganization ? 'Organization: ' + achievement.IssuingOrganization : ''}\n${achievement.Description ? 'Description: ' + achievement.Description : ''}\n`\n  ).join('\\n')}` : ''}\n\n${resume.rawData?.Resume?.PersonalInformation?.LinkedIn ?\n  `ADDITIONAL INFORMATION\\nLinkedIn: ${resume.rawData.Resume.PersonalInformation.LinkedIn}` : ''}\n\nGenerated on: ${new Date().toLocaleDateString()}\n      `.trim();\n\n      // Create and download the file\n      const blob = new Blob([resumeContent], { type: 'text/plain' });\n      const url = URL.createObjectURL(blob);\n      const link = document.createElement('a');\n      link.href = url;\n      link.download = `${resume.name.replace(/\\s+/g, '_')}_Resume.txt`;\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n      URL.revokeObjectURL(url);\n\n      toast.success(`Downloaded resume for ${resume.name}`);\n    } catch (error) {\n      console.error('Download error:', error);\n      toast.error('Failed to download resume. Please try again.');\n    }\n  };\n\n  const handleView = (resume) => {\n    setSelectedResume(resume);\n    setIsModalOpen(true);\n    toast.info(`Opening detailed view for ${resume.name}`);\n  };\n\n  const handleCloseModal = () => {\n    setIsModalOpen(false);\n    setSelectedResume(null);\n  };\n\n  const handleEmailClick = (email, name) => {\n    window.open(`mailto:${email}?subject=Regarding your resume&body=Hi ${name},%0D%0A%0D%0AI found your resume and would like to discuss potential opportunities.%0D%0A%0D%0ABest regards`, '_blank');\n    toast.info(`Opening email client to contact ${name}`);\n  };\n\n  const handlePhoneClick = (phone, name) => {\n    if (navigator.userAgent.match(/Mobile|Android|iPhone|iPad/)) {\n      window.open(`tel:${phone}`, '_blank');\n    } else {\n      navigator.clipboard.writeText(phone).then(() => {\n        toast.success(`Phone number copied: ${phone}`);\n      }).catch(() => {\n        toast.info(`Phone: ${phone}`);\n      });\n    }\n  };\n\n\n\n  const handleExportAll = () => {\n    if (filteredResults.length === 0) {\n      toast.warning('No results to export');\n      return;\n    }\n\n    try {\n      const exportData = filteredResults.map(resume => ({\n        name: resume.name,\n        title: resume.title,\n        email: resume.email,\n        phone: resume.phone,\n        location: resume.location,\n        experience: resume.experience,\n        education: resume.education,\n        skills: resume.skills.join(', '),\n        summary: resume.summary,\n        matchScore: resume.score\n      }));\n\n      const csvContent = [\n        'Name,Title,Email,Phone,Location,Experience,Education,Skills,Summary,Match Score',\n        ...exportData.map(row =>\n          Object.values(row).map(value =>\n            `\"${String(value).replace(/\"/g, '\"\"')}\"`\n          ).join(',')\n        )\n      ].join('\\n');\n\n      const blob = new Blob([csvContent], { type: 'text/csv' });\n      const url = URL.createObjectURL(blob);\n      const link = document.createElement('a');\n      link.href = url;\n      link.download = `Resume_Search_Results_${new Date().toISOString().split('T')[0]}.csv`;\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n      URL.revokeObjectURL(url);\n\n      toast.success(`Exported ${filteredResults.length} resumes to CSV`);\n    } catch (error) {\n      console.error('Export error:', error);\n      toast.error('Failed to export results. Please try again.');\n    }\n  };\n\n  return (\n    <div className=\"search-page\">\n      <div className=\"page-header\">\n        <h1>Search Resumes</h1>\n        <p>Use natural language to find the perfect candidates for your needs</p>\n      </div>\n\n      {/* Search Section */}\n      <div className=\"search-section\">\n        <form onSubmit={handleSearch} className=\"search-form\">\n          <div className=\"search-input-group\">\n            <div className=\"search-input-wrapper\">\n              <FiSearch className=\"search-icon\" />\n              <input\n                type=\"text\"\n                placeholder=\"e.g., 'Find Python developers with 5+ years experience' or 'Show frontend engineers in California'\"\n                value={searchQuery}\n                onChange={(e) => setSearchQuery(e.target.value)}\n                className=\"search-input\"\n                disabled={isSearching}\n              />\n            </div>\n            <div className=\"search-buttons\">\n              <button\n                type=\"submit\"\n                className=\"btn btn-primary\"\n                disabled={isSearching}\n              >\n                {isSearching ? (\n                  <>\n                    <div className=\"loading-spinner\" />\n                    Searching...\n                  </>\n                ) : (\n                  <>\n                    <FiSearch size={16} />\n                    Search\n                  </>\n                )}\n              </button>\n              <button\n                type=\"button\"\n                className=\"btn btn-secondary\"\n                onClick={handleShowAll}\n                disabled={isSearching}\n              >\n                Show All\n              </button>\n            </div>\n          </div>\n        </form>\n\n        {/* Filter Section */}\n        {searchResults.length > 0 && (\n          <div className=\"filter-section\">\n            <div className=\"filter-header\">\n              <div className=\"basic-filter-wrapper\">\n                <FiFilter className=\"filter-icon\" />\n                <input\n                  type=\"text\"\n                  placeholder=\"Quick filter by name, title, or location...\"\n                  value={filterQuery}\n                  onChange={(e) => setFilterQuery(e.target.value)}\n                  className=\"filter-input\"\n                />\n              </div>\n              <div className=\"filter-controls\">\n                <button\n                  type=\"button\"\n                  className={`btn btn-secondary filter-toggle ${showFilters ? 'active' : ''}`}\n                  onClick={() => setShowFilters(!showFilters)}\n                >\n                  <FiSettings size={16} />\n                  Advanced Filters\n                  {showFilters ? <FiChevronUp size={16} /> : <FiChevronDown size={16} />}\n                </button>\n                {hasActiveFilters() && (\n                  <button\n                    type=\"button\"\n                    className=\"btn btn-outline clear-filters\"\n                    onClick={clearAllFilters}\n                  >\n                    <FiX size={16} />\n                    Clear All\n                  </button>\n                )}\n              </div>\n            </div>\n\n            {/* Advanced Filters Panel */}\n            {showFilters && (\n              <div className=\"advanced-filters\">\n                <div className=\"filters-grid\">\n                  <div className=\"filter-group\">\n                    <label>Experience (Years)</label>\n                    <div className=\"range-inputs\">\n                      <input\n                        type=\"number\"\n                        placeholder=\"Min\"\n                        value={advancedFilters.minExperience}\n                        onChange={(e) => handleAdvancedFilterChange('minExperience', e.target.value)}\n                        className=\"filter-input small\"\n                        min=\"0\"\n                      />\n                      <span>to</span>\n                      <input\n                        type=\"number\"\n                        placeholder=\"Max\"\n                        value={advancedFilters.maxExperience}\n                        onChange={(e) => handleAdvancedFilterChange('maxExperience', e.target.value)}\n                        className=\"filter-input small\"\n                        min=\"0\"\n                      />\n                    </div>\n                  </div>\n\n                  <div className=\"filter-group\">\n                    <label>Education Level</label>\n                    <input\n                      type=\"text\"\n                      placeholder=\"e.g., Bachelor, Master, PhD\"\n                      value={advancedFilters.education}\n                      onChange={(e) => handleAdvancedFilterChange('education', e.target.value)}\n                      className=\"filter-input\"\n                    />\n                  </div>\n\n                  <div className=\"filter-group\">\n                    <label>Location</label>\n                    <input\n                      type=\"text\"\n                      placeholder=\"e.g., Mumbai, Delhi, Bangalore\"\n                      value={advancedFilters.location}\n                      onChange={(e) => handleAdvancedFilterChange('location', e.target.value)}\n                      className=\"filter-input\"\n                    />\n                  </div>\n\n                  <div className=\"filter-group\">\n                    <label>Skills</label>\n                    <input\n                      type=\"text\"\n                      placeholder=\"e.g., Python, React, Java\"\n                      value={advancedFilters.skills}\n                      onChange={(e) => handleAdvancedFilterChange('skills', e.target.value)}\n                      className=\"filter-input\"\n                    />\n                  </div>\n\n                  <div className=\"filter-group\">\n                    <label>Institution</label>\n                    <input\n                      type=\"text\"\n                      placeholder=\"e.g., IIT, University name\"\n                      value={advancedFilters.institution}\n                      onChange={(e) => handleAdvancedFilterChange('institution', e.target.value)}\n                      className=\"filter-input\"\n                    />\n                  </div>\n\n                  <div className=\"filter-group\">\n                    <label>Graduation Year</label>\n                    <input\n                      type=\"text\"\n                      placeholder=\"e.g., 2020, 2021\"\n                      value={advancedFilters.graduationYear}\n                      onChange={(e) => handleAdvancedFilterChange('graduationYear', e.target.value)}\n                      className=\"filter-input\"\n                    />\n                  </div>\n\n                  <div className=\"filter-group\">\n                    <label>Company</label>\n                    <input\n                      type=\"text\"\n                      placeholder=\"e.g., Google, Microsoft, TCS\"\n                      value={advancedFilters.company}\n                      onChange={(e) => handleAdvancedFilterChange('company', e.target.value)}\n                      className=\"filter-input\"\n                    />\n                  </div>\n\n                  <div className=\"filter-group\">\n                    <label>Minimum Marks (%)</label>\n                    <input\n                      type=\"number\"\n                      placeholder=\"e.g., 75, 80\"\n                      value={advancedFilters.minMarks}\n                      onChange={(e) => handleAdvancedFilterChange('minMarks', e.target.value)}\n                      className=\"filter-input\"\n                      min=\"0\"\n                      max=\"100\"\n                    />\n                  </div>\n                </div>\n              </div>\n            )}\n\n            <div className=\"results-count\">\n              {filteredResults.length} of {searchResults.length} results\n              {hasActiveFilters() && <span className=\"filtered-indicator\"> (filtered)</span>}\n            </div>\n          </div>\n        )}\n      </div>\n\n      {/* Results Section */}\n      {searchResults.length > 0 ? (\n        <div className=\"results-section\">\n          <div className=\"results-header\">\n            <h2>Search Results</h2>\n            <div className=\"results-actions\">\n              <button\n                className=\"btn btn-secondary\"\n                onClick={handleExportAll}\n                title={`Export ${filteredResults.length} results to CSV`}\n              >\n                <FiDownload size={16} />\n                Export All ({filteredResults.length})\n              </button>\n            </div>\n          </div>\n\n          <div className=\"results-grid\">\n            {filteredResults.map((resume) => (\n              <div key={resume.id} className=\"resume-card\">\n                <div className=\"resume-header\">\n                  <div className=\"resume-avatar\">\n                    <FiUser size={24} />\n                  </div>\n                  <div className=\"resume-info\">\n                    <h3 className=\"resume-name\">{resume.name}</h3>\n                    <p className=\"resume-title\">{resume.title}</p>\n                  </div>\n                </div>\n\n                <div className=\"resume-details\">\n                  <div className=\"detail-item location-item\">\n                    <FiMapPin size={14} />\n                    <span>{resume.location}</span>\n                  </div>\n                  <div\n                    className=\"detail-item email-item clickable\"\n                    onClick={() => handleEmailClick(resume.email, resume.name)}\n                    title={`Send email to ${resume.name}`}\n                  >\n                    <FiMail size={14} />\n                    <span>{resume.email}</span>\n                    <FiExternalLink size={12} className=\"external-icon\" />\n                  </div>\n                  <div\n                    className=\"detail-item phone-item clickable\"\n                    onClick={() => handlePhoneClick(resume.phone, resume.name)}\n                    title={`Call ${resume.name}`}\n                  >\n                    <FiPhone size={14} />\n                    <span>{resume.phone}</span>\n                    <FiExternalLink size={12} className=\"external-icon\" />\n                  </div>\n                </div>\n\n                <div className=\"resume-content\">\n                  <div className=\"content-row\">\n                    <div className=\"content-section\">\n                      <h4>Experience</h4>\n                      <p>{resume.experience}</p>\n                    </div>\n                    <div className=\"content-section\">\n                      <h4>Education</h4>\n                      <p>{resume.education}</p>\n                    </div>\n                  </div>\n                  <div className=\"content-section\">\n                    <h4>Skills</h4>\n                    <div className=\"skills-list\">\n                      {resume.skills.map((skill, index) => (\n                        <span key={index} className=\"skill-tag\">{skill}</span>\n                      ))}\n                    </div>\n                  </div>\n                </div>\n\n                <div className=\"resume-actions\">\n                  <button\n                    className=\"btn btn-secondary\"\n                    onClick={() => handleView(resume)}\n                  >\n                    <FiEye size={16} />\n                    View Details\n                  </button>\n                  <button\n                    className=\"btn btn-primary\"\n                    onClick={() => handleDownload(resume)}\n                  >\n                    <FiDownload size={16} />\n                    Download\n                  </button>\n                </div>\n              </div>\n            ))}\n          </div>\n\n          {/* Pagination */}\n          {totalPages > 1 && (\n            <div className=\"pagination-section\">\n              <div className=\"pagination-info\">\n                Showing {((currentPage - 1) * resultsPerPage) + 1} to {Math.min(currentPage * resultsPerPage, totalCount)} of {totalCount} results\n              </div>\n              <div className=\"pagination-controls\">\n                <button\n                  className=\"btn btn-secondary pagination-btn\"\n                  onClick={() => handlePageChange(currentPage - 1)}\n                  disabled={currentPage === 1}\n                >\n                  Previous\n                </button>\n\n                <div className=\"page-numbers\">\n                  {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {\n                    let pageNum;\n                    if (totalPages <= 5) {\n                      pageNum = i + 1;\n                    } else if (currentPage <= 3) {\n                      pageNum = i + 1;\n                    } else if (currentPage >= totalPages - 2) {\n                      pageNum = totalPages - 4 + i;\n                    } else {\n                      pageNum = currentPage - 2 + i;\n                    }\n\n                    return (\n                      <button\n                        key={pageNum}\n                        className={`btn pagination-btn ${currentPage === pageNum ? 'btn-primary' : 'btn-secondary'}`}\n                        onClick={() => handlePageChange(pageNum)}\n                      >\n                        {pageNum}\n                      </button>\n                    );\n                  })}\n                </div>\n\n                <button\n                  className=\"btn btn-secondary pagination-btn\"\n                  onClick={() => handlePageChange(currentPage + 1)}\n                  disabled={currentPage === totalPages}\n                >\n                  Next\n                </button>\n              </div>\n            </div>\n          )}\n        </div>\n      ) : !isSearching ? (\n        <div className=\"empty-state\">\n          <div className=\"empty-icon\">\n            <FiSearch size={48} />\n          </div>\n          <h3>Ready to Search!</h3>\n          <p>Use the search box above to find resumes using natural language, or click \"Show All\" to view all resumes in the database.</p>\n          <div className=\"search-examples\">\n            <h4>Try searching for:</h4>\n            <ul>\n              <li>\"Find Python developers\"</li>\n              <li>\"Show teachers with 5+ years experience\"</li>\n              <li>\"Software engineers in Mumbai\"</li>\n            </ul>\n          </div>\n        </div>\n      ) : null}\n\n      {/* Resume Details Modal */}\n      <ResumeModal\n        resume={selectedResume}\n        isOpen={isModalOpen}\n        onClose={handleCloseModal}\n      />\n    </div>\n  );\n};\n\nexport default SearchResumes;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,QAAQ,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,KAAK,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,OAAO,EAAEC,cAAc,EAAEC,MAAM,EAAEC,GAAG,EAAEC,UAAU,EAAEC,aAAa,EAAEC,WAAW,QAAQ,gBAAgB;AAC9K,SAASC,KAAK,QAAQ,gBAAgB;AACtC,OAAOC,WAAW,MAAM,2BAA2B;AACnD,OAAO,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE7B,MAAMC,aAAa,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC1B,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACyB,WAAW,EAAEC,cAAc,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAAC2B,aAAa,EAAEC,gBAAgB,CAAC,GAAG5B,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAAC6B,WAAW,EAAEC,cAAc,CAAC,GAAG9B,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC+B,WAAW,EAAEC,cAAc,CAAC,GAAGhC,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACiC,eAAe,EAAEC,kBAAkB,CAAC,GAAGlC,QAAQ,CAAC;IACrDmC,aAAa,EAAE,EAAE;IACjBC,aAAa,EAAE,EAAE;IACjBC,SAAS,EAAE,EAAE;IACbC,QAAQ,EAAE,EAAE;IACZC,MAAM,EAAE,EAAE;IACVC,QAAQ,EAAE,EAAE;IACZC,WAAW,EAAE,EAAE;IACfC,cAAc,EAAE,EAAE;IAClBC,OAAO,EAAE;EACX,CAAC,CAAC;EACF,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAG7C,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAAC8C,WAAW,EAAEC,cAAc,CAAC,GAAG/C,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACgD,WAAW,EAAEC,cAAc,CAAC,GAAGjD,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAACkD,UAAU,EAAEC,aAAa,CAAC,GAAGnD,QAAQ,CAAC,CAAC,CAAC;EAC/C,MAAM,CAACoD,UAAU,EAAEC,aAAa,CAAC,GAAGrD,QAAQ,CAAC,CAAC,CAAC;EAC/C,MAAMsD,cAAc,GAAG,EAAE;;EAEzB;EACA,MAAMC,WAAW,GAAG,CAClB;IACEC,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,UAAU;IAChBC,KAAK,EAAE,0BAA0B;IACjCC,KAAK,EAAE,oBAAoB;IAC3BC,KAAK,EAAE,mBAAmB;IAC1BtB,QAAQ,EAAE,mBAAmB;IAC7BuB,UAAU,EAAE,UAAU;IACtBtB,MAAM,EAAE,CAAC,QAAQ,EAAE,OAAO,EAAE,SAAS,EAAE,KAAK,CAAC;IAC7CF,SAAS,EAAE,qBAAqB;IAChCyB,OAAO,EAAE,2EAA2E;IACpFC,KAAK,EAAE,EAAE;IACTC,OAAO,EAAE;MACPC,MAAM,EAAE;QACNC,mBAAmB,EAAE;UACnBC,QAAQ,EAAE,UAAU;UACpBC,KAAK,EAAE,oBAAoB;UAC3BC,aAAa,EAAE,mBAAmB;UAClCC,OAAO,EAAE;QACX,CAAC;QACDC,cAAc,EAAE,CACd;UACEC,IAAI,EAAE,0BAA0B;UAChCC,WAAW,EAAE,WAAW;UACxBC,SAAS,EAAE,MAAM;UACjBC,OAAO,EAAE,SAAS;UAClB,4BAA4B,EAAE;QAChC,CAAC,EACD;UACEH,IAAI,EAAE,mBAAmB;UACzBC,WAAW,EAAE,YAAY;UACzBC,SAAS,EAAE,MAAM;UACjBC,OAAO,EAAE,MAAM;UACf,4BAA4B,EAAE;QAChC,CAAC,CACF;QACDC,SAAS,EAAE,CACT;UACEC,MAAM,EAAE,qBAAqB;UAC7BC,WAAW,EAAE,qBAAqB;UAClCC,cAAc,EAAE,MAAM;UACtB,aAAa,EAAE;QACjB,CAAC,CACF;QACDC,MAAM,EAAE,CAAC,QAAQ,EAAE,OAAO,EAAE,SAAS,EAAE,KAAK,CAAC;QAC7CC,0BAA0B,EAAE;MAC9B;IACF;EACF,CAAC,EACD;IACEzB,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,YAAY;IAClBC,KAAK,EAAE,oBAAoB;IAC3BC,KAAK,EAAE,sBAAsB;IAC7BC,KAAK,EAAE,mBAAmB;IAC1BtB,QAAQ,EAAE,cAAc;IACxBuB,UAAU,EAAE,UAAU;IACtBtB,MAAM,EAAE,CAAC,OAAO,EAAE,YAAY,EAAE,KAAK,EAAE,YAAY,CAAC;IACpDF,SAAS,EAAE,qBAAqB;IAChCyB,OAAO,EAAE,iEAAiE;IAC1EC,KAAK,EAAE,EAAE;IACTC,OAAO,EAAE;MACPC,MAAM,EAAE;QACNC,mBAAmB,EAAE;UACnBC,QAAQ,EAAE,YAAY;UACtBC,KAAK,EAAE,sBAAsB;UAC7BC,aAAa,EAAE,mBAAmB;UAClCC,OAAO,EAAE;QACX,CAAC;QACDC,cAAc,EAAE,CACd;UACEC,IAAI,EAAE,oBAAoB;UAC1BC,WAAW,EAAE,eAAe;UAC5BC,SAAS,EAAE,MAAM;UACjBC,OAAO,EAAE,SAAS;UAClB,4BAA4B,EAAE;QAChC,CAAC,CACF;QACDC,SAAS,EAAE,CACT;UACEC,MAAM,EAAE,qBAAqB;UAC7BC,WAAW,EAAE,KAAK;UAClBC,cAAc,EAAE,MAAM;UACtB,aAAa,EAAE;QACjB,CAAC,CACF;QACDC,MAAM,EAAE,CAAC,OAAO,EAAE,YAAY,EAAE,KAAK,EAAE,YAAY,CAAC;QACpDC,0BAA0B,EAAE;MAC9B;IACF;EACF,CAAC,EACD;IACEzB,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,cAAc;IACpBC,KAAK,EAAE,iBAAiB;IACxBC,KAAK,EAAE,wBAAwB;IAC/BC,KAAK,EAAE,mBAAmB;IAC1BtB,QAAQ,EAAE,YAAY;IACtBuB,UAAU,EAAE,UAAU;IACtBtB,MAAM,EAAE,CAAC,QAAQ,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,CAAC;IACjDF,SAAS,EAAE,2BAA2B;IACtCyB,OAAO,EAAE,wEAAwE;IACjFC,KAAK,EAAE,EAAE;IACTC,OAAO,EAAE;MACPC,MAAM,EAAE;QACNC,mBAAmB,EAAE;UACnBC,QAAQ,EAAE,cAAc;UACxBC,KAAK,EAAE,wBAAwB;UAC/BC,aAAa,EAAE,mBAAmB;UAClCC,OAAO,EAAE;QACX,CAAC;QACDC,cAAc,EAAE,CACd;UACEC,IAAI,EAAE,iBAAiB;UACvBC,WAAW,EAAE,qBAAqB;UAClCC,SAAS,EAAE,MAAM;UACjBC,OAAO,EAAE,SAAS;UAClB,4BAA4B,EAAE;QAChC,CAAC,CACF;QACDC,SAAS,EAAE,CACT;UACEC,MAAM,EAAE,2BAA2B;UACnCC,WAAW,EAAE,WAAW;UACxBC,cAAc,EAAE,MAAM;UACtB,aAAa,EAAE;QACjB,CAAC,CACF;QACDC,MAAM,EAAE,CAAC,QAAQ,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,CAAC;QACjDC,0BAA0B,EAAE;MAC9B;IACF;EACF,CAAC,CACF;EAED,MAAMC,YAAY,GAAG,MAAOC,CAAC,IAAK;IAChCA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,IAAI,CAAC7D,WAAW,CAAC8D,IAAI,CAAC,CAAC,EAAE;MACvBtE,KAAK,CAACuE,OAAO,CAAC,6BAA6B,CAAC;MAC5C;IACF;IAEA5D,cAAc,CAAC,IAAI,CAAC;IAEpB,IAAI;MACF;MACA,MAAM6D,QAAQ,GAAG,MAAMC,KAAK,CAAC,6CAA6CC,kBAAkB,CAAClE,WAAW,CAAC,EAAE,EAAE;QAC3GmE,MAAM,EAAE,KAAK;QACbC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB;MACF,CAAC,CAAC;MAEF,MAAMC,IAAI,GAAG,MAAML,QAAQ,CAACM,IAAI,CAAC,CAAC;MAElC,IAAIN,QAAQ,CAACO,EAAE,EAAE;QACf;QACA,IAAIC,kBAAkB,GAAG,EAAE;QAE3B,IAAIH,IAAI,CAACI,UAAU,IAAIC,KAAK,CAACC,OAAO,CAACN,IAAI,CAACI,UAAU,CAAC,EAAE;UACrD;UACAD,kBAAkB,GAAGH,IAAI,CAACI,UAAU,CAACG,GAAG,CAACC,MAAM,IAAI;YAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,sBAAA;YACjD;YACA,MAAMC,UAAU,GAAGT,MAAM,CAACnC,MAAM,IAAImC,MAAM;YAE1C,OAAO;cACL5C,EAAE,EAAE4C,MAAM,CAACU,GAAG,IAAIC,IAAI,CAACC,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;cACzDzD,IAAI,EAAE,EAAA4C,qBAAA,GAAAQ,UAAU,CAAC3C,mBAAmB,cAAAmC,qBAAA,uBAA9BA,qBAAA,CAAgClC,QAAQ,KAAI0C,UAAU,CAACpD,IAAI,IAAI,SAAS;cAC9EC,KAAK,EAAE,EAAA4C,sBAAA,GAAAO,UAAU,CAAC3C,mBAAmB,cAAAoC,sBAAA,uBAA9BA,sBAAA,CAAgCa,WAAW,KAAIN,UAAU,CAACnD,KAAK,IAAI,UAAU;cACpFC,KAAK,EAAE,EAAA4C,sBAAA,GAAAM,UAAU,CAAC3C,mBAAmB,cAAAqC,sBAAA,uBAA9BA,sBAAA,CAAgCnC,KAAK,KAAIyC,UAAU,CAAClD,KAAK,IAAI,UAAU;cAC9EC,KAAK,EAAE,EAAA4C,sBAAA,GAAAK,UAAU,CAAC3C,mBAAmB,cAAAsC,sBAAA,uBAA9BA,sBAAA,CAAgCnC,aAAa,KAAIwC,UAAU,CAACjD,KAAK,IAAI,UAAU;cACtFtB,QAAQ,EAAE,EAAAmE,sBAAA,GAAAI,UAAU,CAAC3C,mBAAmB,cAAAuC,sBAAA,uBAA9BA,sBAAA,CAAgCnC,OAAO,KAAIuC,UAAU,CAACvE,QAAQ,IAAI,aAAa;cACzFuB,UAAU,EAAEgD,UAAU,CAAC5B,0BAA0B,GAC/C,GAAG4B,UAAU,CAAC5B,0BAA0B,QAAQ,GAChD,CAAAyB,qBAAA,GAAAG,UAAU,CAACtC,cAAc,cAAAmC,qBAAA,eAAzBA,qBAAA,CAA2BU,MAAM,GAC/B,GAAGP,UAAU,CAACtC,cAAc,CAAC6C,MAAM,aAAa,GAChDP,UAAU,CAAChD,UAAU,IAAI,oBAAoB;cACjDtB,MAAM,EAAEsE,UAAU,CAAC7B,MAAM,IAAI6B,UAAU,CAACtE,MAAM,IAAI,EAAE;cACpDF,SAAS,EAAE,CAAAsE,qBAAA,GAAAE,UAAU,CAACjC,SAAS,cAAA+B,qBAAA,eAApBA,qBAAA,CAAsBS,MAAM,GACrC,EAAAR,sBAAA,GAAAC,UAAU,CAACjC,SAAS,CAAC,CAAC,CAAC,cAAAgC,sBAAA,uBAAvBA,sBAAA,CAAyB/B,MAAM,KAAI,qBAAqB,GACxDgC,UAAU,CAACxE,SAAS,IAAI,mBAAmB;cAC7CyB,OAAO,EAAE+C,UAAU,CAACQ,SAAS,IAAIR,UAAU,CAACS,OAAO,IAAIT,UAAU,CAAC/C,OAAO,IAAI,sBAAsB;cACnGC,KAAK,EAAE,EAAE;cAAE;cACXC,OAAO,EAAEoC,MAAM,CAAC;YAClB,CAAC;UACH,CAAC,CAAC;QACJ,CAAC,MAAM;UACL;UACAL,kBAAkB,GAAGxC,WAAW,CAACgE,MAAM,CAACnB,MAAM,IAC5CA,MAAM,CAAC3C,IAAI,CAAC+D,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAClG,WAAW,CAACiG,WAAW,CAAC,CAAC,CAAC,IAC7DpB,MAAM,CAAC1C,KAAK,CAAC8D,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAClG,WAAW,CAACiG,WAAW,CAAC,CAAC,CAAC,IAC9DpB,MAAM,CAAC7D,MAAM,CAACmF,IAAI,CAACC,KAAK,IAAIA,KAAK,CAACH,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAClG,WAAW,CAACiG,WAAW,CAAC,CAAC,CAAC,CACrF,CAAC;QACH;QAEA5F,gBAAgB,CAACmE,kBAAkB,CAAC;;QAEpC;QACA,IAAIH,IAAI,CAACgC,MAAM,EAAE;UACf7G,KAAK,CAAC8G,OAAO,CAAC,iBAAiBjC,IAAI,CAACgC,MAAM,EAAE,CAAC;QAC/C,CAAC,MAAM;UACL7G,KAAK,CAAC8G,OAAO,CAAC,SAAS9B,kBAAkB,CAACqB,MAAM,mBAAmB,CAAC;QACtE;;QAEA;QACA,IAAIxB,IAAI,CAACkC,KAAK,EAAE;UACdC,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEpC,IAAI,CAACkC,KAAK,CAAC;UAC1DC,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEpC,IAAI,CAACgC,MAAM,CAAC;QACzC;MACF,CAAC,MAAM;QACL,MAAM,IAAIK,KAAK,CAACrC,IAAI,CAACsC,KAAK,IAAI,eAAe,CAAC;MAChD;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdH,OAAO,CAACG,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;;MAErC;MACA,MAAMC,eAAe,GAAG5E,WAAW,CAACgE,MAAM,CAACnB,MAAM,IAC/CA,MAAM,CAAC3C,IAAI,CAAC+D,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAClG,WAAW,CAACiG,WAAW,CAAC,CAAC,CAAC,IAC7DpB,MAAM,CAAC1C,KAAK,CAAC8D,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAClG,WAAW,CAACiG,WAAW,CAAC,CAAC,CAAC,IAC9DpB,MAAM,CAAC7D,MAAM,CAACmF,IAAI,CAACC,KAAK,IAAIA,KAAK,CAACH,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAClG,WAAW,CAACiG,WAAW,CAAC,CAAC,CAAC,CACrF,CAAC;MAED5F,gBAAgB,CAACuG,eAAe,CAAC;MACjCpH,KAAK,CAACuE,OAAO,CAAC,2BAA2B6C,eAAe,CAACf,MAAM,mBAAmB,CAAC;IACrF,CAAC,SAAS;MACR1F,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;EAED,MAAM0G,aAAa,GAAG,MAAAA,CAAOC,IAAI,GAAG,CAAC,KAAK;IACxC3G,cAAc,CAAC,IAAI,CAAC;IACpB,IAAI;MACF;MACA,MAAM6D,QAAQ,GAAG,MAAMC,KAAK,CAAC,qBAAqB6C,IAAI,UAAU/E,cAAc,EAAE,CAAC;MACjF,MAAMsC,IAAI,GAAG,MAAML,QAAQ,CAACM,IAAI,CAAC,CAAC;MAElC,IAAIN,QAAQ,CAACO,EAAE,EAAE;QACf,IAAIC,kBAAkB,GAAG,EAAE;QAE3B,IAAIH,IAAI,CAAC0C,OAAO,IAAIrC,KAAK,CAACC,OAAO,CAACN,IAAI,CAAC0C,OAAO,CAAC,EAAE;UAC/C;UACAvC,kBAAkB,GAAGH,IAAI,CAAC0C,OAAO,CAACnC,GAAG,CAACC,MAAM,IAAI;YAAA,IAAAmC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;YAC9C;YACA,MAAMjC,UAAU,GAAGT,MAAM,CAACnC,MAAM,IAAImC,MAAM;YAE1C,OAAO;cACL5C,EAAE,EAAE4C,MAAM,CAACU,GAAG,IAAIC,IAAI,CAACC,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;cACzDzD,IAAI,EAAE,EAAA8E,sBAAA,GAAA1B,UAAU,CAAC3C,mBAAmB,cAAAqE,sBAAA,uBAA9BA,sBAAA,CAAgCpE,QAAQ,KAAI0C,UAAU,CAACpD,IAAI,IAAI,SAAS;cAC9EC,KAAK,EAAE,EAAA8E,sBAAA,GAAA3B,UAAU,CAAC3C,mBAAmB,cAAAsE,sBAAA,uBAA9BA,sBAAA,CAAgCrB,WAAW,KAAIN,UAAU,CAACnD,KAAK,IAAI,UAAU;cACpFC,KAAK,EAAE,EAAA8E,sBAAA,GAAA5B,UAAU,CAAC3C,mBAAmB,cAAAuE,sBAAA,uBAA9BA,sBAAA,CAAgCrE,KAAK,KAAIyC,UAAU,CAAClD,KAAK,IAAI,UAAU;cAC9EC,KAAK,EAAE,EAAA8E,sBAAA,GAAA7B,UAAU,CAAC3C,mBAAmB,cAAAwE,sBAAA,uBAA9BA,sBAAA,CAAgCrE,aAAa,KAAIwC,UAAU,CAACjD,KAAK,IAAI,UAAU;cACtFtB,QAAQ,EAAE,EAAAqG,sBAAA,GAAA9B,UAAU,CAAC3C,mBAAmB,cAAAyE,sBAAA,uBAA9BA,sBAAA,CAAgCrE,OAAO,KAAIuC,UAAU,CAACvE,QAAQ,IAAI,aAAa;cACzFuB,UAAU,EAAEgD,UAAU,CAAC5B,0BAA0B,GAC/C,GAAG4B,UAAU,CAAC5B,0BAA0B,QAAQ,GAChD,CAAA2D,sBAAA,GAAA/B,UAAU,CAACtC,cAAc,cAAAqE,sBAAA,eAAzBA,sBAAA,CAA2BxB,MAAM,GAC/B,GAAGP,UAAU,CAACtC,cAAc,CAAC6C,MAAM,aAAa,GAChDP,UAAU,CAAChD,UAAU,IAAI,oBAAoB;cACjDtB,MAAM,EAAEsE,UAAU,CAAC7B,MAAM,IAAI6B,UAAU,CAACtE,MAAM,IAAI,EAAE;cACpDF,SAAS,EAAE,CAAAwG,sBAAA,GAAAhC,UAAU,CAACjC,SAAS,cAAAiE,sBAAA,eAApBA,sBAAA,CAAsBzB,MAAM,GACrC,EAAA0B,sBAAA,GAAAjC,UAAU,CAACjC,SAAS,CAAC,CAAC,CAAC,cAAAkE,sBAAA,uBAAvBA,sBAAA,CAAyBjE,MAAM,KAAI,qBAAqB,GACxDgC,UAAU,CAACxE,SAAS,IAAI,mBAAmB;cAC7CyB,OAAO,EAAE+C,UAAU,CAACQ,SAAS,IAAIR,UAAU,CAACS,OAAO,IAAIT,UAAU,CAAC/C,OAAO,IAAI,sBAAsB;cACnGC,KAAK,EAAE,EAAE;cAAE;cACXC,OAAO,EAAEoC,MAAM,CAAC;YAClB,CAAC;UACH,CAAC,CAAC;QACJ,CAAC,MAAM;UACL;UACAL,kBAAkB,GAAGxC,WAAW;QAClC;QAEA3B,gBAAgB,CAACmE,kBAAkB,CAAC;QACpC9C,cAAc,CAACoF,IAAI,CAAC;QACpBhF,aAAa,CAACuC,IAAI,CAACmD,KAAK,IAAInD,IAAI,CAACoD,KAAK,IAAIjD,kBAAkB,CAACqB,MAAM,CAAC;QACpEjE,aAAa,CAAC4D,IAAI,CAACkC,IAAI,CAAC,CAACrD,IAAI,CAACmD,KAAK,IAAInD,IAAI,CAACoD,KAAK,IAAIjD,kBAAkB,CAACqB,MAAM,IAAI9D,cAAc,CAAC,CAAC;QAClG9B,cAAc,CAAC,EAAE,CAAC;QAElB,IAAIoE,IAAI,CAACsD,MAAM,KAAK,UAAU,EAAE;UAC9BnI,KAAK,CAAC8G,OAAO,CAAC,gBAAgBQ,IAAI,OAAOtB,IAAI,CAACkC,IAAI,CAAC,CAACrD,IAAI,CAACmD,KAAK,IAAInD,IAAI,CAACoD,KAAK,IAAIjD,kBAAkB,CAACqB,MAAM,IAAI9D,cAAc,CAAC,KAAKsC,IAAI,CAACmD,KAAK,IAAInD,IAAI,CAACoD,KAAK,iBAAiB,CAAC;QAC7K,CAAC,MAAM;UACLjI,KAAK,CAACoI,IAAI,CAAC,WAAWpD,kBAAkB,CAACqB,MAAM,UAAU,CAAC;QAC5D;MACF,CAAC,MAAM;QACL,MAAM,IAAIa,KAAK,CAACrC,IAAI,CAACsC,KAAK,IAAI,wBAAwB,CAAC;MACzD;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdH,OAAO,CAACG,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;;MAE3C;MACAtG,gBAAgB,CAAC2B,WAAW,CAAC;MAC7BN,cAAc,CAAC,CAAC,CAAC;MACjBI,aAAa,CAACE,WAAW,CAAC6D,MAAM,CAAC;MACjCjE,aAAa,CAAC4D,IAAI,CAACkC,IAAI,CAAC1F,WAAW,CAAC6D,MAAM,GAAG9D,cAAc,CAAC,CAAC;MAC7D9B,cAAc,CAAC,EAAE,CAAC;MAElBT,KAAK,CAACuE,OAAO,CAAC,6BAA6B/B,WAAW,CAAC6D,MAAM,iBAAiB,CAAC;IACjF,CAAC,SAAS;MACR1F,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;EAED,MAAM0H,gBAAgB,GAAIf,IAAI,IAAK;IACjC,IAAIA,IAAI,IAAI,CAAC,IAAIA,IAAI,IAAInF,UAAU,EAAE;MACnCkF,aAAa,CAACC,IAAI,CAAC;IACrB;EACF,CAAC;EAED,MAAMgB,eAAe,GAAG1H,aAAa,CAAC4F,MAAM,CAACK,MAAM,IAAI;IACrD;IACA,IAAI/F,WAAW,EAAE;MACf,MAAMyH,WAAW,GAAG1B,MAAM,CAACnE,IAAI,CAAC+D,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC5F,WAAW,CAAC2F,WAAW,CAAC,CAAC,CAAC,IAC9DI,MAAM,CAAClE,KAAK,CAAC8D,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC5F,WAAW,CAAC2F,WAAW,CAAC,CAAC,CAAC,IAC9DI,MAAM,CAACtF,QAAQ,CAACkF,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC5F,WAAW,CAAC2F,WAAW,CAAC,CAAC,CAAC;MACpF,IAAI,CAAC8B,WAAW,EAAE,OAAO,KAAK;IAChC;;IAEA;IACA,MAAMC,OAAO,GAAGtH,eAAe;;IAE/B;IACA,IAAIsH,OAAO,CAACpH,aAAa,IAAIoH,OAAO,CAACnH,aAAa,EAAE;MAAA,IAAAoH,eAAA,EAAAC,qBAAA;MAClD,MAAMC,eAAe,GAAG,EAAAF,eAAA,GAAA5B,MAAM,CAAC5D,OAAO,cAAAwF,eAAA,wBAAAC,qBAAA,GAAdD,eAAA,CAAgBvF,MAAM,cAAAwF,qBAAA,uBAAtBA,qBAAA,CAAwBxE,0BAA0B,KACnD0E,sBAAsB,CAAC/B,MAAM,CAAC/D,UAAU,CAAC,IAAI,CAAC;MAErE,IAAI0F,OAAO,CAACpH,aAAa,IAAIuH,eAAe,GAAGE,QAAQ,CAACL,OAAO,CAACpH,aAAa,CAAC,EAAE,OAAO,KAAK;MAC5F,IAAIoH,OAAO,CAACnH,aAAa,IAAIsH,eAAe,GAAGE,QAAQ,CAACL,OAAO,CAACnH,aAAa,CAAC,EAAE,OAAO,KAAK;IAC9F;;IAEA;IACA,IAAImH,OAAO,CAAClH,SAAS,EAAE;MAAA,IAAAwH,gBAAA,EAAAC,qBAAA,EAAAC,sBAAA;MACrB,MAAMC,YAAY,GAAG,EAAAH,gBAAA,GAAAjC,MAAM,CAAC5D,OAAO,cAAA6F,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgB5F,MAAM,cAAA6F,qBAAA,wBAAAC,sBAAA,GAAtBD,qBAAA,CAAwBlF,SAAS,cAAAmF,sBAAA,uBAAjCA,sBAAA,CAAmCrC,IAAI,CAACuC,GAAG,IAC9D,CAACA,GAAG,CAACpF,MAAM,IAAI,EAAE,EAAE2C,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC8B,OAAO,CAAClH,SAAS,CAACmF,WAAW,CAAC,CAAC,CAC3E,CAAC,KAAI,CAACI,MAAM,CAACvF,SAAS,IAAI,EAAE,EAAEmF,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC8B,OAAO,CAAClH,SAAS,CAACmF,WAAW,CAAC,CAAC,CAAC;MACrF,IAAI,CAACwC,YAAY,EAAE,OAAO,KAAK;IACjC;;IAEA;IACA,IAAIT,OAAO,CAACjH,QAAQ,EAAE;MACpB,MAAM4H,eAAe,GAAG,CAACtC,MAAM,CAACtF,QAAQ,IAAI,EAAE,EAAEkF,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC8B,OAAO,CAACjH,QAAQ,CAACkF,WAAW,CAAC,CAAC,CAAC;MACtG,IAAI,CAAC0C,eAAe,EAAE,OAAO,KAAK;IACpC;;IAEA;IACA,IAAIX,OAAO,CAAChH,MAAM,EAAE;MAAA,IAAA4H,cAAA,EAAAC,gBAAA,EAAAC,qBAAA;MAClB,MAAMC,UAAU,GAAGf,OAAO,CAAChH,MAAM,CAACiF,WAAW,CAAC,CAAC;MAC/C,MAAM+C,QAAQ,GAAG,EAAAJ,cAAA,GAAAvC,MAAM,CAACrF,MAAM,cAAA4H,cAAA,uBAAbA,cAAA,CAAezC,IAAI,CAACC,KAAK,IAAIA,KAAK,CAACH,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC6C,UAAU,CAAC,CAAC,KACvE,CAAC,EAAAF,gBAAA,GAAAxC,MAAM,CAAC5D,OAAO,cAAAoG,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBnG,MAAM,cAAAoG,qBAAA,uBAAtBA,qBAAA,CAAwBrF,MAAM,KAAI,EAAE,EAAE0C,IAAI,CAACC,KAAK,IAC/CA,KAAK,CAACH,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC6C,UAAU,CACzC,CAAC;MACjB,IAAI,CAACC,QAAQ,EAAE,OAAO,KAAK;IAC7B;;IAEA;IACA,IAAIhB,OAAO,CAAC9G,WAAW,EAAE;MAAA,IAAA+H,gBAAA,EAAAC,qBAAA,EAAAC,sBAAA;MACvB,MAAMC,cAAc,IAAAH,gBAAA,GAAG5C,MAAM,CAAC5D,OAAO,cAAAwG,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBvG,MAAM,cAAAwG,qBAAA,wBAAAC,sBAAA,GAAtBD,qBAAA,CAAwB7F,SAAS,cAAA8F,sBAAA,uBAAjCA,sBAAA,CAAmChD,IAAI,CAACuC,GAAG,IAChE,CAACA,GAAG,CAACnF,WAAW,IAAImF,GAAG,CAACW,MAAM,IAAI,EAAE,EAAEpD,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC8B,OAAO,CAAC9G,WAAW,CAAC+E,WAAW,CAAC,CAAC,CAChG,CAAC;MACD,IAAI,CAACmD,cAAc,EAAE,OAAO,KAAK;IACnC;;IAEA;IACA,IAAIpB,OAAO,CAAC7G,cAAc,EAAE;MAAA,IAAAmI,gBAAA,EAAAC,qBAAA,EAAAC,sBAAA;MAC1B,MAAMC,WAAW,IAAAH,gBAAA,GAAGjD,MAAM,CAAC5D,OAAO,cAAA6G,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgB5G,MAAM,cAAA6G,qBAAA,wBAAAC,sBAAA,GAAtBD,qBAAA,CAAwBlG,SAAS,cAAAmG,sBAAA,uBAAjCA,sBAAA,CAAmCrD,IAAI,CAACuC,GAAG,IAC7D,CAACA,GAAG,CAAClF,cAAc,IAAIkF,GAAG,CAACgB,IAAI,IAAI,EAAE,EAAEhE,QAAQ,CAAC,CAAC,CAACQ,QAAQ,CAAC8B,OAAO,CAAC7G,cAAc,CACnF,CAAC;MACD,IAAI,CAACsI,WAAW,EAAE,OAAO,KAAK;IAChC;;IAEA;IACA,IAAIzB,OAAO,CAAC5G,OAAO,EAAE;MAAA,IAAAuI,gBAAA,EAAAC,qBAAA,EAAAC,sBAAA;MACnB,MAAMC,UAAU,IAAAH,gBAAA,GAAGtD,MAAM,CAAC5D,OAAO,cAAAkH,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBjH,MAAM,cAAAkH,qBAAA,wBAAAC,sBAAA,GAAtBD,qBAAA,CAAwB5G,cAAc,cAAA6G,sBAAA,uBAAtCA,sBAAA,CAAwC1D,IAAI,CAAC4D,GAAG,IACjE,CAACA,GAAG,CAACC,OAAO,IAAID,GAAG,CAAC7G,WAAW,IAAI6G,GAAG,CAACE,YAAY,IAAI,EAAE,EAAEhE,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC8B,OAAO,CAAC5G,OAAO,CAAC6E,WAAW,CAAC,CAAC,CACjH,CAAC;MACD,IAAI,CAAC6D,UAAU,EAAE,OAAO,KAAK;IAC/B;;IAEA;IACA,IAAI9B,OAAO,CAAC/G,QAAQ,EAAE;MAAA,IAAAiJ,gBAAA,EAAAC,qBAAA,EAAAC,sBAAA;MACpB,MAAMC,WAAW,IAAAH,gBAAA,GAAG7D,MAAM,CAAC5D,OAAO,cAAAyH,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBxH,MAAM,cAAAyH,qBAAA,wBAAAC,sBAAA,GAAtBD,qBAAA,CAAwB9G,SAAS,cAAA+G,sBAAA,uBAAjCA,sBAAA,CAAmCjE,IAAI,CAACuC,GAAG,IAAI;QACjE,MAAM4B,KAAK,GAAGC,UAAU,CAAC7B,GAAG,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC;QACjD,OAAO4B,KAAK,IAAIC,UAAU,CAACvC,OAAO,CAAC/G,QAAQ,CAAC;MAC9C,CAAC,CAAC;MACF,IAAI,CAACoJ,WAAW,EAAE,OAAO,KAAK;IAChC;IAEA,OAAO,IAAI;EACb,CAAC,CAAC;;EAEF;EACA,MAAMjC,sBAAsB,GAAIoC,cAAc,IAAK;IACjD,IAAI,CAACA,cAAc,EAAE,OAAO,CAAC;IAC7B,MAAMC,KAAK,GAAGD,cAAc,CAACC,KAAK,CAAC,oBAAoB,CAAC;IACxD,OAAOA,KAAK,GAAGpC,QAAQ,CAACoC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;EACvC,CAAC;EAED,MAAMC,0BAA0B,GAAGA,CAACC,UAAU,EAAEC,KAAK,KAAK;IACxDjK,kBAAkB,CAACkK,IAAI,KAAK;MAC1B,GAAGA,IAAI;MACP,CAACF,UAAU,GAAGC;IAChB,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAME,eAAe,GAAGA,CAAA,KAAM;IAC5BvK,cAAc,CAAC,EAAE,CAAC;IAClBI,kBAAkB,CAAC;MACjBC,aAAa,EAAE,EAAE;MACjBC,aAAa,EAAE,EAAE;MACjBC,SAAS,EAAE,EAAE;MACbC,QAAQ,EAAE,EAAE;MACZC,MAAM,EAAE,EAAE;MACVC,QAAQ,EAAE,EAAE;MACZC,WAAW,EAAE,EAAE;MACfC,cAAc,EAAE,EAAE;MAClBC,OAAO,EAAE;IACX,CAAC,CAAC;EACJ,CAAC;EAED,MAAM2J,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,OAAOzK,WAAW,IAAI0K,MAAM,CAACC,MAAM,CAACvK,eAAe,CAAC,CAACyF,IAAI,CAACyE,KAAK,IAAIA,KAAK,KAAK,EAAE,CAAC;EAClF,CAAC;EAED,MAAMM,cAAc,GAAIrG,MAAM,IAAK;IACjC,IAAI;MAAA,IAAAsG,eAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,qBAAA,EAAAC,sBAAA;MACF;MACA,MAAMC,aAAa,GAAG;AAC5B,EAAEvH,MAAM,CAAC3C,IAAI;AACb,EAAE2C,MAAM,CAAC1C,KAAK;AACd,EAAE0C,MAAM,CAACzC,KAAK,MAAMyC,MAAM,CAACxC,KAAK,MAAMwC,MAAM,CAAC9D,QAAQ;AACrD;AACA;AACA,EAAE8D,MAAM,CAACtC,OAAO;AAChB;AACA;AACA,EAAE,CAAA4I,eAAA,GAAAtG,MAAM,CAACpC,OAAO,cAAA0I,eAAA,gBAAAC,qBAAA,GAAdD,eAAA,CAAgBzI,MAAM,cAAA0I,qBAAA,eAAtBA,qBAAA,CAAwBpI,cAAc,GACtC6B,MAAM,CAACpC,OAAO,CAACC,MAAM,CAACM,cAAc,CAAC4B,GAAG,CAACmF,GAAG,IAC1C,GAAGA,GAAG,CAACsC,QAAQ,IAAItC,GAAG,CAACuC,QAAQ,OAAOvC,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,YAAY;AACzE,YAAYF,GAAG,CAACwC,QAAQ,IAAIxC,GAAG,CAACyC,KAAK,IAAI,eAAe;AACxD,EAAEzC,GAAG,CAAC0C,WAAW,GAAG,eAAe,GAAG1C,GAAG,CAAC0C,WAAW,GAAG,EAAE;AAC1D,EAAE1C,GAAG,CAAC2C,gBAAgB,GAAG,oBAAoB,GAAG3C,GAAG,CAAC2C,gBAAgB,GAAG,EAAE;AACzE,CACE,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC,GAAG9H,MAAM,CAACvC,UAAU;AAClC;AACA,EAAE,CAAA+I,gBAAA,GAAAxG,MAAM,CAACpC,OAAO,cAAA4I,gBAAA,gBAAAC,qBAAA,GAAdD,gBAAA,CAAgB3I,MAAM,cAAA4I,qBAAA,eAAtBA,qBAAA,CAAwB5H,0BAA0B,GAClD,qBAAqBmB,MAAM,CAACpC,OAAO,CAACC,MAAM,CAACgB,0BAA0B,UAAU,GAAG,EAAE;AACtF;AACA;AACA,EAAE,CAAA6H,gBAAA,GAAA1G,MAAM,CAACpC,OAAO,cAAA8I,gBAAA,gBAAAC,qBAAA,GAAdD,gBAAA,CAAgB7I,MAAM,cAAA8I,qBAAA,eAAtBA,qBAAA,CAAwBnI,SAAS,GACjCwB,MAAM,CAACpC,OAAO,CAACC,MAAM,CAACW,SAAS,CAACuB,GAAG,CAAC8D,GAAG,IACrC,GAAGA,GAAG,CAACpF,MAAM,IAAI,QAAQ,SAASoF,GAAG,CAACnF,WAAW,IAAImF,GAAG,CAACW,MAAM,IAAI,aAAa;AACpF,mBAAmBX,GAAG,CAAClF,cAAc,IAAIkF,GAAG,CAACgB,IAAI,IAAI,eAAe;AACpE,EAAEhB,GAAG,CAAC,aAAa,CAAC,IAAIA,GAAG,CAAC,aAAa,CAAC,GAAG,CAAC,GAAG,SAAS,GAAGA,GAAG,CAAC,aAAa,CAAC,GAAG,GAAG,GAAG,EAAE;AAC1F,CACE,CAAC,CAACiE,IAAI,CAAC,IAAI,CAAC,GAAG9H,MAAM,CAAC/D,SAAS;AACjC;AACA;AACA,EAAE+D,MAAM,CAAC7D,MAAM,CAAC2L,IAAI,CAAC,IAAI,CAAC;AAC1B;AACA,EAAE,CAAAlB,gBAAA,GAAA5G,MAAM,CAACpC,OAAO,cAAAgJ,gBAAA,gBAAAC,qBAAA,GAAdD,gBAAA,CAAgB/I,MAAM,cAAAgJ,qBAAA,eAAtBA,qBAAA,CAAwBkB,SAAS,IAAI/H,MAAM,CAACpC,OAAO,CAACC,MAAM,CAACkK,SAAS,CAAC/G,MAAM,GAAG,CAAC,GAC/E,cAAchB,MAAM,CAACpC,OAAO,CAACC,MAAM,CAACkK,SAAS,CAACD,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,EAAE;AACnE;AACA,EAAE,CAAAhB,gBAAA,GAAA9G,MAAM,CAACpC,OAAO,cAAAkJ,gBAAA,gBAAAC,qBAAA,GAAdD,gBAAA,CAAgBjJ,MAAM,cAAAkJ,qBAAA,eAAtBA,qBAAA,CAAwBiB,QAAQ,IAAIhI,MAAM,CAACpC,OAAO,CAACC,MAAM,CAACmK,QAAQ,CAAChH,MAAM,GAAG,CAAC,GAC7E,aAAahB,MAAM,CAACpC,OAAO,CAACC,MAAM,CAACmK,QAAQ,CAACjI,GAAG,CAACkI,OAAO,IACrD,GAAGA,OAAO,CAACC,IAAI,IAAID,OAAO,CAACE,KAAK,IAAI,SAAS;AACjD,EAAEF,OAAO,CAACL,WAAW,GAAG,eAAe,GAAGK,OAAO,CAACL,WAAW,GAAG,EAAE;AAClE,EAAEK,OAAO,CAACG,YAAY,GAAG,gBAAgB,GAAGH,OAAO,CAACG,YAAY,GAAG,EAAE;AACrE,CACE,CAAC,CAACN,IAAI,CAAC,IAAI,CAAC,EAAE,GAAG,EAAE;AACrB;AACA,EAAE,CAAAd,gBAAA,GAAAhH,MAAM,CAACpC,OAAO,cAAAoJ,gBAAA,gBAAAC,qBAAA,GAAdD,gBAAA,CAAgBnJ,MAAM,cAAAoJ,qBAAA,eAAtBA,qBAAA,CAAwBoB,cAAc,IAAIrI,MAAM,CAACpC,OAAO,CAACC,MAAM,CAACwK,cAAc,CAACrH,MAAM,GAAG,CAAC,GACzF,mBAAmBhB,MAAM,CAACpC,OAAO,CAACC,MAAM,CAACwK,cAAc,CAACtI,GAAG,CAACuI,IAAI,IAC9D,GAAGA,IAAI,CAACJ,IAAI,IAAII,IAAI,CAACH,KAAK,IAAI,eAAe;AACjD,EAAEG,IAAI,CAACC,mBAAmB,GAAG,aAAa,GAAGD,IAAI,CAACC,mBAAmB,GAAG,EAAE;AAC1E,EAAED,IAAI,CAACE,SAAS,GAAG,QAAQ,GAAGF,IAAI,CAACE,SAAS,GAAG,EAAE;AACjD,CACE,CAAC,CAACV,IAAI,CAAC,IAAI,CAAC,EAAE,GAAG,EAAE;AACrB;AACA,EAAE,CAAAZ,gBAAA,GAAAlH,MAAM,CAACpC,OAAO,cAAAsJ,gBAAA,gBAAAC,qBAAA,GAAdD,gBAAA,CAAgBrJ,MAAM,cAAAsJ,qBAAA,eAAtBA,qBAAA,CAAwBsB,YAAY,IAAIzI,MAAM,CAACpC,OAAO,CAACC,MAAM,CAAC4K,YAAY,CAACzH,MAAM,GAAG,CAAC,GACrF,iBAAiBhB,MAAM,CAACpC,OAAO,CAACC,MAAM,CAAC4K,YAAY,CAAC1I,GAAG,CAAC2I,WAAW,IACjE,OAAOA,WAAW,KAAK,QAAQ,GAAGA,WAAW,GAC7C,GAAGA,WAAW,CAACC,eAAe,IAAID,WAAW,CAACR,IAAI,IAAI,aAAa;AACvE,EAAEQ,WAAW,CAACF,SAAS,GAAG,QAAQ,GAAGE,WAAW,CAACF,SAAS,GAAG,EAAE;AAC/D,EAAEE,WAAW,CAACH,mBAAmB,GAAG,gBAAgB,GAAGG,WAAW,CAACH,mBAAmB,GAAG,EAAE;AAC3F,EAAEG,WAAW,CAACd,WAAW,GAAG,eAAe,GAAGc,WAAW,CAACd,WAAW,GAAG,EAAE;AAC1E,CACE,CAAC,CAACE,IAAI,CAAC,IAAI,CAAC,EAAE,GAAG,EAAE;AACrB;AACA,EAAE,CAAAV,gBAAA,GAAApH,MAAM,CAACpC,OAAO,cAAAwJ,gBAAA,gBAAAC,qBAAA,GAAdD,gBAAA,CAAgBvJ,MAAM,cAAAwJ,qBAAA,gBAAAC,sBAAA,GAAtBD,qBAAA,CAAwBvJ,mBAAmB,cAAAwJ,sBAAA,eAA3CA,sBAAA,CAA6CsB,QAAQ,GACrD,qCAAqC5I,MAAM,CAACpC,OAAO,CAACC,MAAM,CAACC,mBAAmB,CAAC8K,QAAQ,EAAE,GAAG,EAAE;AAChG;AACA,gBAAgB,IAAIC,IAAI,CAAC,CAAC,CAACC,kBAAkB,CAAC,CAAC;AAC/C,OAAO,CAAC7J,IAAI,CAAC,CAAC;;MAER;MACA,MAAM8J,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACzB,aAAa,CAAC,EAAE;QAAE0B,IAAI,EAAE;MAAa,CAAC,CAAC;MAC9D,MAAMC,GAAG,GAAGC,GAAG,CAACC,eAAe,CAACL,IAAI,CAAC;MACrC,MAAMM,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;MACxCF,IAAI,CAACG,IAAI,GAAGN,GAAG;MACfG,IAAI,CAACI,QAAQ,GAAG,GAAGzJ,MAAM,CAAC3C,IAAI,CAACqM,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,aAAa;MAChEJ,QAAQ,CAACK,IAAI,CAACC,WAAW,CAACP,IAAI,CAAC;MAC/BA,IAAI,CAACQ,KAAK,CAAC,CAAC;MACZP,QAAQ,CAACK,IAAI,CAACG,WAAW,CAACT,IAAI,CAAC;MAC/BF,GAAG,CAACY,eAAe,CAACb,GAAG,CAAC;MAExBvO,KAAK,CAAC8G,OAAO,CAAC,yBAAyBzB,MAAM,CAAC3C,IAAI,EAAE,CAAC;IACvD,CAAC,CAAC,OAAOyE,KAAK,EAAE;MACdH,OAAO,CAACG,KAAK,CAAC,iBAAiB,EAAEA,KAAK,CAAC;MACvCnH,KAAK,CAACmH,KAAK,CAAC,8CAA8C,CAAC;IAC7D;EACF,CAAC;EAED,MAAMkI,UAAU,GAAIhK,MAAM,IAAK;IAC7BvD,iBAAiB,CAACuD,MAAM,CAAC;IACzBrD,cAAc,CAAC,IAAI,CAAC;IACpBhC,KAAK,CAACoI,IAAI,CAAC,6BAA6B/C,MAAM,CAAC3C,IAAI,EAAE,CAAC;EACxD,CAAC;EAED,MAAM4M,gBAAgB,GAAGA,CAAA,KAAM;IAC7BtN,cAAc,CAAC,KAAK,CAAC;IACrBF,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;EAED,MAAMyN,gBAAgB,GAAGA,CAAC3M,KAAK,EAAEF,IAAI,KAAK;IACxC8M,MAAM,CAACC,IAAI,CAAC,UAAU7M,KAAK,0CAA0CF,IAAI,6GAA6G,EAAE,QAAQ,CAAC;IACjM1C,KAAK,CAACoI,IAAI,CAAC,mCAAmC1F,IAAI,EAAE,CAAC;EACvD,CAAC;EAED,MAAMgN,gBAAgB,GAAGA,CAAC7M,KAAK,EAAEH,IAAI,KAAK;IACxC,IAAIiN,SAAS,CAACC,SAAS,CAAC3E,KAAK,CAAC,4BAA4B,CAAC,EAAE;MAC3DuE,MAAM,CAACC,IAAI,CAAC,OAAO5M,KAAK,EAAE,EAAE,QAAQ,CAAC;IACvC,CAAC,MAAM;MACL8M,SAAS,CAACE,SAAS,CAACC,SAAS,CAACjN,KAAK,CAAC,CAACkN,IAAI,CAAC,MAAM;QAC9C/P,KAAK,CAAC8G,OAAO,CAAC,wBAAwBjE,KAAK,EAAE,CAAC;MAChD,CAAC,CAAC,CAACmN,KAAK,CAAC,MAAM;QACbhQ,KAAK,CAACoI,IAAI,CAAC,UAAUvF,KAAK,EAAE,CAAC;MAC/B,CAAC,CAAC;IACJ;EACF,CAAC;EAID,MAAMoN,eAAe,GAAGA,CAAA,KAAM;IAC5B,IAAI3H,eAAe,CAACjC,MAAM,KAAK,CAAC,EAAE;MAChCrG,KAAK,CAACuE,OAAO,CAAC,sBAAsB,CAAC;MACrC;IACF;IAEA,IAAI;MACF,MAAM2L,UAAU,GAAG5H,eAAe,CAAClD,GAAG,CAACC,MAAM,KAAK;QAChD3C,IAAI,EAAE2C,MAAM,CAAC3C,IAAI;QACjBC,KAAK,EAAE0C,MAAM,CAAC1C,KAAK;QACnBC,KAAK,EAAEyC,MAAM,CAACzC,KAAK;QACnBC,KAAK,EAAEwC,MAAM,CAACxC,KAAK;QACnBtB,QAAQ,EAAE8D,MAAM,CAAC9D,QAAQ;QACzBuB,UAAU,EAAEuC,MAAM,CAACvC,UAAU;QAC7BxB,SAAS,EAAE+D,MAAM,CAAC/D,SAAS;QAC3BE,MAAM,EAAE6D,MAAM,CAAC7D,MAAM,CAAC2L,IAAI,CAAC,IAAI,CAAC;QAChCpK,OAAO,EAAEsC,MAAM,CAACtC,OAAO;QACvBoN,UAAU,EAAE9K,MAAM,CAACrC;MACrB,CAAC,CAAC,CAAC;MAEH,MAAMoN,UAAU,GAAG,CACjB,iFAAiF,EACjF,GAAGF,UAAU,CAAC9K,GAAG,CAACiL,GAAG,IACnB7E,MAAM,CAACC,MAAM,CAAC4E,GAAG,CAAC,CAACjL,GAAG,CAACgG,KAAK,IAC1B,IAAIkF,MAAM,CAAClF,KAAK,CAAC,CAAC2D,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,GACvC,CAAC,CAAC5B,IAAI,CAAC,GAAG,CACZ,CAAC,CACF,CAACA,IAAI,CAAC,IAAI,CAAC;MAEZ,MAAMiB,IAAI,GAAG,IAAIC,IAAI,CAAC,CAAC+B,UAAU,CAAC,EAAE;QAAE9B,IAAI,EAAE;MAAW,CAAC,CAAC;MACzD,MAAMC,GAAG,GAAGC,GAAG,CAACC,eAAe,CAACL,IAAI,CAAC;MACrC,MAAMM,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;MACxCF,IAAI,CAACG,IAAI,GAAGN,GAAG;MACfG,IAAI,CAACI,QAAQ,GAAG,yBAAyB,IAAIZ,IAAI,CAAC,CAAC,CAACqC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM;MACrF7B,QAAQ,CAACK,IAAI,CAACC,WAAW,CAACP,IAAI,CAAC;MAC/BA,IAAI,CAACQ,KAAK,CAAC,CAAC;MACZP,QAAQ,CAACK,IAAI,CAACG,WAAW,CAACT,IAAI,CAAC;MAC/BF,GAAG,CAACY,eAAe,CAACb,GAAG,CAAC;MAExBvO,KAAK,CAAC8G,OAAO,CAAC,YAAYwB,eAAe,CAACjC,MAAM,iBAAiB,CAAC;IACpE,CAAC,CAAC,OAAOc,KAAK,EAAE;MACdH,OAAO,CAACG,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;MACrCnH,KAAK,CAACmH,KAAK,CAAC,6CAA6C,CAAC;IAC5D;EACF,CAAC;EAED,oBACEhH,OAAA;IAAKsQ,SAAS,EAAC,aAAa;IAAAC,QAAA,gBAC1BvQ,OAAA;MAAKsQ,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1BvQ,OAAA;QAAAuQ,QAAA,EAAI;MAAc;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACvB3Q,OAAA;QAAAuQ,QAAA,EAAG;MAAkE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtE,CAAC,eAGN3Q,OAAA;MAAKsQ,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAC7BvQ,OAAA;QAAM4Q,QAAQ,EAAE5M,YAAa;QAACsM,SAAS,EAAC,aAAa;QAAAC,QAAA,eACnDvQ,OAAA;UAAKsQ,SAAS,EAAC,oBAAoB;UAAAC,QAAA,gBACjCvQ,OAAA;YAAKsQ,SAAS,EAAC,sBAAsB;YAAAC,QAAA,gBACnCvQ,OAAA,CAACjB,QAAQ;cAACuR,SAAS,EAAC;YAAa;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACpC3Q,OAAA;cACEmO,IAAI,EAAC,MAAM;cACX0C,WAAW,EAAC,oGAAoG;cAChH5F,KAAK,EAAE5K,WAAY;cACnByQ,QAAQ,EAAG7M,CAAC,IAAK3D,cAAc,CAAC2D,CAAC,CAAC8M,MAAM,CAAC9F,KAAK,CAAE;cAChDqF,SAAS,EAAC,cAAc;cACxBU,QAAQ,EAAEzQ;YAAY;cAAAiQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACN3Q,OAAA;YAAKsQ,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7BvQ,OAAA;cACEmO,IAAI,EAAC,QAAQ;cACbmC,SAAS,EAAC,iBAAiB;cAC3BU,QAAQ,EAAEzQ,WAAY;cAAAgQ,QAAA,EAErBhQ,WAAW,gBACVP,OAAA,CAAAE,SAAA;gBAAAqQ,QAAA,gBACEvQ,OAAA;kBAAKsQ,SAAS,EAAC;gBAAiB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gBAErC;cAAA,eAAE,CAAC,gBAEH3Q,OAAA,CAAAE,SAAA;gBAAAqQ,QAAA,gBACEvQ,OAAA,CAACjB,QAAQ;kBAACkS,IAAI,EAAE;gBAAG;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,UAExB;cAAA,eAAE;YACH;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK,CAAC,eACT3Q,OAAA;cACEmO,IAAI,EAAC,QAAQ;cACbmC,SAAS,EAAC,mBAAmB;cAC7BY,OAAO,EAAEhK,aAAc;cACvB8J,QAAQ,EAAEzQ,WAAY;cAAAgQ,QAAA,EACvB;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,EAGNlQ,aAAa,CAACyF,MAAM,GAAG,CAAC,iBACvBlG,OAAA;QAAKsQ,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7BvQ,OAAA;UAAKsQ,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5BvQ,OAAA;YAAKsQ,SAAS,EAAC,sBAAsB;YAAAC,QAAA,gBACnCvQ,OAAA,CAAChB,QAAQ;cAACsR,SAAS,EAAC;YAAa;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACpC3Q,OAAA;cACEmO,IAAI,EAAC,MAAM;cACX0C,WAAW,EAAC,6CAA6C;cACzD5F,KAAK,EAAEtK,WAAY;cACnBmQ,QAAQ,EAAG7M,CAAC,IAAKrD,cAAc,CAACqD,CAAC,CAAC8M,MAAM,CAAC9F,KAAK,CAAE;cAChDqF,SAAS,EAAC;YAAc;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACN3Q,OAAA;YAAKsQ,SAAS,EAAC,iBAAiB;YAAAC,QAAA,gBAC9BvQ,OAAA;cACEmO,IAAI,EAAC,QAAQ;cACbmC,SAAS,EAAE,mCAAmCzP,WAAW,GAAG,QAAQ,GAAG,EAAE,EAAG;cAC5EqQ,OAAO,EAAEA,CAAA,KAAMpQ,cAAc,CAAC,CAACD,WAAW,CAAE;cAAA0P,QAAA,gBAE5CvQ,OAAA,CAACN,UAAU;gBAACuR,IAAI,EAAE;cAAG;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,oBAExB,EAAC9P,WAAW,gBAAGb,OAAA,CAACJ,WAAW;gBAACqR,IAAI,EAAE;cAAG;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAAG3Q,OAAA,CAACL,aAAa;gBAACsR,IAAI,EAAE;cAAG;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChE,CAAC,EACRvF,gBAAgB,CAAC,CAAC,iBACjBpL,OAAA;cACEmO,IAAI,EAAC,QAAQ;cACbmC,SAAS,EAAC,+BAA+B;cACzCY,OAAO,EAAE/F,eAAgB;cAAAoF,QAAA,gBAEzBvQ,OAAA,CAACP,GAAG;gBAACwR,IAAI,EAAE;cAAG;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,aAEnB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CACT;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAGL9P,WAAW,iBACVb,OAAA;UAAKsQ,SAAS,EAAC,kBAAkB;UAAAC,QAAA,eAC/BvQ,OAAA;YAAKsQ,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BvQ,OAAA;cAAKsQ,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3BvQ,OAAA;gBAAAuQ,QAAA,EAAO;cAAkB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACjC3Q,OAAA;gBAAKsQ,SAAS,EAAC,cAAc;gBAAAC,QAAA,gBAC3BvQ,OAAA;kBACEmO,IAAI,EAAC,QAAQ;kBACb0C,WAAW,EAAC,KAAK;kBACjB5F,KAAK,EAAElK,eAAe,CAACE,aAAc;kBACrC6P,QAAQ,EAAG7M,CAAC,IAAK8G,0BAA0B,CAAC,eAAe,EAAE9G,CAAC,CAAC8M,MAAM,CAAC9F,KAAK,CAAE;kBAC7EqF,SAAS,EAAC,oBAAoB;kBAC9Ba,GAAG,EAAC;gBAAG;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR,CAAC,eACF3Q,OAAA;kBAAAuQ,QAAA,EAAM;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACf3Q,OAAA;kBACEmO,IAAI,EAAC,QAAQ;kBACb0C,WAAW,EAAC,KAAK;kBACjB5F,KAAK,EAAElK,eAAe,CAACG,aAAc;kBACrC4P,QAAQ,EAAG7M,CAAC,IAAK8G,0BAA0B,CAAC,eAAe,EAAE9G,CAAC,CAAC8M,MAAM,CAAC9F,KAAK,CAAE;kBAC7EqF,SAAS,EAAC,oBAAoB;kBAC9Ba,GAAG,EAAC;gBAAG;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN3Q,OAAA;cAAKsQ,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3BvQ,OAAA;gBAAAuQ,QAAA,EAAO;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC9B3Q,OAAA;gBACEmO,IAAI,EAAC,MAAM;gBACX0C,WAAW,EAAC,6BAA6B;gBACzC5F,KAAK,EAAElK,eAAe,CAACI,SAAU;gBACjC2P,QAAQ,EAAG7M,CAAC,IAAK8G,0BAA0B,CAAC,WAAW,EAAE9G,CAAC,CAAC8M,MAAM,CAAC9F,KAAK,CAAE;gBACzEqF,SAAS,EAAC;cAAc;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAEN3Q,OAAA;cAAKsQ,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3BvQ,OAAA;gBAAAuQ,QAAA,EAAO;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACvB3Q,OAAA;gBACEmO,IAAI,EAAC,MAAM;gBACX0C,WAAW,EAAC,gCAAgC;gBAC5C5F,KAAK,EAAElK,eAAe,CAACK,QAAS;gBAChC0P,QAAQ,EAAG7M,CAAC,IAAK8G,0BAA0B,CAAC,UAAU,EAAE9G,CAAC,CAAC8M,MAAM,CAAC9F,KAAK,CAAE;gBACxEqF,SAAS,EAAC;cAAc;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAEN3Q,OAAA;cAAKsQ,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3BvQ,OAAA;gBAAAuQ,QAAA,EAAO;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACrB3Q,OAAA;gBACEmO,IAAI,EAAC,MAAM;gBACX0C,WAAW,EAAC,2BAA2B;gBACvC5F,KAAK,EAAElK,eAAe,CAACM,MAAO;gBAC9ByP,QAAQ,EAAG7M,CAAC,IAAK8G,0BAA0B,CAAC,QAAQ,EAAE9G,CAAC,CAAC8M,MAAM,CAAC9F,KAAK,CAAE;gBACtEqF,SAAS,EAAC;cAAc;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAEN3Q,OAAA;cAAKsQ,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3BvQ,OAAA;gBAAAuQ,QAAA,EAAO;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC1B3Q,OAAA;gBACEmO,IAAI,EAAC,MAAM;gBACX0C,WAAW,EAAC,4BAA4B;gBACxC5F,KAAK,EAAElK,eAAe,CAACQ,WAAY;gBACnCuP,QAAQ,EAAG7M,CAAC,IAAK8G,0BAA0B,CAAC,aAAa,EAAE9G,CAAC,CAAC8M,MAAM,CAAC9F,KAAK,CAAE;gBAC3EqF,SAAS,EAAC;cAAc;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAEN3Q,OAAA;cAAKsQ,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3BvQ,OAAA;gBAAAuQ,QAAA,EAAO;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC9B3Q,OAAA;gBACEmO,IAAI,EAAC,MAAM;gBACX0C,WAAW,EAAC,kBAAkB;gBAC9B5F,KAAK,EAAElK,eAAe,CAACS,cAAe;gBACtCsP,QAAQ,EAAG7M,CAAC,IAAK8G,0BAA0B,CAAC,gBAAgB,EAAE9G,CAAC,CAAC8M,MAAM,CAAC9F,KAAK,CAAE;gBAC9EqF,SAAS,EAAC;cAAc;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAEN3Q,OAAA;cAAKsQ,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3BvQ,OAAA;gBAAAuQ,QAAA,EAAO;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACtB3Q,OAAA;gBACEmO,IAAI,EAAC,MAAM;gBACX0C,WAAW,EAAC,8BAA8B;gBAC1C5F,KAAK,EAAElK,eAAe,CAACU,OAAQ;gBAC/BqP,QAAQ,EAAG7M,CAAC,IAAK8G,0BAA0B,CAAC,SAAS,EAAE9G,CAAC,CAAC8M,MAAM,CAAC9F,KAAK,CAAE;gBACvEqF,SAAS,EAAC;cAAc;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAEN3Q,OAAA;cAAKsQ,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3BvQ,OAAA;gBAAAuQ,QAAA,EAAO;cAAiB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAChC3Q,OAAA;gBACEmO,IAAI,EAAC,QAAQ;gBACb0C,WAAW,EAAC,cAAc;gBAC1B5F,KAAK,EAAElK,eAAe,CAACO,QAAS;gBAChCwP,QAAQ,EAAG7M,CAAC,IAAK8G,0BAA0B,CAAC,UAAU,EAAE9G,CAAC,CAAC8M,MAAM,CAAC9F,KAAK,CAAE;gBACxEqF,SAAS,EAAC,cAAc;gBACxBa,GAAG,EAAC,GAAG;gBACPC,GAAG,EAAC;cAAK;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,eAED3Q,OAAA;UAAKsQ,SAAS,EAAC,eAAe;UAAAC,QAAA,GAC3BpI,eAAe,CAACjC,MAAM,EAAC,MAAI,EAACzF,aAAa,CAACyF,MAAM,EAAC,UAClD,EAACkF,gBAAgB,CAAC,CAAC,iBAAIpL,OAAA;YAAMsQ,SAAS,EAAC,oBAAoB;YAAAC,QAAA,EAAC;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3E,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAGLlQ,aAAa,CAACyF,MAAM,GAAG,CAAC,gBACvBlG,OAAA;MAAKsQ,SAAS,EAAC,iBAAiB;MAAAC,QAAA,gBAC9BvQ,OAAA;QAAKsQ,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7BvQ,OAAA;UAAAuQ,QAAA,EAAI;QAAc;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACvB3Q,OAAA;UAAKsQ,SAAS,EAAC,iBAAiB;UAAAC,QAAA,eAC9BvQ,OAAA;YACEsQ,SAAS,EAAC,mBAAmB;YAC7BY,OAAO,EAAEpB,eAAgB;YACzBtN,KAAK,EAAE,UAAU2F,eAAe,CAACjC,MAAM,iBAAkB;YAAAqK,QAAA,gBAEzDvQ,OAAA,CAACf,UAAU;cAACgS,IAAI,EAAE;YAAG;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBACZ,EAACxI,eAAe,CAACjC,MAAM,EAAC,GACtC;UAAA;YAAAsK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN3Q,OAAA;QAAKsQ,SAAS,EAAC,cAAc;QAAAC,QAAA,EAC1BpI,eAAe,CAAClD,GAAG,CAAEC,MAAM,iBAC1BlF,OAAA;UAAqBsQ,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1CvQ,OAAA;YAAKsQ,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5BvQ,OAAA;cAAKsQ,SAAS,EAAC,eAAe;cAAAC,QAAA,eAC5BvQ,OAAA,CAACb,MAAM;gBAAC8R,IAAI,EAAE;cAAG;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjB,CAAC,eACN3Q,OAAA;cAAKsQ,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BvQ,OAAA;gBAAIsQ,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAErL,MAAM,CAAC3C;cAAI;gBAAAiO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC9C3Q,OAAA;gBAAGsQ,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAErL,MAAM,CAAC1C;cAAK;gBAAAgO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN3Q,OAAA;YAAKsQ,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7BvQ,OAAA;cAAKsQ,SAAS,EAAC,2BAA2B;cAAAC,QAAA,gBACxCvQ,OAAA,CAACZ,QAAQ;gBAAC6R,IAAI,EAAE;cAAG;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACtB3Q,OAAA;gBAAAuQ,QAAA,EAAOrL,MAAM,CAAC9D;cAAQ;gBAAAoP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3B,CAAC,eACN3Q,OAAA;cACEsQ,SAAS,EAAC,kCAAkC;cAC5CY,OAAO,EAAEA,CAAA,KAAM9B,gBAAgB,CAAClK,MAAM,CAACzC,KAAK,EAAEyC,MAAM,CAAC3C,IAAI,CAAE;cAC3DC,KAAK,EAAE,iBAAiB0C,MAAM,CAAC3C,IAAI,EAAG;cAAAgO,QAAA,gBAEtCvQ,OAAA,CAACX,MAAM;gBAAC4R,IAAI,EAAE;cAAG;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACpB3Q,OAAA;gBAAAuQ,QAAA,EAAOrL,MAAM,CAACzC;cAAK;gBAAA+N,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC3B3Q,OAAA,CAACT,cAAc;gBAAC0R,IAAI,EAAE,EAAG;gBAACX,SAAS,EAAC;cAAe;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnD,CAAC,eACN3Q,OAAA;cACEsQ,SAAS,EAAC,kCAAkC;cAC5CY,OAAO,EAAEA,CAAA,KAAM3B,gBAAgB,CAACrK,MAAM,CAACxC,KAAK,EAAEwC,MAAM,CAAC3C,IAAI,CAAE;cAC3DC,KAAK,EAAE,QAAQ0C,MAAM,CAAC3C,IAAI,EAAG;cAAAgO,QAAA,gBAE7BvQ,OAAA,CAACV,OAAO;gBAAC2R,IAAI,EAAE;cAAG;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACrB3Q,OAAA;gBAAAuQ,QAAA,EAAOrL,MAAM,CAACxC;cAAK;gBAAA8N,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC3B3Q,OAAA,CAACT,cAAc;gBAAC0R,IAAI,EAAE,EAAG;gBAACX,SAAS,EAAC;cAAe;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN3Q,OAAA;YAAKsQ,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7BvQ,OAAA;cAAKsQ,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BvQ,OAAA;gBAAKsQ,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,gBAC9BvQ,OAAA;kBAAAuQ,QAAA,EAAI;gBAAU;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACnB3Q,OAAA;kBAAAuQ,QAAA,EAAIrL,MAAM,CAACvC;gBAAU;kBAAA6N,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvB,CAAC,eACN3Q,OAAA;gBAAKsQ,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,gBAC9BvQ,OAAA;kBAAAuQ,QAAA,EAAI;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAClB3Q,OAAA;kBAAAuQ,QAAA,EAAIrL,MAAM,CAAC/D;gBAAS;kBAAAqP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACN3Q,OAAA;cAAKsQ,SAAS,EAAC,iBAAiB;cAAAC,QAAA,gBAC9BvQ,OAAA;gBAAAuQ,QAAA,EAAI;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACf3Q,OAAA;gBAAKsQ,SAAS,EAAC,aAAa;gBAAAC,QAAA,EACzBrL,MAAM,CAAC7D,MAAM,CAAC4D,GAAG,CAAC,CAACwB,KAAK,EAAE4K,KAAK,kBAC9BrR,OAAA;kBAAkBsQ,SAAS,EAAC,WAAW;kBAAAC,QAAA,EAAE9J;gBAAK,GAAnC4K,KAAK;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAqC,CACtD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN3Q,OAAA;YAAKsQ,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7BvQ,OAAA;cACEsQ,SAAS,EAAC,mBAAmB;cAC7BY,OAAO,EAAEA,CAAA,KAAMhC,UAAU,CAAChK,MAAM,CAAE;cAAAqL,QAAA,gBAElCvQ,OAAA,CAACd,KAAK;gBAAC+R,IAAI,EAAE;cAAG;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAErB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACT3Q,OAAA;cACEsQ,SAAS,EAAC,iBAAiB;cAC3BY,OAAO,EAAEA,CAAA,KAAM3F,cAAc,CAACrG,MAAM,CAAE;cAAAqL,QAAA,gBAEtCvQ,OAAA,CAACf,UAAU;gBAACgS,IAAI,EAAE;cAAG;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,YAE1B;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA,GAxEEzL,MAAM,CAAC5C,EAAE;UAAAkO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAyEd,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,EAGL3O,UAAU,GAAG,CAAC,iBACbhC,OAAA;QAAKsQ,SAAS,EAAC,oBAAoB;QAAAC,QAAA,gBACjCvQ,OAAA;UAAKsQ,SAAS,EAAC,iBAAiB;UAAAC,QAAA,GAAC,UACvB,EAAE,CAACzO,WAAW,GAAG,CAAC,IAAIM,cAAc,GAAI,CAAC,EAAC,MAAI,EAACyD,IAAI,CAACsL,GAAG,CAACrP,WAAW,GAAGM,cAAc,EAAEF,UAAU,CAAC,EAAC,MAAI,EAACA,UAAU,EAAC,UAC5H;QAAA;UAAAsO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACN3Q,OAAA;UAAKsQ,SAAS,EAAC,qBAAqB;UAAAC,QAAA,gBAClCvQ,OAAA;YACEsQ,SAAS,EAAC,kCAAkC;YAC5CY,OAAO,EAAEA,CAAA,KAAMhJ,gBAAgB,CAACpG,WAAW,GAAG,CAAC,CAAE;YACjDkP,QAAQ,EAAElP,WAAW,KAAK,CAAE;YAAAyO,QAAA,EAC7B;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAET3Q,OAAA;YAAKsQ,SAAS,EAAC,cAAc;YAAAC,QAAA,EAC1BxL,KAAK,CAACuM,IAAI,CAAC;cAAEpL,MAAM,EAAEL,IAAI,CAACsL,GAAG,CAAC,CAAC,EAAEnP,UAAU;YAAE,CAAC,EAAE,CAACuP,CAAC,EAAEC,CAAC,KAAK;cACzD,IAAIC,OAAO;cACX,IAAIzP,UAAU,IAAI,CAAC,EAAE;gBACnByP,OAAO,GAAGD,CAAC,GAAG,CAAC;cACjB,CAAC,MAAM,IAAI1P,WAAW,IAAI,CAAC,EAAE;gBAC3B2P,OAAO,GAAGD,CAAC,GAAG,CAAC;cACjB,CAAC,MAAM,IAAI1P,WAAW,IAAIE,UAAU,GAAG,CAAC,EAAE;gBACxCyP,OAAO,GAAGzP,UAAU,GAAG,CAAC,GAAGwP,CAAC;cAC9B,CAAC,MAAM;gBACLC,OAAO,GAAG3P,WAAW,GAAG,CAAC,GAAG0P,CAAC;cAC/B;cAEA,oBACExR,OAAA;gBAEEsQ,SAAS,EAAE,sBAAsBxO,WAAW,KAAK2P,OAAO,GAAG,aAAa,GAAG,eAAe,EAAG;gBAC7FP,OAAO,EAAEA,CAAA,KAAMhJ,gBAAgB,CAACuJ,OAAO,CAAE;gBAAAlB,QAAA,EAExCkB;cAAO,GAJHA,OAAO;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAKN,CAAC;YAEb,CAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAEN3Q,OAAA;YACEsQ,SAAS,EAAC,kCAAkC;YAC5CY,OAAO,EAAEA,CAAA,KAAMhJ,gBAAgB,CAACpG,WAAW,GAAG,CAAC,CAAE;YACjDkP,QAAQ,EAAElP,WAAW,KAAKE,UAAW;YAAAuO,QAAA,EACtC;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,GACJ,CAACpQ,WAAW,gBACdP,OAAA;MAAKsQ,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1BvQ,OAAA;QAAKsQ,SAAS,EAAC,YAAY;QAAAC,QAAA,eACzBvQ,OAAA,CAACjB,QAAQ;UAACkS,IAAI,EAAE;QAAG;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnB,CAAC,eACN3Q,OAAA;QAAAuQ,QAAA,EAAI;MAAgB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACzB3Q,OAAA;QAAAuQ,QAAA,EAAG;MAAyH;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eAChI3Q,OAAA;QAAKsQ,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9BvQ,OAAA;UAAAuQ,QAAA,EAAI;QAAkB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC3B3Q,OAAA;UAAAuQ,QAAA,gBACEvQ,OAAA;YAAAuQ,QAAA,EAAI;UAAwB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACjC3Q,OAAA;YAAAuQ,QAAA,EAAI;UAAwC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACjD3Q,OAAA;YAAAuQ,QAAA,EAAI;UAA8B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,GACJ,IAAI,eAGR3Q,OAAA,CAACF,WAAW;MACVoF,MAAM,EAAExD,cAAe;MACvBgQ,MAAM,EAAE9P,WAAY;MACpB+P,OAAO,EAAExC;IAAiB;MAAAqB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3B,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAACvQ,EAAA,CA/9BID,aAAa;AAAAyR,EAAA,GAAbzR,aAAa;AAi+BnB,eAAeA,aAAa;AAAC,IAAAyR,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}