#!/usr/bin/env python3
"""
Test script to verify the functionality of the Resume AI Agent application.
This script tests various components to ensure they work correctly.
"""

import os
import sys
import json
import requests
from pathlib import Path

def test_imports():
    """Test if all required modules can be imported."""
    print("🔍 Testing imports...")

    try:
        import streamlit as st
        print("✅ Streamlit imported successfully")
    except ImportError as e:
        print(f"❌ Streamlit import failed: {e}")
        return False

    try:
        from DisplayJson import CDisplayResume
        print("✅ DisplayJson imported successfully")
    except ImportError as e:
        print(f"❌ DisplayJson import failed: {e}")
        return False

    try:
        from helperMongoDb import MongoDBClient
        print("✅ MongoDBClient imported successfully")
    except ImportError as e:
        print(f"❌ MongoDBClient import failed: {e}")
        return False

    try:
        from extractDataParallel import CProcessDocument
        print("✅ CProcessDocument imported successfully")
    except ImportError as e:
        print(f"❌ CProcessDocument import failed: {e}")
        return False

    return True

def test_file_structure():
    """Test if required files and directories exist."""
    print("\n📁 Testing file structure...")

    required_files = [
        "frontendV3.py",
        "app.py",
        "DisplayJson.py",
        "helperMongoDb.py",
        "extractDataParallel.py",
        ".env",
        "requirements.txt"
    ]

    required_dirs = [
        "resource",
        "config",
        "Data",
        "Data/inputData",
        "Data/inputData/Resume_Schema"
    ]

    all_good = True

    for file in required_files:
        if os.path.exists(file):
            print(f"✅ {file} exists")
        else:
            print(f"❌ {file} missing")
            all_good = False

    for dir in required_dirs:
        if os.path.exists(dir):
            print(f"✅ {dir}/ directory exists")
        else:
            print(f"❌ {dir}/ directory missing")
            all_good = False
            # Create missing directories
            os.makedirs(dir, exist_ok=True)
            print(f"📁 Created {dir}/ directory")

    return all_good

def test_env_variables():
    """Test if environment variables are properly set."""
    print("\n🔧 Testing environment variables...")

    from dotenv import load_dotenv
    load_dotenv()

    required_env_vars = [
        "CONNECTION_URI",
        "DATABASE_NAME",
        "COLLECTION_NAME",
        "STR_API_URL"
    ]

    all_good = True

    for var in required_env_vars:
        value = os.getenv(var)
        if value:
            print(f"✅ {var} is set")
        else:
            print(f"❌ {var} is not set")
            all_good = False

    return all_good

def test_resource_files():
    """Test if resource files exist."""
    print("\n📄 Testing resource files...")

    resource_files = [
        "resource/strSystemPromptInput.txt",
        "resource/strResponseFormatInput.json",
        "resource/strResponseFormatOutput.json",
        "resource/SampleResume.json"
    ]

    all_good = True

    for file in resource_files:
        if os.path.exists(file):
            print(f"✅ {file} exists")
        else:
            print(f"❌ {file} missing")
            all_good = False

    return all_good

def test_mongodb_connection():
    """Test MongoDB connection."""
    print("\n🗄️ Testing MongoDB connection...")

    try:
        from helperMongoDb import MongoDBClient
        mongo_client = MongoDBClient()
        print("✅ MongoDB client created successfully")

        # Try to get all resumes (this will test the connection)
        try:
            cursor = mongo_client.get_all_resume_data()
            resumes = list(cursor)
            print(f"✅ MongoDB connection successful - found {len(resumes)} resumes")
            return True
        except Exception as e:
            print(f"❌ MongoDB query failed: {e}")
            print("💡 Make sure MongoDB is running on localhost:27017")
            print("💡 You can start MongoDB with: mongod --dbpath /path/to/your/db")
            return False

    except Exception as e:
        print(f"❌ MongoDB connection failed: {e}")
        print("💡 Check if MongoDB is installed and running")
        return False

def test_api_endpoints():
    """Test if API endpoints are accessible."""
    print("\n🌐 Testing API endpoints...")

    api_url = os.getenv('STR_API_URL', 'http://127.0.0.1:8001/query/')
    base_url = api_url.replace('/query/', '')

    print(f"Testing API at: {base_url}")

    # Test root endpoint
    try:
        response = requests.get(f"{base_url}/", timeout=5)
        if response.status_code == 200:
            print("✅ API root endpoint accessible")
        else:
            print(f"❌ API root endpoint returned status {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ API root endpoint not accessible: {e}")
        print("💡 Make sure to start the API server with: uvicorn app:app --port 8001 --reload")
        return False

    # Test show all endpoint
    try:
        response = requests.get(f"{base_url}/showall", timeout=5)
        if response.status_code == 200:
            print("✅ API showall endpoint accessible")
        else:
            print(f"❌ API showall endpoint returned status {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ API showall endpoint not accessible: {e}")
        return False

    return True

def main():
    """Run all tests."""
    print("🚀 Starting Resume AI Agent Functionality Tests\n")

    tests = [
        ("Import Tests", test_imports),
        ("File Structure Tests", test_file_structure),
        ("Environment Variables Tests", test_env_variables),
        ("Resource Files Tests", test_resource_files),
        ("MongoDB Connection Tests", test_mongodb_connection),
        ("API Endpoints Tests", test_api_endpoints)
    ]

    results = []

    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"Running {test_name}")
        print('='*50)

        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))

    # Summary
    print(f"\n{'='*50}")
    print("TEST SUMMARY")
    print('='*50)

    passed = 0
    total = len(results)

    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name}: {status}")
        if result:
            passed += 1

    print(f"\nOverall: {passed}/{total} tests passed")

    if passed == total:
        print("\n🎉 All tests passed! The application should work correctly.")
        print("\nTo run the application:")
        print("1. Start the backend API: uvicorn app:app --port 8001 --reload")
        print("2. Start the frontend: streamlit run frontendV3.py --server.port 9024")
    else:
        print(f"\n⚠️ {total - passed} test(s) failed. Please fix the issues before running the application.")

    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
