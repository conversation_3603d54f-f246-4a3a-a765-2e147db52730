import logging,os
import mysql.connector
from config import DB_CONFIG
from helpers import connectToDatabase, ExecuteQuery

log_dir = "./logs"
log_file = os.path.join(log_dir, "app.log")

# Create logs directory if it doesn't exist
if not os.path.exists(log_dir):
    os.makedirs(log_dir)

logging.basicConfig(
    filename=log_file,  # Log file name
    level=logging.INFO,  # Log level
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",  # Log format
    filemode="a",  # Append mode (use "w" for overwrite mode)
)
logger = logging.getLogger(__name__)

conn = connectToDatabase()
cursor = conn.cursor(dictionary=True)

#Reads and executes SQL commands from a file.

for i in ["resource//databaseCreateTables.sql", "resource//databaseAddDataWithTotalExp.sql"]:
    # Reading the SQL file
    with open(i, 'r') as file:
        sql_commands = file.read()

    # Executing the SQL commands
    try:
        for command in sql_commands.split(';'):
            if command.strip():
                cursor.execute(command)
                logger.info(f"Executed SQL command: {command.strip()[:50]}...")
                print(f"Executed: {command}")
    except Exception as e:
        print(f"Error executing SQL commands: {e}")
        logger.error(f"Error executing SQL file : {e}")

conn.commit()
logger.info("All SQL files executed successfully.")  
cursor.close()
conn.close()